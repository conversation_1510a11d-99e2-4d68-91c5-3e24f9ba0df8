<template>
  <div class="app-container">
    <el-form ref="form" label-width="140px" :model="form" :rules="rules">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" clearable maxlength="150" show-word-limit />
      </el-form-item>
      <el-form-item label="分类" prop="cateId">
        <el-select v-model="form.cateId" filterable clearable placeholder="请选择">
          <el-option v-for="item in cateList" :key="item.cateId" :label="item.name" :value="item.cateId" />
        </el-select>
      </el-form-item>
      <el-form-item label="封面" required>
        <singleImage v-model="form.pic" width="200px" height="100px" type="article" :url="picUrl" />
        <div class="tip">仅限1个文件。允许的类型：png、gif、jpg、jpeg。图片大小：1M。图片尺寸：306 x 154</div>
      </el-form-item>
      <el-form-item label="Alt">
        <el-input v-model="form.picAlt" clearable maxlength="120" show-word-limit />
        <span class="tip">屏幕阅读器使用的图像的简短描述，并在未加载图象时显示。这对可访问行很重要</span>

      </el-form-item>
      <el-form-item label="来源" required>
        <div class="min-form">
          <div>网址:</div>
          <el-input v-model="form.originUrl" clearable @change="handleWeb" />
          <div class="tip">以http://或https://开头的网址</div>
          <div>链接文字:</div>
          <el-input v-model="form.origin" clearable maxlength="50" show-word-limit />
        </div>
      </el-form-item>
      <el-form-item label="关键词 (keywords)">
        <el-input v-model="form.keywords" type="textarea" clearable maxlength="300" show-word-limit />
      </el-form-item>
      <el-form-item label="描述 (description)">
        <el-input v-model="form.description" type="textarea" clearable maxlength="300" show-word-limit />
      </el-form-item>
      <el-form-item label="内容" required>
        <tinymce ref="tinymce" v-model="form.content" :height="200" upload-type="article" />
      </el-form-item>
      <el-form-item label="标签 (tags)">
        <el-input v-model="form.tags" clearable maxlength="150" show-word-limit />
        <div class="tip">请输入一个逗号分割的列表，如：Amsterdam,Mexico,City,"Cleveland,Onio"</div>
      </el-form-item>
      <el-form-item label="作者" required>
        <div class="min-form">
          <div>网址:</div>
          <el-input v-model="form.authorUrl" clearable @change="handleWeb" />
          <div class="tip">以http://或https://开头的网址</div>
          <div>链接文字:</div>
          <el-input v-model="form.author" clearable maxlength="50" show-word-limit />
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm('form')">返回</el-button>
        <el-button type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import singleImage from './components/index.vue'
import Tinymce from '@/components/Tinymce/index.vue'
import request from '@/api/article'

export default {
  name: 'ArticleDetail',
  components: {
    singleImage,
    Tinymce
  },
  data() {
    return {
      request,
      form: {
        author: '',
        authorUrl: '',
        cateId: '',
        content: '',
        description: '',
        keywords: '',
        newsId: '',
        origin: '',
        originUrl: '',
        pic: '',
        picAlt: '',
        tags: '',
        title: '',
        imgUrl: ''
      },
      picUrl: '',
      rules: Object.freeze({
        title: [
          { required: true, message: '请输入', trigger: 'blur' },
          { message: '长度在 150 个字符以内', trigger: 'blur' }
        ],
        cateId: [{ required: true, message: '请选择', trigger: 'change' }]
      }),
      cateList: []
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.query.id
    }
  },
  created() {
    if (this.isEdit) {
      this.request.mediaDetail(this.$route.query.id).then(res => {
        this.picUrl = res.echoPic
        this.form = res
      }).catch(err => {
        this.$message.error(err)
      })
    }
    this.request.cateList().then(res => {
      this.cateList = res
    }).catch(err => {
      this.$message.error(err)
    })
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$router.go(-1)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (!this.form.origin) {
            return this.$message.error('来源链接文字不能为空')
          }
          if (!this.form.author) {
            return this.$message.error('作者链接文字不能为空')
          }
          const reg = /[http|https]:\/\/.*\.[com|cn|org|net|gov|edu|pub]/
          console.log(!reg.test(this.form.originUrl))
          if (this.form.originUrl && !reg.test(this.form.originUrl)) {
            return this.$message.error('请输入以http://或者 https://开头的正确网址！')
          }
          if (this.form.authorUrl && !reg.test(this.form.authorUrl)) {
            return this.$message.error('请输入以http://或者 https://开头的正确网址！')
          }
          const API = this.isEdit ? this.request.updateMedia : this.request.createMedia
          API(this.form).then(() => {
            this.$message.success('操作成功')
            this.$router.go(-1)
          }).catch(err => {
            this.$message.error(err)
          })
        } else {
          return false
        }
      })
    },
    handleWeb(e) {
      const reg = /[http|https]:\/\/.*\.[com|cn|org|net|gov|edu|pub]/
      if (!reg.test(e)) {
        return this.$message.error('请输入以http://或者 https://开头的正确网址！')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.min-form {
  padding: 10px;
  background: #eee;
  border-radius: 5px;
}
.tip {
 color: #a4a4a4;
}
::v-deep .el-input--suffix .el-input__inner {
    padding-right: 80px;
}
</style>
