<template>
  <el-dialog title="确认费用" :before-close="beforeClose" :visible.sync="visible" width="1000px" top="2vh">
    <div class="contain">
      <div v-show="active===1" class="costDetail">
        <el-form ref="form" label-width="160px">

          <el-form-item label="平台技术服务费:">
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr>
                <td>直播类型</td>
                <td>参考价(元)</td>
                <td>实际费用（元）</td>
              </tr>
              <tr v-if="feeRuleOneDto.length>0">
                <td>{{ feeRuleOneDto[0].feeRuleOneDto.liveType.name }}</td>
                <td>{{ feeRuleOneDto[0].feeRuleOneDto.minPrice }} ~ {{ feeRuleOneDto[0].feeRuleOneDto.maxPrice }}</td>
                <td><el-input v-model="feeRuleOneDto[0].feeRuleOneDto.actualPrice" /></td>
              </tr>
            </table>
          </el-form-item>

          <el-form-item label="流量费用:">
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr>
                <td>单价(元)</td>
                <td>流量（G）</td>
                <td>实际费用(元)</td>
              </tr>
              <tr>
                <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.price }}</td>
                <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.liveFlow }}</td>
                <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.actualPrice }}</td>
              </tr>
            </table>
          </el-form-item>

          <el-form-item label="推广费用:">
            <span><i class="el-icon-warning-outline" /> 请填写实际费用</span>
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr>
                <td>推广类型</td>
                <td>参考单价</td>
                <td>推广人数</td>
                <td>实际费用(元)</td>
              </tr>
              <tr v-if="feeRuleThreeDto.length>0">
                <td>{{ feeRuleThreeDto[0].feeRuleThreeDto.promotionType.name }}</td>
                <td>{{ feeRuleThreeDto[0].feeRuleThreeDto.minPrice }} ~ {{ feeRuleThreeDto[0].feeRuleThreeDto.maxPrice }}</td>
                <td>{{ feeRuleThreeDto[0].feeRuleThreeDto.promotionNum }}</td>
                <td><el-input v-model="feeRuleThreeDto[0].feeRuleThreeDto.actualPrice" /></td>
              </tr>
            </table>
          </el-form-item>

          <el-form-item label="讲师服务费:">
            <span><i class="el-icon-warning-outline" /> 请填写实际费用</span>
            <div>讲师：{{ costInfo.lecturerName }} / {{ costInfo.lecturerPhone }}</div>
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr>
                <td>讲师职称</td>
                <td>直播类型</td>
                <td>参考费用(元)</td>
                <td>实际费用(元)</td>
              </tr>
              <tr v-if="feeRuleFourDto.length">
                <td>{{ feeRuleFourDto[0].feeRuleFourDto.academicClass.name }}</td>
                <td>{{ feeRuleFourDto[0].feeRuleFourDto.liveType.name }}</td>
                <td>{{ feeRuleFourDto[0].feeRuleFourDto.minPrice }} ~ {{ feeRuleFourDto[0].feeRuleFourDto.maxPrice }}</td>
                <td><el-input v-model="feeRuleFourDto[0].feeRuleFourDto.actualPrice" /></td>
              </tr>
            </table>
          </el-form-item>

          <el-form-item label="内容扩展方费用:">
            <span><i class="el-icon-warning-outline" /> 请填写实际费用</span>
            <div>内容扩展方：{{ costInfo.extenderName }} / {{ costInfo.extenderPhone }}</div>
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr>
                <td>讲师职称</td>
                <td>直播类型</td>
                <td>参考价(元)</td>
                <td>实际费用(元)</td>
              </tr>
              <tr v-if="feeRuleFiveDto.length">
                <td>{{ feeRuleFiveDto[0].feeRuleFiveDto.academicClass.name }}</td>
                <td>{{ feeRuleFiveDto[0].feeRuleFiveDto.liveType.name }}</td>
                <td>{{ feeRuleFiveDto[0].feeRuleFiveDto.minPrice }} ~ {{ feeRuleFiveDto[0].feeRuleFiveDto.maxPrice }}</td>
                <td><el-input v-model="feeRuleFiveDto[0].feeRuleFiveDto.actualPrice" /></td>
              </tr>
            </table>
          </el-form-item>

        </el-form>
      </div>
      <div v-show="active===2" class="userList">
        <table class="costRulesTable table2" border cellpadding="0" style="border-collapse:collapse;">
          <tr>
            <td>费用项目</td>
            <td>单价(元)</td>
            <td>数量</td>
            <td>小计(元)</td>
          </tr>
          <!-- 1 -->
          <tr>
            <td>平台技术服务费</td>
            <td>{{ feeRuleOneDto[0].feeRuleOneDto.actualPrice }}</td>
            <td>1</td>
            <td>{{ feeRuleOneDto[0].feeRuleOneDto.actualPrice }}</td>
          </tr>
          <!-- 2 -->
          <tr>
            <td>流量费用</td>
            <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.price }}</td>
            <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.liveFlow }}</td>
            <td>{{ feeRuleTwoDto[0].feeRuleTwoDto.actualPrice }}</td>
          </tr>
          <!-- 3 -->
          <tr>
            <td>推广费用</td>
            <td>{{ priceTree }}</td>
            <td>{{ feeRuleThreeDto[0].feeRuleThreeDto.promotionNum }}</td>
            <td>{{ feeRuleThreeDto[0].feeRuleThreeDto.actualPrice }}</td>
          </tr>
          <!-- 4 -->
          <tr>
            <td>讲师服务费</td>
            <td>{{ feeRuleFourDto[0].feeRuleFourDto.actualPrice }}</td>
            <td>1</td>
            <td>{{ feeRuleFourDto[0].feeRuleFourDto.actualPrice }}</td>
          </tr>
          <!-- 5 -->
          <tr>
            <td>内容扩展方费用</td>
            <td>{{ feeRuleFiveDto[0].feeRuleFiveDto.actualPrice }}</td>
            <td>1</td>
            <td>{{ feeRuleFiveDto[0].feeRuleFiveDto.actualPrice }}</td>
          </tr>
          <!-- 合计 -->
          <tr>
            <td colspan="3">合计</td>
            <td>{{ total }}</td>
          </tr>
        </table>
      </div>
      <div class="footerBtn">
        <el-button @click="cancle()">取 消</el-button>
        <el-button v-show="active!==1" @click="back()">上一步</el-button>
        <el-button v-show="active===1" type="primary" @click="next()">提交</el-button>
        <el-button v-show="active===2" type="primary" @click="confirm()">确认</el-button>
      </div>
    </div>
  </el-dialog>

</template>

<script>
import { confirmCost } from '@/api/liveManage'

export default {
  name: 'ComfirmCost',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    costInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      active: 1,
      feeRuleOneDto: [],
      feeRuleTwoDto: [],
      feeRuleThreeDto: [],
      feeRuleFourDto: [],
      feeRuleFiveDto: []
    }
  },
  computed: {
    priceTree() {
      return parseFloat(this.feeRuleThreeDto[0].feeRuleThreeDto.actualPrice / this.feeRuleThreeDto[0].feeRuleThreeDto.promotionNum).toFixed(2)
    },
    total() {
      const one = parseFloat(this.feeRuleOneDto[0].feeRuleOneDto.actualPrice).toFixed(2)
      const two = parseFloat(this.feeRuleTwoDto[0].feeRuleTwoDto.actualPrice).toFixed(2)
      const tree = parseFloat(this.feeRuleThreeDto[0].feeRuleThreeDto.actualPrice).toFixed(2)
      const four = parseFloat(this.feeRuleFourDto[0].feeRuleFourDto.actualPrice).toFixed(2)
      const five = parseFloat(this.feeRuleFiveDto[0].feeRuleFiveDto.actualPrice).toFixed(2)
      const sum = Number(one) + Number(two) + Number(tree) + Number(four) + Number(five)
      return sum
    }
  },
  created() {
    if (!this.costInfo) {
      return
    }
    const obj = JSON.parse(JSON.stringify(this.costInfo))
    const { feeRuleOneDto, feeRuleTwoDto, feeRuleThreeDto, feeRuleFourDto, feeRuleFiveDto } = obj
    this.feeRuleOneDto.push(feeRuleOneDto)
    this.feeRuleTwoDto.push(feeRuleTwoDto)
    this.feeRuleThreeDto.push(feeRuleThreeDto)
    this.feeRuleFourDto.push(feeRuleFourDto)
    this.feeRuleFiveDto.push(feeRuleFiveDto)
  },
  methods: {
    cancle() {
      this.$emit('update:visible', false)
    },
    back() {
      this.active = this.active - 1
    },
    next() {
      const one = this.feeRuleOneDto[0].feeRuleOneDto.actualPrice
      const three = this.feeRuleThreeDto[0].feeRuleThreeDto.actualPrice
      const four = this.feeRuleFourDto[0].feeRuleFourDto.actualPrice
      const five = this.feeRuleFiveDto[0].feeRuleFiveDto.actualPrice
      if (one && three && four && five) {
        this.active = this.active + 1
      } else {
        this.$message.error('请完善费用信息！')
      }
    },
    confirm() {
      const arr = []
      arr.push(this.feeRuleOneDto[0])
      arr.push(this.feeRuleTwoDto[0])
      arr.push(this.feeRuleThreeDto[0])
      arr.push(this.feeRuleFourDto[0])
      arr.push(this.feeRuleFiveDto[0])
      confirmCost(this.costInfo.liveId, arr).then(res => {
        this.$emit('confirmCostSuccess', this.costInfo.liveId)
        this.$message.success('费用确认成功')
      })
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  padding: 20px;
}
.costDetail{
  width: 800px;
  margin: 0 auto 30px;
}
.userList{
  width: 600px;
  margin: 0 auto 30px;
}
.costRulesTable{
  td{
    width: 150px;
    height: 50px;
    text-align: center;
  }
}
.table2{
  tr:nth-child(odd){
    background-color: #eee;
  }
}
.footerBtn{
  width: 100%;
  text-align: center;
  background-color: #fff;
}
</style>
