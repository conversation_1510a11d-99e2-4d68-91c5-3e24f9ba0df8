<template>
  <el-dialog
    title="设置"
    :visible.sync="dialogVisible"
    width="500px"
    center
    :before-close="handleClose"
  >
    服务商:
    <el-select
      v-model="setServiceProviderId"
      placeholder="请选择服务商"
      @change="getOrg"
    >
      <el-option
        v-for="item in serverOptions"
        :key="item.serviceOrgId"
        :label="item.serviceName"
        :value="item.serviceOrgId"
      />
    </el-select>
    <h2>关联产品</h2>
    <h3>
      <span>企业</span>
      <span>产品</span>
    </h3>
    <ul>
      <li
        v-for="(item, index) in oldList"
        :key="index"
      >
        <el-input
          v-model="item.orgzName"
          disabled
        />
        <el-input
          v-model="item.productName"
          disabled
        />
        <i
          v-if="productIds.length > 1"
          class="el-icon-remove-outline del"
          @click="removeProduct(item.productId,index)"
        />
      </li>
      <li
        v-for="(item,index) in relationList"
        :key="'new' + index"
      >
        <el-select
          v-model="relationList[index].orgizId"
          placeholder="请选择企业"
          @change="getProductList(relationList[index].orgizId,index)"
        >
          <el-option
            v-for="v in orgList"
            :key="v.orgId"
            :label="v.orgName"
            :value="v.orgId"
          />
        </el-select>
        <el-select
          v-model="relationList[index].productId"
          placeholder="请选择产品"
          @change="addIds(relationList[index].productId,index)"
        >
          <el-option
            v-for="i in relationList[index].productList"
            :key="i.id"
            :label="i.name"
            :value="i.id"
          />
        </el-select>
        <i
          class="el-icon-remove-outline del"
          @click="removeProduct(item.productId,'new' + index)"
        />
      </li>
      <li>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addProduct()"
        >添加产品</el-button>
      </li>
    </ul>
    <div
      v-if="areaSet"
      class="area"
    >
      <h2>负责区域</h2>
      <el-cascader
        v-model="chargeAreaIds"
        :options="areaOptions"
        :props="{
          multiple: true,
          emitPath: false,
          value:'areaId',
          label:'name',
          children:'childList'
        }"
        collapse-tags
        clearable
        placeholder="选择地区"
      />
    </div>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="edit()"
      >确定</el-button>
    </span>

    <el-dialog
      title="提示"
      :visible.sync="errorDialogVisible"
      width="400px"
      top="200px"
      center
      append-to-body
      :before-close="errorHandleClose"
    >
      <div v-show="errorParams.errStatus === 2">此身份证已在蚂蚁平台存在，请到蚂蚁平台手动创建该用户后再进行设置</div>
      <div v-show="errorParams.errStatus === 3">
        <p>此银行卡状态异常，请更换银行卡后再完成设置操作。</p>
        <el-form
          ref="form"
          label-width="80px"
          :model="errorParams"
          :rules="rules"
        >
          <el-form-item
            label="银行卡号"
            prop="bankCardNo"
          >
            <el-input v-model="errorParams.bankCardNo" />
          </el-form-item>
          <el-form-item
            label="开户支行"
            prop="bankSubName"
          >
            <el-input v-model="errorParams.bankSubName" />
          </el-form-item>
          <el-form-item
            label="手机号码"
            prop="bankBindPhone"
          >
            <el-input v-model="errorParams.bankBindPhone" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="errorHandleClose">取消</el-button>
        <el-button
          type="primary"
          @click="errorHandleClose('submit')"
        >{{ errorParams.errStatus === 2 ? '已完成创建' : '确定' }}</el-button>
      </div>
    </el-dialog>

  </el-dialog>
</template>

<script>
import { getUnitList } from '@/api/userManage'
import { productList, serviceProviderList } from '@/api/marketing/taskPromote'
import {
  settingDetail,
  settingEdit,
  settingEditByError
} from '@/api/marketing/userPromote'
import { getAreaTree } from '@/api/area'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: null
    },
    serviceProviderId: {
      type: String,
      default: null
    },
    phone: {
      type: String,
      default: null
    },
    areaSet: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      oldList: [],
      serverOptions: [],
      orgList: [],
      relationList: [],
      productIds: [],
      chargeAreaIds: [],
      areaOptions: [],
      productKey: '',
      errorDialogVisible: false,
      errorParams: {
        bankBindPhone: '',
        bankCardNo: '',
        bankSubName: '',
        errStatus: null,
        finishSettingKey: '',
        userId: ''
      },
      rules: {
        bankCardNo: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' }
        ],
        bankSubName: [
          { required: true, message: '请输入开户支行', trigger: 'blur' }
        ],
        bankBindPhone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' }
        ]
      },
      setServiceProviderId: ''
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        if (this.areaSet) {
          getAreaTree().then((res) => {
            this.areaOptions = res
          })
        }
        this.setServiceProviderId = this.serviceProviderId
        this.getOrg()
        settingDetail({ userId: this.userId }).then((res) => {
          this.chargeAreaIds = res.chargeAreaIds
          this.productIds = res.productList.map((v) => v.productId)
          this.oldList = res.productList
        })
      }
    }
  },
  created() {
    this.getOrg()
    serviceProviderList().then((res) => {
      this.serverOptions = res
    })
  },
  methods: {
    handleClose() {
      this.relationList = []
      this.$emit('update:dialogVisible', false)
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002,
          serviceProviderOrgId: this.setServiceProviderId
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then((res) => {
        this.orgList = res.records
      })
    },
    getProductList(id, index) {
      productList({ orgzId: id }).then((res) => {
        this.relationList[index].productList = res
      })
    },
    addProduct() {
      this.relationList.push({ orgId: '', productList: [], productId: '' })
    },
    removeProduct(id, index) {
      if (index >= 0) {
        this.oldList.splice(index, 1)
      } else {
        const i = parseInt(index.substr(3, 1))
        this.relationList.splice(i, 1)
      }
      if (this.productIds.length > 1) {
        this.productIds.splice(this.productIds.indexOf(id), 1)
      } else {
        if (index >= 0) this.$message.error('至少要有一个关联产品')
      }
    },
    addIds(id, index) {
      if (this.productIds.find((item) => item === id)) {
        this.relationList[index].productId = ''
        this.$message.error('请勿重复关联同一产品')
      } else {
        this.productIds.push(id)
      }
    },
    edit() {
      const param = {
        chargeAreaIds: this.chargeAreaIds,
        productIds: this.productIds,
        userId: this.userId,
        serviceProviderOrgId: this.setServiceProviderId
      }
      settingEdit(param).then((res) => {
        if (res.status === 1) {
          this.$message.success('已成功修改设置')
          this.handleClose()
          this.$parent.getUserList()
        } else {
          this.errorParams.errStatus = res.status
          this.errorParams.finishSettingKey = res.finishSettingKey
          this.errorParams.bankBindPhone = this.phone
          this.errorParams.userId = this.userId
          this.errorDialogVisible = true
        }
      })
    },
    errorHandleClose(type) {
      if (type === 'submit') {
        this.$refs.form.validate((valid) => {
          if (this.errorParams.errStatus === 2 || valid) {
            settingEditByError(this.errorParams).then(() => {
              this.errorDialogVisible = false
              this.errorParams = {
                bankBindPhone: this.phone,
                bankCardNo: '',
                bankSubName: '',
                errStatus: null,
                finishSettingKey: '',
                userId: this.userId
              }
            })
          }
        })
      } else {
        this.errorDialogVisible = false
        this.errorParams = {
          bankBindPhone: this.phone,
          bankCardNo: '',
          bankSubName: '',
          errStatus: null,
          finishSettingKey: '',
          userId: this.userId
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 10px 30px;
    color: #000;
    max-height: 510px;
    overflow-y: auto;
    /* 整个滚动条 */
    &::-webkit-scrollbar {
      /* 对应纵向滚动条的宽度 */
      width: 10px;
      /* 对应横向滚动条的宽度 */
      height: 10px;
    }

    /* 滚动条上的滚动滑块 */
    &::-webkit-scrollbar-thumb {
      background-color: #d0d3d9;
      border-radius: 32px;
    }
    h2 {
      display: flex;
      align-items: center;
      font-size: 22px;
      &:before {
        content: '';
        display: block;
        margin-right: 10px;
        width: 3px;
        height: 24px;
        background-color: #409eff;
      }
    }
    h3 {
      font-size: 18px;
      span {
        margin-right: 170px;
      }
    }
    ul {
      margin: 0 auto;
      padding: 0;
      li {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        .el-input,
        .el-select {
          margin-right: 20px;
          width: 191px;
        }
        .del {
          font-size: 18px;
          color: #409eff;
          cursor: pointer;
        }
      }
    }
    .area {
      margin-top: 45px;
      .el-cascader {
        width: 300px;
      }
    }
  }
}
</style>
