<template>
  <div class="app-container">
    <!-- header -->
    <div class="search-column">
      <div class="fl">
        <div class="search-column__item" style="width:400px;">
          <el-input v-model="tableQuery.condition.keyword" clearable placeholder="请输入搜索关键字" @clear="searchKey()" @keydown.enter.native="searchKey()">
            <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKey()" />
          </el-input>
        </div>
      </div>
      <div class="fr">
        <!-- <div class="search-column__item">
          <el-button type="primary" @click="showImport('')">批量导入卫健委单位</el-button>
        </div> -->
        <div class="search-column__item">
          <el-button type="primary" @click="wjwImportHistoryVisiable = true">查看导入结果</el-button>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table
      v-if="listShow"
      :data="userList"
      row-key="id"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :lazy="lazy"
      :load="handleLoadList"
      border
      stripe
      :expand-row-keys="expand"
    >
      <el-table-column
        v-for="col in tableColumnList"
        :key="col.id"
        :prop="col.prop"
        :label="col.label"
        :align="col.align"
        :width="col.width"
      >
        <template slot-scope="scope">
          <span v-if="col.fliter === 'orgName'" :class="selectItem(scope.row.orgName)">
            {{ scope.row.orgName | filterWidth }}({{ scope.row.subCount }})
          </span>
          <span v-else>{{ scope.row[col.prop] === ''?'--':scope.row[col.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="340px"
      >
        <template slot-scope="{row}">
          <el-button v-if="row.name.indexOf('医院') === -1" size="mini" type="primary" @click="showImport(row)">批量导入医院单位</el-button>
          <el-button v-if="row.name.indexOf('医院') === -1" size="mini" type="primary" @click="handleAddRow(row)">添加下级</el-button>
          <el-button v-if="row.hasSub" size="mini" type="danger" @click="handleUnbindRow(row)">解绑下级</el-button>
          <el-button v-if="!row.hasSub && row.parentOrgId" size="mini" type="danger" @click="handleUnbindRow(row)">解绑</el-button>
        </template>
      </el-table-column>

    </el-table>
    <!-- pagination -->
    <Pagination :layout="layout" class="text-center" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <WjwDialog :title="title" :select-org="currentOrgItem" :show.sync="wjwDialogVisiable" @handleAddSuccess="handleAddSuccess()" />
    <WjwImport :show.sync="wjwImportVisiable" :org-id="wjwImportOrgId" :parent-org-id="wjwImportParentOrgId" @error="handleImportErr" />
    <WjwImportHistory :show.sync="wjwImportHistoryVisiable" @error="handleImportErr" />
    <!-- 模板内容异常无法导入 -->
    <el-dialog
      :title="errMessageTitle"
      :visible.sync="errDialogVisible"
      width="635px"
      center
    >
      <!-- <span>请根据以下检测到的问题，重新编辑模板内容后再次导入。</span> -->
      <ul class="message-ul">
        <li v-for="(item, index) in errMessageList" :key="index">{{ errMessageList[index] }}</li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button style="background: #2CB39B;color: #ffffff" @click="errDialogVisible = false">关 闭</el-button>
        <!-- <el-button v-if="batchNo" type="primary" @click="exportIssueDetails()">导出问题内容</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { wjwList, getChildrenByOrgId, unbindWjw } from '@/api/wjw'
import Pagination from '@/components/Pagination'
import WjwDialog from './components/dialog/index'
import WjwImport from './components/import/index'
import WjwImportHistory from './components/import/history'

export default {
  components: {
    Pagination,
    WjwDialog,
    WjwImport,
    WjwImportHistory
  },
  filters: {
    // 机构名称长度限制（超过50省略）
    filterWidth: function(val) {
      if (val.length > 50) {
        return val.slice(0, 50) + '...'
      }
      return val
    }
  },
  data() {
    return {
      lazy: false,
      layout: 'total, prev, pager, next, jumper',
      // add sub org dialog setting
      title: '添加下级单位',
      // 导入弹窗数据
      wjwImportVisiable: false,
      wjwImportOrgId: '',
      wjwImportParentOrgId: '',
      wjwImportHistoryVisiable: false,
      wjwDialogVisiable: false,
      // 导入异常弹窗
      errDialogVisible: false,
      errMessage: '',
      errMessageTitle: '',
      errMessageList: [],
      batchNo: '',
      // org list query
      tableQuery: {
        'condition': {
          'keyword': ''
        },
        'orderBys': [
          {
            'asc': true,
            'column': ''
          }
        ],
        'pager': {
          'page': 1,
          'pageSize': 10
        }
      },
      // table head list
      tableColumnList: [
        { label: '单位名称', prop: '', algin: 'center', fliter: 'orgName', width: '' },
        { label: '层级类型', prop: 'levelName', algin: 'center', width: '120px' },
        { label: '单位类型', prop: 'name', algin: 'center', width: '120px' }
      ],
      userList: [],
      total: 0,
      listShow: true,
      expand: [],
      // current org
      currentOrgItem: {}
    }
  },
  watch: {
    userList: {
      handler(val) {
      },
      deep: true
    }
  },
  created() {
    this.getWjwList()
  },
  methods: {
    // 导出结果内容
    // exportIssueDetails() {
    //   window.location.href = request.getDownloadRecordMessage({ batchNo: this.batchNo })
    // },
    // 导入失败
    handleImportErr(err) {
      this.errDialogVisible = true
      this.errMessageTitle = err.title
      this.errMessage = err.message
      this.errMessageList = err.message.split(';')
      this.batchNo = err.batchNo
    },
    // 显示导入弹窗
    showImport(row) {
      this.wjwImportVisiable = true
      this.wjwImportOrgId = row.orgId
      this.wjwImportParentOrgId = row.parentOrgId
    },
    deepSet(arr) {
      arr.forEach(i => {
        if (i.childList.length) {
          this.deepSet(i.childList)
          if (this.tableQuery.condition.keyword) {
            this.expand.push(i.orgId)
          }
        }
        i.children = i.childList
        i.id = i.orgId
        if (!this.tableQuery.condition.keyword || !i.childList.length) {
          i.hasChildren = i.hasSub
        }
      })
      return arr
    },
    // get wjw list
    getWjwList() {
      this.listShow = false
      this.lazy = !this.tableQuery.condition.keyword
      wjwList(this.tableQuery).then(res => {
        this.expand = []
        this.userList = this.deepSet(res.records)
        this.total = res.total
        this.$nextTick(() => {
          this.lazy = true
          this.listShow = true
        })
      })
    },

    // search keyword for wjw list
    searchKey() {
      this.tableQuery.pager.page = 1
      this.getWjwList()
    },

    // highlight item by search keyword
    selectItem(val) {
      const keyword = this.tableQuery.condition.keyword
      if (keyword.trim() !== '' && val.indexOf(keyword) > -1) {
        return 'text-primary'
      }
      return ''
    },

    // pagination
    handlePagination(val) {
      this.tableQuery.pager.page = val.page
      this.getWjwList()
    },

    // load list
    handleLoadList(tree, treeNode, resolve) {
      const orgId = tree.orgId
      getChildrenByOrgId(orgId).then(res => {
        resolve(this.deepSet(res))
      }).catch(res => {
        resolve([])
      })
    },

    // add row
    handleAddRow(row) {
      this.currentOrgItem = row
      this.title = '选择 "' + row.orgName + '" 的下级单位'
      this.wjwDialogVisiable = true
    },

    // unbind row
    handleUnbindRow(row) {
      this.$confirm('您正在解绑与”' + row.orgName + '”相关的上下级机构关系，请谨慎操作！', '温馨提示', {
        confirmButtonText: '确认解绑',
        cancelButtonText: '取消解绑',
        type: 'warning'
      }).then(() => {
        const orgId = row.orgId
        unbindWjw(orgId).then(res => {
          this.$message({
            type: 'success',
            message: '解绑成功！'
          })
          this.getWjwList()
        })
      }).catch(() => {})
    },
    // add success
    handleAddSuccess() {
      console.log(1)
      this.getWjwList()
    }
  }
}
</script>

<style scoped>
  .text-primary{
    color: #409EFF;
  }
</style>
