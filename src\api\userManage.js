import request from '@/utils/request'

/**
 * 用户相关
 */

// 用户列表
export function userList(params) {
  return request({
    url: '/user/list',
    method: 'post',
    data: params
  })
}

// 用户列表筛选条件
export function searchQueryUser() {
  return request({
    url: '/adminUser',
    method: 'get'
  })
}

// 用户 - 详细信息
export function getUserInfo() {
  return request({})
}

// 添加用户
export function addUser(params) {
  return request({
    url: '/user/add',
    method: 'post',
    data: params
  })
}

// 编辑用户
export function editUser(params) {
  return request({
    url: 'user/edit',
    method: 'post',
    data: params
  })
}

// 重置用户密码
export function resetPassword(params) {
  return request({
    url: '/user/adminResetUserPassword',
    method: 'get',
    params: params
  })
}

/**
 * 单位相关
 */

// 获取单位结构类型列表
export function getOrganTypeList(params) {
  return request({
    url: '/organization/getOrganTypeList',
    method: 'get',
    params
  })
}

// 根据单位类型id获取单位层次
export function getOrganLevelList(params) {
  return request({
    url: '/organization/getOrganLevelList',
    method: 'get',
    params: { typeId: params }
  })
}

// 获取单位列表
export function getUnitList(params) {
  return request({
    url: '/organization/findOrganListPage',
    method: 'post',
    data: params
  })
}

// 获取启用的单位列表
export function getOrgList(params) {
  return request({
    url: '/organization/getOrgList',
    method: 'post',
    data: params
  })
}

// 单位状态操作
export function unitStatusOption(params) {
  return request({
    url: '/organization/enableDisable',
    method: 'get',
    params: { orgId: params }
  })
}

// 单位编辑
export function editUnit(params) {
  return request({
    url: '/organization/edit',
    method: 'post',
    data: params
  })
}

// 单位添加
export function addUnit(params) {
  return request({
    url: '/organization/add',
    method: 'post',
    data: params
  })
}

// 单位明细
export function getUnitDetail(params) {
  return request({
    url: '/organization/detailed',
    method: 'get',
    params: { orgId: params }
  })
}

// 单位题库列表-点击题库数量时展现的列表
export function getBankList(params) {
  return request({
    url: '/organization/bankList',
    method: 'post',
    data: params
  })
}

// 单位题库列表-开通题库时的树形结构
export function getQuestionByTree() {
  return request({
    url: '/organization/getQuestion',
    method: 'get'
  })
}

// 单位题库列表-开通题库时的提交
export function openyktQuestion(params) {
  return request({
    url: '/organization/openyktQuestion',
    method: 'post',
    data: params
  })
}

// 新增管理员
export function addUnitManager(params) {
  return request({
    url: '/user/addOrgAdminStaffUser',
    method: 'post',
    data: params
  })
}

// 新增管理员 - 没有成员信息
export function addUnitManagerWithoutStaffInfo(params) {
  return request({
    url: '/user/addOrgAdminStaff',
    method: 'post',
    data: params
  })
}

// 设置管理员
export function setUnitManager(params) {
  return request({
    url: '/user/setUpOrgStaffSetToAdmin',
    method: 'post',
    data: params
  })
}

// 查询单位管理员列表
export function getOrganAdminListByOrgId(params) {
  return request({
    url: '/user/getOrganAdminListByOrgId',
    method: 'get',
    params: { orgId: params }
  })
}

// 平台邀请管理员手机号校验
export function invitOrgAdminStaffFind(params) {
  return request({
    url: '/user/invitOrgAdminStaffFind',
    method: 'post',
    data: params
  })
}

// 企业邀请管理员成员用户
export function invitOrgAdminUser(params) {
  return request({
    url: '/user/invitOrgAdminUser',
    method: 'post',
    data: params
  })
}

// 停/启用
export function changeUserStatus(params) {
  return request({
    url: `/user/update/status/${params.userId}/${params.status}`,
    method: 'post'
  })
}

// 个人信息
export function getPersonalDetail(params) {
  return request({
    url: '/user/userInfo',
    method: 'get',
    params: params
  })
}

/**
 * 基金会相关
 */

// 基金会列表
export function getFoundationList(params) {
  return request({
    url: '/foundation/list',
    method: 'post',
    data: params
  })
}

// 新增基金会
export function addFoundation(params) {
  return request({
    url: '/foundation/add',
    method: 'post',
    data: params
  })
}

// 删除基金会
export function deleteFoundation(params) {
  return request({
    url: `/foundation/delete/${params.foundationId}`,
    method: 'post'
  })
}

// 创建项目 - 基金会列表
export function getProjectFoundationList(data) {
  return request({
    url: '/foundation/foundation/list',
    method: 'post',
    data
  })
}

// 创建项目 - 单位列表
export function getProjectUintList(params) {
  return request({
    url: '/foundation/uint/list',
    method: 'get'
  })
}

// 更新基金会项目
export function updateFoundation(params) {
  return request({
    url: 'foundation/update',
    method: 'post',
    data: params
  })
}

// 更新基金会项目
export function pitchList(params) {
  return request({
    url: 'foundation/pitch/list',
    method: 'get',
    params
  })
}

// 创建项目页面-单位类型列表
export function getOrganizationType(params) {
  return request({
    url: `/foundation/organization/type`,
    method: 'get',
    params
  })
}
