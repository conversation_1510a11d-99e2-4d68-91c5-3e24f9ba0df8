import request from '@/utils/request'

// 课程库列表
export const courseList = data =>
  request({
    url: '/coursePack/list',
    method: 'post',
    data
  })

// 创建课程
export const addCourse = data =>
  request({
    url: '/coursePack/create',
    method: 'post',
    data
  })

// 删除课程
export const delCourse = data =>
  request({
    url: `/coursePack/del`,
    method: 'post',
    data
  })

// 课程详情
export const courseDetail = params =>
  request({
    url: `/coursePack/detail/${params}`
  })

// 专家信息
export const courseDoctor = params =>
  request({
    url: `/coursePack/doctor/${params}`
  })

// 编辑课程
export const editCourse = data =>
  request({
    url: `/coursePack/edit`,
    method: 'post',
    data
  })

// 设置虚拟量
export const setDummy = data =>
  request({
    url: `/coursePack/set/dummy`,
    method: 'post',
    data
  })

// 设置课程会员等级
export const setCoursePackMemberLevel = data =>
  request({
    url: `/coursePack/setCoursePackMemberLevel`,
    method: 'post',
    data
  })

// 置顶/取消置顶
export const stick = data =>
  request({
    url: `/coursePack/stick`,
    method: 'post',
    data
  })

// 课程视频列表
export const courseVideoList = data =>
  request({
    url: `/coursePack/video/list`,
    method: 'post',
    data
  })

// 医生列表
export const doctorList = data =>
  request({
    url: `/user/doctor/list`,
    method: 'post',
    data
  })

// 单位列表
export const orgList = data =>
  request({
    url: `/organization/list`,
    method: 'post',
    data
  })

// 单位信息
export const orgDetail = params =>
  request({
    url: `/organization/get/detail/${params}`
  })

// 新增推荐
export const recommendAdd = data =>
  request({
    url: `/cms/index/add`,
    method: 'post',
    data
  })

// 删除推荐
export const recommendDel = data =>
  request({
    url: `/cms/index/delete`,
    method: 'post',
    data
  })

// 推荐列表
export const recommendList = data =>
  request({
    url: `/cms/index/list`,
    method: 'post',
    data
  })

// 编辑推荐
export const recommendEdit = data =>
  request({
    url: `/cms/index/update`,
    method: 'post',
    data
  })
