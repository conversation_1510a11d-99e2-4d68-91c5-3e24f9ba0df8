<template>
  <el-dialog title="试题" :visible.sync="show" width="600px" center :close-on-click-modal="false">
    <el-form ref="form" :model="form" label-width="80px" :rules="rules">
      <el-form-item label="试题标题" prop="name">
        <el-input v-model="form.name" clearable placeholder="请填写试题标题" maxlength="120" />
      </el-form-item>
      <el-form-item label="插入时间" required>
        <el-time-picker v-model="form.timeSpot" :clearable="false" :picker-options="selectableRange" value-format="HH:mm:ss" placeholder="任意时间点" />
        <div class="el-form-item__error">*在此时间插入试卷（范围：00:00:06 - {{ formatSeconds(playerDurationTime - 6) }}）</div>
      </el-form-item>
      <el-form-item label="答题时间" required>
        <el-input-number v-model="form.finishTime" style="width:400px;" controls-position="right" :min="20" :max="120" placeholder="请填写规定完成考试答题时间(单位为秒)" />
        <div class="el-form-item__error">*规定时间内完成考试答题（单位为秒），不填默认为30秒作答时间</div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="cancel">取消</el-button>
      <el-button v-if="isAdd" type="primary" size="small" @click="submit('add')">新增</el-button>
      <el-button v-else type="primary" size="small" @click="submit('edit')">保存</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { formatTotalSeconds, formatSeconds } from '@/utils'

export default {
  name: 'DialogInsert',
  props: {
    selectableRange: {
      type: Object,
      default: () => {}
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    playerDurationTime: {
      type: Number,
      default: 0
    },
    videoDto: {
      type: Object,
      default: () => {}
    },
    editPaperIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      form: this.copy(this.videoDto),
      rules: {
        name: [{ required: true, message: '请填写试题名称', trigger: 'blur' }]
      }
    }
  },
  watch: {
    show(v) {
      if (v) this.form = this.copy(this.videoDto)
    }
  },
  methods: {
    formatSeconds,
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    // 取消编辑试卷
    cancel() {
      this.form = {}
      this.show = false
      this.$emit('cancel')
    },
    // 保存试卷  @params{actionType} String - 'add'插入 'edit'编辑
    submit(actionType) {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (!this.form.finishTime) {
            this.form.finishTime = 30
          }
          this.save(actionType)
        } else {
          return false
        }
      })
    },
    save(actionType) {
      const S = formatTotalSeconds(this.form.timeSpot)
      if (S <= 5) {
        this.$message.error('视频前5秒不能插入试卷')
        return
      }
      if (S >= this.playerDurationTime - 5) {
        this.$message.error('视频后5秒不能插入试卷')
        return
      }
      const { paperGroupList } = this.$parent.form.videoList[0]
      if (paperGroupList.length) {
        const flag = paperGroupList.some((v, i) => {
          if (i === this.editPaperIndex) return
          return (
            (S >= v.timeSpot && S <= v.timeSpot + 5) ||
            (S <= v.timeSpot && S >= v.timeSpot - 5)
          )
        })
        if (flag) return this.$message.error('试卷插入时间间隔应大于5秒')
      }
      const obj = this.copy(this.form)
      obj.timeSpot = S
      this.$emit('save', obj)
      this.show = false
    }
  }
}
</script>
