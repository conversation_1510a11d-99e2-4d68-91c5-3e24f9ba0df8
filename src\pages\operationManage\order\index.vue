<template>
  <div class="app-container">
    <div class="total">
      总收入: {{ totalMount }}
    </div>
    <div class="search-column">
      <div class="search-column__item" style="width:330px">
        <el-input v-model="form.keyWord" placeholder="订单编号/下单人/交易订单号/账号/订单内容" clearable @clear="search" @keyup.enter.native="search">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="search" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.device" placeholder="购买终端" clearable @change="search">
          <el-option v-for="item in deviceType" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.orderType" placeholder="订单类型" clearable @change="search">
          <el-option v-for="item in orderTypeList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.payType" placeholder="支付方式" clearable @change="search">
          <el-option v-for="item in payTypeList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.orderStatus" placeholder="订单状态" clearable @change="search">
          <el-option v-for="item in orderStatusList" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </div>
      <!-- 时间选择器 -->
      <div class="search-column__item">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          clearable
          @change="changePicker"
        />
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="exportList">导出订单列表</el-button>
      </div>
    </div>
    <el-table :data="records" border stripe empty-text="暂无数据">
      <el-table-column align="center" label="订单编号" prop="orderCode" />
      <el-table-column align="center" label="交易订单号" prop="outTradeNo" />
      <el-table-column align="center" label="下单人" prop="buyerRealName" />
      <el-table-column align="center" label="账号" prop="buyerUsername" />
      <el-table-column align="center" label="订单类型" prop="orderType" />
      <el-table-column align="center" label="订单内容" prop="title" />
      <el-table-column align="center" label="购买终端">
        <template slot-scope="{row}">{{ row.device | deviceList }}</template>
      </el-table-column>
      <el-table-column align="center" label="金额(元)" prop="amount" />
      <el-table-column align="center" label="支付方式" prop="payType" />
      <el-table-column align="center" label="订单状态" prop="orderStatus" />
      <el-table-column align="center" label="下单时间" prop="orderTime" width="160px" />
    </el-table>
    <Pagination :auto-scroll="false" class="text-center" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getTotal, getOrderList, exportOrderList } from '@/api/systemManage'
export default {
  name: 'OrderManage',
  filters: {
    deviceList(val) {
      const arr = ['', 'web', '', '安卓', 'ios']
      return arr[val]
    }
  },
  components: {
    Pagination
  },
  data() {
    return {
      totalMount: 0,
      deviceType: [
        { label: 'web', value: 1 },
        { label: '安卓', value: 3 },
        { label: 'ios', value: 4 }
      ],
      payTypeList: [
        { label: '其他', value: 0 },
        { label: '微信支付', value: 1 },
        { label: '支付宝支付', value: 2 },
        { label: '苹果内购支付', value: 3 }
      ],
      orderTypeList: [
        { label: '其他', value: 0 },
        { label: 'VIP会员', value: 1 },
        { label: '证书培训报名', value: 2 },
        { label: '申领纸质证书', value: 3 },
        { label: '学分培训', value: 4 }
      ],
      orderStatusList: [
        { label: '已失效或未生效', value: 0 },
        { label: '已付款', value: 1 },
        { label: '待付款', value: 2 },
        { label: '付款错误', value: 3 },
        { label: '已付款但业务处理失败', value: 4 },
        { label: '已取消', value: 5 },
        { label: '已关闭', value: 6 }
      ],
      timeRange: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()// 禁止选择以后的时间
        }
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      total: 0,
      form: {
        device: null,
        orderType: null,
        payType: null,
        orderStatus: null,
        startTime: '',
        endTime: '',
        keyWord: ''
      },
      records: []
    }
  },
  created() {
    getTotal().then(res => {
      this.totalMount = res
    })
    this.getList()
  },
  methods: {
    search() {
      this.pager.page = 1
      this.getList()
    },
    getList() {
      const query = {}
      query.condition = this.form
      query.pager = this.pager
      getOrderList(query).then(res => {
        this.total = res.total
        res.records.forEach(v => {
          v.payType = this.payTypeList.find(i => i.value === v.payType).label
          v.orderType = this.orderTypeList.find(i => i.value === v.orderType).label
          v.orderStatus = this.orderStatusList.find(i => i.value === v.orderStatus).label
        })
        this.records = res.records
      })
    },
    handlePagination(val) {
      this.pager = val
      this.getList()
    },
    // 日期选择器
    changePicker(data) {
      if (data && data.length > 0) {
        this.form.startTime = data[0] + ' 00:00:00'
        this.form.endTime = data[1] + ' 23:59:59'
      } else {
        this.form.startTime = ''
        this.form.endTime = ''
      }
      this.search()
    },
    exportList() {
      exportOrderList(this.form).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.total{
  width: 100%;
  height: 100px;
  line-height: 100px;
  background-color: #e8f3fe;
  border-radius: 16px;
  font-size: 26px;
  color: #000;
  padding-left: 30px;
}
</style>
