<template>
  <div class="scroll-table" :style="{'height': height,'cursor': 'pointer'}" @click="skip">
    <h2>课程学习动态</h2>
    <div class="t-head">
      <span v-for="(item,index) in tableHeader" :key="index">{{ item.label }}</span>
    </div>
    <vue-seamless-scroll :data="data" :class-option="optionSingleHeight" class="t-body">
      <ul ref="scroll">
        <li v-for="item in data" :key="item.rank">
          <div>
            <el-tooltip v-if="item.courseName.length>3" effect="dark" :content="item.courseName" placement="top">
              <span>{{ item.courseName|nameFmt }}</span>
            </el-tooltip>
            <span v-else>{{ item.courseName }}</span>
          </div>
          <div>{{ item.studyTimeStr }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>

<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  name: 'ScrollTable',
  components: {
    vueSeamlessScroll
  },
  filters: {
    nameFmt(v) {
      if (v.length > 40) {
        return v.substring(0, 40) + '...'
      }
      return v
    }
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    height: {
      type: [Number, String],
      default: '322px'
    }
  },
  data() {
    return {
      tableHeader: [
        {
          'label': '课程',
          'key': 'courseName',
          'props': 'courseName'
        },
        {
          'label': '学习时间',
          'key': 'studyTimeStr',
          'props': 'studyTimeStr'
        }
      ]
    }
  },
  computed: {
    cubic(v) {
      return Math.pow(v, 3)
    },
    easeInOutCubic(v) {
      return v < 0.5 ? this.cubic(v * 2) / 2 : 1 - this.cubic(((1 - v) * 2) / 2)
    },
    optionSingleHeight() {
      return {
        // 每次停顿向上滚动的高度
        singleHeight: 20
        // 停顿时间
        // waitTime: 2500
      }
    }
  },
  watch: {
    data: {
      handler(v) {
        if (v.length) {
          this.data = v
          // this.$refs['scroll'].wrap.scrollTop = 0
        }
      },
      deep: true
    }
  },
  methods: {
    skip() {
      this.$router.push({
        name: 'StatisticsCourse'
      })
    },
    renderHeader(h, { column, $index }) {
      return h('span', {
        attrs: {
          class: 'bs-cell',
          alt: column.label
        },
        domProps: {
          innerHTML: column.label
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-table {
  padding-top: 10px;
  overflow: hidden;
  h2 {
    margin: 0 0 13px;
    font-size: 24px
  }
  .t-head {
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    span {
      flex: 1;
      text-align: left;
      display: inline-block;
      font-size: 14px;
      font-weight: bold;
      color: #00f0ff;
      padding: 0 10px;

      &:nth-child(3) {
        flex: 3;
      }

      &:last-child {
        flex: none;
        width: 28%;
      }
    }
  }

  .t-body {
    height: 240px;
    overflow: hidden;
    ::v-deep .el-scrollbar {
      height: 100%;

      .el-scrollbar__wrap {
        overflow-x: hidden;
      }

      .el-scrollbar__thumb {
        background-color: #3aa6f0;
      }
    }
    ul {
      margin: 0;
      padding: 0;
    }
    li {
      height: 20px;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:nth-child(odd) {
        background-color: rgba(57,117,174,.3);
      }

      div {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #fff;
        font-size: 12px;
        flex: 1;
        text-align: left;
        padding: 0 10px;
        width: 20%;
        &:nth-child(3) {
          flex: 3;
        }
        &:last-child {
          flex: none;
          width: 28%;
        }
        span {
          width: 100%;
        }
        .el-button {
          color: #fff;
          padding: 0;
          border: 0;
          font-size: 12px;
          line-height: 35px;
          background: transparent;
        }
      }
    }
  }
}
</style>
