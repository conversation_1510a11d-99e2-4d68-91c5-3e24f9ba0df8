<template>
  <section class="total">
    <div class="total-roughly">
      <h2 class="total-title">总体概况</h2>
      <div class="total-roughly-content">
        <ul>
          <li v-for="item in roughly" :key="item.label">
            <p>{{ item.label }}</p>
            {{ item.value }}
          </li>
        </ul>
      </div>
    </div>
    <div class="total-article">
      <h2 class="total-title">文章</h2>
      <div class="total-article-content">
        <h3 class="total-subtitle">概况</h3>
        <ul>
          <li v-for="item in article" :key="item.label">
            <p>{{ item.label }}</p>
            {{ item.value }}
          </li>
        </ul>
        <h3 class="total-subtitle">
          文章列表
          <span @click="toMore('MarketingStatisticsArticle')">更多<i class="el-icon-d-arrow-right" /></span>
        </h3>
        <article-table :is-total="true" :search-param="searchParam" />
      </div>
    </div>
    <div class="total-article">
      <h2 class="total-title">视频</h2>
      <div class="total-article-content">
        <h3 class="total-subtitle">概况</h3>
        <ul>
          <li v-for="item in video" :key="item.label">
            <p>{{ item.label }}</p>
            {{ item.value }}
          </li>
        </ul>
        <h3 class="total-subtitle">
          视频列表
          <span @click="toMore('MarketingStatisticsVideo')">更多<i class="el-icon-d-arrow-right" /></span>
        </h3>
        <video-table :is-total="true" :search-param="searchParam" />
      </div>
    </div>
    <div class="total-article">
      <h2 class="total-title">抖喇</h2>
      <div class="total-article-content">
        <h3 class="total-subtitle">概况</h3>
        <ul>
          <li v-for="item in doula" :key="item.label">
            <p>{{ item.label }}</p>
            {{ item.value }}
          </li>
        </ul>
        <h3 class="total-subtitle">
          抖喇列表
          <span @click="toMore('MarketingStatisticsDoula')">更多<i class="el-icon-d-arrow-right" /></span>
        </h3>
        <doula-table :is-total="true" :search-param="searchParam" />
      </div>
    </div>
    <div class="total-article">
      <h2 class="total-title">拜访</h2>
      <div class="total-article-content">
        <h3 class="total-subtitle">概况</h3>
        <ul>
          <li v-for="item in visit" :key="item.label">
            <p>{{ item.label }}</p>
            {{ item.value }}
          </li>
        </ul>
        <h3 class="total-subtitle">
          拜访列表
          <span @click="toMore('MarketingStatisticsVisit')">更多<i class="el-icon-d-arrow-right" /></span>
        </h3>
        <visit-table :is-total="true" :search-param="searchParam" />
      </div>
    </div>
  </section>
</template>

<script>
import ArticleTable from './article.vue'
import VideoTable from './video.vue'
import VisitTable from './visit.vue'
import DoulaTable from './doula.vue'
import { totalAnalysisSurvey } from '@/api/marketing/promoteArticle'
import { deleteEmptyProperty } from '@/utils/index'
export default {
  components: {
    ArticleTable,
    VideoTable,
    VisitTable,
    DoulaTable
  },
  props: {
    searchParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      roughly: [],
      article: [],
      video: [],
      doula: [],
      visit: [],
      totalAnaly: {},
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          orgId: null,
          serviceProviderOrgId: null,
          orgzIds: null,
          productId: null,
          categoryId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.getTotalAnalysisSurvey()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    getTotalAnalysisSurvey() {
      const param = JSON.parse(JSON.stringify(this.queryParams.condition))
      deleteEmptyProperty(param)
      totalAnalysisSurvey(param).then(res => {
        this.totalAnaly = res
        this.roughly = [
          { label: '文章', value: res.articleSurveyInfo.articleCount },
          { label: '视频', value: res.videoSurveyInfo.articleCount },
          { label: '抖喇', value: res.doulaSurveyInfo.articleCount },
          { label: '拜访', value: res.visitSurveyInfo.visitNum },
          { label: '总费用 (元)', value: res.totalCostWithUnit }
        ]
        this.article = [
          { label: '文章数量', value: res.articleSurveyInfo.articleCount },
          { label: '浏览量', value: res.articleSurveyInfo.viewNumWithUnit },
          { label: '浏览人数', value: res.articleSurveyInfo.peopleViewNumWithUnit },
          { label: '总费用 (元)', value: res.articleSurveyInfo.costWithUnit }
        ]
        this.video = [
          { label: '视频数量', value: res.videoSurveyInfo.articleCount },
          { label: '播放量', value: res.videoSurveyInfo.viewNumWithUnit },
          { label: '播放人数', value: res.videoSurveyInfo.peopleViewNumWithUnit },
          { label: '总费用 (元)', value: res.videoSurveyInfo.costWithUnit }
        ]
        this.doula = [
          { label: '抖喇数量', value: res.doulaSurveyInfo.articleCount },
          { label: '浏览量', value: res.doulaSurveyInfo.viewNumWithUnit },
          { label: '浏览人数', value: res.doulaSurveyInfo.peopleViewNumWithUnit },
          { label: '费用 (元)', value: res.doulaSurveyInfo.costWithUnit }
        ]
        this.visit = [
          { label: '拜访次数', value: res.visitSurveyInfo.visitNum },
          { label: '费用 (元)', value: res.visitSurveyInfo.costWithUnit }
        ]
      })
    },
    toMore(routeName) {
      this.$router.push({
        name: routeName
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.total {
  padding: 14px 0;
  color: #333;
  &-title {
    margin: 0 auto;
    padding-left: 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  &-subtitle {
    position: relative;
    margin: 0 auto;
    font-size: 18px;
    display: flex;
    align-items: center;
    &:before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      margin-right: 10px;
      background-color: #4ab9a3;
    }
    span {
      position: absolute;
      right: 0;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      i {
        font-size: 20px;
      }
      cursor: pointer;
      &:hover {
        color: #4ab9a3;
      }
    }
  }
  ul {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    li {
      margin-right: 120px;
      p {
        font-size: 18px;
        color: #666;
        font-weight: 400;
        margin-bottom: 20px;
      }
    }
  }
  &-roughly {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    &-content {
      padding-left: 20px;
      ul {
        height: 145px;
      }
    }
  }
  &-article {
    margin: 15px auto;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    &-content {
      padding: 20px;
      ul {
        margin: 22px 0;
      }
      ::v-deep .table {
        padding: 15px 0 0;
      }
    }
  }
}
</style>
