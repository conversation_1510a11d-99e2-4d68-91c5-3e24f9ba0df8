<template>
  <section class="videoEdit">
    <el-form ref="form" :rules="rules" :model="form" label-width="90px" :disabled="$route.query.type === 'read'">
      <el-form-item label="会议视频:" required>
        <div style="display: flex">
          <div v-if="!form.videoFileId || form.videoFileId === ''" class="select-btn" @click="selsecVideoDialogVisible = true">视频素材库选择</div>
          <video-upload v-if="!form.videoFileId || form.videoFileId === ''" :key="form.videoFileId" :width="'260px'" :height="'100px'" :video-file-id.sync="form.videoFileId" />
          <video-play v-show="form.videoFileId && form.videoFileId !== ''" :video-file-id="form.videoFileId" :del="$route.query.type !== 'read'" @del="form.videoFileId = ''" />
        </div>
      </el-form-item>
      <el-form-item label="实景照片:" required prop="imgFileIds">
        <UploadPic tips="上传实景照片" :pic-ids.sync="form.imgFileIds" :urls="form.imgUrlList" />
      </el-form-item>
      <el-form-item label="会议名称:" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入会议名称"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item label="会议人数:" prop="peopleNum">
        <el-input-number v-model="form.peopleNum" :controls="false" :min="1" step-strictly />
      </el-form-item>
      <el-form-item label="会议说明:" prop="description">
        <el-input
          v-model="form.description"
          :rows="6"
          type="textarea"
          placeholder="请输入会议说明"
          maxlength="300"
          show-word-limit
          style="width: 800px"
        />
      </el-form-item>
      <el-form-item label="会议时间:" required>
        <el-date-picker
          v-model="time"
          type="datetimerange"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="timeChange"
        />
      </el-form-item>
      <el-form-item label="所属单位:" prop="ownerOrgId">
        <el-select v-model="form.ownerOrgId" placeholder="请选择单位">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="submit">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button v-if="$route.query.type !== 'read'" type="primary" @click="publish">保存</el-button>
    </div>

    <SelsectVideo :dialog-visible.sync="selsecVideoDialogVisible" :video-file-id.sync="form.videoFileId" />
  </section>
</template>

<script>
import { addOrModify, meetingRecordDetail } from '@/api/marketing/contentManage'
import { getUnitList } from '@/api/userManage'
import VideoUpload from '@/components/Upload/videoUpload.vue'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import UploadPic from '@/components/Upload/SingleImage5.vue'
import SelsectVideo from '@/pages/marketing/task/components/selectVideo.vue'
export default {
  components: {
    VideoUpload,
    VideoPlay,
    UploadPic,
    SelsectVideo
  },
  data() {
    return {
      form: {
        id: null,
        name: '',
        videoFileId: '',
        imgFileIds: [],
        imgUrlList: [],
        peopleNum: 1,
        description: '',
        startTime: '',
        endTime: '',
        ownerOrgId: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入会议名称', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        imgFileIds: [
          { required: true, message: '请上传实景图片', trigger: 'change' }
        ],
        peopleNum: [{ required: true, message: '请填写会议人数', trigger: 'blur' }],
        description: [{ required: true, message: '请输入会议说明', trigger: 'blur' }],
        ownerOrgId: [{ required: true, message: '请选择所属单位', trigger: 'change' }]
      },
      time: [],
      orgList: [],
      detail: {},
      selsecVideoDialogVisible: false
    }
  },
  mounted() {
    this.getOrg()
    if (this.$route.query.id) {
      meetingRecordDetail({ id: this.$route.query.id }).then(res => {
        this.form = res
        this.form.id = res.id
        this.time = [this.form.startTime, this.form.endTime]
      })
    }
  },
  methods: {
    timeChange() {
      this.form.startTime = this.time[0]
      this.form.endTime = this.time[1]
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002,
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.videoFileId === '') {
          this.$message.error('视频未上传')
          return
        }
        if (valid) {
          addOrModify(this.form).then(() => {
            if (this.form.id) {
              this.$message.success('会议素材编辑成功')
            } else {
              this.$message.success('会议素材保存成功')
            }
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.videoEdit {
  background-color: #fff;
  width: 1173px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  .el-form {
    margin-left: 20px;
    width: 600px;
    ::v-deep .el-textarea__inner {
      height: 250px;
    }
    .el-select {
      width: 520px;
    }
    .el-tag {
      height: 36px;
      padding: 5px 10px;
      vertical-align: middle;
    }
    .select-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 100px;
      margin-right: 20px;
      font-size: 22px;
      font-weight: 700;
      color: #000;
      cursor: pointer;
      background-color: #f5f7fa;
    }
  }
  .submit {
    text-align: center;
  }
}
</style>
