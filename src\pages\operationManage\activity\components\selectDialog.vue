<template>
  <el-dialog title="选择广告" :visible.sync="dialogShow" width="900px" top="8vh">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.type" filterable clearable placeholder="请选择类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.keyword" placeholder="活动名称/广告商" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
      </div>
      <div class="search-column__item">
        <el-select v-model="status" filterable clearable placeholder="请选择活动状态">
          <el-option v-for="item in adStateList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
    </div>

    <!-- table -->
    <el-table
      size="mini"
      :data="adList"
      border
      stripe
      highlight-current-row
      @row-click="handleCurrentChange"
    >
      <el-table-column prop="title" label="活动名称" align="center" />
      <el-table-column prop="advertiserName" label="广告商" align="center" />
      <el-table-column label="活动时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.startTime+' ~ '+row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="活动状态" align="center">
        <template slot-scope="{row}">
          <span>{{ row.status|statusFlt }}</span>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </el-dialog>
</template>

<script>
import request from '@/api/activity'
import Pagination from '@/components/Pagination/index.vue'

export default {
  name: 'SelectDialog',
  components: { Pagination },
  filters: {
    statusFlt(v) {
      const arr = ['未开始', '进行中', '已结束']
      return arr[v]
    }
  },
  data() {
    return {
      dialogShow: false,
      // search params
      status: '', // no watch
      tableQuery: {
        condition: {
          type: 1,
          keyword: '',
          startTime: '',
          endTime: '',
          status: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      timeRange: [],
      // select list
      adStateList: [
        { value: 0, name: '未开始' },
        { value: 1, name: '进行中' },
        { value: 2, name: '已结束' }
      ],
      typeList: [
        { value: 1, name: '活动名称' },
        { value: 2, name: '广告商' }
      ],
      // table
      adList: [],
      // pagination
      layout: 'total, prev, pager, next, jumper',
      total: 0
    }
  },
  watch: {
    dialogShow(v) {
      v && this.getAdList()
    },
    status(v) {
      this.tableQuery.condition.status = v
      this.getAdList()
    },
    timeRange(val) {
      this.tableQuery.condition.startTime = val ? val[0] : ''
      this.tableQuery.condition.endTime = val ? val[1] : ''
      this.getAdList()
    }
  },
  methods: {
    handleFilter() {
      this.tableQuery.pager.page = 1
      this.getAdList()
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getAdList()
    },
    // get ad list
    getAdList() {
      request.list(this.tableQuery).then(res => {
        this.adList = res.records
        this.total = res.total
      })
    },
    // select row
    handleCurrentChange(val) {
      this.$emit('select', val)
      this.dialogShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
.search-column__item{
  margin-bottom: 10px;
}
</style>
