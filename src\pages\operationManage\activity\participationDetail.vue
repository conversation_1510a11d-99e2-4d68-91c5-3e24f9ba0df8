<template>
  <div class="app-container">
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe>
      <template slot="actions" slot-scope="{row}">
        <el-button :disabled="row.join_type!=='视频答题'||!row.join_record_id || !row.end_time" size="mini" type="text" @click="toDetail(row)">答题详情</el-button>
      </template>
      <template slot="time" slot-scope="{row}">
        <span>{{ row.use_time|formatSeconds }}</span>
      </template>
      <template slot="selfFaceUrl" slot-scope="{row}">
        <img v-if="row.selfFaceUrl !== '' && row.join_type==='视频答题'" style="width: 100px;height: 100px" :src="row.selfFaceUrl">
      </template>
      <template slot="examineFaceUrl" slot-scope="{row}">
        <img v-if="row.examineFaceUrl !== '' && row.join_type==='视频答题'" style="width: 100px;height: 100px" :src="row.examineFaceUrl">
      </template>
      <template slot="point" slot-scope="{row}">
        <span>{{ row.end_time&&row.join_type==='视频答题'? row.accuracy : '-' }}</span>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination :auto-scroll="false" class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/activity'
import { formatSeconds } from '@/utils'

const columns = [
  { props: { label: '序号', align: 'center', prop: 'rank' }},
  { props: { label: '曝光时间', align: 'center', prop: 'exposure_time' }},
  { props: { label: '点击时间', align: 'center', prop: 'hit_time' }},
  { props: { label: '参与类型', align: 'center', prop: 'join_type' }},
  { props: { label: '开始时间', align: 'center', prop: 'start_time' }},
  { props: { label: '结束时间', align: 'center', prop: 'end_time' }},
  {
    props: { align: 'center', label: '用时' },
    slot: 'time'
  },
  {
    props: { label: '得分', align: 'center', prop: 'accuracy' },
    slot: 'point'
  },
  { props: { label: '考核结果', align: 'center', prop: 'examineResult' }},
  {
    props: { label: '本人人脸', align: 'center', prop: 'selfFaceUrl' },
    slot: 'selfFaceUrl'
  },
  {
    props: { label: '考核人脸', align: 'center', prop: 'examineFaceUrl' },
    slot: 'examineFaceUrl'
  },
  { props: { label: '对比结果', align: 'center', prop: 'faceComparison' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'ParticipationDetail',
  filters: {
    formatSeconds
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      activityId: this.$route.query.actId,
      userId: this.$route.query.userId,
      editPath: 'answerDetail',
      initList: true // 是否初始化调用
    }
  },
  methods: {
    toDetail(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          readOnly: 1,
          id: row.join_record_id
        }
      })
    },
    getList() {
      const param = {
        ...this.pager,
        actId: this.activityId,
        userId: this.userId
      }
      request.actUserJoin(param).then(res => {
        this.list = res.records
        this.total = res.total
      })
    }
  }
}
</script>
