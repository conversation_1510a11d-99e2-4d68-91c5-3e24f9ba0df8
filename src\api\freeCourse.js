import request from '@/utils/request'

// 获取教程上下架列表 -- 新接口
export function getCourseList(params) {
  return request({
    url: '/course/vantage/list',
    method: 'post',
    data: params
  })
}

// 获取SOP上下架列表 -- 新接口
export function getSopList(params) {
  return request({
    url: '/sop/putaway/list',
    method: 'post',
    data: params
  })
}

// 开通免费培训课程
export function addFreeResource(params) {
  return request({
    url: '/organization/freeResource/add',
    method: 'post',
    data: params
  })
}

// 删除免费培训课程
export function deleteFreeResource(data) {
  return request({
    url: '/organization/freeResource/delete',
    method: 'post',
    data
  })
}

// 开通免费培训课程 - 教程
export function enableFreeCourse(params) {
  return request({
    url: '/course/enableFree',
    method: 'post',
    data: params
  })
}

// 开通免费培训课程 - SOP
export function enableFreeSop(params) {
  return request({
    url: '/sop/enableFree',
    method: 'post',
    data: params
  })
}
