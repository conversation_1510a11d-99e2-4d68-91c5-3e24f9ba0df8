<template>
  <!-- 带操作列的 table -->
  <el-table :data="dataAry" stripe style="width: 100%" border @selection-change="handleSelectionChange">
    <!-- 选择 -->
    <el-table-column v-if="hasSelection" type="selection" width="55px" />
    <!-- 序号 -->
    <el-table-column v-if="hasIndex" type="index" width="55" label="序号" align="center" />
    <!-- 数据源 -->
    <el-table-column
      v-for="column in columns"
      :key="column.prop"
      :header-align="column.align"
      :align="column.align"
      :sortable="column.hasSort"
      :prop="column.prop"
      :label="column.label"
    />
    <slot name="handleColumn" />
  </el-table>
</template>

<script>

export default {
  name: 'Vtable',
  props: {
    hasSelection: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    hasIndex: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    columns: {
      type: Array,
      default: function() {
        return []
      }
    },
    dataAry: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  methods: {
    //  选中的行数据
    handleSelectionChange([val]) {
      this.$emit('handleSelection', val)
    }
  }
}
</script>
