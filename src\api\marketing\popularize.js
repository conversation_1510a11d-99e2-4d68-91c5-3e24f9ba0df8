import request from '@/utils/request'

// 推广列表
export function popularizeList(params) {
  return request({
    url: '/promoteRecommendColumn/findColumns',
    method: 'get',
    params
  })
}

// 推广内容列表
export function recommendContentList(params) {
  return request({
    url: '/promoteRecommendColumn/findRecommendContents',
    method: 'post',
    data: params
  })
}

// 清除
export function clearColumn(params) {
  return request({
    url: '/promoteRecommendColumn/clearColumn',
    method: 'get',
    params
  })
}

// 设为广告位
export function setAdvertColumn(params) {
  return request({
    url: '/promoteRecommendColumn/setAdvertColumn',
    method: 'get',
    params
  })
}

// 复制推荐栏目到短视频
export function copy2Video(params) {
  return request({
    url: '/promoteRecommendColumn/copy2Video',
    method: 'get',
    params
  })
}

// 复制推荐栏目到短视频
export function copy2doulaVideo(params) {
  return request({
    url: '/promoteRecommendColumn/copy2doulaVideo',
    method: 'get',
    params
  })
}

// 添加内容到广告位
export function addColumn(params) {
  return request({
    url: '/promoteRecommendColumn/addColumn',
    method: 'post',
    data: params
  })
}

// 一键投放
export function oneKeyAdvertising(params) {
  return request({
    url: '/promoteRecommendColumn/oneKeyAdvertising',
    method: 'post',
    params
  })
}
