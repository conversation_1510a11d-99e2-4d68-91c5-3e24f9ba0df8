<template>
  <div class="map">
    <div ref="map" class="bs-map" :style="{ width: width, height: height }" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
import axios from 'axios'

export default {
  props: {
    className: {
      type: String,
      default: ''
    },
    data: {
      type: [Array, Object],
      default: () => []
    },
    geoName: {
      type: String,
      default: 'china'
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '612px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    areaCode: {
      type: String,
      default: '100000'
    },
    tooltipName: {
      type: String,
      default: '用户数'
    },
    suffix: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      mTime: '',
      index: -1,
      chart: '',
      max: 0
    }
  },
  watch: {
    areaCode: {
      handler(v) {
        this.initMapChart(v)
      }
    },
    data: {
      handler(v) {
        this.max = Math.max(...v.map(i => i.value)) || 0
        this.chart.dispose()
        this.chart = echarts.init(this.$refs.map, 'macarons')
        this.setOptions(v, this.geoName)
        this.mapActive()
      },
      deep: true
    }
  },
  mounted() {
    this.initMapChart()
  },
  beforeDestroy() {
    clearTimeout(this.mTime)
  },
  methods: {
    initMapChart() {
      this.chart = echarts.init(this.$refs.map, 'macarons')
      axios.get('/assets/json/map/100000.json', {}).then(({ data }) => {
        echarts.registerMap('china', data)
        this.max = Math.max(...this.data.map(i => i.value)) || 0
        this.setOptions(this.data, 'china')
        this.mapActive()
        this.mouseEvents()
      })
    },
    setOptions(chartData, name) {
      const _this = this
      this.chart.clear()
      this.chart.on('click', function(item) {
        _this.$router.push({
          name: 'StatisticsUser',
          query: {
            areaId: item.data.areaId
          }
        })
      })
      this.chart.setOption({
        backgroundColor: 'transparent',
        toolbox: {
          top: 10,
          feature: {
          }
        },
        tooltip: {
          trigger: 'item',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(41, 29, 29, 0.5)',
          textStyle: {
            color: 'rgba(255, 255, 255, 1)'
          },
          formatter: (params) => {
            return `【第${params.dataIndex + 1}名/${chartData.length}】<br/>${params.name} ${this.tooltipName}：${params.data.value}`
          }
        },
        geo: {
          map: name,
          zoom: 1.1,
          top: '16%',
          itemStyle: {
            normal: {
              borderWidth: 3,
              borderColor: '#26d1d4',
              shadowColor: '#1a51cd',
              shadowBlur: 40
            }
          }
        },
        // 热力图例
        visualMap: {
          min: 0,
          max: this.max,
          inRange: {
            color: ['#944efe', '#0956f2']
          },
          show: false
        },
        series: {
          type: 'map',
          map: name,
          zoom: 1.1,
          top: '16%',
          label: {
            normal: {
              show: false,
              textStyle: {
                color: 'rgba(255, 255, 255, 1)'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                color: 'rgba(255, 255, 255, 1)'
              }
            }
          },
          itemStyle: {
            normal: {
              areaColor: '#0982cd',
              borderColor: '#26d1d4'
            },
            emphasis: {
              areaColor: '#fb5e60'
            }
          },
          data: chartData
        }
      })
    },
    detail() {
      this.$emit('detail')
    },
    mapActive() {
      if (this.data.length === 0) return
      const dataLength = this.data.length - 1
      // 用定时器控制高亮
      clearInterval(this.mTime)
      this.mTime = setInterval(() => {
        // 清除之前的高亮
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: dataLength
        })
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.index++
        // 当前下标高亮
        this.chart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: this.index
        })
        this.chart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: this.index
        })
        if (this.index >= dataLength) {
          this.index = -1
        }
        clearInterval(this.mTime)
        this.mapActive()
      }, 2000)
    },
    mouseEvents() {
      // 鼠标划入
      this.chart.on('mouseover', () => {
        // 停止定时器，清除之前的高亮
        clearInterval(this.mTime)
        this.mTime = ''
        // console.log(this.mTime)
        this.chart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.index
        })
      })
      // 鼠标划出重新定时器开始
      this.chart.on('mouseout', () => {
        clearInterval(this.mTime)
        this.mapActive()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.bs-box {
  position: relative;
  .bs-detail {
    position: absolute;
    top: 14px;
    right: 30px;
    width: 85px;
    height: 25px;
    line-height: 25px;
    padding:  0 8px;
    background: #112A56;
    border: 1px solid #1E4383;
    border-radius: 2px;
    font-size: 11px;
    color: #44B9A3;
    text-align: center;
    cursor: pointer;
    z-index: 1;
  }
}
</style>
