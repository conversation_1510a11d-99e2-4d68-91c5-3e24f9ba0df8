<template>
  <div class="contain">
    <el-row>
      <!-- 总用户数 -->
      <div class="box">
        <div class="b1">总用户数</div>
        <div class="b2">{{ data.staffNum | toFixed0 }}</div>
        <div v-show="hasTime">
          <div class="b3">
            同比：<span :class="[data.staffNum > data.lyStaffNum ? 'up':'down']">{{ num1 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            环比：<span :class="[data.staffNum > data.lmStaffNum ? 'up':'down']">{{ num2 }}%</span>
          </div>
          <div class="b4">去年：{{ data.lyStaffNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmStaffNum | toFixed0 }}</div>
        </div>
      </div>
      <!-- 培训用户数 -->
      <div class="box">
        <div class="b1">培训用户数</div>
        <div class="b2">{{ data.trainPeopleNum | toFixed0 }}</div>
        <div v-show="hasTime">
          <div class="b3">
            同比：<span :class="[data.trainPeopleNum > data.lyTrainPeopleNum ? 'up':'down']">{{ num3 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            环比：<span :class="[data.trainPeopleNum > data.lmTrainPeopleNum ? 'up':'down']">{{ num4 }}%</span>
          </div>
          <div class="b4">去年：{{ data.lyTrainPeopleNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmTrainPeopleNum | toFixed0 }}</div>
        </div>
      </div>
      <!-- 培训人次 -->
      <div class="box">
        <div class="b1">培训人次</div>
        <div class="b2">{{ data.trainPersonNum | toFixed0 }}</div>
        <div v-show="hasTime">
          <div class="b3">
            同比：<span :class="[data.trainPersonNum > data.lyTrainPersonNum ? 'up':'down']">{{ num5 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            环比：<span :class="[data.trainPersonNum > data.lmTrainPersonNum ? 'up':'down']">{{ num6 }}%</span>
          </div>
          <div class="b4">去年：{{ data.lyTrainPersonNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmTrainPersonNum | toFixed0 }}</div>
        </div>
      </div>
      <!-- 培训任务数 -->
      <div class="box">
        <div class="b1">培训任务数</div>
        <div class="b2">{{ data.trainTaskNum | toFixed0 }}</div>
        <div v-show="hasTime">
          <div class="b3">
            同比：<span :class="[data.trainTaskNum > data.lyTrainTaskNum ? 'up':'down']">{{ num7 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            环比：<span :class="[data.trainTaskNum > data.lmTrainTaskNum ? 'up':'down']">{{ num8 }}%</span>
          </div>
          <div class="b4">去年：{{ data.lyTrainTaskNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmTrainTaskNum | toFixed0 }}</div>
        </div>
      </div>
      <!-- 培训时长(分钟) -->
      <div class="box">
        <div class="b1">培训时长(分钟)</div>
        <div class="b2">{{ data.trainTime | toFixed2 }}</div>
        <div v-show="hasTime">
          <div class="b3">
            同比：<span :class="[data.trainTime > data.lyTrainTime ? 'up':'down']">{{ num9 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            环比：<span :class="[data.trainTime > data.lmTrainTime ? 'up':'down']">{{ num10 }}%</span>
          </div>
          <div class="b4">去年：{{ data.lyTrainTime | toFixed2 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmTrainTime | toFixed2 }}</div>
        </div>
      </div>
    </el-row>
    <el-collapse-transition>
      <div v-show="show">
        <el-row>
          <!-- 参与人次 -->
          <div class="box">
            <div class="b1">参与人次</div>
            <div class="b2">{{ data.joinPeopleNum | toFixed0 }}</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[data.joinPeopleNum > data.lyJoinPeopleNum ? 'up':'down']">{{ num11 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[data.joinPeopleNum > data.lmJoinPeopleNum ? 'up':'down']">{{ num12 }}%</span>
              </div>
              <div class="b4">去年：{{ data.lyJoinPeopleNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmJoinPeopleNum | toFixed0 }}</div>
            </div>
          </div>
          <!-- 通过人次 -->
          <div class="box">
            <div class="b1">通过人次</div>
            <div class="b2">{{ data.passPeopleNum | toFixed0 }}</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[data.passPeopleNum > data.lyPassPeopleNum ? 'up':'down']">{{ num13 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[data.passPeopleNum > data.lmPassPeopleNum ? 'up':'down']">{{ num14 }}%</span>
              </div>
              <div class="b4">去年：{{ data.lyPassPeopleNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmPassPeopleNum | toFixed0 }}</div>
            </div>
          </div>
          <!-- 培训覆盖率 -->
          <div class="box">
            <div class="b1">培训覆盖率</div>
            <div class="b2">{{ trainCoverRate }}%</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[trainCoverRate > lytrainCoverRate ? 'up':'down']">{{ num15 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[trainCoverRate > lmtrainCoverRate ? 'up':'down']">{{ num16 }}%</span>
              </div>
              <div class="b4">去年：{{ lytrainCoverRate }}% &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ lmtrainCoverRate }}%</div>
            </div>
          </div>
          <!-- 培训参与率 -->
          <div class="box">
            <div class="b1">培训参与率</div>
            <div class="b2">{{ trainJoinRate }}%</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[trainJoinRate > lytrainJoinRate ? 'up':'down']">{{ num17 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[trainJoinRate > lmtrainJoinRate ? 'up':'down']">{{ num18 }}%</span>
              </div>
              <div class="b4">去年：{{ lytrainJoinRate }}% &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ lmtrainJoinRate }}%</div>
            </div>
          </div>
          <!-- 培训通过率 -->
          <div class="box">
            <div class="b1">培训通过率</div>
            <div class="b2">{{ trainPassRate }}%</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[trainPassRate > lytrainPassRate ? 'up':'down']">{{ num19 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[trainPassRate > lmtrainPassRate ? 'up':'down']">{{ num20 }}%</span>
              </div>
              <div class="b4">去年：{{ lytrainPassRate }}% &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ lmtrainPassRate }}%</div>
            </div>
          </div>
        </el-row>

        <el-row>
          <!-- 参与达标率 -->
          <div class="box">
            <div class="b1">参与达标率</div>
            <div class="b2">{{ joinPassRate }}%</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[joinPassRate > lyjoinPassRate ? 'up':'down']">{{ num21 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[joinPassRate > lmjoinPassRate ? 'up':'down']">{{ num22 }}%</span>
              </div>
              <div class="b4">去年：{{ lyjoinPassRate }}% &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ lmjoinPassRate }}%</div>
            </div>
          </div>
          <!-- 活跃用户数 -->
          <div class="box">
            <div class="b1">活跃用户数</div>
            <div class="b2">{{ data.activePeopleNum | toFixed0 }}</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[data.activePeopleNum > data.lyActivePeopleNum ? 'up':'down']">{{ num23 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[data.activePeopleNum > data.lmActivePeopleNum ? 'up':'down']">{{ num24 }}%</span>
              </div>
              <div class="b4">去年：{{ data.lyActivePeopleNum | toFixed0 }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ data.lmActivePeopleNum | toFixed0 }}</div>
            </div>
          </div>
          <!-- 活跃率 -->
          <div class="box">
            <div class="b1">活跃率</div>
            <div class="b2">{{ activeRate }}%</div>
            <div v-show="hasTime">
              <div class="b3">
                同比：<span :class="[activeRate > lyactiveRate ? 'up':'down']">{{ num25 }}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                环比：<span :class="[activeRate > lmactiveRate ? 'up':'down']">{{ num26 }}%</span>
              </div>
              <div class="b4">去年：{{ lyactiveRate }}% &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;上期：{{ lmactiveRate }}%</div>
            </div>
          </div>
        </el-row>
      </div>
    </el-collapse-transition>

    <el-row>
      <el-button type="text" class="btn" @click="show=!show">{{ show===true? '收起':'展开' }}</el-button>
    </el-row>

  </div>
</template>

<script>
export default {
  name: 'StatisticsTop',
  filters: {
    // 用户数取整
    toFixed0(v) {
      v = Number(v)
      let res = 0
      if (v < 10000) {
        res = parseInt(v)
      } else {
        res = (v / 10000).toFixed(2) + '万'
      }
      return res
    },
    // 时长取两位
    toFixed2(v) {
      v = Number(v)
      let res = 0
      if (v < 10000) {
        res = v.toFixed(2)
      } else {
        res = (v / 10000).toFixed(2) + '万'
      }
      return res
    }
    // toFixed2(v) {
    //   v = Number(v)
    //   let res = 0
    //   if (v < 10000) {
    //     res = v
    //   } else {
    //     res = (v / 10000).toFixed(2) + '万'
    //   }
    //   return res
    // }
  },
  props: {
    data: {
      type: Object,
      required: true
    },
    hasTime: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false
      // trainTime: this.data.trainTime, // 培训时长
      // lyTrainTime: this.data.lyTrainTime, // 去年培训时长
      // lmTrainTime: this.data.lmTrainTime // 上期培训时长
      // activePeopleNum: this.data.activePeopleNum, // 活跃用户数
      // joinPeopleNum: this.data.joinPeopleNum, // 参与人次
      // lmActivePeopleNum: this.data.lmActivePeopleNum, // 上期活跃用户数
      // lmJoinPeopleNum: this.data.lmJoinPeopleNum, // 上期参与人次
      // lmPassPeopleNum: this.data.lmPassPeopleNum, // 上期通过人次
      // lmStaffNum: this.data.lmStaffNum, // 上期总用户数
      // lmTrainPeopleNum: this.data.lmTrainPeopleNum, // 上期培训用户数
      // lmTrainPersonNum: this.data.lmTrainPersonNum, // 上期培训人次
      // lmTrainTaskNum: this.data.lmTrainTaskNum, // 上期培训任务数
      // lyActivePeopleNum: this.data.lyActivePeopleNum, // 去年活跃用户数
      // lyJoinPeopleNum: this.data.lyJoinPeopleNum, // 去年参与人次
      // lyPassPeopleNum: this.data.lyPassPeopleNum, // 去年通过人次
      // lyStaffNum: this.data.lyStaffNum, // 去年总用户数
      // lyTrainPeopleNum: this.data.lyTrainPeopleNum, // 去年培训用户数
      // lyTrainPersonNum: this.data.lyTrainPersonNum, // 去年培训人次
      // lyTrainTaskNum: this.data.lyTrainTaskNum, // 去年培训任务数
      // passPeopleNum: this.data.passPeopleNum, // 通过人次
      // staffNum: this.data.staffNum, // 总用户数
      // trainPeopleNum: this.data.trainPeopleNum, // 培训用户数
      // trainPersonNum: this.data.trainPersonNum, // 培训人次
      // trainTaskNum: this.data.trainTaskNum // 培训任务数
    }
  },
  computed: {
    // 总用户数
    num1: function() {
      const { staffNum, lyStaffNum } = this.copy(this.data)
      // const num = (staffNum - lyStaffNum) / lyStaffNum * 100
      // return num.toFixed(2)
      return this.handlerZero(staffNum, lyStaffNum)
    },
    num2: function() {
      const { staffNum, lmStaffNum } = this.copy(this.data)
      return this.handlerZero(staffNum, lmStaffNum)
    },
    // 培训用户数
    num3: function() {
      const { trainPeopleNum, lyTrainPeopleNum } = this.copy(this.data)
      return this.handlerZero(trainPeopleNum, lyTrainPeopleNum)
    },
    num4: function() {
      const { trainPeopleNum, lmTrainPeopleNum } = this.copy(this.data)
      return this.handlerZero(trainPeopleNum, lmTrainPeopleNum)
    },
    // 培训人次
    num5: function() {
      const { trainPersonNum, lyTrainPersonNum } = this.copy(this.data)
      return this.handlerZero(trainPersonNum, lyTrainPersonNum)
    },
    num6: function() {
      const { trainPersonNum, lmTrainPersonNum } = this.copy(this.data)
      return this.handlerZero(trainPersonNum, lmTrainPersonNum)
    },
    // 培训任务数
    num7: function() {
      const { trainTaskNum, lyTrainTaskNum } = this.copy(this.data)
      return this.handlerZero(trainTaskNum, lyTrainTaskNum)
    },
    num8: function() {
      const { trainTaskNum, lmTrainTaskNum } = this.copy(this.data)
      return this.handlerZero(trainTaskNum, lmTrainTaskNum)
    },
    // 培训时长(分钟)
    num9: function() {
      const { trainTime, lyTrainTime } = this.copy(this.data)
      return this.handlerZero(Number(trainTime), Number(lyTrainTime))
    },
    num10: function() {
      const { trainTime, lmTrainTime } = this.copy(this.data)
      return this.handlerZero(Number(trainTime), Number(lmTrainTime))
    },
    // 参与人次
    num11: function() {
      const { joinPeopleNum, lyJoinPeopleNum } = this.copy(this.data)
      return this.handlerZero(joinPeopleNum, lyJoinPeopleNum)
    },
    num12: function() {
      const { joinPeopleNum, lmJoinPeopleNum } = this.copy(this.data)
      return this.handlerZero(joinPeopleNum, lmJoinPeopleNum)
    },
    // 通过人次
    num13: function() {
      const { passPeopleNum, lyPassPeopleNum } = this.copy(this.data)
      return this.handlerZero(passPeopleNum, lyPassPeopleNum)
    },
    num14: function() {
      const { passPeopleNum, lmPassPeopleNum } = this.copy(this.data)
      return this.handlerZero(passPeopleNum, lmPassPeopleNum)
    },
    // 活跃用户数
    num23: function() {
      const { activePeopleNum, lyActivePeopleNum } = this.copy(this.data)
      return this.handlerZero(activePeopleNum, lyActivePeopleNum)
    },
    num24: function() {
      const { activePeopleNum, lmActivePeopleNum } = this.copy(this.data)
      return this.handlerZero(activePeopleNum, lmActivePeopleNum)
    },
    // 培训覆盖率
    trainCoverRate: function() {
      const { trainPeopleNum, staffNum } = this.copy(this.data)
      return this.handlerChu(trainPeopleNum, staffNum)
    },
    lytrainCoverRate: function() {
      const { lyTrainPeopleNum, lyStaffNum } = this.copy(this.data)
      return this.handlerChu(lyTrainPeopleNum, lyStaffNum)
    },
    lmtrainCoverRate: function() {
      const { lmTrainPeopleNum, lmStaffNum } = this.copy(this.data)
      return this.handlerChu(lmTrainPeopleNum, lmStaffNum)
    },
    num15: function() {
      return this.handlerZero(this.trainCoverRate, this.lytrainCoverRate)
    },
    num16: function() {
      return this.handlerZero(this.trainCoverRate, this.lmtrainCoverRate)
    },
    // 培训参与率
    trainJoinRate: function() {
      const { joinPeopleNum, trainPersonNum } = this.copy(this.data)
      return this.handlerChu(joinPeopleNum, trainPersonNum)
    },
    lytrainJoinRate: function() {
      const { lyJoinPeopleNum, lyTrainPersonNum } = this.copy(this.data)
      return this.handlerChu(lyJoinPeopleNum, lyTrainPersonNum)
    },
    lmtrainJoinRate: function() {
      const { lmJoinPeopleNum, lmTrainPersonNum } = this.copy(this.data)
      return this.handlerChu(lmJoinPeopleNum, lmTrainPersonNum)
    },
    num17: function() {
      return this.handlerZero(this.trainJoinRate, this.lytrainJoinRate)
    },
    num18: function() {
      return this.handlerZero(this.trainJoinRate, this.lmtrainJoinRate)
    },
    // 培训通过率
    trainPassRate: function() {
      const { passPeopleNum, trainPersonNum } = this.copy(this.data)
      return this.handlerChu(passPeopleNum, trainPersonNum)
    },
    lytrainPassRate: function() {
      const { lyPassPeopleNum, lyTrainPersonNum } = this.copy(this.data)
      return this.handlerChu(lyPassPeopleNum, lyTrainPersonNum)
    },
    lmtrainPassRate: function() {
      const { lmPassPeopleNum, lmTrainPersonNum } = this.copy(this.data)
      return this.handlerChu(lmPassPeopleNum, lmTrainPersonNum)
    },
    num19: function() {
      return this.handlerZero(this.trainPassRate, this.lytrainPassRate)
    },
    num20: function() {
      return this.handlerZero(this.trainPassRate, this.lmtrainPassRate)
    },
    // 参与达标率
    joinPassRate: function() {
      const { passPeopleNum, joinPeopleNum } = this.copy(this.data)
      return this.handlerChu(passPeopleNum, joinPeopleNum)
    },
    lyjoinPassRate: function() {
      const { lyPassPeopleNum, lyJoinPeopleNum } = this.copy(this.data)
      return this.handlerChu(lyPassPeopleNum, lyJoinPeopleNum)
    },
    lmjoinPassRate: function() {
      const { lmPassPeopleNum, lmJoinPeopleNum } = this.copy(this.data)
      return this.handlerChu(lmPassPeopleNum, lmJoinPeopleNum)
    },
    num21: function() {
      return this.handlerZero(this.joinPassRate, this.lyjoinPassRate)
    },
    num22: function() {
      return this.handlerZero(this.joinPassRate, this.lmjoinPassRate)
    },
    // 活跃率
    activeRate: function() {
      const { activePeopleNum, staffNum } = this.copy(this.data)
      return this.handlerChu(activePeopleNum, staffNum)
    },
    lyactiveRate: function() {
      const { lyActivePeopleNum, lyStaffNum } = this.copy(this.data)
      return this.handlerChu(lyActivePeopleNum, lyStaffNum)
    },
    lmactiveRate: function() {
      const { lmActivePeopleNum, lmStaffNum } = this.copy(this.data)
      return this.handlerChu(lmActivePeopleNum, lmStaffNum)
    },
    num25: function() {
      return this.handlerZero(this.activeRate, this.lyactiveRate)
    },
    num26: function() {
      return this.handlerZero(this.activeRate, this.lmactiveRate)
    }
  },
  created() {
    const data1 = this.copy(this.data)
    this.data.trainTime = Number(data1.trainTime)
    this.data.lyTrainTime = Number(data1.lyTrainTime)
    this.data.lmTrainTime = Number(data1.lmTrainTime)
  },
  methods: {
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    handlerZero(son, mon) {
      if (son === 0 && mon === 0) {
        return 0
      } else if (son === 0 || mon === 0) {
        return 100
      } else {
        return ((son - mon) / mon * 100).toFixed(2)
      }
    },
    handlerChu(son, mon) {
      if (son === 0 && mon === 0) {
        return 0
      } else if (son === 0 || mon === 0) {
        return 0
      } else {
        return (son / mon * 100).toFixed(2)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  width: 100%;
  font-family: Microsoft YaHei;
}
.box{
  float: left;
  width: 310px;
  min-height: 140px;
  background: #FFFFFF;
  border-radius: 2px;
  margin-right: 17px;
  margin-bottom: 17px;
}

.b1{
  font-size: 22px;
  font-weight: 400;
  color: #878C98;
  padding-left: 40px;
  padding-top: 24px;
}

.b2{
  font-size: 30px;
  font-weight: bold;
  color: #333333;
  padding-left: 40px;
  padding-top: 14px;
}

.b3{
  font-size: 12px;
  color: #878C98;
  padding-left: 40px;
  padding-top: 12px;
  padding-bottom: 8px;
  .up{
    color: #41C02C;
    &::before{
      content:'+';
    }
    &::after{
      content:'\E6E6';
      font-family:element-icons!important;
    }
  }
  .down{
    color: #FE0606;
      // &::before{
      //   content:'-';
      // }
      &::after{
      content:'\E6EB';
      font-family:element-icons!important;
    }
  }
}

.b4{
  font-size: 12px;
  color: #878C98;
  padding-left: 40px;
  padding-bottom: 15px;
}

.btn{
  float: right;
}
</style>
