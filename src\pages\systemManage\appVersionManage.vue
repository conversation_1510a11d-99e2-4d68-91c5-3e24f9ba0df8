<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="form.keyWord" placeholder="版本名称" clearable @clear="search" @keyup.enter.native="search">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="search" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.force" placeholder="更新类型" clearable @change="search">
          <el-option v-for="item in updateType" :key="item.value" :value="item.value" :label="item.label" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.status" placeholder="版本状态" clearable @change="search">
          <el-option v-for="item in versionState" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="form.typ" placeholder="版本类型" clearable @change="search">
          <el-option v-for="item in versionType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="create">创建</el-button>
      </div>
    </div>
    <el-table :data="records" border stripe empty-text="暂无数据">
      <el-table-column align="center" label="版本ID" prop="svnId" />
      <el-table-column align="center" label="版本类型">
        <template slot-scope="{row}">{{ row.typ | typList }}</template>
      </el-table-column>
      <el-table-column align="center" label="版本号" prop="version" />
      <el-table-column align="center" label="更新类型" width="100px">
        <template slot-scope="{row}">{{ row.force===1 ? '强制更新' : '非强制更新' }}</template>
      </el-table-column>
      <el-table-column align="center" label="版本说明" prop="msg" width="200px" />
      <el-table-column align="center" label="版本地址" prop="url" width="160px" />
      <el-table-column align="center" label="版本编码" prop="versionCode" />
      <el-table-column align="center" label="审核预估结束时间" width="160px">
        <template slot-scope="{row}">{{ row.auditTime | timeFilter }}</template>
      </el-table-column>
      <el-table-column align="center" label="发布人" prop="createBy" />
      <el-table-column align="center" label="发布时间" prop="createTime" width="160px" />
      <el-table-column align="center" label="修改人" prop="updateBy" />
      <el-table-column align="center" label="修改时间" prop="updateTime" width="160px" />
      <el-table-column align="center" label="版本状态">
        <template slot-scope="{row}">
          <el-switch v-model="row.status" :active-value="1" :inactive-value="0" active-color="#13ce66" @change="stateSwitch($event,row.svnId)" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="{row}">
          <el-button type="text" @click="edit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :auto-scroll="false" class="text-center" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import { svnList, svnUpdate } from '@/api/systemManage'
export default {
  name: 'AppVersionManage',
  filters: {
    typList(val) {
      const arr = ['', 'apk', 'ios(个人)', 'ios(企业)', 'HarmonyOS']
      return arr[val]
    },
    timeFilter(val) {
      if (val === '') {
        return '——'
      } else {
        return val
      }
    }
  },
  components: {
    Pagination
  },
  data() {
    return {
      updateType: [
        { label: '强制更新', value: 1 },
        { label: '非强制更新', value: 0 }
      ],
      versionState: [
        { label: '有效', value: 1 },
        { label: '无效', value: 0 }
      ],
      versionType: [
        { label: 'APK', value: 1 },
        { label: 'IOS(个人)', value: 2 },
        { label: 'IOS(企业)', value: 3 },
        { label: 'HarmonyOS', value: 4 }
      ],
      pager: {
        page: 1,
        pageSize: 10
      },
      total: 0,
      form: {
        keyWord: '',
        force: null,
        status: null,
        typ: null
      },
      records: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search() {
      this.pager.page = 1
      this.getList()
    },
    getList() {
      const query = {}
      query.condition = this.form
      query.pager = this.pager
      svnList(query).then(res => {
        this.total = res.total
        this.records = res.records
      })
    },
    // 版本状态开关
    stateSwitch(status, svnId) {
      svnUpdate({ svnId: svnId, status: status }).then(res => {
        if (res === '版本更新成功') {
          this.$message.success('版本状态更新成功')
        } else {
          this.$message.warning('请稍后再试')
        }
      })
    },
    edit(row) {
      this.$router.push({ name: 'EditVersion', query: { row: row }})
    },
    create() {
      this.$router.push({ name: 'CreateVersion' })
    },
    handlePagination(val) {
      this.pager = val
      this.getList()
    }
  }
}
</script>

<style>
</style>
