<template>
  <div class="upload-container" :style="{ width, height }">
    <el-upload
      class="image-uploader"
      :multiple="false"
      :show-file-list="false"
      :on-success="handleImageSuccess"
      :action="uploadFileApi"
      :data="uploadData"
      :before-upload="handleBeforeUpload"
    >
      <i class="el-icon-plus" />
    </el-upload>
    <div v-show="imageUrl.length>0" class="image-preview">
      <div v-show="imageUrl.length>1" class="image-preview-wrapper">
        <img :src="imageUrl">
        <div class="image-preview-action">
          <i class="el-icon-delete" @click="rmImage" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadFileApi } from '@/api/article'
import emitter from 'element-ui/src/mixins/emitter'
import Migrating from 'element-ui/src/mixins/migrating'

export default {
  name: 'SingleImageUpload',
  mixins: [emitter, Migrating],
  props: {
    value: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    },
    url: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      action: '/core/common/file/upload.json',
      headers: {},
      uploadHeaders: { token: this.$store.getters.token },
      uploadData: { data: '' },
      uploadFileApi,
      imageUrl: this.url,
      flag: true
    }
  },
  watch: {
    url(v) {
      this.imageUrl = v
    }
  },
  methods: {
    rmImage() {
      this.imageUrl = ''
      this.emitInput('')
    },
    emitInput(val) {
      this.$emit('input', val)
      this.dispatch('ElFormItem', 'el.form.change', [val])
    },
    // upload
    async handleBeforeUpload(val) {
      this.flag = ['image/png', 'image/gif', 'image/jpg', 'image/jpeg'].includes(val.type)
      if (!this.flag) return this.$message.error('上传的图片只能是 PNG、GIF、 JPG或者JPEG 格式!')
    },
    handleImageSuccess(res, file, fileList) {
      const callback = () => {
        if (this.flag) {
          this.imageUrl = res.data.fileUrl
          this.emitInput(res.data.filePath)
        }
      }
      this.commonSuccess(res, file, fileList, callback)
    },
    // 公共上传成功回调
    commonSuccess(res, file, fileList, callback) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        callback()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.upload-container {
  width: 100%;
  height: 100%;
  position: relative;
  .image-uploader {
    height: 100%;
    &::v-deep .el-upload.el-upload--text {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px dashed #c0ccda;
      .el-icon-plus {
        font-size: 28px;
        color: #8c939d;
      }
    }
  }
  .image-preview {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border: 1px dashed #d9d9d9;
    .image-preview-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .image-preview-action {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, .5);
      transition: opacity .3s;
      cursor: pointer;
      text-align: center;
      line-height: 150px;
      .el-icon-delete {
        font-size: 36px;
      }
    }
    &:hover {
      .image-preview-action {
        opacity: 1;
      }
    }
  }
}
</style>
