import request from '@/utils/request'

// 评论列表
export function commentList(params) {
  return request({
    url: '/promoteComment/getCommentsCms',
    method: 'post',
    data: params
  })
}

// 评论屏蔽
export function commentBlock(params) {
  return request({
    url: '/promoteComment/updateBlockStatus',
    method: 'post',
    data: params
  })
}

// 举报类型列表
export function reportTypeList(params) {
  return request({
    url: '/cms/dict/getByType',
    method: 'get',
    params: params
  })
}

// 举报管理列表
export function reportList(params) {
  return request({
    url: '/promoteComment/commentReportPageList',
    method: 'post',
    data: params
  })
}

// 举报明细列表
export function reportDetailList(params) {
  return request({
    url: '/promoteComment/commentReportDetailPageList',
    method: 'post',
    data: params
  })
}

// 举报审核
export function reportAudit(params) {
  return request({
    url: '/promoteComment/commentReportAudit',
    method: 'post',
    data: params
  })
}
