<template>
  <section class="videoEdit">
    <div class="videoEdit-title">
      视频
    </div>
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-form-item label="推广产品" prop="productId">
        <el-select v-model="form.orgzId" clearable placeholder="全部企业" @change="getProductList()">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="form.productId" clearable placeholder="全部产品" no-data-text="请先选择企业">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="视频" required>
        <div style="display: flex">
          <div v-if="!form.content || form.content === ''" class="select-btn" @click="selsecVideoDialogVisible = true">视频素材库选择</div>
          <video-upload v-if="!form.content || form.content === ''" :key="form.content" :width="'260px'" :height="'100px'" :video-file-id.sync="form.content" />
          <video-play v-show="form.content && form.content !== ''" :video-file-id="form.content" @del="form.content = ''" />
        </div>
      </el-form-item>
      <el-form-item label="封面设置" required prop="coverIds">
        <UploadPic :key="form.coverUrls[0]" tips="添加封面图" :pic-id.sync="form.coverIds[0]" :url="form.coverUrls[0]" />
      </el-form-item>
      <el-form-item label="视频标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入视频标题 (5~100个字)"
          :minlength="5"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item label="视频描述">
        <editor
          v-model="form.description"
          height="200"
          @change="change"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="作者" prop="authorId">
        <div>
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" />
        </div>
      </el-form-item>
      <el-form-item label="推广需求" required>
        <Budget @getBudgetData="getBudgetData" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>

    <SelsectVideo :dialog-visible.sync="selsecVideoDialogVisible" :video-file-id.sync="form.content" />
  </section>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { getUnitList } from '@/api/userManage'
import { productList, saveOrUpdatePlatformArticleCms } from '@/api/marketing/taskPromote'
import VideoUpload from '@/components/Upload/videoUpload.vue'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Editor from '@/components/wangEditor'
import Author from '@/pages/marketing/task/components/author.vue'
import SelsectVideo from '@/pages/marketing/task/components/selectVideo.vue'
import Budget from './budget'
export default {
  components: {
    VideoUpload,
    VideoPlay,
    UploadPic,
    Editor,
    Author,
    SelsectVideo,
    Budget
  },
  data() {
    return {
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      form: {
        articleId: null,
        authorId: null,
        authorName: '',
        title: '',
        contentType: 'VOD_ID',
        content: '',
        description: '',
        categoryIds: [],
        coverIds: [],
        coverUrls: []
      },
      orgList: [],
      productList: [],
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        title: [
          { required: true, message: '请输入视频标题', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        categoryIds: [
          { required: true, message: '请选择专科分类', trigger: 'change' }
        ],
        coverIds: [
          { required: true, message: '请上传封面', trigger: 'change' }
        ],
        productId: [
          { required: true, message: '请选择推广产品', trigger: 'change' }
        ],
        authorId: [
          { required: true, message: '请选择作者', trigger: 'change' }
        ]
      },
      detail: {},
      authorInfo: {},
      authorDialogVisible: false,
      selsecVideoDialogVisible: false,
      agreementDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
        this.form.authorPhone = this.authorInfo.phone
      }
    }
  },
  mounted() {
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
    this.getOrg()
  },
  methods: {
    getOrg() {
      const query = {
        condition: {
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getProductList() {
      if (this.form.orgzId) {
        productList({ orgzId: this.form.orgzId, pageSize: 1000 }).then(res => {
          this.productList = res
        })
      }
    },
    getBudgetData(v) {
      this.form = {
        ...this.form,
        ...v
      }
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.content === '') {
          this.$message.error('视频未上传')
          return
        }
        if (valid) {
          this.form.taskType = this.$route.query.type
          saveOrUpdatePlatformArticleCms(this.form).then(res => {
            this.agreementDialogVisible = true
            this.$message.success('视频发布成功')
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.videoEdit {
  background-color: #fff;
  width: 1200px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  .el-form {
    margin-left: 20px;
    width: 1200px;
    ::v-deep .el-textarea__inner {
      height: 250px;
    }
    .el-select {
      width: 200px;
    }
    .el-tag {
      height: 36px;
      padding: 5px 10px;
      vertical-align: middle;
    }
    .select-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 100px;
      margin-right: 20px;
      font-size: 22px;
      font-weight: 700;
      color: #000;
      cursor: pointer;
      background-color: #f5f7fa;
    }
  }
}
</style>
