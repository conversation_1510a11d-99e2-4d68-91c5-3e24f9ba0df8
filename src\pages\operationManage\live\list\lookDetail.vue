<template>
  <el-dialog title="观看详情" :before-close="beforeClose" append-to-body :visible.sync="visible" width="70vw" top="8vh">
    <div class="app-container">
      <h2>观众概览</h2>
      <el-row style="margin-left:20px">
        <el-table :data="peopleDetail" border stripe>
          <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <h2>观看明细</h2>
      <el-row style="margin-left:20px">
        <el-table :data="watchDetail" border stripe>
          <el-table-column v-for="col in tableColumnList1" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <h2>评论明细</h2>
      <el-row style="margin-left:20px">
        <el-table :data="commentDetail" border stripe>
          <el-table-column v-for="col in tableColumnList2" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.prop==='content'">
                <span v-if="scope.row[col.prop]" v-html="scope.row[col.prop]" />
                <el-image v-else style="width: 100px; height: 100px" :src="scope.row['img']" fit="cover" />
              </template>
              <template v-else>
                <span>{{ scope.row[col.prop] }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'LookDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    peopleDetail: {
      type: Array,
      default: () => []
    },
    watchDetail: {
      type: Array,
      default: () => []
    },
    commentDetail: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: '观众来源', align: 'center', prop: 'watchFrom' },
        { id: 1, label: '观众姓名', align: 'center', prop: 'realName' },
        { id: 2, label: '个人账号', align: 'center', prop: 'userName' },
        { id: 3, label: '手机', align: 'center', prop: 'phone' },
        { id: 4, label: '身份', align: 'center', prop: 'identity' },
        { id: 5, label: '专科', align: 'center', prop: 'majorName' },
        { id: 6, label: '职称', align: 'center', prop: 'academicName' },
        { id: 7, label: '区域', align: 'center', prop: 'areaName' },
        { id: 8, label: '首次进入观看时间', align: 'center', prop: 'firstEnterTime', width: '160px' },
        { id: 9, label: '累计观看时长', align: 'center', prop: 'totalDuration' },
        { id: 10, label: '评论数', align: 'center', prop: 'comments' }
      ]),
      tableColumnList1: Object.freeze([
        { id: 1, label: '进入时间', align: 'center', prop: 'starttime' },
        { id: 2, label: '退出时间', align: 'center', prop: 'endTime' },
        { id: 3, label: '观看时长', align: 'center', prop: 'duration' }
      ]),
      tableColumnList2: Object.freeze([
        { id: 1, label: '评论时间', align: 'center', prop: 'createTime' },
        { id: 2, label: '评论内容', align: 'center', prop: 'content' }
      ])
    }
  },
  methods: {
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    }
  }
}
</script>
