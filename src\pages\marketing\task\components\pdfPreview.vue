<template>
  <el-dialog
    title="作者授权协议"
    :visible.sync="dialogVisible"
    width="1200px"
    :before-close="handleClose"
  >
    <iframe src="https://mycs-v1-2020-test.oss-cn-shenzhen.aliyuncs.com/%E4%B8%89%E7%BA%A7%E5%85%AC%E7%AB%8B%E5%8C%BB%E9%99%A2%E7%BB%A9%E6%95%88%E8%80%83%E6%A0%B8%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C.pdf" />
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    pdf: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog {
  iframe {
    width: 100%;
    height: 496px;
  }
}
</style>
