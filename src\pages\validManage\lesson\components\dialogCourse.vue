<template>
  <el-dialog top="8vh" title="选择课程" :visible.sync="visible" center :before-close="beforeClose" width="1000px">
    <el-row style="max-height: 800px;">
      <el-col :span="6" style="height: 600px;">
        <div style="margin: 15px 0;">
          <span style="font-size: 24px; font-weight: bold;margin-right: 15px;">资源分类</span>
          <el-button v-if="condition.cateId" type="text" @click="close">取消选择</el-button>
        </div>
        <el-scrollbar wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container">
          <el-tree :data="treeList" :props="defaultProps" expand-on-click-node @node-click="handleNodeClick" />
        </el-scrollbar>
      </el-col>
      <el-col :span="18" style="height: 700px;">
        <div class="search-column">
          <div class="search-column__item">
            <el-select v-model="condition.type" filterable clearable @change="init">
              <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-input v-model="condition.keyword" placeholder="根据左侧查询方式对应关键字" clearable @change="init">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
            </el-input>
          </div>
        </div>

        <a-table :columns="columns" :data="list" border stripe max-height="500px">
          <template slot="actions" slot-scope="{row}">
            <el-button type="text" @click="toggleSelect(row)">选择</el-button>
          </template>
          <template slot="cateName" slot-scope="{row}">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ row.cateName }}
              </div>
              <span>{{ row.cateName | filterFont }}</span>
            </el-tooltip>
          </template>
        </a-table>

        <Pagination :total="total" :page="pager.page" @pagination="handlePagination" />
      </el-col>
    </el-row>

  </el-dialog>
</template>

<script>
import ATable from '@/components/ATable'
import Pagination from '@/components/Pagination'
import { courseList } from '@/api/course'

export default {
  name: 'DialogCourse',
  components: { ATable, Pagination },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    treeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'categoryId'
      },
      condition: {
        type: '',
        keyword: '',
        cateId: ''
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      typeList: Object.freeze([
        { value: '1', name: '课程名称' },
        { value: '2', name: '作者' }
      ]),
      columns: Object.freeze([
        { props: { label: '课程ID', align: 'center', prop: 'coursePackId' }},
        { props: { label: '课程名称', align: 'center', prop: 'name' }},
        { props: { label: '分类', align: 'center', prop: 'cateName' }, slot: 'cateName' },
        { props: { label: '作者', align: 'center', prop: 'doctorName' }},
        {
          props: { align: 'center', label: '操作', width: '100' },
          slot: 'actions'
        }
      ]),
      list: [],
      total: 0
    }
  },
  watch: {
    visible(v) {
      v && this.init()
    }
  },
  methods: {
    init(reset = true) {
      reset && (this.pager.page = 1)
      const params = { condition: this.condition, pager: this.pager }
      courseList(params).then(res => {
        this.list = res.records
        this.total = res.total
      })
    },
    handlePagination(val) {
      this.pager = val
      this.init(false)
    },
    toggleSelect(row) {
      this.$emit('selectCourse', row)
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    },
    handleNodeClick(data) {
      this.condition.cateId = data.categoryId
      this.init()
    },
    close() {
      this.condition.cateId = ''
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
.content-container {
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: #fff;
  &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
    overflow-x: hidden;
  }
  &::v-deep .el-scrollbar__view.p20-scrollbar__view {
    background-color: #fff;
    padding-right: 15px;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    -ms-box-sizing: border-box;
  }
  &::v-deep .el-scrollbar__bar.is-vertical {
    width: 8px;
  }
}
</style>
