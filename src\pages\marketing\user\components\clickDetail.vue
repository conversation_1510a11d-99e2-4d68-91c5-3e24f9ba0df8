<template>
  <el-dialog
    title="点击详情设置"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
    center
  >
    <p>文章/视频/抖喇点击详情展示和导出字段：</p>
    <el-checkbox-group v-model="checkedList" size="medium">
      <el-checkbox v-for="item in list" :key="item" :label="item" />
    </el-checkbox-group>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
    </el-checkbox-group></el-dialog>
</template>

<script>
import { settingClickDetail, settingClickEdit } from '@/api/marketing/userPromote'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: ['姓名', '手机', '身份', '专科', '职称', '区域', '单位', '点击时间', 'IP', '点击链接'],
      checkedList: []
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        settingClickDetail({ orgId: this.id }).then(res => {
          this.checkedList = res
        })
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    confirm() {
      settingClickEdit({
        bizId: this.id,
        showFields: JSON.stringify(this.checkedList)
      }).then(() => {
        this.$message.success('设置成功!')
        this.$emit('update:dialogVisible', false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 45px 24px;
    color: #333;
  }
}
</style>
