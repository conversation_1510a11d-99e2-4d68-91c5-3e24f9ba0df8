<template>
  <el-dialog
    title="承揽协议"
    :visible.sync="dialogVisible"
    width="420px"
    :before-close="handleClose"
    center
  >
    <span>请选择: </span>
    <el-select v-model="value" placeholder="请选择需要查看的协议">
      <el-option
        v-for="item in list"
        :key="item.type"
        :label="item.name"
        :value="item.type"
      />
    </el-select>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getLinkType, getContractLink } from '@/api/marketing/userPromote'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      value: 'FLEXIBLE_EMPLOYMENT_TJZY'
    }
  },
  created() {
    getLinkType().then(res => {
      this.list = res
    })
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    confirm() {
      getContractLink({
        bizId: this.id,
        bizType: 2,
        contractType: this.value
      }).then(res => {
        window.open(res)
      })
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 45px 24px;
    color: #333;
    .el-select {
      width: 260px
    }
  }
}
</style>
