apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: mycs-cms
  name: mycs-cms
  namespace: ${NAMESPACE}
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: mycs-cms
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: mycs-cms
    spec:
      containers:
        - env:
            - name: GPG_KEYS
              value: B0F4253373F8F6F510D42178520A9993A1C052F8
            - name: CONFIG
              value: "--prefix=/etc/nginx  --sbin-path=/usr/sbin/nginx  --conf-path=/etc/nginx/nginx.conf  --error-log-path=/etc/logs/error.log  --http-log-path=/etc/logs/access.log  --pid-path=/var/run/nginx.pid  --lock-path=/var/run/nginx.lock  --http-client-body-temp-path=/var/cache/nginx/client_temp  --http-proxy-temp-path=/var/cache/nginx/proxy_temp  --http-fastcgi-temp-path=/var/cache/nginx/fastcgi_temp  --http-uwsgi-temp-path=/var/cache/nginx/uwsgi_temp  --http-scgi-temp-path=/var/cache/nginx/scgi_temp  --user=nginx  --group=nginx  --with-http_ssl_module  --with-http_realip_module  --with-http_addition_module  --with-http_sub_module  --with-http_dav_module  --with-http_flv_module  --with-http_mp4_module  --with-http_gunzip_module  --with-http_gzip_static_module  --with-http_random_index_module  --with-http_secure_link_module  --with-http_stub_status_module  --with-http_auth_request_module  --with-mail  --with-mail_ssl_module  --with-file-aio     --with-http_spdy_module  --with-ipv6"
          image: ${ORIGIN_REPO}/${REPO_NAMESPACE}/${REPO}:${IMAGE_TAG}
          imagePullPolicy: Always
          livenessProbe:
               failureThreshold: 3
               initialDelaySeconds: 15
               periodSeconds: 10
               successThreshold: 1
               tcpSocket:
                  port: 80
               timeoutSeconds: 1   
          name: mycs-cms
          ports:
            - containerPort: 443
              protocol: TCP
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: '1'
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 512Mi
          volumeMounts:
            - mountPath: /etc/nginx/conf.d/
              name: volume-cms
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      volumes:
        - configMap:
            defaultMode: 420
            name: testcms2.mycs.cn
          name: volume-cms

          
