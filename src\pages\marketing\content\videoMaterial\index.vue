<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.videoName"
        class="group"
        placeholder="视频名称"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>
    </div>
    <h2>
      视频列表
      <div>
        <VideoUpload @get-list="getVideoList()" />
      </div>
    </h2>
    <div class="table">
      <ul>
        <li v-for="item in list" :key="item.videoFileId">
          <div class="cover">
            <el-image :src="item.coverUrl" />
            <i v-if="!['UploadSucc', 'Transcoding'].includes(item.status)" class="el-icon-video-play" @click="play(item)" />
            <p v-else>转码中...</p>
          </div>
          <div class="desc">
            <p>{{ item.name }}
              <i
                v-if="!['UploadSucc', 'Transcoding'].includes(item.status)"
                class="el-icon-edit"
                @click="function(){
                  editValue = item.name
                  videoInfoId = item.videoInfoId
                  editDialogVisible = true
                }"
              />
            </p>
            <div v-if="!['UploadSucc', 'Transcoding'].includes(item.status)">
              <span>
                时长: {{ item.duration | formatSeconds }}
                <br>
                大小: {{ (item.size / 1024 / 1024).toFixed(2) }}MB
              </span>
              <i class="el-icon-delete" @click="remove(item)" />
            </div>
          </div>
        </li>
      </ul>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <!-- 重命名弹窗 -->
    <el-dialog
      title="重命名"
      :visible.sync="editDialogVisible"
      width="500px"
      center
    >
      <div style="width: 400px">
        <span style="width: 80px">视频名称:</span>
        <el-input v-model="editValue" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="edit()">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 重命名弹窗 -->
    <el-dialog
      title="视频预览"
      :visible.sync="playDialogVisible"
      width="50%"
      center
      :before-close="handlePlayDialogClose"
    >
      <video-play :converted="true" :video-file-id.sync="videoFileId" :width="'900px'" :height="'500px'" :del="false" />
    </el-dialog>
  </section>
</template>

<script>
import { promoteVideoList, promoteVideoEdit, promoteVideoRemove } from '@/api/marketing/videoMaterial'
import VideoUpload from './components/videoUpload'
import Pagination from '@/components/Pagination'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import { formatSeconds } from '@/utils/index'
export default {
  components: {
    VideoUpload,
    Pagination,
    VideoPlay
  },
  filters: {
    formatSeconds
  },
  data() {
    return {
      listQuery: {
        condition: {
          videoName: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      list: [],
      total: 0,
      editDialogVisible: false,
      editValue: '',
      videoInfoId: '',
      playDialogVisible: false,
      videoFileId: ''
    }
  },
  mounted() {
    this.getVideoList()
  },
  methods: {
    getVideoList() {
      promoteVideoList(this.listQuery).then(res => {
        this.list = res.records
        this.total = res.total
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getVideoList()
    },
    search() {
      this.listQuery.pager.page = 1
      this.getVideoList()
    },
    edit() {
      promoteVideoEdit({ name: this.editValue, videoInfoId: this.videoInfoId }).then(() => {
        this.getVideoList()
        this.editDialogVisible = false
      })
    },
    remove(item) {
      const dom = `<div class="message-content" style="word-wrap: break-word">
                  <i class="el-icon-warning"></i> 确定删除 ${item.name} ?</div>`
      this.$confirm(dom, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        promoteVideoRemove(item.videoInfoId).then(() => {
          this.getVideoList()
        })
      })
    },
    play(item) {
      this.playDialogVisible = true
      this.videoFileId = item.videoFileId
    },
    handlePlayDialogClose() {
      this.playDialogVisible = false
      this.videoFileId = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  min-height: 100vh;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    background-color: #f9f9f9;
  }
  .table {
    ul {
      padding: 0;
      display: flex;
      // justify-content: space-between;
      flex-wrap: wrap;
      li {
        width: 23%;
        // height: 320px;
        margin-right: 20px;
        border: 1px solid #efefef;
        margin-bottom: 20px;
        &:hover {
          box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        .cover {
          width: 100%;
          height: 200px;
          .el-image {
            width: 100%;
            height: 100%;
          }
          position: relative;
          cursor: pointer;
          i {
            font-size: 50px;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
          p {
            position: absolute;
            margin: 0;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #eee;
            font-size: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .desc {
          padding: 10px;
          color: #666;
          font-size: 14px;
          i {
            cursor: pointer;
            font-size: 24px;
            color: #f56c6c;
          }
          p {
            margin: 0 0 20px;
            color: #333;
            font-size: 16px;
            word-wrap:break-word;
            i {
              margin-left: 10px;
              color: #409eff;
            }
          }
          div {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
