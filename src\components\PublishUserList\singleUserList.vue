<template>
  <div class="table">
    <h3>{{ (type===1?'待选':'已选') + '用户列表' }}</h3>
    <!-- search -->
    <div class="search-column">
      <!-- <div class="search-column__item">
        <el-select v-model="conditionWatch.queryType" filterable clearable placeholder="用户账号">
          <el-option v-for="item in accountList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div> -->
      <div class="search-column__item">
        <el-input v-model="condition.keyword" class="input-with-select" placeholder="输入查询方式对应关键字" clearable @keyup.enter.native="handleFilter">
          <el-select slot="prepend" v-model="conditionWatch.queryType" filterable clearable placeholder="用户账号" style="width: 110px">
            <el-option v-for="item in accountList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.areaIds"
          placeholder="请选择区域"
          :options="$parent.areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'areaId',
            label:'name',
            children:'childList'
          }"
          clearable
          @change="change($event,'area')"
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.majorIds"
          placeholder="请选择专科"
          :options="$parent.majorList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'majorId',
            label:'name',
            children:'childList'
          }"
          clearable
          @change="change($event,'major')"
        />
      </div>
      <div v-if="msgId" class="search-column__item">
        <el-cascader
          v-model="conditionWatch.academicIds"
          placeholder="请选择职称"
          :options="$parent.academicTreeList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="change($event,'academic')"
        />
      </div>
      <div class="search-column__item">
        <el-select
          v-model="conditionWatch.identityIds"
          multiple
          filterable
          clearable
          placeholder="请选择身份"
          collapse-tags
          @change="change($event,'identity')"
        >
          <el-option v-for="item in $parent.identityList" :key="item.identityId" :label="item.name" :value="item.identityId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select
          v-model="conditionWatch.departmentPackIds"
          multiple
          filterable
          clearable
          placeholder="请选择部门库"
          collapse-tags
          @change="change($event,'department')"
        >
          <el-option v-for="item in $parent.deptList" :key="item.departmentPackId" :label="item.name" :value="item.departmentPackId" />
        </el-select>
      </div>
      <el-button type="primary" @click="getList">刷新</el-button>
      <div v-if="!disabled" class="search-column__item fr">
        <el-button type="primary" @click="batchAction">{{ type===1 ? '添加全部' : "删除全部" }}</el-button>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe max-height="400px" @selection-change="onSelectChange">
      <template slot="actions" slot-scope="{row}">
        <el-button size="mini" type="text" @click="viewPersonal(row)">员工信息</el-button>
        <el-button v-if="!disabled" size="mini" type="text" @click="singleAction(row)">{{ type===1 ? '添加' : '删除' }}</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination :auto-scroll="false" class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <DialogUserInfo title="查看账号信息" :show.sync="dialogInfoVisible" :data="userInfoData" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/userList'
import DialogUserInfo from '@/pages/user/compontents/dialogUserInfo'
import { getPersonalDetail } from '@/api/userManage'

const columns = [
  { props: { label: 'UID', align: 'center', prop: 'userId' }},
  { props: { label: '姓名', align: 'center', prop: 'realName' }},
  { props: { label: '个人账号', align: 'center', prop: 'userName' }},
  { props: { label: '手机', align: 'center', prop: 'phone' }},
  { props: { label: '身份', align: 'center', prop: 'identity' }},
  { props: { label: '专科', align: 'center', prop: 'majorName' }},
  { props: { label: '区域', align: 'center', prop: 'areaName' }},
  { props: { label: '注册时间', align: 'center', prop: 'regTime' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'SingleUserList',
  components: { DialogUserInfo },
  mixins: [table],
  props: {
    type: {
      // 区分已选待选
      type: Number,
      default: 1
    },
    activityId: {
      type: String,
      default: ''
    },
    advertisementId: {
      type: String,
      default: ''
    },
    msgId: {
      type: String,
      default: null
    },
    batchId: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    apiType: {
      type: String,
      default: 'activity'
    }
  },
  data() {
    return {
      columns,
      request: request[this.apiType || 'activity'],
      condition: {
        operateType: this.type,
        selectType: this.type,
        keyword: ''
      },
      conditionWatch: {
        activityId: this.activityId,
        advertisementId: this.advertisementId,
        msgId: this.msgId,
        batchId: this.batchId,
        areaIds: [],
        majorIds: [],
        identityIds: [],
        departmentPackIds: [],
        academicIds: [],
        queryType: ''
      },
      accountList: [
        { value: 1, name: '用户账号' },
        { value: 2, name: '手机' },
        { value: 3, name: '用户姓名' },
        { value: 4, name: '员工账号' },
        { value: 5, name: '员工姓名' },
        { value: 6, name: '单位名称' }
      ],
      deptList: [],
      majorList: [],
      editPath: 'addActivity',
      mainKey: 'activityId',
      adStateList: [
        { value: 0, name: '未开始' },
        { value: 1, name: '进行中' },
        { value: 2, name: '已结束' }
      ],
      initList: false, // 是否初始化调用
      userInfoData: {}, // 员工信息
      dialogInfoVisible: false // 员工信息弹窗
    }
  },
  watch: {
    activityId(v) {
      this.conditionWatch.activityId = v
    },
    advertisementId(v) {
      this.conditionWatch.advertisementId = v
    },
    msgId: {
      handler(v) {
        if (v === null) return
        this.conditionWatch.msgId = v
        this.conditionWatch.queryType = 'userName'
        this.columns = [
          { props: { label: 'UID', align: 'center', prop: 'uid' }},
          { props: { label: '姓名', align: 'center', prop: 'realName' }},
          { props: { label: '个人账号', align: 'center', prop: 'userName' }},
          { props: { label: '手机', align: 'center', prop: 'phone' }},
          { props: { label: '身份', align: 'center', prop: 'identityName' }},
          { props: { label: '专科', align: 'center', prop: 'majorName' }},
          { props: { label: '职称', align: 'center', prop: 'academicName' }},
          { props: { label: '区域', align: 'center', prop: 'areaName' }},
          { props: { label: '注册时间', align: 'center', prop: 'regTime' }},
          {
            props: { align: 'center', label: '操作', width: '130' },
            slot: 'actions'
          }
        ]
        this.accountList = [
          { value: 'userName', name: '用户账号' },
          { value: 'phone', name: '手机' },
          { value: 'realName', name: '用户姓名' },
          { value: 'staffAccount', name: '员工账号' },
          { value: 'staffName', name: '员工姓名' }
        ]
      },
      immediate: true
    },
    batchId: {
      handler(v) {
        if (v === null) return
        this.conditionWatch.batchId = v
        this.columns = [
          { props: { label: 'UID', align: 'center', prop: 'userId' }},
          { props: { label: '姓名', align: 'center', prop: 'realName' }},
          { props: { label: '个人账号', align: 'center', prop: 'userName' }},
          { props: { label: '手机', align: 'center', prop: 'phone' }},
          { props: { label: '身份', align: 'center', prop: 'identityName' }},
          { props: { label: '专科', align: 'center', prop: 'majorName' }},
          { props: { label: '职称', align: 'center', prop: 'academicName' }},
          { props: { label: '单位', align: 'center', prop: 'orgName' }},
          { props: { label: '区域', align: 'center', prop: 'areaName' }},
          { props: { label: '注册时间', align: 'center', prop: 'regTime' }},
          {
            props: { align: 'center', label: '操作', width: '130' },
            slot: 'actions'
          }
        ]
      },
      immediate: true
    },
    apiType(v) {
      this.request = request[v || 'activity']
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // view personal detail
    viewPersonal(row) {
      getPersonalDetail({ userId: this.msgId ? row.uid : row.userId }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
      })
    },
    getListData() {
      const param = {
        ...this.conditionWatch,
        queryWord: this.condition.keyword,
        areaIds: this.flatten(this.conditionWatch.areaIds),
        majorIds: this.flatten(this.conditionWatch.majorIds)
      }
      if (this.msgId) {
        param[this.conditionWatch.queryType] = this.condition.keyword
      }
      const data = {
        condition: Object.assign(param, this.conditionCache),
        pager: this.pager
      }
      return data
    },
    // single action for user
    singleAction(row) {
      const param = {
        operateType: this.type,
        userId: this.msgId ? row.uid : row.userId
      }
      if (this.apiType === 'activity') {
        param.activityId = this.activityId
      } else if (this.apiType === 'ad') {
        param.advertisementId = this.advertisementId
      } else if (this.apiType === 'message') {
        param.msgId = this.msgId
      } else {
        param.batchId = this.batchId
      }
      this.request.singleAction(param).then(res => {
        this.$emit('change')
        this.$message.success('操作成功')
      })
    },
    // batch action for user
    batchAction() {
      this.$confirm(`是否确定${this.type === 1 ? '添加全部' : '删除全部'}`, '确认信息', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const param = {
          ...this.conditionWatch,
          operateType: this.type,
          queryWord: this.condition.keyword,
          selectType: this.type,
          areaIds: this.flatten(this.conditionWatch.areaIds),
          majorIds: this.flatten(this.conditionWatch.majorIds)
        }
        if (this.msgId) {
          param[this.conditionWatch.queryType] = this.condition.keyword
        }
        this.request.batchAction(param).then(res => {
          setTimeout(() => {
            this.$emit('change')
          }, 600)
          this.$message.success('批量操作成功')
        })
      })
    },
    flatten(arr) {
      return arr.reduce((a, b) => a.concat(Array.isArray(b) ? this.flatten(b) : b), [])
    },
    change(v, property) {
      if (this.type === 1) {
        // 待选用户列表
        if (['major', 'area', 'academic'].indexOf(property) !== -1) {
          v = this.flatten(v)
        }
        if (!v.length) {
          const Index = this.$parent.scopeConditions.findIndex(sv => sv.property === property)
          this.$parent.delSC(Index)
        } else {
          this.$emit('filter', { val: v.join(), property, necessary: 1 })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table {
  width: 80%;
  margin: 70px auto 50px;
  .search-column__item {
    margin-left: 6px;
    margin-bottom: 10px;
    ::v-deep .el-input_inner {
      width: 180px;
    }
  }
}
</style>
