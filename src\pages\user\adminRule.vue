<template>
  <div class="app-container">
    <div class="content-container">
      <el-row class="search-column">
        <div class="search-column__item">
          <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable>
            <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="searchSelectChange()">
              <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-input>
        </div>
        <div class="search-column__item">
          <el-select v-model="condition.signStatus" placeholder="请选择签署状态" clearable>
            <el-option
              v-for="item in signOpt"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item">
          <OrgTree :show="true" :org-id="orgId" @input="setOrgIdSet" />
        </div>
        <div class="search-column__item">
          <el-button type="primary" @click="getList">查询</el-button>
        </div>
      </el-row>
      <el-row class="mt-15">
        <el-table v-loading="loading" :data="tableData" tooltip-effect="dark" :header-cell-style="{background:'#ECF0F1'}">
          <el-table-column label="员工姓名" align="center" prop="realName" />
          <el-table-column label="员工账号" align="center" prop="username" />
          <el-table-column label="所在单位" align="center" prop="orgName" />
          <el-table-column label="单位类型" align="center" prop="orgTypeName" />
          <el-table-column label="所属区域" align="center" prop="areaName" />
          <el-table-column label="签署状态" align="center" prop="signStatus">
            <template slot-scope="{ row }">
              <span>{{ row.signStatus | signStatusOpt }}</span>
            </template>
          </el-table-column>
          <el-table-column label="本人人脸" align="center" prop="faceImg">
            <template slot-scope="{ row }">
              <el-image style="width: 100px; height: 100px" :src="row.faceImg" fit="cover">
                <div slot="error" class="image_error">未认证</div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column label="认证人脸" align="center" prop="contrastFaceImg">
            <template slot-scope="{ row }">
              <el-image style="width: 100px; height: 100px" :src="row.contrastFaceImg" fit="cover">
                <div slot="error" class="image_error">未认证</div>
              </el-image>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="contrastResult">
            <template slot="header">
              <el-tooltip class="item" effect="dark" placement="top">
                <div slot="content">
                  <span>匹配：认证人脸与本人人脸对比符合；</span><br>
                  <span>不匹配：认证人脸与本人人脸对比不符合；</span><br>
                  <span>特殊情况：首次人脸识别只采集本人人脸数据，不做人脸认证，认证人脸为空，对比结果也为“匹配”。</span>
                </div>
                <span>认证结果 <i class="el-icon-question" /></span>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <el-tag :type="row.contrastResult|resultType" size="medium">{{ row.contrastResult | resultStatus }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="签署时间" align="center" prop="recordTime" />
          <el-table-column label="创建时间" align="center" prop="createTime" />
        </el-table>
      </el-row>
    </div>
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="pagination" />
  </div>
</template>

<script>
import { getOrgID, getAdminRuleList } from '@/api/adminRule'
import Pagination from '@/components/Pagination'
import OrgTree from './compontents/orgTree'

const resultStatusOpt = [
  { type: 'warning', name: '未匹配' },
  { type: '', name: '匹配' },
  { type: 'danger', name: '不匹配' }
]

export default {
  name: 'AdminRule',
  components: { Pagination, OrgTree },
  filters: {
    signStatusOpt(val) {
      const arr = ['未签署', '已签署']
      return arr[val]
    },
    resultStatus(val) {
      return resultStatusOpt[val].name
    },
    resultType(val) {
      return resultStatusOpt[val].type
    }
  },
  data() {
    return {
      keyword: '',
      searchType: 'realName',
      searchTypeList: [
        { label: '员工姓名', value: 'realName' },
        { label: '员工账号', value: 'username' },
        { label: '单位名称', value: 'orgName' }
      ],
      condition: {
        realName: '',
        username: '',
        orgName: '',
        signStatus: '',
        selectOrgIds: []
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      total: 0,
      orgId: '', // 用于查询单位
      tableData: [],
      signOpt: [
        { value: 0, name: '未签署' },
        { value: 1, name: '已签署' }
      ],
      loading: false,
      layout: 'total, prev, pager, next, jumper' // 默认分页样式
    }
  },
  created() {
    getOrgID().then(res => {
      this.orgId = res
    })
    this.getList()
  },
  methods: {
    searchSelectChange(val) {
      this.condition.realName = ''
      this.condition.username = ''
      this.condition.orgName = ''
      this.condition[this.searchType] = this.keyword.replace(/\s*/g, '')
      this.pager.page = 1
    },
    getList() {
      this.pager.page = 1
      this.loading = true
      this.searchSelectChange()
      const query = {}
      query.condition = this.condition
      query.pager = this.pager
      getAdminRuleList(query).then(res => {
        this.tableData = res.records
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    setOrgIdSet(arr) {
      this.condition.selectOrgIds = arr
    },
    pagination(pager) {
      this.pager = pager

      this.loading = true
      const query = {}
      query.condition = this.condition
      query.pager = this.pager
      getAdminRuleList(query).then(res => {
        this.tableData = res.records
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    }

  }
}
</script>

<style lang="scss" >
.image_error{
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  color: #c0c4cc;
  background-color: #f5f7fa;

}
</style>
