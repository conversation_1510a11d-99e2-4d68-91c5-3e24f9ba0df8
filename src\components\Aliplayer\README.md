# 名医传世-重构版(前后分离-VUE)
> 版本： 1.0.0

## 一、项目介绍
> 本次项目为组件的说明文档，内容包括组件的用途以及组件的说明。<br/>

## 二、组件的说明及使用
### 2.1 组件的说明
> 该组件以aliplayer为基础封装，便于vue使用的

### 2.2 组件的使用
vue文件

```vue
<template>
  <ali-player
      ref="aliPlayer"
      :play-style="aliPlayerConfig.playStyle"
      :source="aliPlayerConfig.source"
      :cover="aliPlayerConfig.cover"
      :height="aliPlayerConfig.height"
      :skin-layout="aliPlayerConfig.skinLayout"
      @ready="handleReadyVideo"
      @play="handlePlayVideo"
      @pause="handlePauseVideo"
      @ended="handleEnded"
  />
</template>
<script>
  import AliPlayer from '@/components/Aliplayer/index'
  // 个性化配置，可不设置，不设置则使用默认配置
  import initPlayerConfig from './index'
  export default {
    components: { AliPlayer },
    data() {
      aliPlayerConfig: {}
    },
    created() {
      this.aliPlayerConfig = initPlayerConfig
    },
    methods: {
      handleReadyVideo() {
        // todo
      },
      handlePlayVideo() {
        // todo
      },
      handlePauseVideo() {
        // todo
      },
      handleEnded() {
        // todo
      },
    } 
  }
</script>
```
initPlayerConfig文件

```js
export const initPlayerConfig = {
    playStyle: 'background: "#191919"',
    source: detail.m3u8Url,
    cover: detail.thumb,
    height: '640px'
}
```
