<template>
  <section class="task">
    <div class="task-header">
      <el-input
        v-model="listQuery.condition.name"
        placeholder="请输入会议名称"
        clearable
        @change="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.orgId" placeholder="请选择单位" clearable @change="handleFilter()">
        <el-option
          v-for="item in orgList"
          :key="item.orgId"
          :label="item.orgName"
          :value="item.orgId"
        />
      </el-select>
    </div>
    <div class="task-main">
      <h2>
        会议素材
        <div>
          <el-button type="primary" @click="exportList()">导出</el-button>
          <el-button type="primary" @click="toDetail()">创建</el-button>
        </div>
      </h2>
      <div class="table">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.width"
          >
            <template slot-scope="{row}">
              <el-tooltip
                v-if="item.prop === 'description' && row[item.prop].length > 30"
                class="item"
                popper-class="desc-tool-tip"
                effect="dark"
                :content="row[item.prop]"
                placement="top-start"
              >
                <span>{{ row[item.prop].slice(0, 30) }}</span>
              </el-tooltip>
              <sapn v-else>{{ row[item.prop] }}</sapn>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="toDetail(scope.row.id,'edit')">编辑</el-button>
              <el-button type="text" size="mini" @click="deleteTask(scope.row.id)">删除</el-button>
              <el-button type="text" size="mini" @click="toDetail(scope.row.id,'read')">查看</el-button>
              <el-button type="text" size="mini" @click="copy(scope.row.link)">复制链接</el-button>
              <el-button type="text" size="mini" @click="download(scope.row, 'video')">下载视频</el-button>
              <el-button type="text" size="mini" @click="download(scope.row, 'img')">下载图片</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    </div>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getUnitList } from '@/api/userManage'
import { meetingRecordList, meetingRecordExport, meetingRecordDel } from '@/api/marketing/contentManage'
import { getVideoPreview } from '@/api/biz'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          name: '',
          orgId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      orgList: [],
      tableColumn: [
        { prop: 'name', label: '会议名称', width: '100' },
        { prop: 'peopleNum', label: '会议人数', width: '50' },
        { prop: 'description', label: '会议说明', width: '100' },
        { prop: 'meetingTimeStr', label: '会议时间', width: '100' },
        { prop: 'link', label: '会议链接', width: '60' },
        { prop: 'ownerOrgName', label: '所属单位', width: '60' },
        { prop: 'createBy', label: '创建人', width: '60' },
        { prop: 'createTime', label: '创建时间', width: '100' },
        { prop: 'updateBy', label: '最后修改人', width: '60' },
        { prop: 'updateTime', label: '最后修改时间', width: '100' }
      ],
      tableData: []
    }
  },
  created() {
    this.getList()
    this.getOrg()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    getList() {
      meetingRecordList(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002,
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    exportList() {
      meetingRecordExport(this.listQuery.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    toDetail(id, type) {
      this.$router.push(
        {
          name: 'MarketingConferenceDetail',
          query: {
            id,
            type
          }
        }
      )
    },
    deleteTask(id) {
      this.$confirm('确认删除该会议素材?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        meetingRecordDel({ id }).then(() => {
          this.getList()
        })
      })
    },
    copy(link) {
      navigator.clipboard.writeText(link).then(() => {
        this.$message.success('复制成功')
      })
    },
    download(row, type) {
      const down = (url) => {
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
      if (type === 'video') {
        getVideoPreview({ videoFileId: row.videoFileId }).then(res => {
          down(res)
        })
      } else {
        down(`${process.env.VUE_APP_BASE_API}/meetingRecord/downloadPic/${row.id}`)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.task {
  min-height: calc(100vh - 50px);
  height: calc(100% - 50px);
  padding: 20px;
  background-color: #eaeaee;
  &-header {
    .el-input {
      width: 200px;
    }
  }
  &-main {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      background: #f9f9f9;
    }
    .table {
      padding: 16px 16px 0px;
    }
    ::v-deep .pagination-container {
      margin: 0 auto;
    }
  }
}
</style>
