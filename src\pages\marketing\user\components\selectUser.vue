<template>
  <el-dialog
    title="选择用户"
    :visible.sync="dialogVisible"
    width="600px"
    center
    :before-close="handleClose"
  >
    <el-input
      v-model="keyword"
      placeholder="手机搜索"
      class="input-with-select"
      @change="getList()"
    >
      <el-button slot="append" icon="el-icon-search" @click="getList()" />
    </el-input>
    <el-table
      :data="tableData"
      style="width: 100%"
      height="300"
    >
      <el-table-column
        prop="uid"
        label="UID"
        align="center"
      />
      <el-table-column
        prop="realName"
        label="姓名"
        align="center"
      />
      <el-table-column
        prop="phone"
        label="手机"
        align="center"
      />
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="userId === ''"
            size="mini"
            type="primary"
            @click="checked(scope.row.uid)"
          >选择</el-button>
          <el-button
            v-if="userId !== ''"
            size="mini"
            type="danger"
            @click="checked(scope.row.uid)"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="handleClose('confirm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { listUserForSelect } from '@/api/marketing/promoteArticle'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    searchType: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      userId: this.id,
      keyword: '',
      tableData: []
    }
  },
  methods: {
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      if (e) {
        this.$emit('update:id', this.userId)
      } else {
        this.userId = ''
      }
    },
    getList() {
      if (this.keyword === '') return this.$message.error('查询条件不能为空')
      listUserForSelect({
        phone: this.keyword,
        // 用于精确筛选搜索查询的手机号有无注册为创作者
        searchType: this.searchType,
        exactQuery: 1
      }).then(res => {
        this.tableData = res
      })
    },
    checked(uid) {
      if (this.userId === '') {
        this.userId = uid
      } else {
        this.userId = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .input-with-select {
    width: 300px;
  }
  .el-dialog__body {
    padding-bottom: 10px;
  }
}
</style>
