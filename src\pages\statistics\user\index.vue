<template>
  <section class="user">
    <div class="head">
      <el-cascader
        ref="cascader"
        v-model="queryList.areaId"
        placeholder="请选择区域"
        :options="areaList"
        collapse-tags
        :show-all-levels="false"
        :props="{
          value:'areaId',
          label:'name',
          children:'childList',
          checkStrictly:true,
          emitPath:false
        }"
        @change="handlerChange"
      />
      <div>
        <!-- <el-button type="primary">查询</el-button> -->
        <el-button @click="reset">重置</el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{background:'#f9f9f9',color:'#333'}"
      :row-key="row => { return row.majorId + row.parentMajorId }"
      lazy
      :load="load"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column
        type="index"
        width="80"
        label="序号"
        align="center"
      />
      <el-table-column
        prop="name"
        label="专科"
        width="580"
      />
      <el-table-column
        prop="count"
        label="用户数"
        width="480"
      />
      <el-table-column label="操作">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="detail(row)">人员详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </section>
</template>

<script>
import { getAreaTree } from '@/api/area' // 区域树
import { userAnalysis } from '@/api/statistics'
export default {
  data() {
    return {
      queryList: {
        areaId: '0',
        areaLevel: 4,
        majorId: '',
        majorLevel: null
      },
      areaList: [], // 区域
      tableData: []
    }
  },
  watch: {
    // 关闭级联下拉框
    tableData() {
      if (this.$refs.cascader) {
        this.$refs.cascader.dropDownVisible = false
      }
    }
  },
  created() {
    if (this.$route.query.areaId) {
      this.queryList.areaId = this.$route.query.areaId
    }
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      const newArr = res
      newArr.unshift({ name: '全国', areaId: '0', level: 4 })
      this.areaList = newArr
    })
    this.getList()
  },
  methods: {
    reset() {
      this.queryList = {
        areaId: '0',
        areaLevel: 4,
        majorId: '',
        majorLevel: null
      }
      this.getList()
    },
    handlerChange() {
      this.queryList.majorId = ''
      this.queryList.majorLevel = null
      this.queryList.areaLevel = this.$refs.cascader.getCheckedNodes()[0].data.level
      this.getList()
    },
    getList() {
      userAnalysis(this.queryList).then(res => {
        this.tableData = res
      })
    },
    load(tree, treeNode, resolve) {
      this.queryList.majorId = tree.majorId
      this.queryList.majorLevel = tree.level
      let arr = []
      userAnalysis(this.queryList).then(res => {
        arr = res
      })
      setTimeout(() => {
        resolve(arr)
      }, 1000)
    },
    detail(row) {
      console.log(row)
      this.$router.push({
        name: 'StatisticsUserDetail',
        query: {
          areaId: this.queryList.areaId,
          areaLevel: this.queryList.areaLevel,
          majorId: row.majorId,
          majorLevel: row.level,
          parentMajorId: row.parentMajorId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px 40px;
  .head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
}
</style>
