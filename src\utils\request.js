import axios from 'axios'
import { MessageBox, Message, Loading } from 'element-ui'
import store from '@/store'

let loading

// cancel repeat request
// const pending = []
// const CancelToken = axios.CancelToken
// const removePending = (req) => {
//   for (const p in pending) {
//     if (pending[p].u === req.url + '&' + req.method) {
//       pending[p].f()
//       pending.splice(p, 1)
//     }
//   }
// }

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      config.headers['token'] = store.getters.token
    }

    // upload don't need token, params come from api
    if (config.removeToken) {
      delete config.headers['token']
    }

    loading = Loading.service({
      lock: true,
      text: '加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0)'
    })

    // removePending(h5)
    // h5.cancelToken = new CancelToken((c) => {
    //   pending.push({ u: h5.url + '&' + h5.method, f: c })
    // })

    return config
  },
  error => {
    loading.close()
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data
    loading.close()
    // if the custom code is not 1, it is judged as an error.
    if (res.code !== 1) {
      switch (res.code) {
        case 456:
          store.dispatch('user/resetToken')
          MessageBox.confirm('您的登录信息过期，请重新登录', '温馨提示', {
            confirmButtonText: '确认',
            showClose: false,
            lockScroll: true,
            type: 'warning'
          }).then(() => {
            store.dispatch('user/resetToken').then(() => {
              location.reload()
            })
          })
          break
        case 457:
          store.dispatch('user/resetToken')
          MessageBox.confirm('您的账户已经在别的地方登录，请重新登录', '温馨提示', {
            confirmButtonText: '确认',
            showClose: false,
            lockScroll: true,
            type: 'warning'
          }).then(() => {
            store.dispatch('user/resetToken').then(() => {
              location.reload()
            })
          })
          break
        // 专家保存时，存在专家报错处理
        case 1001:
          Message({
            message: '添加失败：选中用户已经是专家，不可重复添加',
            type: 'error',
            duration: 5 * 1000
          })
          break
        default:
          console.log(res.message)
          Message({
            message: res.message || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
      }

      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res.data
    }
  },
  error => {
    loading.close()
    console.log('err' + error) // for debug
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
