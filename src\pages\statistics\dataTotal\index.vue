<template>
  <section class="dataTotal">
    <bs-head />
    <div class="content">
      <div class="content-left">
        <bs-total :key="totalData.total" :total-data="totalData" />
        <bs-specialist :data="majorData" />
        <bs-tendency :data="tendData" />
      </div>
      <div class="content-center">
        <bs-duration :key="durationData" :end-val="durationData * 3.6" />
        <bs-map :data="mapData" />
        <bs-rich-data :data="richData" />
      </div>
      <div class="content-right">
        <bs-rank :data="rankData" />
        <bs-state :data="uvListData" />
      </div>
    </div>
  </section>
</template>

<script>
import BsHead from './components/head.vue'
import BsTotal from './components/total.vue'
import BsSpecialist from './components/specialist.vue'
import BsTendency from './components/tendency.vue'
import BsDuration from './components/duration.vue'
import BsMap from './components/map.vue'
import BsRichData from './components/richData.vue'
import BsRank from './components/rank.vue'
import BsState from './components/state.vue'
import { uvOverview, uvTrend, majorDistribution, mapDistribution, pastTime, getStudyTrends, uvList, getRichData } from '@/api/statistics'
import moment from 'moment'
export default {
  components: {
    BsHead,
    BsTotal,
    BsSpecialist,
    BsTendency,
    BsDuration,
    BsMap,
    BsRichData,
    BsRank,
    BsState
  },
  data() {
    return {
      totalData: {},
      majorData: {},
      tendData: {},
      rankData: [],
      durationData: '',
      mapData: [],
      richData: [],
      uvListData: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      uvOverview().then(res => {
        this.totalData = {
          total: parseInt(res.staffNum),
          day: parseInt(res.todayActiveNum),
          oldDay: parseInt(res.yesterdayActiveNum)
        }
      })
      majorDistribution().then(res => {
        this.majorData = {
          legendData: res.map(v => v.name),
          deptData: res.map(v => ({
            value: v.count,
            name: v.name
          }))
        }
      })
      uvTrend({
        condition: {
          startTime: moment().subtract(29, 'days').format('YYYYMMDD'),
          endTime: moment().startOf('days').format('YYYYMMDD'),
          timeType: 3
        },
        pager: {
          page: 1,
          pageSize: 50
        }
      }).then(res => {
        res.records = res.records.reverse()
        this.tendData = {
          date: [],
          count: [],
          ratio: []
        }
        res.records.forEach(v => {
          this.tendData.date.push(v.ds)
          this.tendData.count.push(v.num)
          this.tendData.ratio.push(parseFloat(v.growRate))
        })
      })
      pastTime().then(res => {
        this.durationData = res
      })
      mapDistribution().then(res => {
        res.forEach(v => {
          v.name = v.name === '澳  门' ? v.name = '澳门' : v.name
          v.name = v.name === '香  港' ? v.name = '香港' : v.name
          v.name = v.name.split('省').join('')
          v.name = v.name.split('市').join('')
        })
        this.mapData = res.map(v => {
          return {
            areaId: v.areaId,
            name: v.name,
            value: v.count
          }
        })
      })
      getStudyTrends().then(res => {
        this.rankData = res
      })
      uvList().then(res => {
        res.forEach(item => {
          // 去除日期
          item.createTime = item.createTime.substr(11)
        })
        this.uvListData = res
      })
      getRichData().then(res => {
        this.richData = [
          {
            name: '培训人次',
            basics: (res.peopleNum / res.coverRate) * 100, // 基数(用于圆圈百分比绘制)
            count: parseFloat(res.peopleNum)
          },
          {
            name: '参与人次',
            basics: (res.joinNum / res.joinRate) * 100,
            count: parseFloat(res.joinNum)
          },
          {
            name: '通过人次',
            basics: (res.passNum / res.passRate) * 100,
            count: parseFloat(res.passNum)
          },
          {
            name: '培训覆盖率',
            basics: 100,
            count: parseFloat(res.coverRate)
          },
          {
            name: '培训参与率',
            basics: 100,
            count: parseFloat(res.joinRate)
          },
          {
            name: '培训通过率',
            basics: 100,
            count: parseFloat(res.passRate)
          }
        ]
        const timer = setInterval(() => {
          clearInterval(timer)
          this.init()
        }, 1000 * 60 * res.refreshTime)
        this.$once('hook:beforeDestroy', () => {
          clearInterval(timer)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dataTotal {
    padding: 0 24px;
    width: 100%;
    height: 1080px;
    background: url(~@/assets/images/bs/big-sceen-bg.png) repeat top center;
    background-size: 100% 100%;
    color: #fff;
    font-family: Microsoft YaHei;
    .content {
        display: flex;
        height: 980px;
        &-left {
          width: 500px
        }
        &-center {
          margin: 0 90px;
          width: 675px
        }
        &-right{
          width:500px
        }
    }
}
</style>
