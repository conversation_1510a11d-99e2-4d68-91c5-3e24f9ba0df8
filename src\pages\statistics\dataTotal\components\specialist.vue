<template>
  <div class="specialist" style="cursor: pointer" @click="skip">
    <h2>用户分布</h2>
    <div ref="chart" class="chart" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  props: {
    data: {
      type: [Array, Object],
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(v) {
        this.myChart.dispose()
        this.myChart = echarts.init(this.$refs.chart)
        this.setOptions(v)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(this.$refs.chart)
      this.setOptions(this.data)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    })
  },
  methods: {
    skip() {
      this.$router.push({
        name: 'StatisticsUser'
      })
    },
    setOptions(data) {
      this.myChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        color: ['#ff701a', '#ffa11a', '#ffea00', '#78ff00', '#00e436', '#3fffea', '#00a8ff', '#1d67e0', '#944efe', '#fb5e60', '#ef2a2d'],
        legend: {
          orient: 'vertical',
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 12,
          textStyle: {
            padding: [0, 0, 0, 4],
            fontSize: 16,
            color: '#fff'
          },
          right: '20%',
          top: 0,
          bottom: 0,
          data: data.legendData
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['20%', '50%'], // 设置居中位置
            radius: ['50%', '80%'],
            data: data.deptData,
            label: { show: false }
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.myChart) {
        return
      }
      this.myChart.dispose()
      this.myChart = null
    }
  }
}
</script>

<style lang="scss" scoped>
.specialist {
  margin: 50px 0;
  h2 {
    font-size: 24px
  }
  .chart {
    width: 600px;
    height: 210px;
  }
}
</style>
