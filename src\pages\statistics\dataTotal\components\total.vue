<template>
  <div class="total">
    <TotalPart class="user" :title="'总用户数'" :end-val="totalData.total" style="cursor: pointer" @click.native="skip()" />
    <div class="period">
      <TotalPart :title="'今日活跃'" :end-val="totalData.day" style="cursor: pointer" @click.native="skip('day')" />
      <TotalPart :title="'昨日活跃'" :end-val="totalData.oldDay" style="cursor: pointer" @click.native="skip('oldDay')" />
    </div>
  </div>
</template>

<script>
import TotalPart from './totalPart.vue'
export default {
  components: { TotalPart },
  props: {
    totalData: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    splitToDigit(n) {
      return [...n + ''].map(Number)
    },
    skip(timeType) {
      if (timeType) {
        this.$router.push({
          name: 'StatisticsActive',
          query: {
            timeType
          }
        })
      } else {
        this.$router.push({
          name: 'StatisticsUser'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.total {
  padding-bottom: 20px;
  p {
    margin: 24px 0;
    font-size: 24px;
  }
  ::v-deep .user {
    span {
      margin-right: 14px;
      width: 47px;
      height: 62px;
      border-width: 2px;
      line-height: 62px;
      font-size: 40px;
    }
  }
  .period {
    display: flex;
    margin-top: 10px;
  }
}
</style>
