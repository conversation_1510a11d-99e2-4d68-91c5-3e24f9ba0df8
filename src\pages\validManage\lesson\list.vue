<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="5">
        <div class="content-left">
          <div class="tree-title">
            <span>资源分类</span>
            <div v-if="showSelect">
              <el-button type="text" @click="cancelSelect">取消选择</el-button>
            </div>
          </div>
          <el-scrollbar wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container">
            <Tree
              ref="tree"
              :type="categoryType"
              :platform-type="platformType"
              :highlight-current="highlightCurrent"
              @nodeAddPlatForm="handleNodeAddPlatForm"
              @nodeClick="handleTreeNodeClick"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <div class="search-column">
          <div class="search-column__item">
            <el-select v-model="condition.type" filterable clearable>
              <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-input v-model="condition.keyword" placeholder="根据左侧查询方式对应关键字" clearable style="width: 240px" @change="init">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
            </el-input>
          </div>
          <div class="search-column__item">
            <el-select v-model="condition.memberLevel" placeholder="会员等级" clearable @change="init">
              <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
            </el-select>
          </div>
          <div class="fr">
            <div class="search-column__item">
              <el-button v-if="selectedList.length" type="primary" @click="batchSetMemberLevel">批量设置会员等级</el-button>
              <el-button :disabled="!selectedList.length" type="primary" @click="del">删除</el-button>
              <el-button :disabled="!selectedList.length" type="primary" @click="setFake">设置虚拟量</el-button>
              <el-button v-if="havChild" type="primary" @click="addCoursePack">增加课程</el-button>
              <el-button :disabled="!selectedList.length" type="primary" @click="editCategory({})">修改分类</el-button>
              <el-button type="primary" @click="add">新增</el-button>
            </div>
          </div>
        </div>

        <a-table :columns="columns" :data="list" border stripe @selection-change="onSelectChange">
          <template slot="cateName" slot-scope="{row}">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ row.cateName }}
              </div>
              <span>{{ row.cateName | filterFont }}</span>
            </el-tooltip>
          </template>
          <template slot="cover" slot-scope="{row}">
            <img style="width:90px;display:block;" :src="row.coverImg" @click="handlePreview(row.coverImg)">
          </template>
          <template slot="actions" slot-scope="{row}">
            <el-button size="mini" type="text" @click="edit(row)">编辑</el-button>
            <el-button size="mini" type="text" @click="del(row)">删除</el-button>
            <el-button size="mini" type="text" @click="setMemberLevel(row)">设置会员等级</el-button>
            <el-button size="mini" type="text" @click="setFake(row)">设置虚拟量</el-button>
            <el-button size="mini" type="text" @click="editCategory(row)">修改分类</el-button>
            <el-button v-if="havChild" size="mini" type="text" @click="setSort(row)">排序</el-button>
          </template>
        </a-table>
      </el-col>
    </el-row>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

    <el-dialog title="设置虚拟量" width="500px" :visible.sync="dialogFormShow">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="虚拟浏览量" prop="baseHits">
          <el-input-number v-model="form.baseHits" :min="0" @change="onNumChange($event,'baseHits')" />
        </el-form-item>
        <el-form-item label="虚拟点赞量" prop="basePraises">
          <el-input-number v-model="form.basePraises" :min="0" @change="onNumChange($event,'basePraises')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="增加课程" width="800px" :visible.sync="addCoursePackVisible" :close-on-click-modal="false" :destroy-on-close="true" @close="addCoursePackCancel">
      <div class="search-column">
        <div class="search-column__item">
          <div class="search-column__label">课程名称：</div>
          <div class="search-column__inner">
            <el-input v-model="addCoursePackTableQuery.condition.keyword" placeholder="请输入课程名称" @change="search" @keydown="search" />
          </div>
        </div>
      </div>
      <div v-if="noQuery" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
        <span>请输入课程名称查询</span>
      </div>
      <div v-else>
        <el-table ref="singleTable" :data="addCoursePackTableData" border>
          <el-table-column v-for="col in addCoursePackTableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.filter === 'cateName'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop]|filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else>
                <span>
                  {{ scope.row[col.prop] }}
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="100">
            <template slot-scope="{row}">
              <el-button v-if="coursePackIdList.includes(row.coursePackId)" size="mini" type="text" @click="handleCloseSelect(row)">已选择</el-button>
              <el-button v-else size="mini" type="text" @click="handleSelect(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- pagination -->
        <Pagination class="text-center" :total="addCoursePackTotal" :page="addCoursePackTableQuery.pager.page" @pagination="handleAddCoursePackPagination" />

        <span slot="footer" class="dialog-footer">
          <el-button @click="addCoursePackCancel">取 消</el-button>
          <el-button type="primary" @click="addCoursePackConfirm">确 定</el-button>
        </span>
      </div>
    </el-dialog>

    <el-dialog title="修改分类" width="600px" :visible.sync="editCategoryVisible" :destroy-on-close="true">
      <el-form ref="categoryForm" :model="categoryForm" label-width="120px">
        <el-form-item v-if="showCheckCategory" label="已选分类：" style="line-height: 25px;">
          <div v-for="item in categoryNameArr" :key="item+1" style="line-height: 20px !important;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 400px;">
                {{ item }}{{ item?'；': '' }}
              </div>
              <span>{{ item | filterFont }}{{ item?'；': '' }}</span>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="资源分类：" prop="sort">
          <el-cascader
            ref="elcascader"
            v-model="categoryForm.cateIdList"
            placeholder="选择分类"
            :options="platformTreeList"
            :props="platformProps"
            :show-all-levels="true"
            clearable
            filterable
            collapse-tags
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editCategoryCancel">取 消</el-button>
        <el-button type="primary" @click="editCategoryConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改排序值" width="500px" :visible.sync="editSortVisible" :destroy-on-close="true">
      <el-form ref="sortForm" :model="sortForm" label-width="120px">
        <el-form-item label="排序值" prop="sort">
          <el-input-number v-model="sortForm.sort" :min="0" :max="99999" @change="onNumChange($event,'sort')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sortCancel">取 消</el-button>
        <el-button type="primary" @click="sortConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="isBatchSetMemberLevel ? '批量设置会员等级' : '设置会员等级'" width="500px" :visible.sync="memberLevelDialogVisible" :destroy-on-close="true">
      <el-form ref="memberLevelForm" :model="memberLevelForm" label-width="120px">
        <el-form-item label="会员等级" prop="memberLevel" :rules="[{ required: true, message: '请选择会员等级', trigger: 'change' }]">
          <el-select v-model="memberLevelForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isBatchSetMemberLevel && selectedList.length >= pager.pageSize" label="">
          <el-checkbox v-model="memberLevelForm.crossPage" label="跨页批量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="memberLevelCancel">取 消</el-button>
        <el-button type="primary" @click="memberLevelConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <Pagination :total="total" :page="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import {
  addCoursePack,
  setCoursePackSort
} from '@/api/validManage'
import ATable from '@/components/ATable'
import Pagination from '@/components/Pagination'
import { courseList, delCourse, stick, setDummy, setCoursePackMemberLevel } from '@/api/course'
import { getCategoryTreeList } from '@/api/category'
import { getVipLevelList } from '@/api/vip'
import Tree from '../components/platformTree.vue'

export default {
  name: 'LessonList',
  components: { ATable, Pagination, Tree },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  data() {
    return {
      condition: {
        type: '1',
        keyword: '',
        cateId: '',
        memberLevel: ''
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      typeList: Object.freeze([
        { value: '1', name: '课程名称' },
        { value: '2', name: '专家' }
      ]),
      columns: [
        { props: { type: 'selection', align: 'center', fixed: 'left' }},
        {
          props: {
            label: 'ID',
            align: 'center',
            prop: 'coursePackId'
          }
        },
        {
          props: {
            label: '课程名称',
            align: 'center',
            prop: 'name',
            width: '200'
          }
        },
        {
          props: { label: '封面', align: 'center', width: '110' },
          slot: 'cover'
        },
        { props: { label: '资源分类', align: 'center', width: '140' }, slot: 'cateName' },
        { props: { label: '章节数', align: 'center', prop: 'num' }},
        { props: { label: '作者', align: 'center', prop: 'doctorName' }},
        { props: { label: '浏览量', align: 'center', prop: 'hits' }},
        { props: { label: '虚拟浏览量', align: 'center', prop: 'baseHits' }},
        { props: { label: '点赞量', align: 'center', prop: 'praises' }},
        {
          props: { label: '虚拟点赞量', align: 'center', prop: 'basePraises' }
        },
        { props: { label: '会员等级', align: 'center', prop: 'memberLevelName' }},
        { props: { label: '修改人', align: 'center', prop: 'updateName' }},
        {
          props: {
            label: '修改时间',
            align: 'center',
            prop: 'updateTime',
            width: '100'
          }
        },
        {
          props: {
            align: 'center',
            label: '操作',
            width: '200',
            fixed: 'right'
          },
          slot: 'actions'
        }
      ],
      list: [],
      selectedList: [],
      total: 0,
      dialogImageUrl: '',
      dialogVisible: false,
      dialogFormShow: false,
      form: {
        basePraises: 0,
        baseHits: 0
      },
      curTarget: {},
      selectedIds: [],
      categoryType: 4,
      platformType: 1,
      highlightCurrent: false, // 是否开启高亮
      showSelect: false, // 是否选中分类
      sortForm: {
        sort: 0,
        coursePackId: '',
        cateId: ''
      },
      editSortVisible: false, // 排序弹窗
      showCheckCategory: false,
      categoryNameArr: [],
      categoryForm: {
        cateIdList: []
      },
      platformTreeList: [],
      platformProps: {
        label: 'name',
        value: 'categoryId',
        children: 'children',
        expandTrigger: 'hover',
        multiple: true,
        emitPath: false
      },
      havChild: false,
      editCategoryVisible: false, // 修改分类弹窗
      noQuery: true,
      addCoursePackTableData: [],
      // 表格表头
      addCoursePackTableColumnList: Object.freeze([
        { id: 1, label: '课程名称', align: 'center', prop: 'name' },
        {
          id: 2,
          prop: 'cateName',
          label: '资源分类',
          width: '140',
          align: 'center',
          filter: 'cateName'
        },
        { id: 3, label: '章节数', align: 'center', prop: 'num' },
        { id: 4, label: '作者', align: 'center', prop: 'doctorName' }
      ]),
      // 请求参数
      addCoursePackTableQuery: {
        condition: {
          type: 1,
          keyword: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      addCoursePackTotal: 0,
      addCoursePackVisible: false,
      coursePackIdList: [], // 视频id 集合
      cateIdList: [], // 分类id 集合
      // 会员等级相关
      memberLevelList: [], // 会员等级列表
      memberLevelDialogVisible: false, // 设置会员等级弹窗
      memberLevelForm: {
        memberLevel: '',
        coursePackIds: [],
        crossPage: false
      },
      isBatchSetMemberLevel: false // 是否批量设置会员等级
    }
  },
  created() {
    // 获取资源分类树数据
    getCategoryTreeList(0).then(res => {
      this.platformTreeList = res
    })
    // 获取会员等级列表
    getVipLevelList().then(res => {
      this.memberLevelList = res || []
    })
    this.init()
  },
  methods: {
    init(reset = true) {
      reset && (this.pager.page = 1)
      const params = { condition: this.condition, pager: this.pager }
      courseList(params).then(res => {
        if (this.havChild) {
          const flag = this.columns.some(item => {
            return item.props.prop === 'listOrder'
          })
          if (!flag) {
            const obj = { props: { label: '排序', align: 'center', prop: 'listOrder' }}
            this.columns.splice(1, 0, obj)
          }
        } else {
          this.columns = this.columns.filter(item => {
            return item.props.prop !== 'listOrder'
          })
        }
        this.list = res.records
        // 添加会员等级名称显示
        this.list.forEach(v => {
          const memberLevelItem = this.memberLevelList.find(i => i.level === v.memberLevel)
          v.memberLevelName = memberLevelItem ? memberLevelItem.name : '-'
        })
        this.total = res.total
      })
    },
    del(row) {
      this.$confirm('确认要删除课程？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          let coursePackIds = []
          if (row.coursePackId) {
            coursePackIds = [row.coursePackId]
          } else {
            coursePackIds = this.selectedList.map(v => v.coursePackId)
          }
          delCourse({ coursePackIds }).then(() => {
            this.$message.success('操作成功')
            this.init()
          })
        })
        .catch(console.log)
    },
    setFake(row) {
      this.dialogFormShow = true
      this.curTarget = row.coursePackId ? row : {}
    },
    confirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const { baseHits, basePraises } = this.form
          let coursePackIds = []
          if (this.curTarget.coursePackId) {
            // 单个
            coursePackIds = [this.curTarget.coursePackId]
          } else {
            // 批量
            coursePackIds = this.selectedList.map(v => v.coursePackId)
          }
          const params = {
            baseHits,
            basePraises,
            coursePackIds
          }
          setDummy(params).then(() => {
            this.$message.success('操作成功')
            this.cancel()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    add() {
      this.$router.push({
        name: 'LessonEdit'
      })
    },
    handlePagination(val) {
      this.pager = val
      this.init(false)
    },
    stick(row) {
      const params = {
        coursePackId: row.coursePackId,
        stick: row.stick ? 0 : 1
      }
      stick(params).then(() => {
        this.$message.success('操作成功')
        this.init()
      })
    },
    onSelectChange(arr) {
      this.selectedList = arr
    },
    handlePreview(url) {
      this.dialogImageUrl = url
      this.dialogVisible = true
    },
    onNumChange(v, key) {
      if (key === 'sort') {
        if (!v) {
          this.$nextTick(() => {
            this.sortForm[key] = 0
          })
        }
      } else {
        if (!v) {
          this.$nextTick(() => {
            this.form[key] = 0
          })
        }
      }
    },
    cancel() {
      this.$refs.form.resetFields()
      this.dialogFormShow = false
      this.curTarget = {}
    },
    edit(row) {
      this.$router.push({
        name: 'LessonEdit',
        query: {
          id: row.coursePackId
        }
      })
    },
    search(e) {
      if (!e) {
        this.noQuery = true
      } else {
        courseList(this.addCoursePackTableQuery).then(res => {
          this.noQuery = false
          this.addCoursePackTableData = res.records
          this.addCoursePackTotal = res.total
        })
      }
    },
    addCoursePack() {
      this.addCoursePackTableQuery.condition.notInCateId = this.condition.cateId
      this.addCoursePackVisible = true
    },
    addCoursePackCancel() {
      this.addCoursePackTableQuery.condition.keyword = ''
      this.addCoursePackTableQuery.pager.page = 1
      this.addCoursePackTableQuery.pager.pageSize = 10
      this.addCoursePackTotal = 0
      this.noQuery = true
      this.coursePackIdList = []
      this.addCoursePackVisible = false
    },
    addCoursePackConfirm() {
      if (this.coursePackIdList.length < 1) { return this.$message.error('请选择课程') }
      if (this.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 1,
        coursePackIdList: this.coursePackIdList,
        cateIdList: this.cateIdList
      }
      addCoursePack(data).then(res => {
        this.$message.success('添加成功')
        this.addCoursePackCancel()
        this.init()
        // this.$refs.tree.automaticTree()
      })
    },
    handleAddCoursePackPagination(val) {
      this.addCoursePackTableQuery.pager = val
      courseList(this.addCoursePackTableQuery).then(res => {
        this.addCoursePackTableData = res.records
        this.addCoursePackTotal = res.total
      })
    },
    // 取消选择事件
    handleCloseSelect(row) {
      this.coursePackIdList = this.coursePackIdList.filter(item => {
        return item !== row.coursePackId
      })
    },
    // 选择事件
    handleSelect(row) {
      this.coursePackIdList.push(row.coursePackId)
    },
    editCategory(row) {
      this.coursePackIdList = []
      if (row && JSON.stringify(row) !== '{}') {
        this.showCheckCategory = true
        this.coursePackIdList.push(row.coursePackId)
        this.categoryNameArr = row.cateName.split(';')
        this.categoryForm.cateIdList = row.cateIds
      } else {
        this.showCheckCategory = false
        this.categoryNameArr = []
        this.categoryForm.cateIdList = []
        this.selectedList.forEach(item => {
          this.coursePackIdList.push(item.coursePackId)
        })
      }
      this.editCategoryVisible = true
    },
    editCategoryCancel() {
      this.coursePackIdList = []
      this.cateIdList = []
      this.categoryForm.cateIdList = []
      this.editCategoryVisible = false
    },
    editCategoryConfirm() {
      if (this.coursePackIdList.length < 1) { return this.$message.error('请选择课程') }
      if (this.categoryForm.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 2,
        coursePackIdList: this.coursePackIdList,
        cateIdList: JSON.parse(JSON.stringify(this.categoryForm.cateIdList))
      }
      addCoursePack(data).then(res => {
        this.$message.success('添加成功')
        this.editCategoryCancel()
        this.init()
      })
    },
    setSort(row) {
      this.sortForm.coursePackId = row.coursePackId
      this.sortForm.sort = row.listOrder
      this.editSortVisible = true
    },
    sortCancel() {
      this.sortForm.sort = 0
      this.sortForm.coursePackId = ''
      this.sortForm.cateId = ''
      this.$refs.sortForm.resetFields()
      this.editSortVisible = false
    },
    sortConfirm() {
      if (this.condition.cateId) {
        this.sortForm.cateId = this.condition.cateId
      } else {
        return this.$message.error('请选择分类')
      }
      this.$refs.sortForm.validate(valid => {
        if (valid) {
          setCoursePackSort(this.sortForm).then(() => {
            this.$message.success('操作成功')
            this.sortCancel()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    cancelSelect() {
      const flag = this.columns.some(item => {
        return item.props.prop === 'listOrder'
      })
      if (flag) {
        this.columns.splice(1, 1)
      }
      this.condition.cateId = ''
      this.highlightCurrent = false
      this.showSelect = false
      this.havChild = false
      this.init()
      // 刷新分类数据
      this.$refs.tree.onRegionHeaderRefresh()
    },
    // 资源分类添加事件
    handleNodeAddPlatForm(data) {
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.addCoursePackTableQuery.condition.notInCateId = data.categoryId
      this.addCoursePackVisible = true
    },
    // 获取当前试题内容
    handleTreeNodeClick(data, node) {
      if (!data.havChild) {
        this.havChild = true
      } else {
        this.havChild = false
      }
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.showSelect = true // 显示取消选择按钮
      this.highlightCurrent = true // 高亮所选中的分类
      this.condition.cateId = data.categoryId
      // this.pager.page = 1
      this.init()
    },
    // 设置会员等级
    setMemberLevel(row) {
      this.isBatchSetMemberLevel = false
      this.memberLevelForm.coursePackIds = [row.coursePackId]
      this.memberLevelForm.memberLevel = row.memberLevel || ''
      this.memberLevelDialogVisible = true
    },
    // 批量设置会员等级
    batchSetMemberLevel() {
      if (this.selectedList.length === 0) {
        this.$message.warning('请选择要设置的课程')
        return
      }
      this.isBatchSetMemberLevel = true
      this.memberLevelForm.coursePackIds = this.selectedList.map(item => item.coursePackId)
      this.memberLevelForm.memberLevel = ''
      this.memberLevelForm.crossPage = false
      this.memberLevelDialogVisible = true
    },
    // 会员等级弹窗取消
    memberLevelCancel() {
      this.memberLevelForm = {
        memberLevel: '',
        coursePackIds: [],
        crossPage: false
      }
      this.memberLevelDialogVisible = false
    },
    // 会员等级弹窗确认
    memberLevelConfirm() {
      this.$refs.memberLevelForm.validate(valid => {
        if (valid) {
          const params = {
            ...this.condition,
            updateMemberLevel: this.memberLevelForm.memberLevel,
            coursePackIdList: this.memberLevelForm.coursePackIds,
            setType: this.memberLevelForm.crossPage ? 1 : 2
          }
          setCoursePackMemberLevel(params).then(() => {
            this.$message.success('设置成功')
            this.memberLevelCancel()
            this.init()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content-left {
  padding-top: 15px;
  height: calc(100vh - 220px);

  .tree-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-size: 22px;
      font-weight: bold;
      margin-right: 10px;
    }
  }
  .content-container {
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: #fff;
    &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
      overflow-x: hidden;
    }
    &::v-deep .el-scrollbar__view.p20-scrollbar__view {
      background-color: #fff;
      padding-right: 15px;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
    }
    &::v-deep .el-scrollbar__bar.is-vertical {
      width: 8px;
    }
  }
}
.content-right {
  flex: 8;
  padding-right: 20px;

}
::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
  display:none;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
