// 统计模块查询当月
export function setDefaultDate() {
  let y = new Date().getFullYear()
  let m = new Date().getMonth() + 1
  if (m === 0) {
    m = 12
    y = y - 1
  }
  const ld = new Date(y, m, 0).getDate() // 最后一天
  const firstDay = y + '-' + (m < 10 ? '0' + m : m) + '-' + '01'
  const lastDay = y + '-' + (m < 10 ? '0' + m : m) + '-' + ld
  return [firstDay, lastDay]
}

// 统计模块月份区间获取最后一天
export function getEndDate(value) {
  value = value.split('')
  value.splice(4, 0, '/')
  value.splice(7, 0, '/')
  value = value.join('')
  const valueDate = new Date(value)
  const m = (valueDate.getMonth() + 1).toString().padStart(2, '0')
  const y = valueDate.getFullYear()
  const day = new Date(y, m, 0)
  return y + '' + m + '' + day.getDate()
}
