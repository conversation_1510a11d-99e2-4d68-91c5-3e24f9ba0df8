import request from '@/utils/request'

// 新增banner
export function addAppBanner(params) {
  return request({
    url: '/appBanner/add',
    method: 'post',
    data: params
  })
}

/**
 *
 * @param {String} params videoRcommendId
 * @description 删除banner
 *
 */
export function deleteBanner(params) {
  return request({
    url: '/appBanner/delete/' + params,
    method: 'post'
  })
}

// 编辑banner
export function editAppBanner(params) {
  return request({
    url: '',
    method: 'post',
    data: params
  })
}

/**
 *
 * @param {String} params videoRecommendId
 * @description 编辑查看
 *
 */
export function editViewBanner(params) {
  return request({
    url: '/appBanner/editView/' + params,
    method: 'get'
  })
}

// banner列表
export function getBannerList(params) {
  return request({
    url: `/appBanner/list`,
    method: 'post',
    data: params
  })
}

