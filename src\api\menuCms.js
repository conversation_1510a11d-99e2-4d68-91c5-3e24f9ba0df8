import request from '@/utils/request'

export default {
  // 菜单权限列表
  list() {
    return request({
      url: '/permission/list',
      method: 'post'
    })
  },
  // 禁用
  deactivate(data) {
    return request({
      url: `/permission/disable/${data}`,
      method: 'post'
    })
  },
  // 启用
  activate(data) {
    return request({
      url: `/permission/enable/${data}`,
      method: 'post'
    })
  },
  // 添加
  create(data) {
    return request({
      url: `/permission/add`,
      method: 'post',
      data
    })
  },
  // 删除
  delete(data) {
    return request({
      url: `/permission/del/${data}`,
      method: 'post'
    })
  },
  // 权限信息
  detail(params) {
    return request({
      url: `/permission/detail/${params}`,
      method: 'get'
    })
  },
  // 编辑
  edit(data) {
    return request({
      url: `/permission/edit`,
      method: 'post',
      data
    })
  },
  // 获取权限列表(选择父级权限用)
  treeList(data) {
    return request({
      url: `/permission/treeList`,
      method: 'post'
    })
  }
}
