<template>
  <el-dialog
    title="选择用户"
    :visible.sync="dialogVisible"
    width="700px"
    center
    :before-close="handleClose"
  >
    <el-input v-model="keyword" placeholder="请输入对应搜索关键词" class="input-with-select">
      <el-select slot="prepend" v-model="searchType" placeholder="请选择">
        <el-option v-for="item in searchOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button slot="append" icon="el-icon-search" @click="getList()" />
    </el-input>
    <el-table
      :data="tableData"
      style="width: 100%"
      height="300"
    >
      <el-table-column
        prop="uid"
        label="UID"
        align="center"
      />
      <el-table-column
        prop="realName"
        label="姓名"
        align="center"
      />
      <el-table-column
        prop="username"
        label="用户账号"
        align="center"
      />
      <el-table-column
        prop="phone"
        label="手机"
        align="center"
      />
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="userId === '' || userId !== scope.row.uid"
            size="mini"
            type="primary"
            @click="checked(scope.row.uid)"
          >选择</el-button>
          <el-button
            v-if="userId === scope.row.uid"
            size="mini"
            type="danger"
            @click="checked(scope.row.uid, 'del')"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="handleClose('confirm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { userList } from '@/api/userManage'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      userId: this.id,
      keyword: '',
      searchType: 'phone',
      searchOptions: [
        { label: '手机', value: 'phone' },
        { label: '用户账号', value: 'username' },
        { label: 'UID', value: 'uid' }
      ],
      tableData: []
    }
  },
  methods: {
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      if (e) {
        this.$emit('update:id', this.userId)
        if (this.userId !== '') {
          this.$emit('enter', this.userId)
        }
      } else {
        this.userId = ''
      }
    },
    getList() {
      if (this.keyword === '') return this.$message.error('查询条件不能为空')
      const query = {
        condition: {
          phone: '',
          username: '',
          uid: ''
        },
        pager: {
          page: 1,
          pageSize: 100
        }
      }
      query.condition[this.searchType] = this.keyword
      userList(query).then(res => {
        this.tableData = res.records
      })
    },
    checked(uid, del) {
      if (del) {
        this.userId = ''
      } else {
        this.userId = uid
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .input-with-select {
    width: 400px;
    .el-select {
      width: 110px;
    }
  }
  .el-dialog__body {
    padding-bottom: 10px;
  }
}
</style>
