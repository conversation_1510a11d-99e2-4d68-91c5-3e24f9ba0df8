<template>
  <div class="container">
    <el-form ref="formQuestion" :model="formQuestion" label-width="100px" :rules="rules" label-position="right">
      <el-form-item label="题干:" prop="title">
        <el-input v-model="formQuestion.title" type="textarea" :resize="none" :autosize="autosize" placeholder="请输入题干" />
      </el-form-item>
      <el-form-item label="选项:" prop="titles">
        <el-radio v-model="radio" label="正确" />
        <el-radio v-model="radio" label="错误" />
      </el-form-item>
      <el-form-item label="考点关键字:">
        <el-input v-model="formQuestion.points" type="textarea" :resize="none" :autosize="autosize" :maxlength="300" placeholder="请输入考点关键字，多个关键字以逗号分隔" />
      </el-form-item>
      <el-form-item label="解析:">
        <el-input v-model="formQuestion.desc" type="textarea" :resize="none" :autosize="autosize" :maxlength="3000" placeholder="请输入试题解析" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Aone',
  props: {
    aData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      title: 'A1',
      none: 'none', // 控制快速录入输入框不能被缩放
      addBtn: false, // 控制添加选项是否禁用
      deleteBtn: false, // 删除选项是否禁用
      autosize: {
        minRows: 6,
        maxRows: 8
      },
      radio: '',
      formQuestion: {
        title: '',
        optionsList: [
          {
            index: 'A',
            content: '正确',
            isAnswer: 0
          },
          {
            index: 'B',
            content: '错误',
            isAnswer: 0
          }
        ],
        points: '',
        desc: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入题干', trigger: 'blur' }
        ],
        titles: [
          { required: true, message: '请输入考点关键字，多个关键字以逗号分隔', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    aData: {
      handler(newval, oldvar) {
        if (JSON.stringify(newval) !== '{}') {
          this.formQuestion.title = newval.title
          this.formQuestion.points = newval.points
          this.formQuestion.desc = newval.desc
          newval.optionsList.forEach(item => {
            if (item.isAnswer === 1) {
              this.radio = item.content
            }
          })
        }
      },
      immediate: true
    },
    type() {
      if (this.type === 1 || this.type === 6) {
        this.title = 'A1'
      } else {
        this.title = 'A2'
      }
    }
  },
  created() {},
  methods: {

  }
}
</script>
  <style lang="scss" scoped>
    .container {
      width: 60%;
      margin: 20px auto;
      .flex{
        display: flex;
        justify-content: space-between;
        align-items: center;
        * {
          margin: 0 10px 20px 0;
        }
        .deleteBtn {
          margin-bottom: 0px;
        }
      }
      ol {
        margin-top: 0;
        margin-bottom: 0;
        list-style-type: upper-latin;
        li {
          list-style-position: outside;
        }
      }
      .xl-btn {
        width: 90%;
        margin: 0px 0 10px 100px;
        border: 1px dashed #DCDFE6
      }
      .xl-btn:hover {
        border: 1px dashed #409EFF;
      }
    }
    ::v-deep .el-checkbox__inner {
      border-radius: 7px;
    }
     ::v-deep .el-radio-group{
       display: block;
      font-size: 16px;
      line-height: 16px;
    }
    .container .li:last-child {
      margin-bottom: -20px;
    }
    .problem{
      border: 1px dashed #DCDFE6;
    }
  </style>
