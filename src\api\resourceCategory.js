import request from '@/utils/request'

// 资源分类

// 分类列表
export function cateList(query) {
  return request({
    url: '/category/cateList',
    method: 'post',
    data: query
  })
}

// 新增分类
export function cateAdd(query) {
  return request({
    url: '/category/add',
    method: 'post',
    data: query
  })
}

// 编辑分类
export function cateEdit(query) {
  return request({
    url: '/category/update',
    method: 'post',
    data: query
  })
}

// 删除分类
export function cateDelete(id) {
  return request({
    url: '/category/delete?id=' + id,
    method: 'get'
  })
}

// 修改分类排序
export function cateChangeOrder(query) {
  return request({
    url: '/category/update/order',
    method: 'post',
    data: query
  })
}

// 更新展示状态
export function updateShowStatus(params) {
  return request({
    url: '/category/updateShowStatus',
    method: 'put',
    params
  })
}
