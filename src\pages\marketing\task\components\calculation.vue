<template>
  <el-dialog
    title="核算结算费用"
    :visible.sync="dialogVisible"
    width="420px"
    :before-close="handleClose"
  >
    结算费用:
    <el-input v-model="checkingFee" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { calculateFee, calculateFeeVisit, calculateFeeDoula } from '@/api/marketing/taskExecute'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    checkingFee: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: 'creator'
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    confirm() {
      const calculate = this.type === 'creator' ? calculateFee : (this.type === 'visit' ? calculateFeeVisit : calculateFeeDoula)
      const query = {
        totalFee: this.checkingFee
      }
      query[this.type === 'visit' ? 'visitUserId' : 'taskUserId'] = this.id
      calculate(query).then(() => {
        this.handleClose()
        this.$parent.getExecuteList()
        this.$message.success('结算完成,进入任务执行人确认结算费用流程')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 45px 24px;
    color: #333;
    .el-input {
      width: 300px;
    }
  }
}
</style>
