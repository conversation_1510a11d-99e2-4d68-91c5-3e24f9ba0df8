<template>
  <div class="app-container">
    <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="100px" class="form">
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择" clearable>
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" show-word-limit maxlength="20" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="上级" prop="pid">
        <el-cascader
          v-model="form.pid"
          :options="menuList"
          :props="{
            emitPath:false,
            checkStrictly: true,
            children:'childList' ,
            label:'name',
            value:'permissionId'
          }"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item label="code" prop="code">
        <el-input v-model="form.code" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="排序号" prop="listOrder">
        <el-input-number v-model="form.listOrder" placeholder="请输入" clearable :min="0" @change="onOrderChange" />
      </el-form-item>
      <el-form-item label="url" prop="url">
        <el-input v-model="form.url" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input v-model="form.description" type="textarea" maxlength="100" show-word-limit placeholder="请输入" clearable />
      </el-form-item>
    </el-form>

    <div v-if="!readOnly">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/api/menuCms'

export default {
  name: 'MenuAdd',
  data() {
    return {
      readOnly: !!this.$route.query.readOnly,
      form: {
        code: '',
        name: '',
        description: '',
        listOrder: '',
        url: '',
        type: '',
        pid: 0
      },
      rules: {
        code: [{ required: true, message: '请输入code', trigger: 'blur' }],
        type: [{ required: true, message: '请选择', trigger: 'change' }],
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        listOrder: [
          { required: true, message: '请填写', trigger: 'blur' }
        ]
      },
      menuList: [],
      typeList: [
        { value: 1, label: '菜单' },
        { value: 2, label: '操作' }
      ],
      detail: {}
    }
  },
  created() {
    request.treeList().then(res => {
      this.menuList = [res]
    })
    if (this.$route.query.id) {
      this.detail = this.$route.query
      this.detail.type -= 0
      this.detail.listOrder += ''
      this.form = { ...this.detail }
    }
  },
  methods: {
    cancel() {
      this.$router.go(-1)
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.detail.id) {
            request.edit(this.form).then(res => {
              this.$message.success('编辑成功')
              this.cancel()
            })
          } else {
            request.create(this.form).then(res => {
              this.$message.success('添加成功')
              this.cancel()
            })
          }
        } else {
          return false
        }
      })
    },
    onOrderChange(v) {
      !v && (this.form.listOrder = 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .form {
    width: 700px;
  }
}
</style>
