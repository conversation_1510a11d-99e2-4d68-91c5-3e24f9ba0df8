<template>
  <div class="editor-wrapper">
    <toolbar
      :editor="editor"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <editor
      v-model="html"
      :style="{height: height + 'px'}"
      :default-config="editorConfig"
      :mode="mode"
      @onCreated="onCreated"
      @onChange="onChange"
    />
    <div v-if="maxlength" class="useful-num">
      {{ useLen }}/{{ maxlength }}
    </div>
  </div>
</template>

<script>
import { preUploadApi, editorUpload } from '@/api/biz'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
// import { DomEditor } from '@wangeditor/editor'

export default {
  name: 'CustomEditor',
  components: {
    Editor,
    Toolbar
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    maxlength: {
      type: Number,
      default: 0
    },
    height: {
      type: [String, Number],
      default: 300
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editor: null,
      html: '',
      toolbarConfig: {
        excludeKeys: [
          'group-video'
        ]
      },
      editorConfig: {
        placeholder: '请输入内容',
        MENU_CONF: {
          uploadImage: {
            // 自定义图片上传功能
            customUpload: (file, insertFn) => {
              const param = {
                filename: file.name,
                size: file.size,
                type: 'adv'
              }
              preUploadApi(param).then(res => {
                const formData = new FormData()
                formData.append('data', res)
                formData.append('file', file)
                editorUpload(formData).then(e => {
                  insertFn(e.url, '', e.url)
                })
              })
            }
          }
        }
      },
      mode: 'default', // or 'simple'
      useLen: 0
    }
  },
  watch: {
    value(val) {
      this.html = val
    }
  },
  beforeDestroy() {
    // editor销毁
    const editor = this.editor
    if (editor == null) {
      return
    }
    editor.destroy()
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      this.editor.setHtml(this.value) // 创建时回显
      if (this.disabled) {
        this.editor.disable()
      }
    },
    onChange() {
      const text = this.editor.getText()
      // 计算当前输入了多少文字
      this.useLen = (text || '').length
      // 每次富文本内容改变，触发change事件
      this.$emit('change', this.html)

      // const toolbar = DomEditor.getToolbar(this.editor)
      // const curToolbarConfig = toolbar.getConfig()
      // console.log(curToolbarConfig.toolbarKeys) // 当前工具栏菜单排序和分组
    }
  }
}
</script>

<style lang="scss" scoped>
    .editor-wrapper{
        z-index: 3;
        position: relative;
        ::v-deep .w-e-toolbar{
            z-index: 2!important;
            border: solid 1px #E6E9EC!important;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            .w-e-bar-item{
                padding: 1px;
            }
        }
        ::v-deep .w-e-text-container{
            z-index: 1!important;
            border: solid 1px #E6E9EC!important;
            border-top: none!important;
            border-bottom-left-radius: 6px;
            border-bottom-right-radius: 6px;
        }
    }
    .useful-num{
        position: absolute;
        right: 6px;
        bottom: 10px;
        z-index: 99999;
        font-size: 12px;
        color: #999;
        background: #fff;
        padding: 0 6px;
        height: 28px;
        line-height: 28px;
    }
</style>
