import request from '@/utils/request'

// 查看文件导出状态
export function getFileUrl(fileId) {
  return request({
    url: `/cms/export/getFileUrl/${fileId}`,
    method: 'get'
  })
}

export function getExportStatus(exportId) {
  return request({
    url: `/cms/export/getExportStatus/${exportId}`,
    method: 'get'
  })
}

/**
 * 导出结果相关
 */
export default {
  // 列表
  list(data) {
    return request({
      url: `/cms/export/list`,
      method: 'post',
      data
    })
  }
}
