<template>
  <section class="detail">
    <div class="module">
      <h2>
        服务商概况
      </h2>
      <ul>
        <li v-for="item in roughly" :key="item.label">
          <p>{{ item.label }}</p>
          {{ item.value }}
        </li>
      </ul>
    </div>
    <div v-for="e in list" :key="e.type" class="module">
      <h2>
        {{ e.title }}列表
        <el-button type="primary" @click="exportList(e.type)">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="e.tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column :label="e.type === 'VISIT' ? '拜访客户' : (e.type === 'DOULA' ? '文字内容' : e.title)" width="260">
            <template slot-scope="scope">
              <span @click="toDetail(e.type,scope.row.id,scope.row.title)">{{ e.type === 'VISIT' ? scope.row.customer : scope.row.title }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in e.tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
      </div>
      <Pagination
        :page="e.pager.page"
        :page-size="e.pager.pageSize"
        :total="e.total"
        @pagination="(val) => handlePagination(val,e.type)"
      />
    </div>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { serviceProviderList } from '@/api/marketing/taskPromote'
import { serviceArticleList, serviceArticleListExport, serviceVisitList, serviceVisitListExport, serviceList } from '@/api/marketing/promoteArticle'
export default {
  components: {
    Pagination
  },
  props: {
    searchParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      roughly: [],
      list: [
        {
          type: 'ARTICLE',
          title: '文章',
          tableData: [],
          tableColumn: [
            { prop: 'serviceProvider', label: '服务商', width: '130' },
            { prop: 'orgName', label: '企业', width: '130' },
            { prop: 'productName', label: '产品', width: '140' },
            { prop: 'createdName', label: '发布人', width: '70' },
            { prop: 'author', label: '作者', width: '140' },
            { prop: 'releaseTime', label: '发布时间', width: '140' },
            { prop: 'viewNum', label: '浏览量', width: '' },
            { prop: 'peopleViewNum', label: '浏览人数', width: '' },
            { prop: 'totalBudget', label: '推广总预算 (元)', width: '' },
            { prop: 'totalBudgetFee', label: '制作费 (元)', width: '' },
            { prop: 'extraBonusPrice', label: '点击单价 (元)', width: '' },
            { prop: 'targetPeopleViewNum', label: '目标点击量', width: '' },
            { prop: 'validPeopleViewNum', label: '有效点击量', width: '' },
            { prop: 'totalClickFee', label: '点击奖励', width: '' },
            { prop: 'totalCheckingFee', label: '核算总费用 (元)', width: '' },
            { prop: 'totalFee', label: '结算总费用 (元)', width: '' }
          ],
          pager: {
            page: 1,
            pageSize: 10
          },
          total: 0
        },
        {
          type: 'VIDEO',
          title: '视频',
          tableData: [],
          tableColumn: [
            { prop: 'serviceProvider', label: '服务商', width: '130' },
            { prop: 'orgName', label: '企业', width: '130' },
            { prop: 'productName', label: '产品', width: '140' },
            { prop: 'createdName', label: '发布人', width: '70' },
            { prop: 'author', label: '作者', width: '140' },
            { prop: 'releaseTime', label: '发布时间', width: '140' },
            { prop: 'viewNum', label: '播放量', width: '' },
            { prop: 'peopleViewNum', label: '播放人数', width: '' },
            { prop: 'totalBudget', label: '推广总预算 (元)', width: '' },
            { prop: 'totalBudgetFee', label: '制作费 (元)', width: '' },
            { prop: 'extraBonusPrice', label: '点击单价 (元)', width: '' },
            { prop: 'targetPeopleViewNum', label: '目标点击量', width: '' },
            { prop: 'validPeopleViewNum', label: '有效点击量', width: '' },
            { prop: 'totalClickFee', label: '点击奖励', width: '' },
            { prop: 'totalCheckingFee', label: '核算总费用 (元)', width: '' },
            { prop: 'totalFee', label: '结算总费用 (元)', width: '' }
          ],
          pager: {
            page: 1,
            pageSize: 10
          },
          total: 0
        },
        {
          type: 'DOULA',
          title: '抖喇',
          tableData: [],
          tableColumn: [
            { prop: 'serviceProvider', label: '服务商', width: '130' },
            { prop: 'orgName', label: '企业', width: '130' },
            { prop: 'articleTypeStr', label: '类型', width: '80' },
            { prop: 'productName', label: '产品', width: '140' },
            { prop: 'createdName', label: '发布人', width: '70' },
            { prop: 'author', label: '作者', width: '140' },
            { prop: 'releaseTime', label: '发布时间', width: '140' },
            { prop: 'viewNum', label: '播放量', width: '' },
            { prop: 'peopleViewNum', label: '播放人数', width: '' },
            { prop: 'totalBudget', label: '推广总预算 (元)', width: '' },
            { prop: 'totalBudgetFee', label: '制作费 (元)', width: '' },
            { prop: 'extraBonusPrice', label: '点击单价 (元)', width: '' },
            { prop: 'targetPeopleViewNum', label: '目标点击量', width: '' },
            { prop: 'validPeopleViewNum', label: '有效点击量', width: '' },
            { prop: 'totalClickFee', label: '点击奖励', width: '' },
            { prop: 'totalCheckingFee', label: '核算总费用 (元)', width: '' },
            { prop: 'totalFee', label: '结算总费用 (元)', width: '' }
          ],
          pager: {
            page: 1,
            pageSize: 10
          },
          total: 0
        },
        {
          type: 'VISIT',
          title: '拜访',
          tableData: [],
          tableColumn: [
            { prop: 'companyName', label: '客户单位', width: '280' },
            { prop: 'type', label: '拜访类型', width: '140' },
            { prop: 'serviceProvider', label: '服务商', width: '130' },
            { prop: 'orgName', label: '企业', width: '130' },
            { prop: 'productDesc', label: '产品', width: '300' },
            { prop: 'userName', label: '推广员', width: '140' },
            { prop: 'visitTime', label: '拜访时间', width: '160' },
            { prop: 'totalBudget', label: '推广预算 (元)', width: '' },
            { prop: 'totalCheckingFee', label: '核算费用 (元)', width: '' },
            { prop: 'totalFee', label: '结算费用 (元)', width: '' }
          ],
          pager: {
            page: 1,
            pageSize: 10
          },
          total: 0
        }
      ],
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          orgId: null,
          orgIds: null,
          productId: null,
          type: null,
          serviceProviderOrgId: null,
          serviceProviderId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.init()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    init() {
      const obj = this.$route.params
      this.queryParams.condition.serviceProviderOrgId = obj.serviceProviderId
      this.queryParams.condition.serviceProviderId = obj.serviceProviderId
      serviceProviderList().then(res => {
        const params = {
          condition: {
            status: 1,
            type: 2002,
            serviceProviderOrgId: obj.serviceProviderId
          },
          pager: {
            page: 1,
            pageSize: 100
          }
        }
        this.$parent.$parent.getOrganListPage(params)
        this.queryParams.condition.orgzIds = res.find(v => v.serviceOrgId === obj.serviceProviderId).manufacturerOrgIdList
        this.list.forEach(v => { v.pager.page = 1 })
        this.getList('ARTICLE')
        this.getList('VIDEO')
        this.getList('DOULA')
        this.getList('VISIT')
      })
    },
    getList(type) {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      query.condition.type = type
      query.pager = this.list.find(v => v.type === type).pager
      if (type !== 'VISIT') {
        serviceArticleList(query).then(res => {
          this.list.find(v => v.type === type).tableData = res.records
          this.list.find(v => v.type === type).total = res.total
        })
      } else {
        serviceVisitList(query).then(res => {
          this.list.find(v => v.type === type).tableData = res.records
          this.list.find(v => v.type === type).total = res.total
        })

        serviceList(this.queryParams).then(res => {
          const obj = res.records[0]
          this.roughly = [
            { value: obj.serviceProvider, label: '服务商' },
            { value: obj.articleFeeWithUnit, label: '文章征集业务量' },
            { value: obj.videoFeeWithUnit, label: '视频征集业务量' },
            { value: obj.doulaFeeWithUnit, label: '抖喇征集业务量' },
            { value: obj.visitFeeWithUnit, label: '拜访业务量' },
            { value: obj.totalFeeWithUnit, label: '总业务量' }
          ]
        })
      }
    },
    exportList(type) {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      query.condition.type = type
      if (type !== 'VISIT') {
        serviceArticleListExport(query.condition).then(() => {
          this.$message.success('导出成功，请在导出管理中查看')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      } else {
        serviceVisitListExport(query.condition).then(() => {
          this.$message.success('导出成功，请在导出管理中查看')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      }
    },
    toDetail(type, id, title) {
      const target = type.toLowerCase().replace(/^\S/, s => s.toUpperCase()) // 转换为首字母大写其余小写
      this.$router.push({
        name: `MarketingStatistics${target}Detail`,
        query: {
          id,
          title
        }
      })
    },
    handlePagination(v, type) {
      this.list.find(v => v.type === type).pager = v
      this.getList(type)
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  .module {
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      font-size: 20px;
      line-height: 55px;
      background-color: #f9f9f9;
      .el-button {
        height: 35px;
      }
    }
    ul {
      display: flex;
      padding: 38px 0 0 24px;
      height: 145px;
      font-size: 20px;
      font-weight: bold;
      li {
        margin-right: 100px;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        p {
          font-size: 18px;
          color: #666;
          font-weight: 400;
          margin-bottom: 20px;
        }
      }
    }
    .preview {
      height: 125px;
      padding-left: 24px;
      line-height: 125px;
      a {
        color: #409eff;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .table {
      padding: 18px 24px 0;
      .pagination-container {
        padding: 30px 20px;
      }
      .cell {
        span {
          color: #409eff;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
