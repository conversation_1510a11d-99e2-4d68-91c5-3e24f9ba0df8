<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="conditionWatch.queryType" filterable clearable placeholder="用户账号">
          <el-option v-for="item in accountList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.queryWord" placeholder="根据左侧查询方式对应关键字" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.areaIds"
          placeholder="请选择区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'areaId',
            label:'name',
            children:'childList'
          }"
          clearable
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.majorIds"
          placeholder="请选择专科"
          :options="majorList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'majorId',
            label:'name',
            children:'childList'
          }"
          clearable
        />
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.identityIds" multiple filterable clearable placeholder="请选择身份" collapse-tags>
          <el-option v-for="item in identityList" :key="item.identityId" :label="item.name" :value="item.identityId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.departmentPackIds" multiple filterable clearable placeholder="请选择部门库" collapse-tags>
          <el-option v-for="item in deptList" :key="item.departmentPackId" :label="item.name" :value="item.departmentPackId" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe>
      <template slot="actions" slot-scope="{row}">
        <el-button size="mini" type="text" @click="viewPersonal(row)">员工信息</el-button>
        <el-button size="mini" type="text" @click="toDetail(row)">详情</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination :auto-scroll="false" class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <!-- staff info -->
    <DialogUserInfo title="查看账号信息" :show.sync="dialogInfoVisible" :data="userInfoData" />
    <a v-show="false" ref="downId" :download="download" :href="excelUrl" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/ad'
import { getAreaTree } from '@/api/area'
import { identityList } from '@/api/category'
import { treeList } from '@/api/major'
import { deptNameList } from '@/api/dept'
import DialogUserInfo from '@/pages/user/compontents/dialogUserInfo.vue'
import { getPersonalDetail } from '@/api/userManage'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import { parseTime } from '@/utils'

const columns = [
  { props: { label: '姓名', align: 'center', prop: 'real_name' }},
  { props: { label: '个人账号', align: 'center', prop: 'username' }},
  { props: { label: '手机', align: 'center', prop: 'phone' }},
  { props: { label: '身份', align: 'center', prop: 'identity_name' }},
  { props: { label: '专科', align: 'center', prop: 'major_name' }},
  { props: { label: '区域', align: 'center', prop: 'area_name' }},
  { props: { label: '曝光次数', align: 'center', prop: 'exposure_num' }},
  { props: { label: '点击次数', align: 'center', prop: 'hit_num' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'StatisticsDetail',
  components: { DialogUserInfo },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      adId: this.$route.query.id,
      condition: {
        operateType: this.type,
        selectType: this.type,
        queryWord: ''
      },
      conditionWatch: {
        adId: this.$route.query.id,
        areaIds: [],
        majorIds: [],
        identityIds: [],
        departmentPackIds: [],
        queryType: ''
      },
      accountList: [
        { value: 1, name: '用户账号' },
        { value: 2, name: '手机' },
        { value: 3, name: '用户姓名' },
        { value: 4, name: '员工账号' },
        { value: 5, name: '员工姓名' }
      ],
      deptList: [],
      majorList: [],
      areaList: [],
      identityList: [],
      editPath: 'statisticsPersonalDetail',
      mainKey: 'adId',
      initList: true, // 是否初始化调用
      userInfoData: {}, // 员工信息
      dialogInfoVisible: false, // 员工信息弹窗
      // download
      excelUrl: '',
      download: ''
    }
  },
  created() {
    Promise.all([
      deptNameList({}),
      getAreaTree(),
      identityList(),
      treeList()
    ]).then(res => {
      [this.deptList, this.areaList, this.identityList, this.majorList] = res
    })
  },
  methods: {
    // export excel
    exportExcel() {
      // window.location.href = request.actExport(param)
      axios.post(request.adUserRankExport(), this.getListData(), {
        headers: { 'token': getToken() },
        responseType: 'blob'
      }).then(res => {
        if (!res.headers['content-type'] || res.headers['content-type'].indexOf('application/json') === -1) {
          const blob = new Blob([res.data], { type: res.headers['content-type'] })
          this.excelUrl = window.URL.createObjectURL(blob)
          // if (res.headers['content-disposition']) {
          //   this.download = res.headers['content-disposition'].split(';')[1].split('=')[1]
          // } else {
          //   this.download = '导出-' + parseTime(new Date().getTime(), '{y}-{m}-{d}-{h}-{i}-{s}')
          // }
          this.download = '广告统计明细-' + parseTime(new Date().getTime(), '{y}-{m}-{d}-{h}-{i}-{s}')
          this.$nextTick(() => this.$refs.downId.click())
        }
      }, (error) => {
        console.log('err' + error) // for debug
        this.$message({
          message: error.message,
          type: 'error',
          duration: 5 * 1000
        })
      })
    },
    // view personal detail
    viewPersonal(row) {
      getPersonalDetail({ userId: row.user_id }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
      })
    },
    flatten(arr) {
      return arr.reduce((a, b) => a.concat(Array.isArray(b) ? this.flatten(b) : b), [])
    },
    getListData() {
      const param = {
        ...this.conditionWatch,
        areaIds: this.flatten(this.conditionWatch.areaIds),
        majorIds: this.flatten(this.conditionWatch.majorIds)
      }
      const data = {
        ...Object.assign(param, this.conditionCache),
        ...this.pager
      }
      return data
    },
    // view detail
    toDetail(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          readOnly: 1,
          adId: this.adId,
          userId: row.user_id
        }
      })
    },
    getList() {
      request.adUserRank(this.getListData()).then(res => {
        this.list = res.records
        this.total = res.total
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .search-column__item {
    margin-bottom: 10px;
  }
}
</style>
