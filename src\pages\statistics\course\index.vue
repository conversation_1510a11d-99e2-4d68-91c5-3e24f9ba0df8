<template>
  <section class="course">
    <div v-if="!$route.path.includes('course/detail')">
      <el-input v-model="keyword" placeholder="请输入关键字" class="input-with-select" @change="search()">
        <el-select slot="prepend" v-model="keyType" icon="el-icon-search">
          <el-option
            v-for="item in keyTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <i slot="suffix" class="el-input__icon el-icon-search" @click="search()" />
      </el-input>
      <el-cascader
        ref="elcascader"
        v-model="queryParams.condition.cateId"
        placeholder="分类"
        :options="treeList"
        collapse-tags
        :show-all-levels="false"
        :props="{
          checkStrictly: true,
          emitPath:false,
          expandTrigger:'hover',
          value:'categoryId',
          label:'name',
          children:'children'
        }"
        clearable
        filterable
        @change="search()"
      />
      <div class="content">
        <h2>
          课程列表
          <el-button type="primary" @click="exportList">导出</el-button>
        </h2>
        <div class="table">
          <el-table
            :data="tableData"
            border
            :header-cell-style="{background:'#f9f9f9',color:'#333'}"
            style="width: 100%"
          >
            <el-table-column prop="title" label="名称">
              <template slot-scope="scope">
                <span @click="toDetail(scope.row)">{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-for="item in tableColumn"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
            />
          </el-table>
        </div>
        <Pagination
          :page="queryParams.pager.page"
          :page-size="queryParams.pager.pageSize"
          :total="total"
          @pagination="handlePagination"
        />
      </div>
    </div>
    <!-- 详情 -->
    <router-view />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getCategoryTreeList } from '@/api/category'
import { courseList, courseListExport } from '@/api/statistics'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      keyword: '',
      keyType: 'title',
      keyTypeOptions: [
        { label: '名称', value: 'title' },
        { label: '作者', value: 'authorName' }
      ],
      treeList: [],
      tableColumn: [
        { prop: 'cate', label: '分类' },
        { prop: 'authorInfo', label: '作者' },
        { prop: 'studyTimes', label: '学习人次' }
      ],
      tableData: [],
      queryParams: {
        condition: {
          cateId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0
    }
  },
  watch: {
    keyword(v) {
      this.queryParams.condition[this.keyType] = v
    },
    keyType() {
      this.queryParams.condition = {
        cateId: this.queryParams.condition.cateId
      }
      this.keyword = ''
    }
  },
  created() {
    getCategoryTreeList(0).then(res => {
      this.treeList = res
    })
    this.getcourseList()
  },
  methods: {
    toDetail(row) {
      this.$router.push({
        name: 'StatisticsCourseDetail',
        params: row
      })
    },
    getcourseList() {
      courseList(this.queryParams).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.queryParams.pager.page = 1
      this.getcourseList()
    },
    exportList() {
      if (this.total <= 0) {
        this.$message.error('暂无可导出数据')
        return
      }
      courseListExport(this.queryParams.condition).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getcourseList()
    }
  }
}
</script>

<style lang="scss" scoped>
.course {
  padding: 20px 40px;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  .content {
    margin-top: 20px;
    border: 1px solid #EBEEF5
  }
  h2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
    .el-button {
      width: 80px;
      height: 35px;
    }
  }
  .el-input {
    width: 250px;
    .el-select {
      width: 80px;
    }
  }
  .table {
    padding: 25px 20px 0;
    .cell {
      span {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}
</style>
