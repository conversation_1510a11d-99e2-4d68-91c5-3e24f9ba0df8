<template>
  <div class="contain">
    <el-card>

      <div class="title">
        <span>{{ data.type|questionTypeList }}题型</span>
        <span>{{ data.difficulty|difficultyList }}</span>
        <div>{{ data.uname }}<span v-if="data.uname&&data.orgName"> / </span>{{ data.orgName }}</div>
        <span>ID:{{ data.id }}</span>
      </div>

      <!-- A1,A2,X -->
      <div v-if="data.type===1||data.type===2||data.type===6">
        <div>
          <div class="A_title" v-html="data.aone.title" />
          <ul>
            <li v-for="(item,index) in data.aone.optionsList" :key="index" :class="[{'bingo':item.isAnswer===1}]">
              {{ item.index }}. <span v-html="item.content" />
            </li>
          </ul>
        </div>
        <div class="keyword">考点关键字：<span v-html="data.aone.points" /></div>
        <div class="explain">解析：<span v-html="data.aone.desc" /></div>
      </div>

      <!-- A3,A4 -->
      <div v-else-if="data.type===3||data.type===4">
        <div class="big_title" v-html="data.athree.title" />

        <div v-for="(item,index) in data.athree.aone" :key="index">
          <div class="A_title">{{ index+1 }}. <span v-html="item.title" /></div>
          <ul>
            <li v-for="(v,i) in item.optionsList" :key="i" :class="[{'bingo':v.isAnswer===1}]">
              {{ v.index }}. <span v-html="v.content" />
            </li>
          </ul>
          <div class="keyword">考点关键字：<span v-html="item.points" /></div>
          <div class="explain">解析：<span v-html="item.desc" /></div>
        </div>
      </div>

      <!-- B1 -->
      <div v-else-if="data.type===5">
        <ul class="B1_ul">
          <li v-for="(v,i) in data.bone.optionsList" :key="i" :class="[{'bingo':v.isAnswer===1}]">
            {{ v.index }}. <span v-html="v.content" />
          </li>
        </ul>

        <div v-for="(item,index) in data.bone.aOneList" :key="index">
          <div class="B1_title">{{ index+1 }}.
            <span v-html="item.title" />
            <span v-for="(v,i) in item.optionsList" v-show="v.isAnswer===1" :key="i" class="bingo">{{ v.index }}</span>
          </div>
          <div class="keyword">考点关键字：<span v-html="item.points" /></div>
          <div class="explain">解析：<span v-html="item.desc" /></div>
        </div>

      </div>

      <!-- 判断 -->
      <div v-else-if="data.type===7">
        <div class="A_title" v-html="data.judge.title" />
        <ul>
          <li v-for="(item,index) in data.judge.optionsList" :key="index" :class="[{'bingo':item.isAnswer===1}]" v-html="item.content" />
        </ul>
        <div class="keyword">考点关键字：<span v-html="data.judge.points" /></div>
        <div class="explain">解析：<span v-html="data.judge.desc" /></div>
      </div>

      <!-- 选择填空 -->
      <div v-else-if="data.type===8">
        <div class="A_title" v-html="data.gap.title" />
        <!-- 选项 -->
        <ul>
          <li v-for="(item,index) in data.gap.optionsList" :key="index">
            {{ item.index }}. <span v-html="item.content" />
          </li>
        </ul>
        <div class="A_title">正确答案：</div>
        <!-- 正确答案组 -->
        <ul>
          <li v-for="(item,index) in data.gap.gapGroup.answer" :key="index" class="bingo">
            {{ item.num }}. <span>{{ item.opt }}</span>
          </li>
        </ul>
        <div class="keyword">考点关键字：<span v-html="data.gap.points" /></div>
        <div class="explain">解析：<span v-html="data.gap.desc" /></div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  filters: {
    questionTypeList(val) {
      const arr = ['A1', 'A2', 'A3', 'A4', 'B1', 'X', '判断', '填空']
      return arr[val - 1]
    },
    difficultyList(val) {
      const arr = ['简单', '普通', '困难']
      return arr[val - 1]
    }
  },
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  watch: {
    data: {
      handler(n) {
        if (n && n.type === 8) {
          const group = JSON.parse(JSON.stringify(n.gap.gapGroup.group))
          if (group && group.length > 0) {
            group.forEach(item => {
              const a = item[0]
              const b = item[1]
              const aOpt = n.gap.gapGroup.answer[a - 1].opt
              const bOpt = n.gap.gapGroup.answer[b - 1].opt
              n.gap.gapGroup.answer[a - 1].opt = aOpt + '/' + bOpt
              n.gap.gapGroup.answer[b - 1].opt = bOpt + '/' + aOpt
            })
          }
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  margin: 5px;
}

// 主标题
.title{
  padding: 10px 0 10px 15px;
  font-size: 17px;
  color: #333;
  font-weight: 700;
  font-family: Microsoft YaHei;
  div{
    display: inline;
  }
  span{
    margin-right: 10px;
  }
}

.A_title{
  padding: 10px 0 0 15px;
}

.big_title{
  padding: 0 0 10px 15px;
}

.B1_title{
  padding: 0 0 15px 15px;
}

.keyword{
  width: 100%;
  font-size: 14px;
  padding: 0 10px 16px 32px;
}

.explain{
  font-size: 14px;
  padding: 0 10px 16px 32px;
}

li{
  list-style: none;
  margin-bottom: 5px;
}

// 正确答案
.bingo{
  color: #3399ff;
}

.B1_ul{
  padding-bottom: 10px;
}
</style>
