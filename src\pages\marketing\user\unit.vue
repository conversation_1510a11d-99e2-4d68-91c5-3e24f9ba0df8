<template>
  <div class="app-container">
    <!-- header -->

    <div class="search-column">
      <div class="search-column__item">
        <div class="search-column__inner">
          <el-input v-model="tableQuery.condition.keyword" clearable placeholder="请输入搜索关键字" @clear="searchKeyword()" @keydown.enter.native="searchKeyword()">
            <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKeyword()" />
          </el-input>
        </div>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.type" clearable placeholder="请选择单位类型" @change="handleChangeType">
          <el-option v-for="item in typeList" :key="'t'+item.orgTypeId" :label="item.name" :value="item.orgTypeId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.status" clearable placeholder="请选择服务状态" @change="handleChangeStatus">
          <el-option v-for="item in statusList" :key="'s'+item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <h2>
      单位列表
      <el-button type="primary" @click="addUnitDialogShow()">创建单位</el-button>
    </h2>
    <!-- body -->
    <div class="table">
      <el-table :data="userList" border stripe>
        <el-table-column v-for="col in tableColumnList" :key="'c'+col.id" :prop="col.prop" :label="col.label" :align="col.align">
          <template slot-scope="scope">
            <span v-if="col.prop === 'status'">
              {{ scope.row[col.prop] | filterStatus }}
            </span>
            <span v-else>
              {{ scope.row[col.prop] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="240px">
          <template slot-scope="{row}">
            <el-button size="mini" type="text" @click="setManager(row)">设置管理员</el-button>
            <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
            <el-button size="mini" type="text" @click="setStatusRow(row)">{{ row.status === 0?'启用':'停用' }}</el-button>
            <el-button size="mini" type="text" @click="clickDetail(row)">点击详情设置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- pagination -->
      <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    </div>
    <!-- unit user add/edit -->
    <DialogUnitEdit :title="title" :action="action" :visible.sync="dialogVisible" :data="formData" @handleCancel="handleCancelDialogForm" @handleSave="handleSaveDialogForm" />

    <!-- set user manager -->
    <el-dialog v-loading="loading" :close-on-click-modal="false" title="创建管理员" :visible.sync="dialogSetManagerShow" width="500px" @close="setUserManagerDialogColse">
      <el-form ref="form" :model="formDataManager" label-width="90px" :rules="rules">
        <el-form-item label="手机号">
          <el-input v-model="formDataManager.phone" v-search="searchOption" maxlength="11" />
          <div class="warn-tips">通过手机号查找平台是否已存在用户。如用户已存在，立即成为本单位管理员。如用户不存在，将直接创建该管理员。</div>
        </el-form-item>
        <el-row v-if="hasUser">
          <el-form-item label="姓名" prop="realName">
            <el-input v-model="formDataManager.realName" placeholder="请输入姓名" />
          </el-form-item>
          <!-- 身份选择器 -->
          <el-form-item label="身份" prop="identityId">
            <el-cascader
              v-model="formDataManager.identityId"
              placeholder="请选择身份"
              :options="identityTree"
              :show-all-levels="false"
              :props="{
                value:'identityId',
                label:'name',
                children:'childList',
                leaf: 'leaf',
                emitPath: false
              }"
              @change="identityChange"
            />
          </el-form-item>
          <!-- 职称选择器 -->
          <el-form-item label="职称" prop="academicId">
            <el-cascader
              v-model="formDataManager.academicId"
              placeholder="请选择职称"
              :options="academicList"
              :props="{
                value:'academicId',
                label:'name',
                children:'childList',
                emitPath: false
              }"
              :disabled="disabled"
            />
          </el-form-item>
          <!-- 专科选择器 -->
          <el-form-item label="专科" prop="majorId">
            <el-cascader
              v-model="formDataManager.majorId"
              placeholder="请选择专科"
              :options="majorList"
              :props="{
                value:'majorId',
                label:'name',
                children:'childList',
                emitPath: false,
                checkStrictly: true
              }"
              :disabled="disabled"
            />
          </el-form-item>
          <el-form-item label="工作单位:" prop="orgName">
            <el-input v-model="formDataManager.orgName" :disabled="true" />
          </el-form-item>
          <el-form-item label="部门/科室:" prop="department">
            <el-cascader
              v-model="formDataManager.deptId"
              placeholder="请选择部门/科室"
              :options="deptList"
              :props="{
                value:'departmentId',
                label:'deptName',
                children:'childList',
                emitPath: false,
                checkStrictly: true
              }"
            />
          </el-form-item>
          <el-form-item label="所在地区:" prop="region">
            <el-cascader
              v-model="formDataManager.region"
              placeholder="请选择区域"
              :options="areaList"
              :props="{
                value:'areaId',
                label:'name',
                children:'childList'
              }"
              @change="handleRegion"
            />
          </el-form-item>
          <el-form-item label="擅长:">
            <el-input v-model="formDataManager.skill" />
          </el-form-item>
          <el-form-item label="个人简介:">
            <el-input
              v-model="formDataManager.introduction"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button @click="setUserManagerDialogColse">取 消</el-button>
        <el-button :disabled="!submitDisable" type="primary" @click="handleSetManagerSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- get user manager -->
    <el-dialog title="设置单位管理员" :visible.sync="dialogGetManagerShow" width="800px">
      <div class="mg-b">
        <el-button size="mini" type="primary" @click="showCreateManager()">创建管理员</el-button>
      </div>

      <el-table :data="userManagerList" border stripe>
        <el-table-column v-for="col in userManageTableColumn" :key="'u'+col.id" :prop="col.prop" :label="col.label" :align="col.align" />
        <el-table-column label="操作" align="center">
          <template slot-scope="{row}">
            <el-button type="text" @click="quickLogin(row.safeCode)">一键登录</el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-dialog>

    <ClickDetail :id="curOrgId" :dialog-visible.sync="clickDetailDialogVisible" />
  </div>
</template>

<script>
import {
  getUnitList,
  getUnitDetail,
  addUnit,
  editUnit,
  unitStatusOption,
  setUnitManager,
  getOrganTypeList,
  getOrganAdminListByOrgId,
  invitOrgAdminStaffFind,
  addUnitManager,
  addUnitManagerWithoutStaffInfo
} from '@/api/userManage'
import { easyLogin } from '@/api/user'
import Pagination from '@/components/Pagination'
import DialogUnitEdit from '@/pages/user/compontents/dialogUnitEdit'
import { identityTreeList, majorTreeListId } from '@/api/category' // 身份树，随身份联动的专科树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树
import { getDeptTree } from '@/api/dept' // 职称树
import { getAreaTree } from '@/api/area' // 区域树
import ClickDetail from './components/clickDetail'

export default {
  name: 'Unit',
  components: {
    Pagination,
    DialogUnitEdit,
    ClickDetail
  },
  filters: {
    filterStatus(val) {
      return val ? '启用' : '停用'
    }
  },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // loading
      loading: false,
      // 通过检验
      approved: false,
      // 提交按钮
      // submitDisable: true,
      // 手机号校验
      searchOption: {
        fn: this.invitOrgAdminStaffFind
      },
      // 事件行为
      action: 'add',
      clickDetailDialogVisible: false,
      // 获取管理员列表
      dialogGetManagerShow: false,
      // 设置管理员列表
      dialogSetManagerShow: false,
      dialogVisible: false,
      title: '创建单位',
      // 单位列表表头
      tableColumnList: [
        { id: 0, label: '单位ID', prop: 'orgId', align: 'center' },
        { id: 1, label: '单位名称', prop: 'orgName', align: 'center' },
        { id: 2, label: '单位类型', prop: 'typeName', align: 'center' },
        { id: 6, label: '服务状态', prop: 'status', align: 'center' },
        { id: 7, label: '创建时间', prop: 'createTime', align: 'center' }
      ],
      // 单位列表
      userList: [],
      // 类型
      typeList: [],
      typeFilterList: [
        { type: 183, name: '医院' },
        { type: 9, name: '卫计委' },
        { type: 8, name: '平台管理机构' }
      ],
      // 状态
      statusList: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 }
      ],
      // 请求参数
      tableQuery: {
        condition: {
          isPromote: 1,
          keyword: null,
          status: null,
          type: null
        },
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 是否存在用户
      hasUser: false,
      // 单位管理员列表表头
      userManageTableColumn: [
        { id: 0, label: '手机号', prop: 'userPhone', align: 'center' },
        { id: 1, label: '姓名', prop: 'realName', align: 'center' },
        { id: 2, label: '用户账号', prop: 'userAccounts', align: 'center' },
        { id: 3, label: '员工账号', prop: 'username', align: 'center' }
      ],
      userManagerList: [], // 单位管理员列表
      // 设置管理员表单
      formDataManager: {
        region: [],
        realName: '',
        phone: '',
        academicId: '',
        areaId: '',
        cityId: '',
        provinceId: '',
        orgName: '',
        deptId: '',
        identityId: '',
        majorId: '',
        orgId: 0,
        userId: 0,
        skill: '',
        introduction: ''
      },
      rules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        identityId: [
          { required: true, message: '请选择身份', trigger: 'change' }
        ],
        majorId: [
          { required: true, message: '请选择执业专科', trigger: 'change' }
        ],
        academicId: [
          { required: true, message: '请选择职称', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择所在地区', trigger: 'change' }
        ]
      },
      identityTree: [],
      majorList: [],
      academicList: [],
      deptList: [],
      areaList: [],
      disabled: false,
      curUserId: '', // 当前用户id
      curOrgId: '', // 当前单位id
      // 新增/编辑单位表单
      formData: {
        createTime: '',
        createUid: 0,
        level: null,
        maxStaffCount: 0,
        maxSizeCount: 0,
        orgId: 0,
        orgName: '',
        parentOrgId: 0,
        parentOrgIds: 0,
        status: null,
        type: '',
        shortCode: '',
        contacts: '',
        phone: '',
        provinceId: null,
        cityId: null,
        areaId: null,
        address: '',
        scale: '',
        logo: '',
        logoUrl: '',
        subjuctCode: '',
        faceFlag: null,
        examFlag: null,
        bannerId: null,
        bannerUrl: '',
        trainBannerId: null,
        trainBannerUrl: '',
        serviceProviderOrgId: null,
        socialCreditCode: null,
        visitScope: null,
        statisticsLimit: 0,
        keySecret: {
          key: '',
          secret: ''
        },
        sceneCode: {
          creatorCode: '',
          visitCode: ''
        }
      },
      // 提交表单
      submitFormData: {
        enterDto: {
          address: '',
          buyVideoSize: 0,
          contacts: '',
          industryId: '',
          introdution: '',
          maxSizeCount: 0,
          maxStaffCount: 0,
          phone: '',
          scale: ''
        },
        provinceId: null,
        cityId: null,
        areaId: null,
        level: 0,
        status: 0,
        orgName: '',
        shortCode: '',
        type: 0
      },
      // 分页
      total: 0,
      // 校验手机获取的内容
      currentStaffInfo: {},
      openYKTorgId: '',
      openYKTorgName: '',
      quesTree: [],
      quesTreeProps: {
        children: 'children',
        label: 'title'
      },
      checkedTitles: [],
      lookYKTdialog: false,
      yktTableQuery: {
        condition: {
          bankName: '',
          orgId: '',
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      alreadyIds: []
    }
  },
  computed: {
    submitDisable() {
      if (this.hasUser) {
        return !!(this.formDataManager.realName)
      } else {
        return !!(this.formDataManager.phone && this.approved)
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    quesTreeChange() {
      this.checkedTitles = []
      this.$refs.quesTree.getCheckedNodes().forEach(item => {
        if (!item.children) {
          this.checkedTitles.push(item.title)
        }
      })
    },
    // hard code..
    filterType(val) {
      let typeName = ''
      this.typeFilterList.forEach(v => {
        if (v.type === val) {
          typeName = v.name
        }
      })
      return typeName
    },
    // quick login
    async quickLogin(safeCode) {
      const res = await easyLogin({
        safeCode,
        actionCode: 'UnitIndex' + 'easyLogin'
      })
      const newWindow = window.open()
      if (res) {
        const params = encodeURIComponent(JSON.stringify(res))
        newWindow.location.href = process.env.VUE_APP_SAAS_URL + '#/home?p=' + params
      }
    },
    // checkNumberCode
    filterCheckPhoneCode(code) {
      const codeToMessage = {
        1: '手机号用户不存在，创建成为单位管理员',
        2: '手机号用户是本单位员工，确认后添加为单位管理员',
        3: '手机号用户已存在，编辑确认后添加为单位管理员',
        4: '该手机用户正在导入中，无法新增'
      }
      return codeToMessage[code]
    },
    // init
    init() {
      this.handleGetOrganTypeList()
      this.getUnitUserList()
      // 获取身份树
      identityTreeList().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        newArr.push({ name: '无', identityId: '0' })
        this.identityTree = newArr
      })
      // 获取专科树
      treeList().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        newArr.push({ name: '无', majorId: '0' })
        this.majorList = newArr
      })
      // 获取区域树
      getAreaTree().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.areaList = newArr
      })
      if (this.$route.query.id) {
        this.getDateil()
      }
    },
    handleRegion() {
      this.formDataManager.provinceId = this.formDataManager.region[0]
      this.formDataManager.cityId = this.formDataManager.region[1]
      this.formDataManager.areaId = this.formDataManager.region[2]
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      if (e && e === '0') {
        // 身份选'无'，专科职称也为无
        this.formDataManager.majorId = '0'
        this.formDataManager.academicId = '0'
        this.disabled = true
        return
      } else {
        this.disabled = false
        majorTreeListId(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: '0' })
          this.majorList = newArr
        })
        academicTreeListById(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: '0' })
          this.academicList = newArr
        })
      }
    },
    // search
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getUnitUserList()
    },
    // service status
    handleChangeStatus() {
      this.getUnitUserList()
    },
    handleChangeType() {
      this.getUnitUserList()
    },
    // get type list
    handleGetOrganTypeList() {
      getOrganTypeList({ action: 2 }).then(res => {
        this.typeList = res
      })
    },
    // get unit user
    getUnitUserList() {
      getUnitList(this.tableQuery).then(res => {
        this.userList = res.records
        this.total = res.total
      })
    },
    // add unit user
    addUnitDialogShow() {
      this.action = 'add'
      this.title = '创建单位'
      this.dialogVisible = true
    },
    addUnitUser() {
      addUnit(this.submitFormData).then(res => {
        this.$message.success('添加成功!')
        this.dialogVisible = false
        this.getUnitUserList()
        this.clearUnitSubmitForm()
      })
    },
    // set unit manager
    setManager(row) {
      this.formDataManager.orgId = row.orgId
      this.curOrgId = row.orgId
      getOrganAdminListByOrgId(row.orgId).then(res => {
        this.userManagerList = res.records
        this.dialogGetManagerShow = true
      })
    },
    // edit uint user
    editRow(row) {
      getUnitDetail(row.orgId).then(res => {
        this.formData.createTime = res.createTime
        this.formData.createUid = res.createUid
        this.formData.areaId = res.areaId
        this.formData.provinceId = res.provinceId
        this.formData.cityId = res.cityId
        this.formData.address = res.enterDto.address
        this.formData.buyVideoSize = res.enterDto.buyVideoSize
        this.formData.contacts = res.enterDto.contacts
        this.formData.industryId = res.enterDto.industryId
        this.formData.maxSizeCount = res.enterDto.maxSizeCount
        this.formData.maxStaffCount = res.enterDto.maxStaffCount
        this.formData.phone = res.enterDto.phone
        this.formData.scale = res.enterDto.scale
        this.formData.logo = res.enterDto.logo
        this.formData.logoUrl = res.enterDto.logoUrl
        this.formData.bannerId = res.enterDto.bannerId
        this.formData.bannerUrl = res.enterDto.bannerUrl
        this.formData.trainBannerId = res.enterDto.trainBannerId
        this.formData.trainBannerUrl = res.enterDto.trainBannerUrl
        this.formData.subjuctCode = res.enterDto.subjuctCode || 'green'
        this.formData.serviceProviderOrgId = res.enterDto.serviceProviderOrgId
        this.formData.socialCreditCode = res.enterDto.socialCreditCode
        this.formData.visitScope = res.enterDto.visitScope
        this.formData.statisticsLimit = res.enterDto.statisticsLimit
        this.formData.keySecret = res.enterDto.keySecret
        this.formData.sceneCode = res.enterDto.sceneCode
        this.formData.level = res.level
        this.formData.orgId = res.orgId
        this.formData.orgName = res.orgName
        this.formData.parentOrgId = res.parentOrgId
        this.formData.shortCode = res.shortCode
        this.formData.status = res.status
        this.formData.type = res.type
        this.formData.faceFlag = res.faceFlag
        this.formData.examFlag = res.examFlag
        this.formData.conExtenderLiveFlag = res.conExtenderLiveFlag

        // show dialog
        this.action = 'edit'
        this.title = '编辑单位'
        this.dialogVisible = true
      })
    },
    editUnitUser() {
      editUnit(this.submitFormData).then(res => {
        this.$message.success('编辑成功!')
        this.dialogVisible = false
        this.getUnitUserList()
        this.clearUnitSubmitForm()
      })
    },
    // 创建管理员
    showCreateManager() {
      this.dialogSetManagerShow = true
    },
    // set unit user status
    setStatusRow(row) {
      this.$confirm(
        '您确认' +
          (row.status ? '停用' : '启用') +
          ' "' +
          row.orgName +
          '" 这个单位吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      ).then(() => {
        unitStatusOption(row.orgId).then(res => {
          if (row.status) {
            row.status = 0
            this.$message.success('停用成功！')
          } else {
            row.status = 1
            this.$message.success('启用成功！')
          }
          this.getUnitUserList()
        })
      })
    },
    clickDetail(row) {
      this.curOrgId = row.orgId
      this.clickDetailDialogVisible = true
    },
    // cancel form
    handleCancelDialogForm() {
      this.clearUnitSubmitForm()
      this.dialogVisible = false
    },
    // save form
    handleSaveDialogForm(val) {
      if (this.action === 'add') {
        this.submitFormData.enterDto.address = val.address
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.maxSizeCount = val.maxSizeCount
        this.submitFormData.enterDto.maxStaffCount = val.maxStaffCount
        this.submitFormData.enterDto.phone = val.phone === '' ? null : val.phone
        this.submitFormData.enterDto.logo = val.logo || 0
        this.submitFormData.enterDto.bannerId = val.bannerId || 0
        this.submitFormData.enterDto.trainBannerId = val.trainBannerId || 0
        this.submitFormData.enterDto.subjuctCode = val.subjuctCode
        this.submitFormData.enterDto.faceFlag = val.faceFlag
        this.submitFormData.enterDto.examFlag = val.examFlag
        this.submitFormData.enterDto.serviceProviderOrgId = val.serviceProviderOrgId
        this.submitFormData.enterDto.socialCreditCode = val.socialCreditCode
        this.submitFormData.enterDto.visitScope = val.visitScope
        this.submitFormData.enterDto.statisticsLimit = val.statisticsLimit
        this.submitFormData.enterDto.keySecret = val.keySecret || ''
        this.submitFormData.enterDto.sceneCode = val.sceneCode || ''
        this.submitFormData.provinceId = val.provinceId
        this.submitFormData.cityId = val.cityId
        this.submitFormData.areaId = val.areaId
        this.submitFormData.level = val.level
        this.submitFormData.orgName = val.orgName
        this.submitFormData.shortCode = val.shortCode
        this.submitFormData.type = val.type
        this.submitFormData.status = val.status
        this.addUnitUser()
      } else if (this.action === 'edit') {
        this.submitFormData.enterDto.address = val.address
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.maxSizeCount = val.maxSizeCount
        this.submitFormData.enterDto.maxStaffCount = val.maxStaffCount
        this.submitFormData.enterDto.buyVideoSize = val.buyVideoSize
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.introdution = val.introdution
        this.submitFormData.enterDto.industryId = val.industryId
        this.submitFormData.enterDto.phone = val.phone === '' ? null : val.phone
        this.submitFormData.enterDto.scale = val.scale
        this.submitFormData.enterDto.logo = val.logo || 0
        this.submitFormData.enterDto.bannerId = val.bannerId || 0
        this.submitFormData.enterDto.trainBannerId = val.trainBannerId || 0
        this.submitFormData.enterDto.subjuctCode = val.subjuctCode
        this.submitFormData.enterDto.serviceProviderOrgId = val.serviceProviderOrgId
        this.submitFormData.enterDto.socialCreditCode = val.socialCreditCode
        this.submitFormData.enterDto.visitScope = val.visitScope
        this.submitFormData.enterDto.statisticsLimit = val.statisticsLimit
        this.submitFormData.enterDto.keySecret = val.keySecret
        this.submitFormData.enterDto.sceneCode = val.sceneCode || ''
        this.submitFormData.provinceId = val.provinceId
        this.submitFormData.cityId = val.cityId
        this.submitFormData.areaId = val.areaId
        this.submitFormData.level = val.level
        this.submitFormData.orgName = val.orgName
        this.submitFormData.shortCode = val.shortCode
        this.submitFormData.type = val.type
        this.submitFormData.status = val.status
        this.submitFormData.orgId = val.orgId
        this.submitFormData.faceFlag = val.faceFlag
        this.submitFormData.examFlag = val.examFlag
        this.editUnitUser()
      }
    },
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUnitUserList()
    },
    // 设置管理员
    handleSetManagerSubmit() {
      // 无任何信息
      if (this.currentStaffInfo.code === 1) {
        // 新增
        addUnitManager({ ...this.formDataManager, orgId: this.curOrgId }).then(
          res => {
            this.$message.success('新增管理员成功！')
            getOrganAdminListByOrgId(this.curOrgId).then(res => {
              this.userManagerList = res.records
            })
            this.dialogSetManagerShow = false
            this.hasUser = false
          }
        )
      } else if (this.currentStaffInfo.code === 2) {
        // 存在成员信息
        // 设置
        this.$confirm(
          '是否设置“' +
            this.currentStaffInfo.staffDto.username +
            '”为单位管理员？',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          setUnitManager({
            ...this.formDataManager,
            orgId: this.curOrgId,
            staffId: this.currentStaffInfo.staffDto.staffId
          }).then(res => {
            this.$message.success('设置管理员成功！')
            getOrganAdminListByOrgId(this.curOrgId).then(res => {
              this.userManagerList = res.records
            })
            this.dialogSetManagerShow = false
            this.hasUser = false
          })
        })
      } else if (this.currentStaffInfo.code === 3) {
        // 有用户信息,无成员信息
        this.formDataManager.userId = this.currentStaffInfo.staffDto.userId
        this.curUserId = this.currentStaffInfo.staffDto.userId
        addUnitManagerWithoutStaffInfo({
          ...this.formDataManager,
          orgId: this.curOrgId
        }).then(res => {
          this.$message({
            type: 'success',
            message: '邀请成员成功！'
          })
          this.dialogSetManagerShow = false
          this.dialogGetManagerShow = false
          this.hasUser = false
        })
      }
    },
    setUserManagerDialogColse() {
      this.dialogSetManagerShow = false
      this.hasUser = false
      this.clearDialogGetManagerForm()
    },
    // 手机号校验
    invitOrgAdminStaffFind() {
      if (!this.formDataManager.phone) {
        return this.$message.error('请输入手机号')
      }
      this.loading = true
      const params = {
        orgId: this.curOrgId,
        phone: this.formDataManager.phone
      }
      getDeptTree(params).then(res => {
        this.deptList = res
      })
      invitOrgAdminStaffFind(params)
        .then(res => {
          this.loading = false
          this.currentStaffInfo = res
          if ([1, 2, 3].includes(res.code)) {
            this.$message.success(this.filterCheckPhoneCode(res.code))
            this.hasUser = true
            this.formDataManager.region = [
              res.orgInfo.provinceId,
              res.orgInfo.cityId,
              res.orgInfo.areaId
            ]
            this.handleRegion()
            this.formDataManager.orgName = res.orgInfo.orgName
            this.formDataManager.orgId = res.orgInfo.orgId
            if ([2, 3].includes(res.code)) {
              this.formDataManager.identityId = res.staffDto.identityId.toString()
              this.formDataManager.majorId = res.staffDto.majorId.toString()
              this.formDataManager.realName = res.staffDto.realName
              this.formDataManager.academicId = res.staffDto.academicId
              this.formDataManager.deptId = res.staffDto.deptId.toString()
              this.identityChange(this.formDataManager.identityId.toString())
            }
          } else {
            this.$message.success(this.filterCheckPhoneCode(res.code))
          }
          this.approved = true
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 清空表单
    clearDialogGetManagerForm() {
      this.formDataManager = {
        region: [],
        realName: '',
        phone: '',
        academicId: '',
        areaId: '',
        cityId: '',
        provinceId: '',
        orgName: '',
        deptId: '',
        identityId: '',
        majorId: '',
        orgId: 0,
        userId: 0,
        skill: '',
        introduction: ''
      }
      this.approved = false
    },
    // 清空表单
    clearUnitSubmitForm() {
      const formdata = this.formData
      formdata.createTime = ''
      formdata.createUid = 0
      formdata.level = null
      formdata.maxStaffCount = 0
      formdata.maxSizeCount = 0
      formdata.orgId = 0
      formdata.orgName = ''
      formdata.parentOrgId = 0
      formdata.parentOrgIds = 0
      formdata.status = null
      formdata.type = ''
      formdata.shortCode = ''
      formdata.contacts = ''
      formdata.phone = ''
      formdata.provinceId = null
      formdata.cityId = null
      formdata.areaId = null
      formdata.address = ''
      formdata.scale = ''
      formdata.logo = ''
      formdata.logoUrl = ''
      formdata.subjuctCode = ''
      formdata.faceFlag = null
      formdata.examFlag = null
      formdata.bannerId = null
      formdata.bannerUrl = ''
      formdata.trainBannerId = null
      formdata.trainBannerUrl = ''
      formdata.serviceProviderOrgId = null
      formdata.socialCreditCode = null
      formdata.visitScope = null
      formdata.statisticsLimit = 0
      formdata.keySecret = { key: '', secret: '' }
      formdata.sceneCode = { creatorCode: '', visitCode: '' }
    }
  }
}
</script>

<style lang="scss" scoped>
.warn-tips {
  padding: 5px 0;
  font-size: 12px;
  line-height: 16px;
  color: #666;
}
h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
</style>
