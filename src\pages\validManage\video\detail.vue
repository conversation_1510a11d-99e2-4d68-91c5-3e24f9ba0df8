<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">{{ auditDetail.name }}</header>
    </div>
    <!-- body -->
    <div class="video-detail">
      <h3 class="detail-title">视频名称：{{ auditDetail.name }}</h3>
      <div class="detail-content">
        <!-- player -->
        <ali-player
          ref="aliplayer"
          :play-style="aliPlayerConfig.playStyle"
          :source="aliPlayerConfig.source"
          :cover="aliPlayerConfig.cover"
          :height="aliPlayerConfig.height"
          :skin-layout="aliPlayerConfig.skinLayout"
          @ready="handleReadyVideo"
          @pause="handlePauseVideo"
          @error="handleError"
        />
        <!-- paper -->
        <el-collapse v-model="paperNames" accordion @change="handleChangeCollapse">
          <el-collapse-item
            v-for="(paper, paperIndex) in auditDetail.testPaper"
            :key="paper.paperId"
            :name="paper.paperId"
          >
            <template slot="title">
              <div class="paper-title" :class="paper.view? 'bg-success':''">考卷{{ paperIndex | filtersIndex }}：{{ paper.name }}
                <i v-if="paper.view" class="el-icon-success ic-success fr">已查阅</i>
              </div>
            </template>
            <!-- content -->
            <div
              v-for="(test, testIndex) in paper.webTestPaperGroupResponseDto"
              :key="test.id"
              class="test-content"
            >
              <div class="test-title">试题{{ testIndex | filtersIndex }}: {{ test.name }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;插入时间：{{ test.timeSpot | parseSeconds }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;答题时间：{{ test.answerTime }}秒</div>
              <div v-for="item in test.webTestPaperDtos" :key="item.id" class="test-item">
                <div class="item-title">试题名称：{{ item.name }}</div>
                <div class="item-content">
                  <div v-for="option in item.webPaperItemDtos" :key="option.id" class="option">
                    {{ option.order | filtersEnIndex }}、{{ option.content }} <i v-if="option.answerStatus" class="el-icon-success" />
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <div class="lecturer">
      <h3>讲师</h3>
      <div>{{ doctorInfo.realName }}</div>
      <div>{{ doctorInfo.company }}&nbsp;{{ doctorInfo.department }}</div>
    </div>

    <div class="introduction">
      <h3>视频简介</h3>
      <div>{{ auditDetail.description || '无' }}</div>
    </div>
    <!-- bottom button -->
    <div class="fixed-btn-group">
      <div class="group-content">
        <el-button type="default" @click="handleValidVideo(0)">驳回</el-button>
        <el-button :disabled="btnComfirmDisabled" type="primary" @click="handleValidVideo(1)">通过</el-button>
      </div>
    </div>
    <!-- dialog -->
    <el-dialog
      :title="dialogValidVideo.title"
      :visible.sync="dialogValidVideo.visible"
      width="600px"
      @close="handleCloseDialog()"
    >
      <el-form ref="form" :model="validVideoFrom" label-width="120px">
        <el-form-item label="视频名称">
          <span>{{ auditDetail.name }}</span>
        </el-form-item>
        <el-form-item label="视频说明">
          <span>{{ auditDetail.description }}</span>
        </el-form-item>
        <el-form-item label="视频权限">
          <span>{{ auditDetail.extPermission | filterExtPermission }}</span>
        </el-form-item>
        <el-form-item label="创建者名称">
          <span>{{ auditDetail.author }}</span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="validVideoFrom.opinion" type="textarea" maxlength="30" placeholder="请输入审核理由" />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="handleCloseDialog()">取 消</el-button>
        <el-button type="primary" @click="handleSubmitFrom()">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import AliPlayer from '@/components/Aliplayer/index'
import { videoValidDetail, videoValidate } from '@/api/validManage'
import { parseSeconds, filtersIndex, filterExtPermission, filtersEnIndex } from '@/utils'
export default {
  name: 'VideoDetail',
  components: {
    AliPlayer
  },
  filters: {
    parseSeconds,
    filtersIndex,
    filterExtPermission,
    filtersEnIndex
  },
  data() {
    return {
      paperNames: [],
      // 已查看的内容
      viewList: [],
      // 按钮
      btnComfirmDisabled: true,
      // 阿里云配置
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            'name': 'bigPlayButton',
            'align': 'cc'
          },
          {
            'name': 'H5Loading',
            'align': 'cc'
          },
          {
            'name': 'errorDisplay',
            'align': 'tlabs',
            'x': 0,
            'y': 0
          },
          {
            'name': 'infoDisplay'
          },
          {
            'name': 'tooltip',
            'align': 'blabs',
            'x': 0,
            'y': 56
          },
          {
            'name': 'controlBar',
            'align': 'blabs',
            'x': 0,
            'y': 0,
            'children': [
              {
                'name': 'progress',
                'align': 'blabs',
                'x': 0,
                'y': 44
              },
              {
                'name': 'playButton',
                'align': 'tl',
                'x': 15,
                'y': 12
              },
              {
                'name': 'timeDisplay',
                'align': 'tl',
                'x': 10,
                'y': 7
              },
              {
                'name': 'fullScreenButton',
                'align': 'tr',
                'x': 10,
                'y': 12
              },
              {
                'name': 'volume',
                'align': 'tr',
                'x': 5,
                'y': 10
              }
            ]
          }
        ]
      },
      // 审核详情
      auditDetail: {},
      // 通过/驳回
      dialogValidVideo: {
        visible: false,
        title: '确认驳回审核？'
      },
      // 提交审核表单
      validVideoFrom: {
        opinion: '',
        result: null, // 0--不通过 1--通过
        videoId: '',
        videoInfoId: ''
      },
      doctorInfo: {}
    }
  },
  created() {
    const videoInfoId = this.$route.params.videoInfoId
    videoValidDetail({ videoInfoId: videoInfoId }).then(res => {
      if (JSON.stringify(res.doctorInfo) === '{}') {
        const obj = {
          company: '',
          department: '',
          realName: '无'
        }
        this.doctorInfo = obj
      } else {
        this.doctorInfo = res.doctorInfo
      }
      this.auditDetail = res
      if (res.testPaper.length) {
        res.testPaper.forEach(v => {
          v.view = false
        })
      } else {
        this.btnComfirmDisabled = false
      }
      const sourceURL = {}
      setTimeout(() => {
        if (this.auditDetail.videoUrl.length) {
          this.auditDetail.videoUrl.reverse().map(v => {
            sourceURL[v.definition] = v.playURL
          })
          this.aliPlayerConfig.source = JSON.stringify(sourceURL)
          this.$refs.aliplayer.init()
        } else {
          this.$message.error('视频源获取失败，请刷新重试！')
        }
      }, 0)
    })
  },
  methods: {
    // 点击查看时
    handleChangeCollapse(val) {
      if (val) {
        if (!this.viewList.includes()) {
          this.viewList.push(val)
          this.auditDetail.testPaper.forEach(v => {
            if (v.paperId.toString() === val) {
              v.view = true
            }
          })
          if (this.viewList.length === this.auditDetail.testPaper.length) {
            this.btnComfirmDisabled = false
          } else {
            this.btnComfirmDisabled = true
          }
        }
      }
    },
    // 通过/驳回
    handleValidVideo(type) {
      if (type) {
        this.dialogValidVideo.title = '确认通过审核？'
      } else {
        this.dialogValidVideo.title = '确认驳回审核？'
      }
      this.validVideoFrom.videoId = this.auditDetail.videoId
      this.validVideoFrom.videoInfoId = this.auditDetail.videoInfoId
      this.validVideoFrom.result = type
      this.dialogValidVideo.visible = true
    },
    handleCloseDialog() {
      this.dialogValidVideo.visible = false
    },
    handleSubmitFrom() {
      videoValidate(this.validVideoFrom).then(res => {
        let str = ''
        if (this.validVideoFrom.result) {
          str = '已通过审核！'
        } else {
          str = '已驳回审核！'
        }
        this.$message.success(str)
        setTimeout(() => {
          this.$router.go(-1)
        }, 300)
      })
    },
    // 阿里云播放器事件
    handleReadyVideo(val) {
      this.player = val
    },
    handlePauseVideo() {
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  height: calc(100vh - 130px);
  overflow-y: auto;
  overflow-x: hidden;

  .lecturer {
    margin-bottom: 20px;
    h3 {
      border-left: 2px solid #333;
      padding-left: 10px;
    }
  }
  .introduction {
    margin-bottom: 20px;

    h3 {
      border-left: 2px solid #333;
      padding-left: 10px;
    }
  }
}
  .bg-success{
    background: rgba($color: #67C23A, $alpha: 0.4);
  }
  .ic-success{
    font-size: 14px;
    line-height: 50px;
    color: #67C23A;
  }
  .video-detail{
    border: 1px solid #e4e4e4;
    margin: 0 auto 100px;
    width: 800px;
    .detail-title{
      margin: 0;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 600;
      line-height: 50px;
      background: #f2f5f9;
    }
    .paper-title{
      padding: 0 10px;
      width: 100%;
    }
    .test-content{
      background: #f2f5f9;
      .test-title, .test-item{
        padding: 0 10px;
      }
      .test-item{
        background: #fff;
      }
      .test-title, .item-title{
        border-bottom: 1px solid #e4e4e4;
        font-size: 14px;
        line-height: 40px;
      }
       .item-title{
         border-bottom: 0;
       }
      .item-content{
        text-indent: 20px;
        .option{
          font-size: 14px;
          line-height: 30px;
        }
      }
    }
  }
  .fixed-btn-group{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    .group-content{
      padding: 20px 0;
      text-align: center;
      background: #fff;
    }
  }
</style>
