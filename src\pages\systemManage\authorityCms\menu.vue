<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column-item fr">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>

    <a-table row-key="permissionId" :tree-props="{children: 'childList'}" :columns="columns" fit :data="tableData" border stripe>
      <template slot="type" slot-scope="{row}">
        <span>{{ row.type|typeFmt }}</span>
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.status|statusFmt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="!row.status" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button v-if="row.status" type="text" @click="deactivateRow(row)">停用</el-button>
        <el-button v-else type="text" @click="activateRow(row)">启用</el-button>
      </template>
    </a-table>

  </div>
</template>

<script>
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件
import request from '@/api/menuCms'

export default {
  name: 'Menu',
  components: { ATable },
  filters: {
    typeFmt(v) {
      const arr = ['', '菜单', '操作']
      return arr[v]
    },
    statusFmt(v) {
      const arr = ['停用', '启用']
      return arr[v]
    }
  },
  data() {
    return {
      columns: [
        { props: { prop: 'permissionId', label: 'ID', width: '210px', align: 'left' }},
        { props: { prop: 'name', label: '名称', align: 'center' }},
        { props: { prop: 'type', label: '类型', width: '70px', align: 'center' }, slot: 'type' },
        { props: { prop: 'listOrder', label: '排序号', width: '140px', align: 'center' }},
        { props: { prop: 'uri', label: 'URL', width: '50px', align: 'center' }},
        { props: { prop: 'code', label: 'code', align: 'center' }},
        { props: { prop: 'status', label: '状态', width: '70px', align: 'center' }, slot: 'status' },
        { props: { prop: 'description', label: '说明', align: 'center' }},
        { props: { prop: 'uname', label: '修改人', width: '90px', align: 'center' }},
        { props: { prop: 'utime', label: '修改时间', width: '154px', align: 'center' }},
        { props: { label: '操作', width: '140px', align: 'center' }, slot: 'actions' }
      ],
      tableData: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      request.list().then(res => {
        this.tableData = res.records
      })
    },
    add() {
      this.$router.push('cmsMenuAdd')
    },
    editRow(row) {
      this.$router.push({
        path: 'cmsMenuAdd',
        query: {
          id: row.permissionId,
          code: row.code,
          name: row.name,
          description: row.description,
          listOrder: row.listOrder,
          url: row.uri,
          type: row.type,
          pid: row.parentId
        }
      })
    },
    deleteRow(row) {
      request.delete(row.permissionId).then(res => {
        this.$message.success('删除成功')
        this.init()
      })
    },
    deactivateRow(row) {
      this.$confirm('菜单（菜单/操作）停用后，将在系统不可见', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          request.deactivate(row.permissionId).then(res => {
            this.$message.success('停用成功')
            this.init()
          })
        })
        .catch(console.log)
    },
    activateRow(row) {
      request.activate(row.permissionId).then(res => {
        this.$message.success('启用成功')
        this.init()
      })
    }
  }
}
</script>
