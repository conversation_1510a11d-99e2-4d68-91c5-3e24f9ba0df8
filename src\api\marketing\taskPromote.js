import request from '@/utils/request'

// 发布创作任务
export function addTask(params) {
  return request({
    url: '/promoteTask/addTask',
    method: 'post',
    data: params
  })
}
// 删除创作任务
export function delTask(id) {
  return request({
    url: `/promoteTask/deleteTask?id=${id}`,
    method: 'post'
  })
}
// 编辑创作任务
export function editTask(params) {
  return request({
    url: '/promoteTask/editTask',
    method: 'post',
    data: params
  })
}
// 导出创作任务
export function exportTask(params) {
  return request({
    url: '/promoteTask/exportTask',
    method: 'post',
    data: params
  })
}

// 创作任务列表
export function taskList(params) {
  return request({
    url: '/promoteTask/taskList',
    method: 'post',
    data: params
  })
}

// 创作任务详情
export function taskDetail(params) {
  return request({
    url: '/promoteTask/getTaskCms',
    method: 'get',
    params: params
  })
}

// 发布拜访任务
export function addVisitTask(params) {
  return request({
    url: '/promoteVisitTask/addTask',
    method: 'post',
    data: params
  })
}
// 删除拜访任务
export function delVisitTask(id) {
  return request({
    url: `/promoteVisitTask/deleteTask?id=${id}`,
    method: 'post'
  })
}
// 编辑拜访任务
export function editVisitTask(params) {
  return request({
    url: '/promoteVisitTask/editTask',
    method: 'post',
    data: params
  })
}
// 导出拜访任务
export function exportVisitTask(params) {
  return request({
    url: '/promoteVisitTask/exportTask',
    method: 'post',
    data: params
  })
}

// 拜访任务列表
export function visitTaskList(params) {
  return request({
    url: '/promoteVisitTask/taskList',
    method: 'post',
    data: params
  })
}

// 拜访任务详情
export function visitTaskDetail(params) {
  return request({
    url: '/promoteVisitTask/getTaskCms',
    method: 'get',
    params: params
  })
}

// 发布抖喇任务
export function addDoulaTask(params) {
  return request({
    url: '/promoteDoulaTask/addTask',
    method: 'post',
    data: params
  })
}
// 删除抖喇任务
export function delDoulaTask(id) {
  return request({
    url: `/promoteDoulaTask/deleteTask?id=${id}`,
    method: 'post'
  })
}
// 编辑抖喇任务
export function editDoulaTask(params) {
  return request({
    url: '/promoteDoulaTask/editTask',
    method: 'post',
    data: params
  })
}
// 导出抖喇任务
export function exportDoulaTask(params) {
  return request({
    url: '/promoteDoulaTask/exportTask',
    method: 'post',
    data: params
  })
}

// 抖喇任务列表
export function doulaTaskList(params) {
  return request({
    url: '/promoteDoulaTask/taskList',
    method: 'post',
    data: params
  })
}

// 抖喇任务详情
export function doulaTaskDetail(params) {
  return request({
    url: '/promoteDoulaTask/getTaskCms',
    method: 'get',
    params: params
  })
}

// 选择创作者/推广员
export function selectUserPromotes(params) {
  return request({
    url: '/promoteTask/selectUserPromotes',
    method: 'post',
    data: params
  })
}

// 服务商列表
export function serviceProviderList(params) {
  return request({
    url: '/organization/serviceProviderList',
    method: 'get',
    params: params
  })
}

// 下拉产品列表
export function productList(params) {
  return request({
    url: '/promoteProduct/list',
    method: 'get',
    params: params
  })
}

// 获取所有代征平台
export function platformList(params) {
  return request({
    url: '/promoteTaskUser/getAllCollectionPlatforms',
    method: 'get',
    params: params
  })
}

// 获取任务管理配置
export function getPromoteConfig() {
  return request({
    url: '/userPromote/getPromoteConfig',
    method: 'get'
  })
}

// 设置任务管理配置
export function setPromoteConfig(params) {
  return request({
    url: '/userPromote/setPromoteConfig',
    method: 'post',
    data: params
  })
}

// 文章审核列表
export function auditList(params) {
  return request({
    url: '/promoteArticle/articleAuditList',
    method: 'post',
    data: params
  })
}

// 年度额度检测
export function annualQuotaWarning(params) {
  return request({
    url: '/userPromote/annualQuotaWarning',
    method: 'get',
    params: params
  })
}

// 平台推广列表
export function userTaskListCms(params) {
  return request({
    url: '/promotePlatform/userTaskListCms',
    method: 'post',
    data: params
  })
}

// 导出平台推广列表
export function exportPlatformArticles(params) {
  return request({
    url: '/promotePlatform/exportPlatformArticles',
    method: 'post',
    data: params
  })
}

// 删除平台文章
export function deletePlatformArticle(params) {
  return request({
    url: '/promotePlatform/deletePlatformArticle',
    method: 'post',
    params
  })
}

// 发布文章
export function saveOrUpdatePlatformArticleCms(params) {
  return request({
    url: '/promotePlatform/saveOrUpdatePlatformArticleCms',
    method: 'post',
    data: params
  })
}
