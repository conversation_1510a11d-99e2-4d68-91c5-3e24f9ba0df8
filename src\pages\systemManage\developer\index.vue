<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.keyword" placeholder="接入客户" clearable @change="init">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.type" clearable placeholder="请选择接入类型" @change="init">
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.status" clearable placeholder="请选择接入状态" @change="init">
          <el-option v-for="item in statusList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <!-- 预留SDK需求 -->
      <!-- <div class="search-column__item fr">
        <el-button type="primary" @click="add">新增</el-button>
      </div> -->
    </div>
    <!-- table -->
    <a-table ref="multipleTable" :columns="columns" fit :data="list" border stripe>
      <template slot="authType" slot-scope="{row}">
        <div>{{ row.authType | applyAuthType }}</div>
      </template>
      <template slot="createUname" slot-scope="{row}">
        <div>{{ row.createUname || '系统' }}</div>
      </template>
      <template slot="status" slot-scope="{row}">
        <div>{{ row.status | applyStatus }}</div>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button v-if="row.status ===2" size="mini" type="text" @click="handleAccess(row)">处理</el-button>
        <el-button v-if="[0,1,3].includes(row.status)" size="mini" type="text" @click="handleStatus(row)">{{ [1,3].includes(row.status)?'停用':'启用' }}</el-button>
        <el-button v-if="[0,1].includes(row.status)" size="mini" type="text" @click="handleExamine(row)">查看</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="tableQuery.pager.page" @pagination="handlePagination" />

    <!-- 处理弹窗 -->
    <div>
      <el-dialog title="钉钉接入处理" :visible.sync="dialogVisible" width="50%" center @open="handleOpen">
        <div>
          <el-form ref="ruleForm" :model="form" label-width="150px">
            <el-form-item label="选择关联单位：" required>
              <el-input v-model="form.orgName" placeholder="请点击" clearable @focus="handleFocus" />
              <div class="text">
                <i class="el-icon-warning-outline" />通过名称查找单位进行关联，开通钉钉权限；若单位不存在，则先创建单位后再关联单位开通钉钉权限。
              </div>
            </el-form-item>
            <el-form-item label="corpid：">
              <el-input v-model="form.channelOrgId" style="color: #333;font-weight: bold" disabled />
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleEnter">确 定</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 查看 -->
    <div class="dialog-check">
      <el-dialog title="" :visible.sync="checkDialogVisible" width="25%" center>
        <div class="demo-input-suffix">
          <span>单位：{{ see.orgName }}</span>
        </div>
        <div v-if="isShow">
          <div class="demo-input-suffix align">
            <span>AppID：{{ see.clientId }}</span>
            <el-button type="text" @click="copyText(see.clientId)">复制</el-button>
          </div>
          <div class="demo-input-suffix align">
            <span>秘钥：{{ see.secret }}</span>
            <el-button type="text" @click="copyText(see.secret)">复制</el-button>
          </div>
        </div>
        <div v-else>
          <div class="demo-input-suffix align">
            <span>cropid：{{ see.channelOrgId }}</span>
            <el-button type="text" @click="copyText(see.channelOrgId)">复制</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
    <!-- 单位弹窗 -->
    <div>
      <searchOrg :show="showSearchDialog" :current="currentRow" @confirm="handleConfirm" @close="close" />
    </div>
  </div>
</template>
<script>
import {
  developersList,
  enabledisable,
  developersDetail,
  disposeBefore,
  dispose
} from '@/api/developer'
import Pagination from '@/components/Pagination/index.vue' // Secondary package based on el-pagination
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件
import searchOrg from './components/searchOrg.vue'

const columns = [
  { props: { label: '接入类型', align: 'center' }, slot: 'authType' },
  { props: { label: '接入客户', align: 'center', prop: 'orgName' }},
  { props: { label: '客户类型', align: 'center', prop: 'typeName' }},
  { props: { label: '客户地区', align: 'center', prop: 'area' }},
  { props: { label: '接入状态', align: 'center' }, slot: 'status' },
  { props: { label: '创建人', align: 'center' }, slot: 'createUname' },
  { props: { label: '创建时间', align: 'center', prop: 'createTime' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'DeveloperIndex',
  components: {
    ATable,
    Pagination,
    searchOrg
  },
  filters: {
    applyStatus(val) {
      let index = val
      const arr = ['停用', '启用', '待接入']
      if (index === 3) index = 1
      return arr[index]
    },
    applyAuthType(val) {
      const arr = ['常规应用', '机构SDK开发者', '钉钉渠道机构']
      return arr[val - 1]
    }
  },
  data() {
    return {
      columns,
      statusList: [
        { name: '停用', value: 0 },
        { name: '启用', value: 1 },
        { name: '待接入', value: 2 }
      ],
      typeList: [
        { name: '常规应用', value: 1 },
        { name: '机构SDK开发者', value: 2 },
        { name: '钉钉渠道机构', value: 3 }
      ],
      tableQuery: {
        condition: {
          keyword: '',
          status: '',
          type: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      see: {
        authType: '',
        channelOrgId: '',
        clientId: '',
        orgName: '',
        secret: '',
        cropid: ''
      },
      form: {
        orgName: '',
        orgId: '',
        channelOrgId: '',
        oauthClientId: null
      },
      // 列表数据
      list: [],
      total: 0,
      // pagination
      layout: 'total, prev, pager, next, jumper', // 默认分页样式
      queryList: [],

      isShow: false,
      dialogVisible: false, // 待处理弹窗显示隐藏
      checkDialogVisible: false, // 查看弹窗显示隐藏
      showSearchDialog: false,
      currentRow: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    init() {
      this.tableQuery.pager.page = 1
      this.getList()
    },
    getList() {
      developersList(this.tableQuery)
        .then(res => {
          this.list = res.records
          this.total = res.total
        })
        .catch(err => {
          this.$message.error(err)
        })
    },
    handlePagination(v) {
      this.tableQuery.pager = v
      this.getList()
    },
    add() {
      this.$router.push({
        name: 'DeveloperDetail'
      })
    },
    // 是否启用/停用该账号
    handleStatus(row) {
      this.$confirm(
        '是否' + row.status === 0 ? '启用' : '停用' + '该账号',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          enabledisable(row.oauthClientId)
            .then(res => {
              this.init()
            })
            .catch(err => {
              this.$message.error(err)
            })
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },
    handleExamine(row) {
      this.checkDialogVisible = true
      developersDetail(row.oauthClientId)
        .then(res => {
          this.see = res
        })
        .catch(err => {
          this.$message.error(err)
        })
    },

    // ------------处理弹窗事件
    handleOpen() {
      this.form.orgName = ''
      this.form.orgId = '91'
      this.form.channelOrgId = ''
      this.form.oauthClientId = null
      this.currentRow = {}
    },
    // 处理
    handleAccess(row) {
      this.dialogVisible = true
      this.getCorpId(row.oauthClientId)
    },
    // 获取corpid
    async getCorpId(id) {
      await disposeBefore(id).then(res => {
        this.form.channelOrgId = res.channelOrgId
        this.form.oauthClientId = res.oauthClientId
      }).catch(err => {
        this.$message.error(err)
      })
    },
    handleEnter() {
      if (!this.form.orgId) return this.$message.error('请选择关联单位')
      if (!this.form.oauthClientId) return this.$message.error('正在获取corpid，请稍后重试...')
      const data = {
        oauthClientId: this.form.oauthClientId,
        orgId: this.form.orgId
      }
      dispose(data).then(res => {
        this.dialogVisible = false
        this.$message.success(res)
        this.init()
      })
    },
    // --------------  选择关联单位
    handleFocus() {
      this.showSearchDialog = true
    },
    handleConfirm(val) {
      this.currentRow = val
      this.form.orgName = val.orgName
      this.form.orgId = val.orgId
      this.showSearchDialog = false
    },
    close(val) {
      this.showSearchDialog = false
    },
    copyText(val) {
      var input = document.createElement('input') // js创建一个input输入框
      input.value = val // 将需要复制的文本赋值到创建的input输入框中
      document.body.appendChild(input) // 将输入框暂时创建到实例里面
      input.select() // 选中输入框中的内容
      document.execCommand('Copy') // 执行复制操作
      document.body.removeChild(input) // 最后删除实例中临时创建的input输入框，完成复制操作
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-check {

  .demo-input-suffix {
    margin-bottom: 20px;

  }
}

.align {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center;

}
.text {
  color: #a9a9a9;
}
</style>
