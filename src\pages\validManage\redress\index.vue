<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="searchKeyword"
        class="group"
        placeholder="请输入搜索关键字"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
        <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="clearKeyWord">
          <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-input>

      <el-select
        v-model="listQuery.condition.status"
        placeholder="全部状态"
        @change="getList()"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.errorType"
        placeholder="全部类型"
        @change="getList()"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      评论列表
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === '未处理'"
              type="text"
              size="mini"
              @click="pop(scope.row)"
            >处理</el-button>
            <el-button
              v-if="scope.row.status === '已处理'"
              type="text"
              size="mini"
              @click="pop(scope.row)"
            >查看处理结果</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="20%"
      center
    >
      <el-form :model="handleQuery" style="height: 300px">
        <el-form-item label="处理结果:" required>
          <el-radio v-model="handleQuery.result" :disabled="dialogTitle === '处理结果' ? true : false" :label="0">属实</el-radio>
          <el-radio v-model="handleQuery.result" :disabled="dialogTitle === '处理结果' ? true : false" :label="1">不属实</el-radio>
        </el-form-item>
        <el-form-item label="处理说明:" style="display: flex" required>
          <el-input
            v-model="handleQuery.resultDesc"
            width="100%"
            type="textarea"
            resize="none"
            :disabled="dialogTitle === '处理结果' ? true : false"
            :autosize="{ minRows: 6, maxRows: 12 }"
            placeholder="请输入说明"
            :maxlength="300"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <span v-if="dialogTitle === '纠错处理' ? true : false" slot="footer" class="dialog-footer">
        <el-button type="primary" style="width: 200px" @click="handle">提交</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { redressList, redressHandle } from '@/api/validManage'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      searchType: 'userRealName',
      searchTypeList: [
        { label: '纠错人', value: 'userRealName' },
        { label: '纠错人账号', value: 'userAccount' },
        { label: '教程/SOP', value: 'courseName' }
      ],
      searchKeyword: '',
      stateOptions: [
        { label: '全部状态', value: null },
        { label: '未处理', value: 0 },
        { label: '已处理', value: 1 }
      ],
      typeOptions: [
        { label: '全部类型', value: null },
        { label: '答案错误', value: 1 },
        { label: '题目错误', value: 2 },
        { label: '其他', value: 3 }
      ],
      listQuery: {
        condition: {
          status: null,
          errorType: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'id', label: 'ID', width: '60' },
        { prop: 'errorCorrectionName', label: '纠错人', width: '80' },
        { prop: 'courseName', label: '纠错教程/sop', width: '80' },
        { prop: 'examineQuestionName', label: '纠错试题', width: '200' },
        { prop: 'errorType', label: '错误类型', width: '50' },
        { prop: 'errorDesc', label: '错误说明', width: '120' },
        { prop: 'createTime', label: '提交时间', width: '90' },
        { prop: 'updateUserName', label: '处理人', width: '60' },
        { prop: 'updateTime', label: '处理时间', width: '90' },
        { prop: 'status', label: '状态', width: '40' }
      ],
      tableData: [],
      dialogVisible: false,
      dialogTitle: '',
      handleQuery: {
        id: null,
        result: null,
        resultDesc: null
      }
    }
  },
  watch: {
    listQuery: {
      handler() {
        this.listQuery.pager.page = 1
      },
      deep: true
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    getList() {
      redressList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.status = this.stateOptions.find(i => i.value === v.status).label
          v.errorType = this.typeOptions.find(i => i.value === v.errorType).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    clearKeyWord() {
      if (this.searchKeyword === '') return
      this.searchKeyword = ''
      this.listQuery.condition = {
        status: this.listQuery.condition.status,
        errorType: this.listQuery.condition.errorType
      }
      this.getList()
    },
    search() {
      if (this.searchKeyword === '') {
        this.listQuery.condition = {
          status: this.listQuery.condition.status,
          errorType: this.listQuery.condition.errorType
        }
      } else {
        this.listQuery.condition[this.searchType] = this.searchKeyword
      }
      this.listQuery.pager.page = 1
      this.getList()
    },
    pop(row) {
      this.dialogVisible = true
      if (row.status === '未处理') {
        this.dialogTitle = '纠错处理'
        this.handleQuery.id = row.id
      } else {
        this.dialogTitle = '处理结果'
        this.handleQuery.result = row.result
        this.handleQuery.resultDesc = row.resultDesc
      }
    },
    handle() {
      redressHandle(this.handleQuery).then(() => {
        this.dialogVisible = false
        this.handleQuery = {
          id: null,
          result: null,
          resultDesc: null
        }
        this.getList()
      }).catch(err => {
        this.$message.error(err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: #eaeaee;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
