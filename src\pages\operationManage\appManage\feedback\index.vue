<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">APP意见反馈列表</header>
    </div>
    <div class="search-column">
      <div class="fl">
        <div class="search-column__item">
          <div class="search-column__label">已读状态：</div>
          <div class="search-column__inner">
            <el-select v-model="tableQuery.condition.hasView" placeholder="请选择已读状态" @change="handleChangeViewStatus">
              <el-option v-for="item in readStatusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
        </div>
        <div class="search-column__item">
          <div class="search-column__label">提交时间：</div>
          <div class="search-column__inner">
            <el-date-picker
              v-model="dateArray"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleChangeDate"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table :data="feedbackList" border stripe>
      <el-table-column
        v-for="col in tableColumnList"
        :key="col.id"
        :prop="col.prop"
        :label="col.label"
        :align="col.align"
      >
        <template slot-scope="scope">
          <span v-if="col.filter === ' hasView'">
            {{ scope.row[col.prop] | filterStatus }}
          </span>
          <span v-else>
            {{ scope.row[col.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
          <el-button v-if="!row.hasCheck" size="mini" type="text" @click="viewRow(row)">设为已读</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getFeedbackList, deleteFeedback, viewFeedback } from '@/api/feedback'
export default {
  components: {
    Pagination
  },
  filters: {
    filterStatus(val) {
      switch (val) {
        case 0:
          return '未查看'
        case 1:
          return '已查看'
        default:
          return '未知状态'
      }
    }
  },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // 总数
      total: 0,
      // 已读状态
      readStatusList: [
        { value: 2, label: '全部' },
        { value: 1, label: '已查看' },
        { value: 0, label: '未查看' }
      ],
      // 日期
      dateArray: [],
      // 请求参数
      tableQuery: {
        condition: {
          endTime: '',
          startTime: '',
          hasView: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 表格头列表
      tableColumnList: [
        { id: 0, label: 'ID', align: 'center', prop: 'id' },
        { id: 1, label: '联系方式', align: 'center', prop: 'contact' },
        { id: 2, label: '内容', align: 'center', prop: 'content' },
        { id: 3, label: '提交时间', align: 'center', prop: 'createTime' },
        { id: 4, label: '已读状态', align: 'center', prop: 'hasView', filter: 'hasView' }
      ],
      // 反馈列表
      feedbackList: []
    }
  },
  created() {
    this.getFeedbackList()
  },
  methods: {
    getFeedbackList() {
      getFeedbackList(this.tableQuery).then(res => {
        this.feedbackList = res.records
        this.total = res.total
      })
    },
    handleChangeViewStatus(val) {
      this.tableQuery.pager.page = 1
      this.getFeedbackList()
    },
    handleChangeDate(val) {
      this.tableQuery.pager.page = 1
      this.tableQuery.condition.startTime = val[0]
      this.tableQuery.condition.endTime = val[1]
      this.getFeedbackList()
    },
    handlePagination(val) {
      this.tableQuery.pager.page = val.page
      this.getFeedbackList()
    },
    deleteRow(row) {
      this.$confirm('确定要删除这条问题反馈？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deleteFeedback({ questionId: row.id }).then(res => {
          this.$message.success('删除成功')
          this.getFeedbackList()
        })
      })
    },
    viewRow(row) {
      viewFeedback({ questionId: row.id }).then(res => {
        this.$message.success('已查看该反馈')
        row.hasView = 1
        this.getFeedbackList()
      })
    }
  }
}
</script>

<style>

</style>
