<template>
  <div class="app-container">
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe />
    <!-- pagination -->
    <Pagination :auto-scroll="false" class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/ad'

const columns = [
  { props: { label: '序号', align: 'center', prop: 'rank' }},
  { props: { label: '曝光时间', align: 'center', prop: 'exposure_time' }},
  { props: { label: '点击时间', align: 'center', prop: 'hit_time' }}
]

export default {
  name: 'PersonalDetail',
  mixins: [table],
  data() {
    return {
      columns,
      request,
      adId: this.$route.query.adId,
      userId: this.$route.query.userId,
      initList: true // 是否初始化调用
    }
  },
  methods: {
    getList() {
      const param = {
        ...this.pager,
        adId: this.adId,
        userId: this.userId
      }
      request.adUserJoin(param).then(res => {
        this.list = res.records
        this.total = res.total
      })
    }
  }
}
</script>
