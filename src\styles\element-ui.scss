// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}



.el-tooltip__popper.is-dark {
  max-width: 400px;
}

.el-table {
  .el-table__expanded-cell,
  .el-table__expanded-cell:hover {
    padding: 20px 50px;
    background: #eff5fa !important;
  }

  .el-table__expand-column {
    .el-table__expand-icon {
      -webkit-transform: none;
      -moz-transform: none;
      -ms-transform: none;
      -o-transform: none;
      transform: none;

      .el-icon {
        width: 30px;
        line-height: 20px;
        position: unset;
        margin-left: 0;
        margin-top: 0;
        color: #409EFF;
      }
    }

    .el-table__expand-icon {
      .el-icon {
        position: relative;
        line-height: 0;
        top: 0;
        left: 0;
      }

      .el-icon:before {
        content: "展开";
      }
    }

    .el-table__expand-icon.el-table__expand-icon--expanded {
      .el-icon:before {
        content: "收起";
      }
    }
  }
}
