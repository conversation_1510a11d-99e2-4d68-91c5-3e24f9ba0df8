<template>
  <el-dialog :title="title" :visible.sync="visible" width="720px" top="8vh" :close-on-click-modal="false" :before-close="beforeClose">
    <el-form ref="form" size="mini" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="单位logo" prop="logo">
        <singleImage ref="upload" v-model="form.logo" width="200px" height="100px" type="org_logo" :url.sync="form.logoUrl" @input="getLogoId" />
      </el-form-item>
      <el-form-item label="单位主题" prop="subjuctCode">
        <div class="color_box">
          <div class="c_img" @click="chooseColor('green')">
            <img src="@/assets/images/colorSetting/img_green.png">
            <i v-if="form.subjuctCode==='green'" class="el-icon-check active" style="color:#30B08F" />
          </div>
          <div class="c_img" @click="chooseColor('blue')">
            <img src="@/assets/images/colorSetting/img_blue.png">
            <i v-if="form.subjuctCode==='blue'" class="el-icon-check active" style="color:#348FE4" />
          </div>
          <div class="c_img" @click="chooseColor('red')">
            <img src="@/assets/images/colorSetting/img_red.png">
            <i v-if="form.subjuctCode==='red'" class="el-icon-check active" style="color:#C20505" />
          </div>
          <div class="c_img" @click="chooseColor('darkGray')">
            <img src="@/assets/images/colorSetting/img_gray.png">
            <i v-if="form.subjuctCode==='darkGray'" class="el-icon-check active" style="color:#2C3544" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="培训平台banner" prop="trainBanner">
        <singleImage
          v-model="form.trainBannerId"
          width="100%"
          height="100px"
          type="org_banner"
          :url.sync="form.trainBannerUrl"
          @input="getTrainBannerId"
        />
      </el-form-item>
      <el-form-item label="营销平台banner" prop="banner">
        <singleImage
          v-model="form.bannerId"
          width="100%"
          height="100px"
          type="org_banner"
          :url.sync="form.bannerUrl"
          @input="getBannerId"
        />
      </el-form-item>
      <el-form-item label="单位类型" prop="type">
        <el-select v-model="form.type" :disabled="isDisabled" placeholder="请选择单位类型" @change="handleChangeType">
          <el-option v-for="item in typeList" :key="item.orgTypeId" :label="item.name" :value="item.orgTypeId" />
        </el-select>
      </el-form-item>
      <el-form-item prop="orgName" label="单位名称">
        <el-input v-model="form.orgName" :disabled="form.conExtenderLiveFlag===1" />
      </el-form-item>
      <el-form-item prop="level" label="单位层级">
        <el-select v-model="form.level" placeholder="" :disabled="form.conExtenderLiveFlag===1">
          <el-option v-for="item in levelList" :key="item.orgLevelId" :label="item.name" :value="item.orgLevelId" />
        </el-select>
      </el-form-item>
      <el-form-item prop="area" label="所属区域">
        <el-select v-model="form.provinceId" placeholder="请选择省" :disabled="form.conExtenderLiveFlag===1" @change="handleChangeSelect(form.provinceId, 'city', true)">
          <el-option v-for="item in provinceList" :key="item.areaId" :label="item.name" :value="item.areaId" />
        </el-select>
        <el-select v-model="form.cityId" placeholder="请选择市" :disabled="form.conExtenderLiveFlag===1" @change="handleChangeSelect(form.cityId, 'area', true)">
          <el-option v-for="item in cityList" :key="item.areaId" :label="item.name" :value="item.areaId" />
        </el-select>
        <el-select v-model="form.areaId" :disabled="form.conExtenderLiveFlag===1" placeholder="请选择区">
          <el-option v-for="item in areaList" :key="item.areaId" :label="item.name" :value="item.areaId" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input v-model="form.address" placeholder="请输入详细地址" :disabled="form.conExtenderLiveFlag===1" />
      </el-form-item>
      <el-form-item label="单位联系人">
        <el-input v-model="form.contacts" :disabled="form.conExtenderLiveFlag===1" />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input v-model="form.phone" maxlength="15" :disabled="form.conExtenderLiveFlag===1" />
      </el-form-item>
      <div v-if="form.type === 2002">
        <el-form-item label="服务单位" prop="serviceProviderOrgId">
          <el-select v-model="form.serviceProviderOrgId" placeholder="请选择服务商" clearable>
            <el-option v-for="item in serveList" :key="item.serviceOrgId" :label="item.serviceName" :value="item.serviceOrgId" />
          </el-select>
        </el-form-item>
        <el-form-item label="拜访范围限制 (米)" prop="visitScope">
          <el-input v-model="form.visitScope" />
        </el-form-item>
        <el-form-item label="统计上限" prop="statisticsLimit">
          <el-radio v-model="form.statisticsLimit" :label="1">是</el-radio>
          <el-radio v-model="form.statisticsLimit" :label="0">否</el-radio>
        </el-form-item>
      </div>
      <div v-if="form.type === 2003">
        <!-- 仅单位类型为服务商(2003) 时有以下字段 -->
        <el-form-item label="统一社会信用代码">
          <el-input v-model="form.socialCreditCode" />
        </el-form-item>
        <el-form-item label="key">
          <el-input v-model="form.keySecret.key" :disabled="isDisabled && data.keySecret.key !== ''" />
        </el-form-item>
        <el-form-item label="secret">
          <el-input v-model="form.keySecret.secret" :disabled="isDisabled && data.keySecret.secret !== ''" />
        </el-form-item>
        <el-form-item label="场景代码">
          <table>
            <tr>
              <th>场景类型</th>
              <th>场景编码</th>
            </tr>
            <tr>
              <td>
                创作任务
              </td>
              <td>
                <el-input v-model="form.sceneCode.creatorCode" :disabled="isDisabled && data.sceneCode.creatorCode !== ''" />
              </td>
            </tr>
            <tr>
              <td>
                拜访任务
              </td>
              <td>
                <el-input v-model="form.sceneCode.visitCode" :disabled="isDisabled && data.sceneCode.visitCode !== ''" />
              </td>
            </tr>
          </table>
        </el-form-item>
      </div>
      <h3 style="margin:40px 0">单位服务配置</h3>
      <el-form-item prop="shortCode" label="单位前缀">
        <el-input v-model="form.shortCode" :disabled="isDisabled" placeholder="请输入单位前缀" />
      </el-form-item>
      <el-form-item label="员工限额" prop="maxStaffCount">
        <el-input v-model.number="form.maxStaffCount" placeholder="" style="width:120px">
          <template slot="append">人</template>
        </el-input>
      </el-form-item>
      <el-form-item label="视频空间容量" prop="maxSizeCount">
        <el-input v-model.number="form.maxSizeCount" placeholder="" style="width:120px">
          <template slot="append">G</template>
        </el-input>
      </el-form-item>
      <el-form-item label="启用服务">
        <el-radio-group v-model="form.status">
          <el-radio v-for="item in statusList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="人脸识别">
        <el-radio-group v-model="form.faceFlag">
          <el-radio v-for="item in faceFlagList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="组卷服务">
        <el-radio-group v-model="form.examFlag">
          <el-radio v-for="item in faceFlagList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="form.conExtenderLiveFlag===1" label="内容扩展方直播">
        <el-radio-group v-model="form.conExtenderLiveFlag" disabled>
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="会员等级" prop="memberLevel">
        <el-radio-group v-model="form.memberLevel" @change="handleMemberLevelChange">
          <el-radio v-for="item in memberLevelList" :key="item.level" :label="item.level">
            {{ item.name }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="showMemberTime" label="会员时限" prop="memberTime">
        <el-date-picker
          v-model="form.memberStartTime"
          type="date"
          placeholder="开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 150px; margin-right: 10px;"
        />
        <span style="margin: 0 10px;">至</span>
        <el-date-picker
          v-model="form.memberEndTime"
          type="date"
          placeholder="结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          style="width: 150px;"
        />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleSaveForm">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import singleImage from '@/components/SingleImage'
import { getOrganTypeList, getOrganLevelList } from '@/api/userManage'
import { serviceProviderList } from '@/api/marketing/taskPromote'
import { getArea } from '@/api/area'
import { getVipLevelList } from '@/api/vip'

export default {
  name: 'DialogUnitedit',
  components: { singleImage },
  props: {
    action: {
      type: String,
      default: 'add'
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '创建单位'
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const shortCodeValid = (rules, value, callback) => {
      if (value === '') {
        callback(new Error('请输入单位前缀'))
      } else if (!/^[A-Za-z0-9]+$/.test(value)) {
        callback(new Error('单位前缀只支持英文+数字的组合'))
      } else {
        callback()
      }
    }
    const levelValid = (rules, value, callback) => {
      if (this.form.type === '') {
        callback(new Error('请先选择单位类型'))
      } else if (value === '') {
        callback(new Error('请选择单位层级'))
      } else {
        callback()
      }
    }
    const phoneValid = (rules, value, callback) => {
      if (value === '') {
        callback(new Error('请输入手机号'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        type: [{ required: true, message: '请选择单位类型', trigger: 'blur' }],
        orgName: [
          { required: true, message: '请输入单位名称', trigger: 'blur' },
          {
            min: 4,
            max: 30,
            message: '单位名称长度在4到30个字符',
            trigger: 'blur'
          }
        ],
        level: [{ validator: levelValid, required: true, trigger: 'blur' }],
        contacts: [
          { required: true, message: '请输入单位联系人', trigger: 'blur' }
        ],
        phone: [{ validator: phoneValid, required: true, trigger: 'blur' }],
        shortCode: [
          { validator: shortCodeValid, required: true, trigger: 'blur' }
        ],
        maxStaffCount: [
          {
            required: true,
            type: 'number',
            message: '请输入员工限额',
            trigger: 'blur'
          }
        ],
        maxSizeCount: [
          {
            required: true,
            type: 'number',
            message: '请输入视频空间容量',
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: '请选择是否启用服务', trigger: 'change' }
        ],
        memberLevel: [
          { required: true, message: '请选择会员等级', trigger: 'change' }
        ]
      },
      form: JSON.parse(JSON.stringify(this.data)),
      // 单位结构类型
      typeList: [],
      // 单位层级
      levelList: [],
      // 服务单位列表
      serveList: [],
      // 启用服务
      statusList: [
        { label: '启用', key: 1 },
        { label: '停用', key: 0 }
      ],
      // 人脸识别
      faceFlagList: [
        { label: '开启', key: 1 },
        { label: '关闭', key: 0 }
      ],
      // 省市区
      provinceList: [],
      cityList: [],
      areaList: [],
      // 会员等级
      memberLevelList: []
    }
  },
  computed: {
    isDisabled() {
      return this.action === 'edit'
    },
    // 是否显示会员时限字段（付费会员需要显示）
    showMemberTime() {
      if (!this.form.memberLevel) return false
      const selectedLevel = this.memberLevelList.find(item => item.level === this.form.memberLevel)
      return selectedLevel && selectedLevel.level > 0 // 假设level > 0为付费会员
    }
  },
  watch: {
    visible(v) {
      !v && this.$refs.upload.rmImage()
      if (this.action === 'add') {
        this.$set(this.form, 'conExtenderLiveFlag', 0)
      }
    },
    data: {
      handler(v) {
        this.form = JSON.parse(JSON.stringify(v))
      },
      deep: true
    },
    'form.type': {
      handler: function(val) {
        if (this.form.type) {
          getOrganLevelList(this.form.type).then(res => {
            this.levelList = res
          })
          if (val === 2002) {
            this.handleGetUnitList()
          }
        }
      },
      immediate: true
    },
    'form.provinceId': {
      handler: function(val) {
        if (this.action === 'edit') {
          this.handleGetArea(this.form.provinceId, 'city', false)
        }
      },
      immediate: true
    },
    'form.cityId': {
      handler: function(val) {
        if (this.action === 'edit') {
          this.handleGetArea(this.form.cityId, 'area', false)
        }
      },
      immediate: true
    }
  },
  created() {
    const cityRules = (rule, value, callback) => {
      const { areaId, cityId, provinceId } = this.form
      if (!provinceId || !cityId || !areaId) {
        callback(new Error('请选择所属区域'))
      } else {
        callback()
      }
    }
    this.rules.area = [
      { required: true, trigger: 'blur', validator: cityRules }
    ]
  },
  mounted() {
    this.handleGetOrganTypeList()
    this.hasAreaCode()
    this.getMemberLevelList()
  },
  methods: {
    chooseColor(color) {
      this.form.subjuctCode = color
    },
    getLogoId(logoId) {
      this.form.logo = logoId || '0'
    },
    getTrainBannerId(bannerId) {
      this.form.trainBannerId = bannerId || '0'
    },
    getBannerId(bannerId) {
      this.form.bannerId = bannerId || '0'
    },
    handleGetOrganTypeList() {
      getOrganTypeList({ action: this.$route.name === 'MarketingUserUnit' ? 2 : 1 }).then(res => {
        this.typeList = res
      })
    },
    handleGetUnitList() {
      serviceProviderList().then(res => {
        this.serveList = res
      })
    },
    close() {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
      this.$emit('update:visible', false)
      this.$emit('handleCancel')
    },
    handleSaveForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log(this.form)
          this.$emit('handleSave', this.form)
        } else {
          return false
        }
      })
    },
    handleGetAreaCode(val) {
      this.form.areaId = val.area
      this.form.address = val.address
    },
    handleChangeType(val) {
      getOrganLevelList(val).then(res => {
        this.levelList = res
      })
    },
    hasAreaCode() {
      this.handleGetArea(null, 'province', false)
      if (this.form.provinceId && this.form.provinceId !== '') {
        this.handleGetArea(this.form.provinceId, 'city', false)
      }
      if (this.form.cityId && this.form.cityId !== '') {
        this.handleGetArea(this.form.cityId, 'area', false)
      }
    },
    /**
       * @params {Object} params parentCode
       * @params {String} type area type
       */
    handleGetArea(params, type, flag) {
      getArea({ parentId: params }).then(res => {
        switch (type) {
          case 'province':
            this.provinceList = res
            break
          case 'city':
            this.cityList = res
            this.areaList = []
            if (flag) {
              this.form.cityId = ''
              this.form.areaId = ''
            }
            break
          case 'area':
            this.areaList = res
            if (flag) {
              this.form.areaId = ''
            }
            break
          default:
            this.provinceList = res
            break
        }
      })
    },
    handleChangeSelect(params, type, flag) {
      this.handleGetArea(params, type, flag)
    },
    beforeClose(done) {
      this.close()
      this.$nextTick(() => {
        done()
      })
    },
    // 获取会员等级列表
    async getMemberLevelList() {
      try {
        const response = await getVipLevelList()
        this.memberLevelList = response || []
      } catch (error) {
        console.error('获取会员等级列表失败:', error)
      }
    },
    // 会员等级变化处理
    handleMemberLevelChange(level) {
      if (level && level > 0) {
        // 付费会员，设置默认时限（当前日期开始，+364天结束）
        const today = new Date()
        const endDate = new Date(today)
        endDate.setDate(today.getDate() + 364)

        this.form.memberStartTime = this.formatDate(today)
        this.form.memberEndTime = this.formatDate(endDate)
      } else {
        // 免费会员，清空时限
        this.form.memberStartTime = ''
        this.form.memberEndTime = ''
      }
    },
    // 格式化日期为 yyyy-MM-dd
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style lang="scss" scoped>
.color_box{
  width: 400px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;

  .c_img{
    position: relative;
    width: 80px;
    height: 80px;
    cursor: pointer;
    img{
      object-fit: contain;
      width: 100%;
      height: 100%;
    }
    .active{
      position: absolute;
      top: 40%;
      left: 40%;
      font-size: 24px;
      font-weight: 700;
    }
  }
}
table {
    width: 100%;
    border: 1px solid #ebeef5;
    border-spacing: 0;
    th,
    td {
      text-align: center;
      border-right: 1px solid #ebeef5;
      &:nth-last-child(1) {
        border-right: none;
      }
    }
    th {
      color: #909399;
      font-size: 14px;
    }
    td {
      border-top: 1px solid #ebeef5;
    }
    .el-input {
      width: 100%;
    }
  }
</style>
