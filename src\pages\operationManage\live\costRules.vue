<template>
  <div>
    <el-form ref="form" label-width="160px">

      <el-form-item label="平台技术服务费:">
        <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
          <tr>
            <td>直播类型</td>
            <td>参考价(元)</td>
          </tr>
          <tr v-for="(item,index) in list1" :key="'a'+index">
            <td>{{ item.feeRuleOneDto.liveType.name }}</td>
            <td>
              <div class="twoInput">
                <el-input v-model="item.feeRuleOneDto.minPrice" />
                ~
                <el-input v-model="item.feeRuleOneDto.maxPrice" />
              </div>
            </td>
          </tr>
        </table>
      </el-form-item>

      <el-form-item v-if="list2.length" label="流量费用(每GB):">
        <el-input v-model="list2[0].feeRuleTwoDto.price" style="width:200px;" />
        <span><i class="el-icon-warning-outline" /> 直播消耗的流量</span>
      </el-form-item>

      <el-form-item label="推广费用(每人):">
        <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
          <tr>
            <td>推广类型</td>
            <td>参考单价(元)</td>
          </tr>
          <tr v-for="(item,index) in list3" :key="'b'+index">
            <td>{{ item.feeRuleThreeDto.promotionType.name }}</td>
            <td>
              <div class="twoInput">
                <el-input v-model="item.feeRuleThreeDto.minPrice" />
                ~
                <el-input v-model="item.feeRuleThreeDto.maxPrice" />
              </div>
            </td>
          </tr>
        </table>
      </el-form-item>

      <el-form-item label="讲师服务费(每人):">
        <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
          <tr>
            <td>讲师职称</td>
            <td>直播类型</td>
            <td>参考价(元)</td>
          </tr>
          <tr v-for="(item,index) in list4" :key="'c'+index">
            <td v-if="[0,6,12,18].includes(index)" rowspan="6">{{ item.feeRuleFourDto.academicClass.name }}</td>
            <td>{{ item.feeRuleFourDto.liveType.name }}</td>
            <td>
              <div class="twoInput">
                <el-input v-model="item.feeRuleFourDto.minPrice" />
                ~
                <el-input v-model="item.feeRuleFourDto.maxPrice" />
              </div>
            </td>
          </tr>
        </table>
      </el-form-item>

      <el-form-item label="内容扩展方费用:">
        <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
          <tr>
            <td>讲师职称</td>
            <td>直播类型</td>
            <td>参考价(元)</td>
          </tr>
          <tr v-for="(item,index) in list5" :key="'c'+index">
            <td v-if="[0,6,12,18].includes(index)" rowspan="6">{{ item.feeRuleFiveDto.academicClass.name }}</td>
            <td>{{ item.feeRuleFiveDto.liveType.name }}</td>
            <td>
              <div class="twoInput">
                <el-input v-model="item.feeRuleFiveDto.minPrice" />
                ~
                <el-input v-model="item.feeRuleFiveDto.maxPrice" />
              </div>
            </td>
          </tr>
        </table>
      </el-form-item>

    </el-form>
    <div style="width:100%;text-align:center;">
      <el-button @click="cancle()">取消</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
    </div><br>
  </div>
</template>

<script>
import { getRule, saveRule } from '@/api/liveManage'
export default {
  data() {
    return {
      list1: [],
      list2: [],
      list3: [],
      list4: [],
      list5: []
    }
  },
  created() {
    this.getRule()
  },
  methods: {
    getRule() {
      getRule().then(res => {
        res.forEach(item => {
          if (item.feeItemCode === '1') {
            this.list1.push(item)
          } else if (item.feeItemCode === '2') {
            this.list2.push(item)
          } else if (item.feeItemCode === '3') {
            this.list3.push(item)
          } else if (item.feeItemCode === '4') {
            this.list4.push(item)
          } else if (item.feeItemCode === '5') {
            this.list5.push(item)
          }
        })
      })
    },
    cancle() {
      this.$confirm('是否取消保存当前改动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$router.go(0)
      })
    },
    save() {
      this.$confirm('是否保存当前改动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const arr = [...this.list1, ...this.list2, ...this.list3, ...this.list4, ...this.list5]
        saveRule(arr).then(res => {
          this.$router.go(0)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.costRulesTable{
  td{
    width: 240px;
    text-align: center;
    ::v-deep .el-input .el-input__inner{
      width: 200px;
      border: none !important;
      text-align: center;
    }
  }

  .twoInput{
    display: flex;
    ::v-deep .el-input .el-input__inner{
      width: 80px !important;
    }
  }
}

</style>
