/**
 * 作用：表格类页面的公共属性和方法
 * 作者：张石城
 * 时间：2020年9月14日
 */

import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import ATable from '@/components/ATable' // 引入再封装的element-ui table组件

export default {
  name: 'ComplexTable',
  components: { Pagination, ATable },
  data() {
    return {
      // 列表数据
      list: null,
      total: 0,
      selection: [], // 选中的值
      // search params
      conditionWatch: {}, // 值更改自动查找的参数
      condition: {}, // 不自动查找的参数
      conditionCache: {}, // 不自动查找参数的搜索缓存
      conditionDefault: {}, // 预设值
      otherDefaultKey: [], // 其他需要缓存的预设值
      // orderBys: {},
      pager: {
        page: 1,
        pageSize: 10
      },
      // pagination
      layout: 'total, prev, pager, next, jumper', // 默认分页样式
      mainKey: 'id', // 主要id 用于增删改查
      mainName: 'name', // 主要name 操作的名称显示
      editPath: '', // 修改编辑页面的路径
      listLoading: false, // 列表请求状态
      initList: true, // 是否初始化调用
      init: false // 是否已初始化
    }
  },
  watch: {
    conditionWatch: {
      handler() {
        this.pager.page = 1
        this.init
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.conditionCache = this.copy(this.condition)
    const other = {}
    this.otherDefaultKey.forEach(key => {
      other[key] = this.copy(this[key])
    })
    this.conditionDefault = this.copy({ ...other, condition: this.condition, conditionWatch: this.conditionWatch })
    this.getList()
    this.initList
    this.init = true
  },
  methods: {
    // 重置预设数据
    reset() {
      this.condition = this.copy(this.conditionDefault.condition)
      this.conditionCache = this.copy(this.condition)
      this.otherDefaultKey.forEach(key => {
        this[key] = this.copy(this.conditionDefault[key])
      })
      this.conditionWatch = this.copy(this.conditionDefault.conditionWatch)
    },
    handleFilter() {
      this.conditionCache = this.copy(this.condition)
      this.pager.page = 1
      this.getList()
    },
    // table select
    onSelectChange(arr) {
      this.selection = arr
    },
    getListData() {
      const data = {
        condition: Object.assign(this.conditionWatch, this.conditionCache),
        pager: this.pager
      }
      return data
    },
    getList() {
      if (this.listLoading) {
        return
      }
      const data = this.getListData()
      this.listLoading = true
      return this.request.list(data).then(res => {
        this.list = res.records || []
        this.total = res.total || 0
        this.listLoading = false
      }, () => {
        this.listLoading = false
      })
    },
    // pagination change
    handlePagination(val) {
      this.pager = val
      this.getList()
    },
    // put on the shelf
    onShelf(row, title = '上架') {
      const data = { status: 2 }
      data[this.mainKey] = row[this.mainKey]
      this.request.putaway(data).then((res) => {
        this.getList()
        this.$message.success(title + '成功')
      })
    },
    // off shelf
    offShelf(row, title = '下架') {
      const data = { status: 1 }
      data[this.mainKey] = row[this.mainKey]
      this.confirm(title, row, 'putaway', data)
    },
    // delete row
    deleteRow(row) {
      const data = {}
      data[this.mainKey] = row[this.mainKey]
      this.confirm('删除', row, 'del', data)
    },
    // common notice
    confirm(action, row, API, obj) {
      this.$confirm(`是否${action} "` + row[this.mainName] + '"？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request[API](obj).then(() => {
          if (action === '删除' && this.list.length === 1 && this.pager.page > 1) {
            this.pager.page--
          }
          this.getList()
          this.$message.success(`${action}成功`)
        })
      }).catch(console.log)
    },
    // add ad
    add() {
      this.$router.push(this.editPath)
    },
    // view detail
    toDetail(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          readOnly: 1,
          id: row[this.mainKey]
        }
      })
    },
    // edit row
    editRow(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          isEdit: 1,
          id: row[this.mainKey]
        }
      })
    },
    // 深拷贝
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    parseTime
  }
}
