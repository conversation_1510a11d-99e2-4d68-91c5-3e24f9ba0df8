<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="condition.type" filterable clearable placeholder="请选择类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="活动名称/广告商" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="handleChange" />
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.status" filterable clearable placeholder="请选择活动状态" @change="handleFilter">
          <el-option v-for="item in adStateList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="add">添加活动</el-button>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="img" slot-scope="{row}">
        <el-image style="width: 120px" :src="row.imgUrl" />
      </template>
      <template slot="title" slot-scope="{row}">
        <el-button class="title" type="text" @click="toDetail(row)">{{ row.title }}</el-button>
      </template>
      <template slot="time" slot-scope="{row}">
        <span>{{ row.startTime+' ~ '+row.endTime }}</span>
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.status|statusFlt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button v-if="row.status!==2" size="mini" type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="[0,2].includes(row.status)" size="mini" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button v-if="[0,1].includes(row.status)" size="mini" type="text" @click="stop(row)">终止</el-button>
        <el-button v-if="row.status!==0" size="mini" type="text" @click="toStatistics(row)">活动统计</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/activity'

const columns = [
  {
    props: { label: '活动封面', align: 'center', width: '150' },
    slot: 'img'
  },
  {
    props: { label: '活动名称', align: 'center' },
    slot: 'title'
  },
  {
    props: { label: '活动时间', align: 'center' },
    slot: 'time'
  },
  { props: { label: '广告商', align: 'center', prop: 'advertiserName' }},
  {
    props: { label: '活动状态', align: 'center', width: '90' },
    slot: 'status'
  },
  { props: { label: '创建人', align: 'center', prop: 'updateUsername' }},
  { props: { label: '创建时间', align: 'center', prop: 'createTime' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'ActivityIndex',
  filters: {
    statusFlt(v) {
      const arr = ['未开始', '进行中', '已结束']
      return arr[v]
    }
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      timeRange: [],
      condition: {
        keyword: '',
        type: 1
      },
      conditionWatch: {
        startTime: '',
        endTime: '',
        status: ''
      },
      editPath: 'addActivity',
      mainKey: 'activityId',
      mainName: 'title',
      adStateList: [
        { value: 0, name: '未开始' },
        { value: 1, name: '进行中' },
        { value: 2, name: '已结束' }
      ],
      typeList: [
        { value: 1, name: '活动名称' },
        { value: 2, name: '广告商' }
      ]
    }
  },
  methods: {
    handleChange(v) {
      this.pager.page = 1
      this.conditionWatch.startTime = v ? v[0] : ''
      this.conditionWatch.endTime = v ? v[1] : ''
    },
    deleteRow(row) {
      this.$confirm(
        '删除后活动在列表消失，且活动统计数据不可见，活动相关的所有数据都删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        request.del(row.activityId).then(res => {
          this.$message.success(`删除成功`)
          this.getList()
        })
      })
    },
    stop(row) {
      this.$confirm(
        '活动终止后无论当前时间是否未超过活动时间，活动状态均更新为“已结束”',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        request.stop(row.activityId).then(res => {
          this.$message.success(`终止成功`)
          this.getList()
        })
      })
    },
    toStatistics(row) {
      this.$router.push({
        path: 'statistics',
        query: {
          id: row.activityId,
          title: row.title
        }
      })
    }, // edit row
    editRow(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          isProcessing: row.status === 1 ? 1 : 0, // 进行中的活动仅可编辑部分
          isEdit: 1,
          id: row[this.mainKey]
        }
      })
    } // view detail
  }
}
</script>

<style lang="scss" scoped>
.el-button.title{
  white-space: normal;
}
</style>
