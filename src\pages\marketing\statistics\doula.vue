<template>
  <section class="article">
    <div v-if="!$route.path.includes('doula/detail')">
      <h2 v-if="!isTotal">
        抖喇列表
        <el-button type="primary" @click="exportList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="!isTotal ? tableData : tableData.slice(0,4) "
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column prop="title" label="文字内容" width="260">
            <template slot-scope="scope">
              <span @click="toDetail(scope.row.id)">{{ scope.row.title | textFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
      </div>
      <Pagination
        v-if="!isTotal"
        :page="queryParams.pager.page"
        :page-size="queryParams.pager.pageSize"
        :total="total"
        @pagination="handlePagination"
      />
    </div>
    <!-- 详情 -->
    <router-view />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { articleAnalysisList, articleAnalysisListExport, userAnalysisDetailList, userAnalysisDetailListExport } from '@/api/marketing/promoteArticle'
import { deleteEmptyProperty } from '@/utils/index'
export default {
  components: {
    Pagination
  },
  filters: {
    textFilter(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  props: {
    isTotal: {
      type: Boolean,
      default: false
    },
    searchParam: {
      type: Object,
      default: () => {}
    },
    userType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableColumn: [
        { prop: 'articleTypeStr', label: '类型', width: '100' },
        { prop: 'serviceProvider', label: '服务商', width: '160' },
        { prop: 'orgName', label: '企业', width: '160' },
        { prop: 'productName', label: '产品', width: '180' },
        { prop: 'createdName', label: '发布人', width: '140' },
        { prop: 'author', label: '作者', width: '260' },
        { prop: 'releaseTime', label: '发布时间', width: '140' },
        { prop: 'viewNum', label: '浏览量', width: '' },
        { prop: 'peopleViewNum', label: '浏览人数', width: '' },
        { prop: 'totalFee', label: '费用 (元)', width: '' }
      ],
      tableData: [],
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          orgId: null,
          productId: null,
          categoryId: null,
          keyword: null,
          authorName: null,
          taskUserName: null,
          serviceProviderOrgId: null,
          orgzId: null,
          orgzIds: null,
          userType: null,
          userId: null,
          type: 'DOULA',
          articleType: 'DOULA'
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.queryParams.pager.page = newVal.pager.page
        this.queryParams.condition['orgzId'] = this.queryParams.condition['orgId']
        if (this.$route.path.includes('information/detail')) return
        this.getArticleAnalysisList()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toDetail(id) {
      this.$router.push({
        name: 'MarketingStatisticsDoulaDetail',
        query: {
          id
        }
      })
    },
    getArticleAnalysisList() {
      if (this.userType !== '') {
        this.queryParams.condition.userType = this.userType
        this.tableColumn.forEach(v => {
          v.width = ''
        })
        if (this.tableColumn.length === 8) {
          this.userType === 'CREATOR' ? this.tableColumn.splice(2, 2) : this.tableColumn.splice(2, 1)
        }
      }
      const param = JSON.parse(JSON.stringify(this.queryParams))
      deleteEmptyProperty(param)
      const getList = this.userType !== '' ? userAnalysisDetailList : articleAnalysisList
      getList(param).then(res => {
        res.records.forEach(item => {
          item.author = `${item.authorInfoResp.authorName} ${item.authorInfoResp.academic} ${item.authorInfoResp.company} ${item.authorInfoResp.department}`
          item.categorieName = ''
          for (const i in item.categories) {
            item.categorieName += item.categories[i].categoryName + ' '
          }
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportList() {
      if (this.total <= 0) {
        this.$message.error('暂无可导出数据')
        return
      }
      const param = JSON.parse(JSON.stringify(this.queryParams.condition))
      deleteEmptyProperty(param)
      const exportList = this.userType !== '' ? userAnalysisDetailListExport : articleAnalysisListExport
      exportList(param).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getArticleAnalysisList()
    }
  }
}
</script>

<style lang="scss" scoped>
.article {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  h2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
    .el-button {
      width: 80px;
      height: 35px;
    }
  }
  .table {
    padding: 25px 20px 0;
    .cell {
      span {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}
</style>
