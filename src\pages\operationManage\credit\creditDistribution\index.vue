<template>
  <section class="studentList">
    <div class="screen">
      <el-input
        v-model="listQuery.condition"
        placeholder="姓名/手机/单位"
        @change="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
    </div>
    <h2>
      学分派发
      <el-button type="primary" @click="batch">派发学分</el-button>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="record(scope.row.userId, 1)">派发记录</el-button>
            <el-button type="text" size="mini" @click="record(scope.row.userId, 2)">抵扣记录</el-button>
            <el-button type="text" size="mini" @click="distribute(scope.row)">派发学分</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <el-dialog title="派发记录" :visible.sync="distributeRecordVisible">
      <el-table :data="distributeRecordData">
        <el-table-column property="score" label="派发学分" width="150" />
        <el-table-column property="opUserName" label="派发人" width="200" />
        <el-table-column property="createTime" label="派发时间" />
      </el-table>
    </el-dialog>

    <el-dialog title="抵扣记录" :visible.sync="deductionRecordVisible">
      <el-table :data="deductionRecordData">
        <el-table-column property="deductionScore" label="抵扣学分" width="150" />
        <el-table-column property="projectName" label="学分项目" width="200" />
        <el-table-column property="credit" label="学分" width="200" />
        <el-table-column property="createTime" label="抵扣时间" />
      </el-table>
    </el-dialog>

    <el-dialog center title="派发学分" :visible.sync="distributeVisible" width="400px">
      <el-form v-if="form.userIds.length > 0" :model="form">
        <el-form-item label="派发学分:" required :label-width="120">
          <el-input-number v-model="form.score" :controls="false" :min="1" :precision="0" />
        </el-form-item>
        <el-form-item label="派发学员:" required :label-width="120">
          <el-input v-model="userName" style="width: 180px;" disabled />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="distributeVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { creditCardList, distributeRecord, deductionRecord, distributeCredits } from '@/api/credit'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      time: '',
      listQuery: {
        condition: '',
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      distributeRecordData: {},
      distributeRecordVisible: false,
      deductionRecordData: {},
      deductionRecordVisible: false,
      tableData: [],
      tableColumn: [
        { prop: 'userName', label: '姓名', width: '110' },
        { prop: 'phone', label: '手机', width: '110' },
        { prop: 'identityName', label: '身份', width: '80' },
        { prop: 'majorName', label: '专科', width: '80' },
        { prop: 'academicName', label: '职称', width: '80' },
        { prop: 'orgName', label: '单位', width: '180' },
        { prop: 'areaName', label: '地区', width: '160' },
        { prop: 'totalAmount', label: '派发学分总额', width: '110' },
        { prop: 'useAmount', label: '已抵扣学分', width: '110' },
        { prop: 'balanceAmount', label: '剩余学分', width: '80' }
      ],
      distributeVisible: false,
      form: {
        score: 1,
        userIds: []
      },
      userName: ''
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    record(userId, type) {
      if (type === 1) {
        distributeRecord(userId).then(res => {
          this.distributeRecordData = res
          this.distributeRecordVisible = true
        })
      } else {
        deductionRecord(userId).then(res => {
          res.forEach(v => {
            v.credit = `${v.creditCategory}  ${v.score}分`
          })
          this.deductionRecordData = res
          this.deductionRecordVisible = true
        })
      }
    },
    distribute(row) {
      this.form.userIds = [row.userId]
      this.userName = row.userName
      this.distributeVisible = true
    },
    submit() {
      distributeCredits(this.form).then(() => {
        this.$message.success('派发成功')
        this.distributeVisible = false
        this.getList()
      }).catch(error => {
        this.$message.error(error)
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    timeChange() {
      this.listQuery.condition.startTime = this.time[0]
      this.listQuery.condition.endTime = this.time[1]
    },
    getList() {
      creditCardList(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    batch() {
      this.$router.push({
        name: 'BatchDistribute'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.studentList {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input,
    .el-cascader,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
}
</style>
