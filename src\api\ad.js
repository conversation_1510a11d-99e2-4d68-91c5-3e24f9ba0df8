import request from '@/utils/request'

/**
 * 广告相关
 */

// 广告列表
export function adList(params) {
  return request({
    url: '/positive/list',
    method: 'post',
    data: params
  })
}

// 广告类型
export function adType() {
  return request({
    url: '/positive/type',
    method: 'post'
  })
}

// 广告位置
export function adPlace() {
  return request({
    url: '/positive/place',
    method: 'post'
  })
}

// 广告上下架
export function putaway(params) {
  return request({
    url: `/positive/putaway/${params.advertisementId}/${params.status}`,
    method: 'post'
  })
}

// 广告删除
export function adDel(params) {
  return request({
    url: `/positive/delete/${params.advertisementId}`,
    method: 'post'
  })
}

// 广告添加
export function adAdd(params) {
  return request({
    url: `/positive/create`,
    method: 'post',
    data: params
  })
}

// 广告编辑
export function adEdit(params) {
  return request({
    url: `/positive/edit`,
    method: 'post',
    data: params
  })
}

// 广告详情
export function adDetail(params) {
  return request({
    url: `/positive/detail/${params}`,
    method: 'get'
  })
}

export function adOrgList() {
  return request({
    url: `/organization/getAdvertisementOrgList`,
    method: 'get'
  })
}

// 预创建广告
export function preAdd(param) {
  return request({
    url: `/positive/pre/add`,
    method: 'post',
    data: param
  })
}

// 废弃接口
// // 广告推广流水
// export function adFlowWater(params) {
//   return request({
//     url: `/statistics/ad/adFlowWater/${params.adId}`,
//     method: 'post',
//     data: params
//   })
// }
// // 广告推广流水导出
// export function adFlowWaterExport(adId) {
//   return process.env.VUE_APP_BASE_API + `/statistics/ad/adFlowWaterExport/${adId}`
// }

// 广告曝光点击明细
export function adExposureClickList(params) {
  return request({
    url: `/statistics/ad/adExposureClickList/${params.adId}`,
    method: 'post',
    data: params
  })
}
// 广告曝光点击明细导出
export function adExposureClickExport(adId) {
  return request({
    url: `/statistics/ad/adExposureClickExport/${adId}`,
    method: 'post'
  })
}

export default {
  list(params) {
    return request({
      url: '/positive/list',
      method: 'post',
      data: params
    })
  },
  putaway(params) {
    return request({
      url: `/positive/putaway/${params.advertisementId}/${params.status}`,
      method: 'post'
    })
  },
  del(params) {
    return request({
      url: `/positive/delete/${params.advertisementId}`,
      method: 'post'
    })
  },
  // 广告概况
  overview(params) {
    return request({
      url: `/statistics/ad/adOverview/${params.adId}`,
      method: 'get',
      params
    })
  },
  // 广告趋势
  adTrend(params) {
    return request({
      url: `/statistics/ad/adTrend/${params.adId}`,
      method: 'get',
      params
    })
  },
  // 广告统计明细
  adUserRank(params) {
    return request({
      url: `/statistics/ad/adUserRank`,
      method: 'post',
      data: params
    })
  },
  // 人员详情
  adUserJoin(params) {
    return request({
      url: `/statistics/ad/adUserJoin/${params.adId}/${params.userId}`,
      method: 'get',
      params
    })
  },
  // 广告统计明细导出
  adUserRankExport() {
    return process.env.VUE_APP_BASE_API + `/statistics/ad/adUserRankExport`
  }
}
