<template>
  <div class="vip-manage">
    <div class="container">
      <!-- 左侧会员等级列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <span class="header-title">会员等级</span>
          <el-button type="primary" size="small" @click="handleCreate">添加会员等级</el-button>
        </div>

        <div class="member-list" v-loading="loading">
          <div
            v-for="item in packageList"
            :key="item.packageId"
            class="member-item"
            :class="{ active: selectedMember && selectedMember.packageId === item.packageId }"
            @click="selectMember(item)"
          >
            <div class="member-name">
              {{ item.name }}
              <span
                class="status-badge"
                :class="{ 'status-enabled': item.status === 1, 'status-disabled': item.status === 0 }"
              >
                {{ item.status === 1 ? '启用' : '禁用' }}
              </span>
            </div>
            <div class="member-level">等级{{ item.memberLevel }}</div>
            <div class="member-actions">
              <el-button
                type="text"
                size="mini"
                :style="{ color: item.status === 1 ? '#f56c6c' : '#67c23a' }"
                @click.stop="handleToggleStatus(item)"
              >
                {{ item.status === 1 ? '禁用' : '启用' }}
              </el-button>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="!loading && packageList.length === 0" class="empty-state">
            <p>暂无会员等级</p>
            <el-button type="primary" @click="handleCreate">添加会员等级</el-button>
          </div>
        </div>
      </div>

      <!-- 右侧编辑表单 -->
      <div class="right-panel">
        <div class="panel-header">
          <span class="header-title">{{ formTitle }}</span>
        </div>

        <div class="form-container">
          <el-form
            ref="packageForm"
            :model="packageForm"
            :rules="formRules"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="会员名称" prop="name">
              <el-input v-model="packageForm.name" placeholder="会员名称" />
            </el-form-item>

            <el-form-item label="会员等级" prop="memberLevel">
              <el-select
                v-model="packageForm.memberLevel"
                placeholder="请选择会员等级"
                style="width: 200px"
              >
                <el-option
                  v-for="level in memberLevels"
                  :key="level.memberLevel"
                  :label="level.name"
                  :value="level.level"
                />
              </el-select>
            </el-form-item>

            <!-- 会员价格 -->
            <el-form-item label="会员价格(元/年)" required>
              <div class="price-grid">
                <div class="price-item">
                  <label>安卓</label>
                  <el-form-item prop="anPrice">
                    <el-input-number
                      v-model="packageForm.anPrice"
                      :min="0"
                      :precision="2"
                      placeholder="安卓"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>苹果</label>
                  <el-form-item prop="iosPrice">
                    <el-input-number
                      v-model="packageForm.iosPrice"
                      :min="0"
                      :precision="2"
                      placeholder="苹果价格"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>华为鸿蒙</label>
                  <el-form-item prop="hmPrice">
                    <el-input-number
                      v-model="packageForm.hmPrice"
                      :min="0"
                      :precision="2"
                      placeholder="鸿蒙"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>web</label>
                  <el-form-item prop="webPrice">
                    <el-input-number
                      v-model="packageForm.webPrice"
                      :min="0"
                      :precision="2"
                      placeholder="Web"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </el-form-item>

            <!-- 苹果价格码 -->
            <el-form-item label="苹果价格码" prop="iosProductCode">
              <el-input v-model="packageForm.iosProductCode" placeholder="请输入苹果价格码" />
            </el-form-item>

            <!-- 会员划线价 -->
            <el-form-item label="会员划线价(元/年)" required>
              <div class="price-grid">
                <div class="price-item">
                  <label>安卓</label>
                  <el-form-item prop="anOrigPrice">
                    <el-input-number
                      v-model="packageForm.anOrigPrice"
                      :min="0"
                      :precision="2"
                      placeholder="安卓"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>苹果</label>
                  <el-form-item prop="iosOrigPrice">
                    <el-input-number
                      v-model="packageForm.iosOrigPrice"
                      :min="0"
                      :precision="2"
                      placeholder="苹果"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>鸿蒙</label>
                  <el-form-item prop="hmOrigPrice">
                    <el-input-number
                      v-model="packageForm.hmOrigPrice"
                      :min="0"
                      :precision="2"
                      placeholder="鸿蒙"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
                <div class="price-item">
                  <label>web</label>
                  <el-form-item prop="webOrigPrice">
                    <el-input-number
                      v-model="packageForm.webOrigPrice"
                      :min="0"
                      :precision="2"
                      placeholder="web"
                      style="width: 100%"
                    />
                  </el-form-item>
                </div>
              </div>
            </el-form-item>

            <el-form-item label="会员介绍" prop="description">
              <custom-editor
                v-model="packageForm.description"
                :height="200"
                placeholder="请输入内容"
              />
            </el-form-item>

            <el-form-item>
              <el-button @click="handleCancel">取消</el-button>
              <el-button type="primary" @click="handleSubmit">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createPackage, listPackage, updatePackage, getVipLevelList } from '@/api/vip'
import CustomEditor from '@/components/wangEditor'

export default {
  name: 'VipManage',
  components: {
    CustomEditor
  },
  data() {
    return {
      loading: false,
      packageList: [],
      memberLevels: [], // 会员等级选项
      selectedMember: null, // 当前选中的会员
      isEdit: false,
      packageForm: {
        packageId: null,
        name: '',
        memberLevel: null,
        anPrice: 0,
        iosPrice: 0,
        iosProductCode: '',
        hmPrice: 0,
        webPrice: 0,
        anOrigPrice: 0,
        iosOrigPrice: 0,
        hmOrigPrice: 0,
        webOrigPrice: 0,
        description: '',
        status: 1
      },
      formRules: {
        name: [
          { required: true, message: '请输入会员名称', trigger: 'blur' },
          { validator: this.validateName, trigger: 'blur' }
        ],
        memberLevel: [
          { required: true, message: '请输入会员等级', trigger: 'blur' },
          { validator: this.validateLevel, trigger: 'blur' }
        ],
        anPrice: [
          { required: true, message: '请输入安卓价格', trigger: 'blur' },
          { validator: this.validatePrice, trigger: 'blur' }
        ],
        iosPrice: [
          { required: true, message: '请输入苹果价格', trigger: 'blur' },
          { validator: this.validatePrice, trigger: 'blur' }
        ],
        iosProductCode: [
          { required: true, message: '请输入苹果价格码', trigger: 'blur' }
        ],
        hmPrice: [
          { required: true, message: '请输入鸿蒙价格', trigger: 'blur' },
          { validator: this.validatePrice, trigger: 'blur' }
        ],
        webPrice: [
          { required: true, message: '请输入Web价格', trigger: 'blur' },
          { validator: this.validatePrice, trigger: 'blur' }
        ],
        anOrigPrice: [
          { required: true, message: '请输入安卓划线价', trigger: 'blur' },
          { validator: this.validateOriginalPrice, trigger: 'blur' }
        ],
        iosOrigPrice: [
          { required: true, message: '请输入苹果划线价', trigger: 'blur' },
          { validator: this.validateOriginalPrice, trigger: 'blur' }
        ],
        hmOrigPrice: [
          { required: true, message: '请输入鸿蒙划线价', trigger: 'blur' },
          { validator: this.validateOriginalPrice, trigger: 'blur' }
        ],
        webOrigPrice: [
          { required: true, message: '请输入Web划线价', trigger: 'blur' },
          { validator: this.validateOriginalPrice, trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入会员介绍', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    formTitle() {
      if (!this.selectedMember && !this.isEdit) {
        return '编辑'
      }
      return this.isEdit ? '编辑' : '编辑'
    }
  },
  mounted() {
    this.getPackageList()
    this.getMemberLevels()
  },
  methods: {
    // 获取会员套餐列表
    async getPackageList() {
      this.loading = true
      try {
        const res = await listPackage({})
        this.packageList = res

        // 如果有数据且没有选中项，默认选中第一个
        if (this.packageList.length > 0 && !this.selectedMember) {
          this.selectMember(this.packageList[0])
        }
      } catch (error) {
        this.$message.error('获取会员套餐列表失败')
      } finally {
        this.loading = false
      }
    },

    // 获取会员等级列表
    async getMemberLevels() {
      try {
        const response = await getVipLevelList()
        this.memberLevels = response || []
      } catch (error) {
        this.$message.error('获取会员等级列表失败')
      }
    },

    // 选择会员
    selectMember(member) {
      this.selectedMember = member
      this.isEdit = true
      this.packageForm = { ...member }
    },

    // 新增
    handleCreate() {
      this.isEdit = false
      this.selectedMember = null
      this.resetForm()
    },



    // 取消编辑
    handleCancel() {
      this.isEdit = false
      if (this.selectedMember) {
        this.packageForm = { ...this.selectedMember }
      } else {
        this.resetForm()
      }
    },

    // 切换启用/禁用状态
    async handleToggleStatus(item) {
      const newStatus = item.status === 1 ? 0 : 1
      const actionText = newStatus === 1 ? '启用' : '禁用'

      try {
        await this.$confirm(`确定要${actionText}该会员套餐吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用更新API
        const updateData = { ...item, status: newStatus }
        await updatePackage(updateData)

        this.$message.success(`${actionText}成功`)
        this.getPackageList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error(`${actionText}失败`)
        }
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        await this.$refs.packageForm.validate()

        const submitData = { ...this.packageForm }

        if (this.selectedMember && this.selectedMember.packageId) {
          // 编辑现有会员
          await updatePackage(submitData)
          this.$message.success('编辑成功')
        } else {
          // 创建新会员
          await createPackage(submitData)
          this.$message.success('创建成功')
        }

        this.isEdit = false
        this.getPackageList()
      } catch (error) {
        if (error !== false) { // 排除表单验证失败的情况
          this.$message.error(this.selectedMember ? '编辑失败' : '创建失败')
        }
      }
    },

    // 重置表单
    resetForm() {
      this.packageForm = {
        packageId: null,
        name: '',
        memberLevel: null,
        anPrice: 0,
        iosPrice: 0,
        iosProductCode: '',
        hmPrice: 0,
        webPrice: 0,
        anOrigPrice: 0,
        iosOrigPrice: 0,
        hmOrigPrice: 0,
        webOrigPrice: 0,
        description: '',
        status: 1
      }
      if (this.$refs.packageForm) {
        this.$refs.packageForm.clearValidate()
      }
    },

    // 验证会员名称唯一性
    validateName(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 检查是否与现有套餐名称重复（编辑时排除自己）
      const existingPackage = this.packageList.find(pkg =>
        pkg.name === value && (!this.isEdit || pkg.packageId !== this.packageForm.packageId)
      )

      if (existingPackage) {
        callback(new Error('会员名称不能重复'))
      } else {
        callback()
      }
    },

    // 验证会员等级唯一性
    validateLevel(rule, value, callback) {
      if (!value) {
        callback()
        return
      }

      // 检查是否与现有等级重复（编辑时排除自己）
      const existingLevel = this.packageList.find(pkg =>
        pkg.memberLevel === value && (!this.selectedMember || pkg.packageId !== this.packageForm.packageId)
      )

      if (existingLevel) {
        callback(new Error('会员等级不能重复'))
      } else {
        callback()
      }
    },

    // 验证价格层级关系
    validatePrice(rule, value, callback) {
      if (value === undefined || value === null) {
        callback()
        return
      }

      // 检查高等级会员价格是否高于低等级会员
      const currentLevel = this.packageForm.memberLevel
      // 获取平台名称，处理字段名称映射
      let platform = rule.field.replace('Price', '')
      if (platform === 'an') platform = 'anPrice'
      else if (platform === 'ios') platform = 'iosPrice'
      else if (platform === 'hm') platform = 'hmPrice'
      else if (platform === 'web') platform = 'webPrice'

      const lowerLevelPackages = this.packageList.filter(pkg =>
        pkg.memberLevel < currentLevel && (!this.selectedMember || pkg.packageId !== this.packageForm.packageId)
      )

      for (const pkg of lowerLevelPackages) {
        const lowerPrice = pkg[platform]
        if (lowerPrice && value <= lowerPrice) {
          callback(new Error(`价格必须高于等级${pkg.memberLevel}的会员价格(¥${lowerPrice})`))
          return
        }
      }

      callback()
    },

    // 验证划线价必须高于会员价格
    validateOriginalPrice(rule, value, callback) {
      if (value === undefined || value === null) {
        callback()
        return
      }

      // 获取对应的会员价格字段
      let platform = rule.field.replace('OrigPrice', '') // 获取平台名称
      let memberPriceField = platform + 'Price'

      const memberPrice = this.packageForm[memberPriceField]

      if (memberPrice && value <= memberPrice) {
        callback(new Error('划线价必须高于会员价格'))
      } else {
        callback()
      }
    }
  }
}
</script>

<style scoped>
.vip-manage {
  height: 100vh;
  background: #f5f5f5;
}

.container {
  display: flex;
  height: 100%;
}

/* 左侧面板 */
.left-panel {
  width: 300px;
  background: white;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.member-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.member-item {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.member-item:hover {
  background: #f8f9fa;
}

.member-item.active {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: normal;
}

.status-enabled {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-disabled {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.member-level {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.member-actions {
  display: none;
}

.member-item:hover .member-actions {
  display: block;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
  color: #999;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.form-container {
  flex: 1;
  padding: 20px 40px;
  overflow-y: auto;
}

/* 价格网格布局 */
.price-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 8px;
}

.price-item {
  display: flex;
  flex-direction: column;
}

.price-item label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

/* 表单样式调整 */
::v-deep .el-form-item {
  margin-bottom: 24px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

::v-deep .el-input-number {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .price-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
    height: 200px;
  }

  .price-grid {
    grid-template-columns: 1fr;
  }
}
</style>
