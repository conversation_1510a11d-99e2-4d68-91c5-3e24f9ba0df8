<template>
  <el-dialog
    class="authorDialog"
    title="选择作者"
    :visible.sync="authorDialogVisible"
    width="420px"
    :before-close="handleClose"
    center
  >
    <el-input
      v-model="phone"
      placeholder="请输入手机号查找"
      @change="search"
    >
      <el-button slot="append" icon="el-icon-search" @click="search" />
    </el-input>
    <div v-show="!info[0].authorId && phone !== ''" class="data-none">
      <h2>暂无数据</h2>
      <p>请作者认证为平台创作者后再选择</p>
    </div>
    <div v-show="info[0].authorId && phone !== ''" class="authorCard">
      <img :src="info[0].avatarUrl">
      <div>
        <h3>{{ info[0].authorName }}
          <p>{{ info[0].identityName }}</p>
        </h3>
        <p>{{ info[0].company }} {{ info[0].department }}</p>
        <p>
          <span>抖喇 <i>{{ info[0].doulaNum }}</i></span>
          <span>文章 <i>{{ info[0].articleNum }}</i></span>
          <span>视频 <i>{{ info[0].videoNum }}</i></span>
          <span>关注 <i>{{ info[0].followNum }}</i></span>
          <span>粉丝 <i>{{ info[0].fansNum }}</i></span>
        </p>
      </div>
      <i class="el-icon-success" />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleClose('enter')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { authorList, authorListDoula } from '@/api/marketing/taskExecute'
export default {
  props: {
    authorDialogVisible: {
      type: Boolean,
      default: false
    },
    authorInfo: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'creator'
    }
  },
  data() {
    return {
      phone: '',
      info: [{}]
    }
  },
  watch: {
    phone(v) {
      if (v.length === 11) this.search()
    }
  },
  methods: {
    handleClose(e) {
      if (this.info[0].authorId && e === 'enter') {
        this.$emit('update:authorInfo', this.info[0])
      }
      this.phone = ''
      this.info = [{}]
      this.$emit('update:authorDialogVisible', false)
    },
    search() {
      const list = this.type === 'creator' ? authorList : authorListDoula
      const query = {
        phone: this.phone
      }
      list(query).then(res => {
        if (res.length > 0) this.info = res
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.authorDialog {
    ::v-deep .el-dialog__body {
      padding: 45px 20px 28px;
    }
    ::v-deep .el-dialog__footer {
      border-top: 1px solid #f0f2f5;
    }
    .data-none {
      margin-top: 30px;
      text-align: center;
      h2 {
        margin: 0 auto;
        font-size: 20px;
        color: #333;
        line-height: 34px;
      }
      p {
        margin: 0 auto;
        font-size: 12px;
        color: #c5c5c5;
        line-height: 34px;
      }
    }
    .authorCard {
      position: relative;
      margin-top: 24px;
      display: flex;
      align-items: center;
      padding: 16px;
      width: 380px;
      height: 106px;
      background-color: #f5f4f4;
      i {
        color: #44b9a2;
        font-style: normal;
        &.el-icon-success {
          position: absolute;
          top: calc(50% - 12px);
          right: 16px;
          font-size: 25px;
        }
      }
      img {
        border-radius: 50%;
        width: 75px;
        height: 75px;
        margin-right: 12px;
      }
      h3 {
        display: flex;
        align-items: center;
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: bold;
        line-height: 22px;
        p {
          margin-left: 6px;
        }
      }
      p {
        color: #999;
        margin: 5px 0;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
</style>
