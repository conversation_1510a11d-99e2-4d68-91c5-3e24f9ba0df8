import request from '@/utils/request'

// 教程基本信息详情
export function getCourseDetail(query) {
  return request({
    url: '/course/web/detail/' + query,
    method: 'post',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// sop教程详情
export function getSopCourseDetail(query) {
  return request({
    url: `/sop/web/detail/${query.sopId}/${query.sopSource}`,
    method: 'post',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// sop任务详情信息
export function getSopTaskDetail(query) {
  return request({
    url: `/sop/web/task/detail/${query.sopId}/${query.sopSource}`,
    method: 'post',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// sop任务数据列表
export function getTaskSOPList(query) {
  return request({
    url: '/sop/task/data/sop/list',
    method: 'post',
    data: query,
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// course任务数据列表
export function getTaskCourseList(query) {
  return request({
    url: '/course/task/data/course/list',
    method: 'post',
    data: query,
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}
