/**
 * 作用：表格类页面的公共属性和方法
 * 作者：邱一池
 * 时间：2021年6月29日
 */

// import { parseTime } from '@/utils' // 时间格式转换方法
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import ATable from '@/components/ATable' // 引入再封装的element-ui table组件

export default {
  name: 'CTable',
  components: { Pagination, ATable },
  data() {
    const _this = this
    return {
      listLoading: false, // 列表请求状态
      list: [],
      total: 0,
      pager: {
        page: 1,
        pageSize: 10
      },
      layout: 'total, prev, pager, next, jumper', // 默认分页样式
      duration: [],
      pickerOptions: {
        disabledDate(time) {
          if (typeof (_this.duration[0]) === 'undefined') {
            return time.getTime() > Date.now()// 禁止选择以后的时间
          } else {
            const currentTime = _this.duration[0]
            const threeMonths = 60 * 60 * 1000 * 24 * 31 * 11
            if (currentTime) {
              return ((time.getTime() > currentTime.getTime() + threeMonths || time.getTime() < currentTime.getTime() - threeMonths) || time.getTime() > Date.now())
    	      }
          }
        },
        onPick({ minDate, maxDate }) {
          // 当第一时间选中才设置禁用
          if (minDate && !maxDate) {
            _this.duration[0] = minDate
          }
          if (maxDate) {
            _this.duration[1] = maxDate
          }
        }
      },
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ]
    }
  },
  watch: {
    condition: {
      handler() {
        this.pager.page = 1
        this.getList()
      },
      deep: true
    }
  },
  // created时会调用getDate(),会改变日期，触发getList()
  methods: {
    getList() {
      if (this.listLoading) {
        return
      }
      this.listLoading = true

      const data = this.getListData()
      return this.request.list(data).then(res => {
        this.listLoading = false

        if (this.hasPage === 1) { // 有分页，数据格式有records
          if (res && res.records && res.records.length === 0) {
            this.list = []
          } else {
            this.handleData(res.records) // 对数据进行处理，添加各种百分率数据
          }
          this.total = res.total
        } else if (this.hasPage === 0) { // 无分页，数据格式是[]
          if (JSON.stringify(res) === '{}' || JSON.stringify(res) === '[]') { // 兼容无数据时返回{}或者[]
            this.list = []
          } else {
            this.handleData(res) // 对数据进行处理，添加各种百分率数据
          }
        }
      }, () => {
        this.listLoading = false
      })
    },
    handleData(records) {
      const rec = records
      rec.forEach((item, index) => {
        item.staffNum = parseInt(item.staffNum) // 用户数
        item.trainPeopleNum = parseInt(item.trainPeopleNum) // 培训人数
        item.trainPersonNum = parseInt(item.trainPersonNum) // 培训人次
        item.trainTaskNum = parseInt(item.trainTaskNum) // 培训任务数
        item.joinPeopleNum = parseInt(item.joinPeopleNum) // 参与人次
        item.passPeopleNum = parseInt(item.passPeopleNum) // 通过人次
        item.activePeopleNum = parseInt(item.activePeopleNum) // 活跃数
        item.coverageRate = item.coverageRate + '%' // 培训覆盖率
        item.partRate = item.partRate + '%' // 培训参与率
        item.passRate = item.passRate + '%'// 培训通过率
        item.controlRate = item.controlRate + '%'// 参与达标率
        item.activeRate = item.activeRate + '%'// 活跃率
      })
      this.list = rec
    },
    handleFilter() {
      this.pager.page = 1
      this.getList()
    },
    handlePagination(val) {
      this.pager = val
      this.getList()
    },
    // 日期选择器
    changePicker(data) {
      if (data && data.length > 0) {
        this.condition.startTime = data[0]
        this.condition.endTime = data[1]
      }
    },
    // 默认显示本月及上月
    getDate() {
      const myDate = new Date()
      const year = myDate.getFullYear() // 获取当前年份 (2021)
      const month = myDate.getMonth() // 获取当前月份 (0-11)
      const monthNew = month + 1 < 11 ? '0' + month : '' + month
      this.condition.startTime = year + monthNew
      this.condition.endTime = year + monthNew
      this.timeRange.push(this.condition.startTime)
      this.timeRange.push(this.condition.endTime)
      this.dataJson.startTime = this.copy(this.condition.startTime)
      this.dataJson.endTime = this.copy(this.condition.endTime)
    },
    // 区域级联change事件
    handlerChange(e) {
      if (e) {
        this.condition.areaId = e[e.length - 1]
        if (e[0] == '0') {
          this.condition.areaType = 1
        } else {
          const arr = [1, 2, 3, 4]
          this.condition.areaType = arr[e.length]
        }
      }
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
    // parseTime
  }
}
