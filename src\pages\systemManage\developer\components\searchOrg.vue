<template>
  <el-dialog title="选择关联单位" :visible.sync="show" top="50px" width="1200px" center :show-close="false" :destroy-on-close="true" :before-close="beforeClose" @open="open">
    <div class="search-column">
      <div class="search-column__item">
        <div class="search-column__label">单位名称：</div>
        <div class="search-column__inner">
          <el-input v-model="tableQuery.condition.keyword" v-search="searchObj" placeholder="请输入搜索关键字" />
        </div>
      </div>
    </div>
    <el-table ref="singleTable" :data="tableData" :row-style="rowClass" highlight-current-row style="width: 100%" @current-change="handleCurrentChange">
      <el-table-column property="orgId" label="ID" />
      <el-table-column property="orgName" label="单位名称" width="400px" />
      <el-table-column property="typeName" label="单位类型" />
      <el-table-column property="status" label="服务状态" :formatter="parseStatus" />
    </el-table>

    <!-- pagination -->
    <Pagination class="text-center padding-none" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleConfirm()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getUnitList } from '@/api/userManage'
import Pagination from '@/components/Pagination/index.vue'

export default {
  name: 'DialogOperation',
  components: {
    Pagination
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    current: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      // 请求参数
      tableQuery: {
        condition: {
          keyword: null,
          level: null,
          status: 1,
          type: null
        },
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 分页
      total: 0,
      layout: 'total, prev, pager, next, jumper',
      // 单位名称搜索
      searchObj: {
        fn: this.searchKeyword
      },
      currentRow: null,
      currentRowIndex: null
    }
  },
  methods: {
    open() {
      this.tableQuery.condition.keyword = ''
      this.currentRow = null
      this.currentRowIndex = null
      this.getUnitUserList()
      if (!(JSON.stringify(this.current) === '{}')) {
        this.currentRow = this.current
        this.handleCurrentChange(this.current)
      }
    },
    // get unit user
    getUnitUserList() {
      getUnitList(this.tableQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    rowClass({ row, rowIndex }) {
      const currentRow = {
        background: '#ecf5ff'
      }
      if (rowIndex === this.currentRowIndex) {
        return currentRow
      }
    },
    handleCurrentChange(val) {
      if (val) {
        this.currentRowIndex = this.tableData.findIndex(item => {
          return item.orgId === val.orgId
        })
        this.currentRow = val
      }
    },
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getUnitUserList()
    },
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUnitUserList()
    },
    handleConfirm() {
      if (!this.currentRow) return this.$message.warning('请选择关联单位')
      this.$emit('confirm', this.currentRow)
    },
    handleClose() {
      this.currentRow = null
      this.$emit('close', false)
    },
    beforeClose() {
      this.currentRow = null
      this.$refs.singleTable.setCurrentRow()
      this.$emit('update:show', false)
    },
    parseStatus(row) {
      return row.status === 1 ? '启用' : '停用'
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination-container {
  padding: 0 !important;
}
.check-row {
  background: #66b1ff !important;
  color: #fff !important;
}
</style>
