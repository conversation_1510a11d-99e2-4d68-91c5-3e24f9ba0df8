<template>
  <section class="studentList">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        placeholder="姓名/手机/单位/学分项目"
        @change="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.status" placeholder="状态" @change="handleFilter()">
        <el-option
          v-for="item in statusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-date-picker
        v-model="time"
        type="daterange"
        range-separator="~"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="yyyy-MM-dd"
        @change="timeChange"
      />
    </div>
    <h2>
      学分列表
      <el-button type="primary" @click="exportList()">导出</el-button>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="look(scope.row)">查看信息</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <info-detail
      :dialog-visible.sync="infoDialogVisible"
      :look="true"
      :info="info"
    />
  </section>
</template>

<script>
import InfoDetail from './infoDetail.vue'
import Pagination from '@/components/Pagination'
import { getStudentList, exportStudentList } from '@/api/credit'
import { userDetail } from '@/api/certificate'
export default {
  components: {
    Pagination,
    InfoDetail
  },
  data() {
    return {
      time: [],
      listQuery: {
        condition: {
          keyword: '',
          status: null,
          startTime: '',
          endTime: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      statusOptions: [
        { label: '全部状态', value: null },
        { label: '待付款', value: 0 },
        { label: '学习中', value: 1 },
        { label: '已学完', value: 2 },
        { label: '已考核', value: 3 },
        { label: '已申请学分', value: 4 },
        { label: '获得学分', value: 5 }
      ],
      infoDialogVisible: false,
      tableData: [],
      tableColumn: [
        { prop: 'userName', label: '姓名', width: '110' },
        { prop: 'phone', label: '手机', width: '110' },
        { prop: 'identityName', label: '身份', width: '80' },
        { prop: 'majorName', label: '专科', width: '80' },
        { prop: 'academicName', label: '职称', width: '80' },
        { prop: 'orgName', label: '单位', width: '160' },
        { prop: 'areaName', label: '地区', width: '160' },
        { prop: 'projectName', label: '学分项目', width: '180' },
        { prop: 'credit', label: '学分', width: '150' },
        { prop: 'status', label: '状态', width: '80' },
        { prop: 'joinTime', label: '报名时间', width: '160' }
      ],
      info: {}
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    look(info) {
      userDetail({ userId: info.userId }).then(res => {
        this.info = res
        this.infoDialogVisible = true
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    timeChange() {
      if (this.time) {
        this.listQuery.condition.startTime = this.time[0]
        this.listQuery.condition.endTime = this.time[1]
      } else {
        this.listQuery.condition.startTime = ''
        this.listQuery.condition.endTime = ''
      }
      this.getList()
    },
    getList() {
      getStudentList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.status = this.statusOptions.find(i => i.value === v.status).label
          v.credit = `${v.creditCategory}  ${v.score}分`
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportList() {
      exportStudentList(this.listQuery.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(res => {
        if (res !== null && (res.message === null || res.message === '')) {
          this.$message.error('导出失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.studentList {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input,
    .el-cascader,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
}
</style>
