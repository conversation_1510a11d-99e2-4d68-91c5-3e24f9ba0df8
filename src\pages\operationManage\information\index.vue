<template>
  <div>
    <div v-if="$route.name === 'InformationManage'" class="app-container">
      <!-- search -->
      <div class="search-column">
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.type" filterable clearable placeholder="请选择搜索方式">
            <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-input v-model="tableQuery.condition.keywords" placeholder="根据左侧查询方式对应关键字" clearable @change="init">
            <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
          </el-input>
        </div>
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.cateId" filterable clearable placeholder="请选择文章类型" @change="handleCateId">
            <el-option v-for="item in cateList" :key="item.cateId" :label="item.name" :value="item.cateId" />
          </el-select>
        </div>
        <div class="search-column__item fr">
          <el-button type="primary" :disabled="selection.length<1" @click="del">删除</el-button>
          <el-button type="primary" :disabled="selection.length<1" @click="set">设置虚拟量</el-button>
          <el-button type="primary" @click="add">新增</el-button>
        </div>
      </div>
      <!-- table -->
      <a-table ref="multipleTable" :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
        <template slot="cover" slot-scope="{row}">
          <img style="width: 120px;display:block;" :src="row.cover" @click="handlePreview(row.cover)">
        </template>
        <template slot="actions" slot-scope="{row}">
          <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
          <el-button size="mini" type="text" @click="stop(row)">{{ row.stickTime == '0'?'置顶':'取消置顶' }}</el-button>
          <el-button size="mini" type="text" @click="toStatistics(row)">设置虚拟量</el-button>
        </template>
      </a-table>
      <!-- pagination -->
      <Pagination class="text-center" :layout="layout" :total="total" :page.sync="tableQuery.pager.page" @pagination="handlePagination" />

      <!-- 封面预览 -->
      <el-dialog :visible.sync="dialogVisible">
        <img width="100%" :src="dialogImageUrl">
      </el-dialog>

      <!-- del dialog -->
      <el-dialog
        title="提醒"
        :visible.sync="delVisible"
        width="30%"
        center
        :before-close="handleClose"
      >
        <span class="dialogContent">确定要删除文章？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="delVisible = false">取 消</el-button>
          <el-button type="primary" @click="handlerDelConfirm">确 定</el-button>
        </span>
      </el-dialog>
      <!-- setInvented dialog -->
      <el-dialog
        title="设置虚拟量"
        :visible.sync="setInventedVisible"
        width="30%"
        center
        :before-close="handleClose"
        :destroy-on-close="true"
      >
        <div class="setContent">
          <span>虚拟阅读量:</span>
          <el-input
            v-model="baseHits"
            type="number"
            placeholder="请输入内容"
            oninput="this.value = this.value.replace(/[^0-9]/g, '');if(value.length>9)value=value.slice(0,9);if(value<0)value=0"
          />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="setInventedVisible = false">取 消</el-button>
          <el-button type="primary" @click="setInventedConfirm">确 定</el-button>
        </span>
      </el-dialog>

    </div>

    <router-view />
  </div>
</template>

<script>
import request from '@/api/article'
import Pagination from '@/components/Pagination/index.vue' // Secondary package based on el-pagination
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件

const columns = [
  { props: { type: 'selection', align: 'center', fixed: 'left' }},
  { props: { label: '文章标题', align: 'center', prop: 'title' }},
  {
    props: { label: '封面', align: 'center', width: '150' },
    slot: 'cover'
  },
  { props: { label: '文章分类', align: 'center', prop: 'cateName' }},

  { props: { label: '作者', align: 'center', prop: 'author' }},
  { props: { label: '来源', align: 'center', prop: 'origin' }},

  { props: { label: '关键词', align: 'center', prop: 'keywords' }},

  { props: { label: '阅读量', align: 'center', prop: 'hits' }},
  { props: { label: '虚拟阅读量', align: 'center', prop: 'baseHits' }},
  { props: { label: '发布人', align: 'center', prop: 'createUname' }},
  { props: { label: '发布时间', align: 'center', prop: 'createTime' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]
export default {
  name: 'ArticleIndex',
  components: {
    Pagination,
    ATable
  },
  data() {
    return {
      columns,
      request,
      typeList: [
        { value: 1, name: '标题' },
        { value: 2, name: '关键字' },
        { value: 3, name: '作者' },
        { value: 4, name: '来源' }
      ],
      cateList: [],
      // 请求参数
      tableQuery: {
        condition: {
          keywords: '',
          type: 1,
          cateId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 列表数据
      list: null,
      total: 0,
      // pagination
      layout: 'total, prev, pager, next, jumper', // 默认分页样式
      selection: [], // 选中的值
      setInventedVisible: false,
      delVisible: false,
      dialogImageUrl: '',
      dialogVisible: false,
      listLoading: false,
      baseHits: '' // 虚拟阅读量
    }
  },
  async created() {
    if (this.$route.name !== 'InformationManage') return
    await this.request.cateList().then(res => {
      this.cateList = res
      // res.forEach(v => {
      //   if (v.name === '企业动态') {
      //     this.tableQuery.condition.cateId = v.cateId
      //     return
      //   }
      // })
    }).catch(err => {
      this.$message.error(err)
    })
    await this.init()
  },
  methods: {
    init(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      this.request.list(this.tableQuery).then(res => {
        this.list = res.records
        this.total = res.total
      }).catch(err => {
        this.$message.error(err)
      })
    },
    // table select
    onSelectChange(arr) {
      this.selection = arr
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.init(false)
    },
    handleCateId(e) {
      this.tableQuery.condition.cateId = e
      this.init()
    },
    del() {
      this.delVisible = true
    },
    set() {
      this.setInventedVisible = true
    },
    add() {
      this.$router.push({ name: 'ArticleDetail' })
    },
    editRow(row) {
      this.$router.push({
        name: 'ArticleDetail',
        query: {
          id: row.id
        }
      })
    },
    deleteRow(row) {
      this.selection = []
      this.selection.push(row)
      this.delVisible = true
    },
    stop(row) {
      this.$confirm(`${row.stickTime === '0' ? '是否确认置顶该文章?' : '是否确认取消该文章置顶?'}`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          newsId: row.id,
          stick: row.stickTime === '0' ? 1 : 0
        }
        this.request.mediaStick(data).then(res => {
          this.$message.success('操作成功')
          this.init()
        }).catch(err => {
          this.$message.error(err)
        })
      }).catch(() => {
        this.$message.info('已取消该操作')
      })
    },
    toStatistics(row) {
      this.selection = []
      this.selection.push(row)
      this.baseHits = row.baseHits
      this.setInventedVisible = true
    },
    handlerDelConfirm() {
      const ids = []
      this.selection.forEach(v => {
        ids.push(v.id)
      })
      this.request.deleteMedia(ids).then(res => {
        this.$message.success('删除成功')
        this.delVisible = false
        this.selection = []
        this.init()
      }).catch(err => {
        this.$message.error(err)
      })
    },
    setInventedConfirm() {
      const data = {
        baseHits: this.baseHits,
        ids: []
      }
      this.selection.forEach(v => {
        data.ids.push(v.id)
      })
      this.request.virtualSet(data).then(res => {
        this.$message.success('操作成功')
        this.setInventedVisible = false
        this.baseHits = null
        this.selection = []
        this.init()
      }).catch(err => {
        this.$message.error(err)
      })
    },
    handlePreview(url) {
      this.dialogImageUrl = url
      this.dialogVisible = true
    },
    handleClose(done) {
      this.selection = []
      this.baseHits = null
      this.$refs.multipleTable.$children[0].clearSelection()
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
.dialogContent {
  text-align: center;
}

.setContent {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  span {
    display: inline-block;
    width: 120px;
  }
}
</style>
