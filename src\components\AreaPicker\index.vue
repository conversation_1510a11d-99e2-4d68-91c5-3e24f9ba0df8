<template>
  <div class="area-picker">
    <el-row class="row-extra">
      <el-col :span="8">
        <el-select v-model="province" placeholder="请选择省" @change="handleChangeSelect(province,'city')">
          <el-option
            v-for="item in provinceList"
            :key="item.areaId"
            :label="item.name"
            :value="item.areaId"
          />
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-select v-model="city" placeholder="请选择市" @change="handleChangeSelect(city, 'area')">
          <el-option
            v-for="item in cityList"
            :key="item.areaId"
            :label="item.name"
            :value="item.areaId"
          />
        </el-select>
      </el-col>
      <el-col :span="8">
        <el-select v-model="area" placeholder="请选择区" @change="handleChangeSelect(null, null)">
          <el-option
            v-for="item in areaList"
            :key="item.areaId"
            :label="item.name"
            :value="item.areaId"
          />
        </el-select>
      </el-col>
      <el-col v-if="hasAddress" class="picker-address" :span="24">
        <el-input v-model="address" placeholder="请填写详细地址信息" @keypress.native.enter="handleEnterNative($event)" @blur="handleBlur()" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getArea } from '@/api/area'
export default {
  name: 'AreaPicker',
  props: {
    obj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    hasAddress: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 省市区相关
      province: '',
      city: '',
      area: '',
      address: '',
      provinceList: [],
      cityList: [],
      areaList: [],
      // 省市区返回值
      returnData: {}
    }
  },
  created() {
    // 省市区联动
    this.hasAreaCode()
  },
  methods: {
    hasAreaCode() {
      const { province, city, area, address } = this.obj
      this.handleGetArea(null, 'province')
      if (province && province !== '') {
        this.province = province
        this.handleGetArea(province, 'city')
      }
      if (city && city !== '') {
        this.city = city
        this.handleGetArea(city, 'area')
      }
      if (area && area !== '') {
        this.area = area
      }
      if (address && address !== '') {
        this.address = address
      }
    },
    handleGetArea(params, type) {
      /**
       * @params {Object} params parentCode
       * @params {String} type area type
       */
      getArea({ parentId: params }).then(res => {
        switch (type) {
          case 'province':
            this.provinceList = res
            break
          case 'city':
            this.cityList = res
            this.areaList = []
            this.city = ''
            this.area = ''
            break
          case 'area':
            this.areaList = res
            this.area = ''
            break
          default:
            this.provinceList = res
            break
        }
      })
    },
    handleChangeSelect(params, type) {
      if (type === 'city') {
        this.returnData.province = this.province
      } else if (type === 'area') {
        this.returnData.city = this.city
      } else {
        this.returnData.area = this.area
      }
      this.handleGetArea(params, type)
      this.$emit('handleGetAreaCode', this.returnData)
    },
    handleEnterNative(e) {
      e.target.blur()
    },
    handleBlur() {
      this.returnData.address = this.address
      this.$emit('handleGetAreaCode', this.returnData)
    }
  }
}
</script>

<style scoped>
  .row-extra .el-col:not(:first-child){
    /* margin-left: 20px; */
  }
  .row-extra .picker-address{
    margin-top: 20px;
    margin-left: 0 !important;
  }
</style>
