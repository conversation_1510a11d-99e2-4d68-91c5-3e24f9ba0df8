<template>
  <div class="contain">
    <div v-show="hasData" id="levelFourChart" class="levelFourChart" />
    <div v-show="!hasData" class="empty">暂无数据</div>
  </div>
</template>

<script>
const echarts = require('echarts/lib/echarts')
require('echarts/lib/component/grid')
require('echarts/lib/chart/bar')

export default {
  name: 'StatisticsLevelFour',
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      hasData: true
    }
  },
  watch: {
    data: {
      handler(arr) {
        if (arr.length === 0) {
          this.hasData = false
          return
        } else {
          this.hasData = true
        }
        this.initCharts()
        this.setOptions()
      },
      deep: true
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.myChart = echarts.init(document.getElementById('levelFourChart'))
        window.addEventListener('resize', () => {
          this.myChart.resize()
        })
      })
    },
    handleDownload() {
      // 将echarts图表转换为canvas,并将canvas下载为图片
      const aLink = document.createElement('a')
      const blob = this.base64ToBlob()
      const evt = document.createEvent('HTMLEvents')
      evt.initEvent('click', true, true)
      aLink.download = '区域分布'
      aLink.href = URL.createObjectURL(blob)
      aLink.click()
    },
    exportImg() { // echarts返回一个 base64的URL
      const myChart = echarts.init(
        document.getElementById('levelFourChart')
      )
      return myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
    },
    base64ToBlob() { // 将base64转换blob
      const img = this.exportImg()
      const parts = img.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    setOptions() {
      const data = JSON.parse(JSON.stringify(this.data))
      const xArr = []
      const yArr = []
      data.forEach(item => {
        xArr.push(item.name)
        yArr.push(item.num)
      })
      this.myChart.setOption({
        tooltip: {},
        xAxis: {
          type: 'category',
          data: xArr
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: yArr,
          type: 'bar',
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
          },
          emphasis: { // 柱子高亮样式
            focus: 'series',
            blurScope: 'coordinateSystem',
            itemStyle: {
              color: '#6cf'
            }
          }
        }]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.levelFourChart{
  height: 640px;
  color: #E0E6F1;
}
.empty{
  width: 100%;
  height: 640px;
  line-height: 640px;
  text-align: center;
  color: #999;
  font-size: 24px;
}
</style>
