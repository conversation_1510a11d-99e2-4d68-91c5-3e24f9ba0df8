<template>
  <div>
    <div class="btns">
      <el-button type="primary" @click="addBrother">添加同级分类</el-button><br><br>
      <el-button v-if="info.level!==4" type="primary" @click="addChild">添加下级分类</el-button><br><br>
      <el-button @click="edit">编 辑</el-button><br><br>
      <el-button @click="changeOrder">修改排序</el-button><br><br>
      <el-button type="danger" @click="categoryDelete">删 除</el-button>
    </div>
    <!-- 添加分类 -->
    <el-dialog
      :title="addDialogTitle"
      :visible.sync="addDialog"
      append-to-body
      width="25%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-form ref="addForm" :model="addForm" :rules="rules">
        <el-form-item label="分类名称:" prop="name">
          <el-input v-model="addForm.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="icon:">
          <el-upload
            class="avatar-uploader"
            :data="uploadData"
            :headers="uploadHeaders"
            :action="isUploadFileApi"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <el-button type="primary" size="small" class="select-btn">上传图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div style="text-align:center;">
        <div v-show="!imgUrl" class="image_error">暂无图片</div>
        <el-image v-show="imgUrl" :size="100" fit="cover" :src="imgUrl">
          <div slot="error">
            <div class="image_error">加载失败</div>
          </div>
        </el-image><br><br>
        <el-button v-show="imgUrl" size="small" @click="deleteIcon">删除图片</el-button>
      </div>
      <p>建议98*98px，或等比例，大小在500KB及以下的JPG、JPEG、PNG格式图片。</p>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="addDialog = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="addSave">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 编辑分类 -->
    <el-dialog
      title="编辑分类"
      :visible.sync="editDialog"
      append-to-body
      width="25%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-form ref="editForm" :model="editForm" :rules="rules">
        <el-form-item v-if="info.level !== 1" label="分类上级:" prop="parentId">
          <el-cascader
            ref="elcascader"
            v-model="editForm.parentId"
            placeholder="选择分类"
            :options="cateOptions"
            :props="{
              label: 'name',
              value: 'categoryId',
              children: 'children',
              expandTrigger: 'hover',
              checkStrictly: true,
              emitPath: false,
              disabled: 'disabled'
            }"
            :show-all-levels="true"
            clearable
            filterable
            @change="handleParentId"
            @visible-change="function() {if(editForm.parentId === '') { editForm.parentId = info.parentId}}"
          />
        </el-form-item>
        <el-form-item label="分类名称:" prop="name">
          <el-input v-model="editForm.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="icon:">
          <el-upload
            class="avatar-uploader"
            :data="uploadData"
            :headers="uploadHeaders"
            :action="isUploadFileApi"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <el-button type="primary" size="small" class="select-btn">上传图片</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div style="text-align:center;">
        <div v-show="!imgUrl" class="image_error">暂无图片</div>
        <el-image v-show="imgUrl" :size="100" fit="cover" :src="imgUrl">
          <div slot="error">
            <div class="image_error">加载失败</div>
          </div>
        </el-image><br><br>
        <el-button v-show="imgUrl" size="small" @click="deleteIcon">删除图片</el-button>
      </div>
      <p>建议98*98px，或等比例，大小在500KB及以下的JPG、JPEG、PNG格式图片。</p>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="editDialog = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="editSave">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { cateAdd, cateEdit, cateDelete } from '@/api/resourceCategory'
import { getToken } from '@/utils/auth'
import { preUploadApi, uploadFileApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'

export default {
  name: 'RightClickDialog',
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      addDialogTitle: '',
      addDialog: false,
      editDialog: false,
      addForm: {
        name: '',
        iconId: 0,
        parentId: 0
      },
      editForm: {
        parentId: this.info.parentId,
        name: '',
        categoryId: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      },
      uploadData: {
        data: ''
      },
      uploadHeaders: {
        token: getToken()
      },
      isUploadFileApi: '',
      imgUrl: '',
      cateOptions: []
    }
  },
  created() {
    getCategoryTreeList(0).then(res => {
      this.cateOptions = res
    })
  },
  methods: {
    addBrother() {
      this.addDialogTitle = '添加同级分类'
      this.addForm.name = ''
      this.addForm.parentId = this.info.parentId
      // this.addForm.listOrder = this.info.listOrder + 1
      this.addForm.iconId = 0
      this.imgUrl = ''
      this.addDialog = true
    },
    addChild() {
      this.addDialogTitle = '添加下级分类'
      // if (this.addForm.listOrder || this.addForm.listOrder === 0) {
      //   delete this.addForm.listOrder
      // }
      this.addForm.name = ''
      this.addForm.parentId = this.info.categoryId
      this.addForm.iconId = 0
      this.imgUrl = ''
      this.addDialog = true
    },
    addSave() {
      this.$refs.addForm.validate(valid => {
        if (!valid) {
          return
        }
        cateAdd(this.addForm).then(() => {
          this.$message.success('添加成功')
          this.addDialog = false
          this.$emit('getList', this.info.parentId, this.info.level, this.addDialogTitle)
        })
      })
    },
    edit() {
      if (this.editForm.iconId || this.editForm.iconId === 0) {
        delete this.editForm.iconId
      }
      this.editForm.name = this.info.name
      this.editForm.categoryId = this.info.categoryId
      this.editForm.parentId = this.info.parentId
      this.imgUrl = this.info.icon
      const _info = this.info
      let levelAmount = 0
      function handleList(list, disabled, i) {
        list.forEach(v => {
          if (v.categoryId === _info.categoryId || disabled) {
            v.disabled = true
            levelAmount = Math.max(levelAmount, i)
            if (v.havChild) {
              handleList(v.children, true, i + 1)
            }
          } else {
            if (v.havChild) { handleList(v.children, false, i) }
          }
        })
      }
      handleList(this.cateOptions, false, 1)
      this.info.levelAmount = levelAmount
      this.editDialog = true
    },
    handleParentId() {
      if (this.$refs.elcascader.getCheckedNodes()[0].level + this.info.levelAmount > 4) {
        this.$message.error('分类层级不可超过四级')
        this.editForm.parentId = ''
      }
    },
    editSave() {
      this.$refs.editForm.validate(valid => {
        if (!valid) {
          return
        }
        cateEdit(this.editForm).then(() => {
          this.$message.success('编辑成功')
          if (this.editForm.parentId !== this.info.parentId) {
            this.$emit('getList', 0, 1)
          } else {
            this.$emit('getList', this.info.parentId, this.info.level)
          }
          this.editDialog = false
        })
      })
    },
    deleteIcon() {
      this.imgUrl = ''
      this.addForm.iconId = 0
      this.editForm.iconId = 0
    },
    categoryDelete() {
      this.$confirm('分类被删除后不可恢复，请确认', '提示', {
        confirmButtonText: '确 定',
        cancelButtonText: '取 消',
        type: 'warning'
      }).then(() => {
        cateDelete(this.info.categoryId).then(() => {
          this.$message.success('删除成功')
          this.$emit('getList', this.info.parentId, this.info.level)
        })
      })
    },
    handleAvatarSuccess(val) {
      this.imgUrl = val.data.url
      this.addForm.iconId = val.data.id
      this.editForm.iconId = val.data.id
    },
    async beforeAvatarUpload(val) {
      this.isUploadFileApi = ''
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      if (!JPG && !PNG && !JPEG) {
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return
      }
      const isLt500K = val.size / 1024 < 500
      if (!isLt500K) {
        this.$message.error('上传图片大小不能超过 500KB')
        return
      }
      const param = {
        filename: val.name,
        size: val.size,
        type: val.type
      }
      const res = await preUploadApi(param)
      this.uploadData.data = res
      this.isUploadFileApi = uploadFileApi
    },
    changeOrder() {
      this.$emit('changeOrder')
    }
  }
}
</script>

<style scoped lang="scss">
.btns{
  text-align: center;
  .el-button{
    width: 160px;
  }
}
.image_error{
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  color: #c0c4cc;
  background-color: #f5f7fa;
  margin: 0 auto;
}
</style>
