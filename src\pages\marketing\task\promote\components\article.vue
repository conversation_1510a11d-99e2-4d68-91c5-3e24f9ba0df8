<template>
  <section class="articleEdit">
    <div class="articleEdit-title">
      文章
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="推广产品" prop="productId">
        <el-select v-model="form.orgzId" clearable placeholder="全部企业" @change="getProductList()">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="form.productId" clearable placeholder="全部产品" no-data-text="请先选择企业">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文章标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入文章标题 (5~100个字)"
          :maxlength="100"
          style="width: 520px"
        />
      </el-form-item>
      <el-form-item label="内容类型" required>
        <el-radio-group v-model="form.contentType">
          <el-radio label="RICH_TEXT">直接编辑</el-radio>
          <el-radio label="PDF">PDF</el-radio>
        </el-radio-group>
        <editor
          v-if="form.contentType === 'RICH_TEXT'"
          v-model="form.content"
          height="356"
          @change="change"
        />
        <el-upload
          v-if="form.contentType === 'PDF'"
          accept="application/pdf"
          :show-file-list="false"
          :class="{ hideContentUpdate: form.contentList.length >= 1 }"
          :action="uploadFileApi"
          :data="uploadData"
          :limit="1"
          :file-list="form.contentList"
          :before-upload="handleBeforeUpload"
          :on-success="handleSuccess"
        >
          <div class="uploadPdf">
            <h2>
              <i class="el-icon-plus avatar-uploader-icon" /> 上传PDF
            </h2>
            大小不超过50M
          </div>
        </el-upload>
        <div v-if="form.contentType === 'PDF' && form.contentList.length >= 1" class="pdf-success">
          <img src="@/assets/images/fabu_icon_pdf.png">
          <p>{{ pdfName }}</p>
          <img src="@/assets/images/fabu_icon_del.png" class="pdf-success-del" @click="handleContentRemove">
        </div>
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="封面设置" required prop="coverIds">
        <el-radio-group v-model="form.imgSum" @input="clearImg">
          <el-radio-button :label="1">单图</el-radio-button>
          <el-radio-button :label="3">三图</el-radio-button>
        </el-radio-group>
        <UploadPic v-if="form.imgSum === 1" :key="form.coverUrls[0]" tips="添加封面图" :pic-id.sync="form.coverIds[0]" :url="form.coverUrls[0]" />
        <el-upload
          v-if="form.imgSum === 3"
          ref="uploadFile"
          list-type="picture-card"
          :class="{ hideUpdate: form.fileList.length >= 3 }"
          :action="uploadFileApi"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess"
          :on-remove="removePic"
          :file-list="form.fileList"
        >
          <span slot="default" class="imgUploadStyle">
            <i class="el-icon-plus" />
            <span>添加封面图</span>
          </span>
        </el-upload>
      </el-form-item>
      <el-form-item label="作者" prop="authorId">
        <div>
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" />
        </div>
      </el-form-item>
      <el-form-item label="推广需求" required>
        <Budget @getBudgetData="getBudgetData" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import Editor from '@/components/wangEditor'
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { getUnitList } from '@/api/userManage'
import { productList, saveOrUpdatePlatformArticleCms } from '@/api/marketing/taskPromote'
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Author from '@/pages/marketing/task/components/author.vue'
import Budget from './budget'
export default {
  components: {
    Editor,
    UploadPic,
    Author,
    Budget
  },
  data() {
    return {
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      orgList: [],
      productList: [],
      form: {
        articleId: null,
        authorId: null,
        taskUserId: null,
        authorName: '',
        title: '',
        contentType: 'RICH_TEXT',
        content: '',
        contentList: '',
        categoryIds: [],
        imgSum: 1,
        coverIds: [],
        coverUrls: [],
        fileList: []
      },
      authorInfo: {},
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        title: [
          { required: true, message: '请输入文章标题', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        productId: [
          { required: true, message: '请选择推广产品', trigger: 'change' }
        ],
        authorId: [
          { required: true, message: '请选择作者', trigger: 'change' }
        ],
        categoryIds: [
          { required: true, message: '请选择专科分类', trigger: 'change' }
        ],
        coverIds: [
          { required: true, message: '请上传封面', trigger: 'change' }
        ]
      },
      pdfName: '',
      detail: {},
      authorDialogVisible: false,
      agreementDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
        this.form.authorPhone = this.authorInfo.phone
      }
    }
  },
  mounted() {
    // 获取专科分类
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
    this.getOrg()
  },
  methods: {
    getOrg() {
      const query = {
        condition: {
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getProductList() {
      if (this.form.orgzId) {
        productList({ orgzId: this.form.orgzId, pageSize: 1000 }).then(res => {
          this.productList = res
        })
      }
    },
    change(val) {
      // 富文本回调函数,后续测试联调看是否移除
    },
    async handleBeforeUpload(val) {
      const param = {
        filename: val.name,
        size: val.size,
        type: 'adv'
      }
      this.pdfName = val.name
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        this.form.content = res.data.url
        this.form.contentList = [
          {
            url: this.form.content,
            name: this.fileName
          }
        ]
      }
    },
    handleRemove() {
      this.form.fileList = []
    },
    handleContentRemove() {
      this.form.contentList = []
      this.form.content = ''
    },
    uploadSuccess(val) {
      this.form.coverIds.push(val.data.id)
      this.form.fileList.push({
        url: val.data.url,
        name: val.data.id
      })
    },
    clearImg() {
      this.form.coverIds = []
      this.form.fileList = []
      this.form.coverUrls = []
    },
    removePic(val) {
      const index = this.form.fileList.findIndex(item => item.uid === val.uid)
      this.form.coverIds.splice(index, 1)
      this.form.fileList.splice(index, 1)
      this.form.coverUrls.splice(index, 1)
    },
    async beforeUpload(val) {
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      const isLt50M = val.size / 1024 / 1024 < 50
      if (!JPG && !PNG && !JPEG) {
        const uid = val.uid // 关键作用代码，去除文件列表失败文件
        const index = this.$refs.uploadFile.uploadFiles.findIndex(item => item.uid === uid) // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）
        this.$refs.uploadFile.uploadFiles.splice(index, 1) // 关键作用代码，去除文件列表失败文件
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过50M')
        return
      }
      const param = {
        filename: val.name,
        size: val.size,
        type: val.type
      }
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    getBudgetData(v) {
      this.form = {
        ...this.form,
        ...v
      }
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.imgSum === 3 && this.form.coverIds.length !== 3) {
          return this.$message.error('请上传三张封面图')
        }
        if (valid) {
          this.form.taskType = this.$route.query.type
          saveOrUpdatePlatformArticleCms(this.form).then(res => {
            this.agreementDialogVisible = true
            this.$message.success('文章发布完成')
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.articleEdit {
  background-color: #fff;
  width: 1173px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  ::v-deep .el-form {
    margin-left: 20px;
    &-item {
      &__label {
        color: #666;
      }
      .pdf-success,.uploadPdf {
        width: 520px;
        height: 184px;
        background: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
        color: #999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        h2 {
          margin: 0 auto;
          font-size: 22px;
          color: #000;
        }
      }
      .pdf-success {
        position: relative;
        img{
          width: 60px;
          &.pdf-success-del {
            position: absolute;
            top: 14px;
            right: 14px;
            width: 24px;
            cursor: pointer;
          }
        }
      }
      .el-radio-button {
        &__inner {
          margin-right: 10px;
          padding: 7px 0;
          width: 52px;
          height: 28px;
          border-radius: 14px;
        }
      }
      .el-upload-list__item {
        margin-top: 18px;
      }
      .el-upload--picture-card {
        margin-top: 18px;
        width: 146px;
        height: 146px;
        .imgUploadStyle {
          display: flex;
          flex-direction: column;
          margin-top: 50px;
          opacity: .6;
          i {
            font-size: 26px;
          }
          span{
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
      .el-cascader,
      .el-select {
        width: 200px;
      }
      .el-tag {
        max-width: 180px;
        height: 36px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .editor-wrapper {
      width: 948px;
    }
  }
}
.hideUpdate ::v-deep .el-upload--picture-card {
    display: none;
}
.hideContentUpdate {
  ::v-deep .el-upload {
    display: none;
  }
}
</style>
