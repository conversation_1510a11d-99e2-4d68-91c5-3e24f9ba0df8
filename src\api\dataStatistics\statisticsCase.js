import request from '@/utils/request'

/**
 * 统计概况
 */

export default {
  // 统计概况
  list(param) {
    return request({
      url: '/data/statistics/overview',
      method: 'post',
      data: param
    })
  },
  // 区域分布
  area(param) {
    return request({
      url: '/data/statistics/area/distribution',
      method: 'post',
      data: param
    })
  },
  // 趋势
  trend(param) {
    return request({
      url: '/data/statistics/trend',
      method: 'post',
      data: param
    })
  },
  // 趋势对比
  trendContrast(param) {
    return request({
      url: '/data/statistics/trend/contrast',
      method: 'post',
      data: param
    })
  },
  // 身份分布
  ident(param) {
    return request({
      url: '/data/statistics/identity/distribution',
      method: 'post',
      data: param
    })
  },
  // 专科分布
  major(param) {
    return request({
      url: '/data/statistics/major/distribution',
      method: 'post',
      data: param
    })
  },
  // 职称分布 单个身份
  academic(param) {
    return request({
      url: '/data/statistics/acad/distribution',
      method: 'post',
      data: param
    })
  },
  // 职称级别分布 多个身份
  academics(param) {
    return request({
      url: '/data/statistics/academic/distribution',
      method: 'post',
      data: param
    })
  },
  // ---------------------------------
  // 区域分布 导出
  exportArea(param) {
    return request({
      url: '/export/cmsdata/area/distribution',
      method: 'post',
      data: param
    })
  },
  // 身份分布 导出
  exportIdent(param) {
    return request({
      url: '/export/cmsdata/identity/distribution',
      method: 'post',
      data: param
    })
  },
  // 专科分布 导出
  exportMajor(param) {
    return request({
      url: '/export/cmsdata/major/distribution',
      method: 'post',
      data: param
    })
  },
  // 职称分布 导出
  exportAcad(param) {
    return request({
      url: '/export/cmsdata/acad/distribution',
      method: 'post',
      data: param
    })
  },
  // 职称级别分布 导出
  exportAcademic(param) {
    return request({
      url: '/export/cmsdata/academic/distribution',
      method: 'post',
      data: param
    })
  },
  // 趋势 导出
  exportTrend(param) {
    return request({
      url: '/export/cmsdata/trend',
      method: 'post',
      data: param
    })
  },
  // 趋势对比 导出
  exportTrendContrast(param) {
    return request({
      url: '/export/cmsdata/trend/contrast',
      method: 'post',
      data: param
    })
  }
}
