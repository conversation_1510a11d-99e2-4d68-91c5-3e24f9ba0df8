<template>
  <div class="app-container">
    <a v-show="false" ref="downId" :download="download" :href="excelUrl" />
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="ad" placeholder="请选择活动" clearable @focus="$refs.selectDialog.dialogShow=true" />
      </div>
      <div class="search-column__item">
        <el-button type="primary" @click="$refs.selectDialog.dialogShow=true">选择活动</el-button>
      </div>
    </div>
    <select-dialog ref="selectDialog" :time-range="timeRange" @select="onSelect" />
    <overview :data="overview" class="ad-info" :act-id="actId" />
    <trend class="ad-info" :data="trendData" />

    <!-- table -->
    <div class="ad-info">
      <h3>活动排名
        <el-tooltip class="item" effect="dark" content="排名用户列表：所有参加视频答题有得分的用户计入排名，同一用户多个得分，只将最高分计入排名" placement="top-start">
          <i class="el-icon-warning" />
        </el-tooltip>
      </h3>
      <div class="app-container">
        <div class="search-column">
          <div class="search-column__item">
            <el-input v-model="tableQuery.queryWord" placeholder="请输入姓名/手机" clearable @change="handleFilter">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
            </el-input>
          </div>
          <div class="search-column__item fr">
            <el-button type="primary" @click="exportExcel">导出</el-button>
          </div>
        </div>
        <el-table :data="adRankList" border stripe>
          <el-table-column prop="rank" label="排名" align="center" width="50" />
          <el-table-column prop="real_name" label="姓名" align="center" />
          <el-table-column prop="phone" label="手机" align="center" />
          <el-table-column prop="area" label="所在地区" align="center" />
          <el-table-column prop="company" label="所属单位" align="center" />
          <el-table-column prop="major" label="专科" align="center" />
          <el-table-column prop="address" label="联系地址" align="center" />
          <el-table-column label="本人人脸" align="center">
            <template slot-scope="scope">
              <img v-if="scope.row.selfFaceUrl !== ''" style="width: 100px;height: 100px" :src="scope.row.selfFaceUrl">
            </template>
          </el-table-column>
          <el-table-column label="考核人脸" align="center">
            <template slot-scope="scope">
              <img v-if="scope.row.examineFaceUrl !== ''" style="width: 100px;height: 100px" :src="scope.row.examineFaceUrl">
            </template>
          </el-table-column>
          <el-table-column prop="faceComparison" label="人脸对比结果" align="center" />
          <el-table-column prop="best_record_end_time" label="完成时间" align="center" />
          <el-table-column prop="accuracy" label="得分" align="center" />
          <el-table-column label="奖项" align="center" prop="awards" />
        </el-table>
        <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.page" @pagination="handlePagination" />
      </div>
    </div>
  </div>
</template>

<script>
import overview from './components/overview.vue'
import selectDialog from './components/selectDialog.vue'
import trend from './components/trend.vue'
import Pagination from '@/components/Pagination/index.vue'
import request from '@/api/activity'
import axios from 'axios'
import { getToken } from '@/utils/auth'
import { parseTime } from '@/utils'

export default {
  name: 'Statistics',
  components: { overview, trend, selectDialog, Pagination },
  filters: {
    awardsFlt(v) {
      const arr = []
      return arr[v]
    }
  },
  data() {
    return {
      actId: this.$route.query.id,
      ad: this.$route.query.title,
      // time picker
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e7
        }
      },
      timeRange: [],
      // component data
      overview: {
        click: {},
        exposure: {},
        participate: {}
      },
      trendData: {
        xAxisData: [],
        videoAnswer: [],
        exposure: [],
        videoPlay: [],
        hit: []
      },
      // table data
      tableQuery: {
        queryWord: '',
        page: 1,
        pageSize: 10
      },
      adRankList: [],
      // pagination
      layout: 'total, prev, pager, next, jumper',
      total: 0,
      excelUrl: '',
      download: ''
    }
  },
  async created() {
    if (!this.actId) {
      const param = {
        condition: {
          type: 1,
          keyword: '',
          startTime: '',
          endTime: '',
          status: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
      const res = await request.list(param)
      this.actId = res.records[0].activityId
      this.ad = res.records[0].title
    }
    this.getOverview()
    this.getRankData()
    this.getTrend()
  },
  methods: {
    getOverview() {
      request.actOverview(this.actId).then(res => {
        this.overview = {
          click: {
            hitNum: res.hitNum,
            hitPersonNum: res.hitPersonNum,
            avgHitNum: res.avgHitNum,
            hitRate: (res.hitRate * 100).toFixed(2) + '%'
          },
          exposure: {
            exposureNum: res.exposureNum,
            exposurePersonNum: res.exposurePersonNum,
            avgExposureNum: res.avgExposureNum,
            exposureRate: (res.exposureRate * 100).toFixed(2) + '%'
          },
          participate: {
            videoAnswerNum: res.videoAnswerNum,
            videoAnswerPersonNum: res.videoAnswerPersonNum,
            avgVideoAnswerNum: res.avgVideoAnswerNum,
            conversionRate: (res.conversionRate * 100).toFixed(2) + '%'
          },
          video: {
            videoPlayNum: res.videoPlayNum,
            videoPlayPersonNum: res.videoPlayPersonNum,
            avgVideoPlayNum: res.avgVideoPlayNum
          }
        }
      })
    },
    // dialog select ad
    onSelect(v) {
      this.ad = v.title
      this.actId = v.activityId
      this.getOverview()
      this.getRankData()
      this.getTrend()
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.page = val.page
      this.tableQuery.pageSize = val.pageSize
      this.getRankData()
    },
    handleFilter() {
      this.tableQuery.page = 1
      this.getRankData()
    },
    getRankData() {
      const param = {
        ...this.tableQuery,
        actId: this.actId
      }
      request.rankList(param).then(res => {
        this.adRankList = res.records
        this.total = res.total
      })
    },
    getTrend() {
      request.actTrend(this.actId).then(res => {
        const trendData = {
          xAxisData: [],
          exposure: [],
          hit: [],
          videoAnswer: [],
          videoPlay: []
        }
        res.forEach(v => {
          trendData.xAxisData.push(v.ds)
          trendData.exposure.push(v.exposureNum)
          trendData.hit.push(v.hitNum)
          trendData.videoAnswer.push(v.videoAnswerNum)
          trendData.videoPlay.push(v.videoPlayNum)
        })
        this.trendData = trendData
      })
    },
    exportExcel() {
      const data = {
        actId: this.actId,
        queryWord: this.tableQuery.queryWord
      }
      // window.location.href = request.actExport(param)
      axios.post(request.actExport(), data, {
        headers: { 'token': getToken() },
        responseType: 'blob'
      }).then((res) => {
        console.log(res)
        if (!res.headers['content-type'] || res.headers['content-type'].indexOf('application/json') === -1) {
          const blob = new Blob([res.data], { type: res.headers['content-type'] })
          this.excelUrl = window.URL.createObjectURL(blob)
          // if (res.headers['content-disposition']) {
          //   this.download = res.headers['content-disposition'].split(';')[1].split('=')[1]
          // } else {
          //   this.download = '导出-' + parseTime(new Date().getTime(), '{y}-{m}-{d}-{h}-{i}-{s}')
          // }
          this.download = '活动排名-' + parseTime(new Date().getTime(), '{y}-{m}-{d}-{h}-{i}-{s}')
          this.$nextTick(() => this.$refs.downId.click())
        }
      }, (error) => {
        console.log('err' + error) // for debug
        this.$message({
          message: error.message,
          type: 'error',
          duration: 5 * 1000
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .ad-info {
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      background: #eee;
      line-height: 50px;
      padding-left: 20px;
      i {
        cursor: pointer;
        color: #3bb19c;
      }
    }
    .chart {
      height: 400px;
    }
  }
}
</style>
