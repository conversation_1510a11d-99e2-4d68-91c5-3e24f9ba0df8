<template>
  <el-dialog
    title="添加推广内容"
    :visible.sync="dialogVisible"
    width="50%"
    center
    :before-close="handleClose"
  >
    <h3>选择推广内容</h3>
    <el-input
      v-model="listQuery.condition.keyword"
      placeholder="内容名称/发布人/作者"
      class="input-with-select"
      style="width: 300px"
    >
      <el-button slot="append" icon="el-icon-search" @click="search()" />
    </el-input>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        v-for="item in tableColumn"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        align="center"
      />
      <el-table-column prop="handle" label="操作" width="50" align="center">
        <template slot-scope="scope">
          <el-radio v-model="query.bizId" :label="scope.row.id" @input="select(scope.row)">&nbsp; </el-radio>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <h3>推广时间</h3>
    <el-date-picker
      v-model="time"
      type="daterange"
      value-format="yyyy-MM-dd HH:mm:ss"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      :default-time="['00:00:00', '23:59:59']"
    />
    <h3>点击量要求</h3>
    <el-input
      v-model.number="query.targetClickNum"
      style="width: 350px"
      oninput="value=value.replace(/[^1-9]/g,(word,key)=> { if(word==='0'&&key!==0) {return word} return '' })"
    />
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">返回</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
    </div>
  </el-dialog>

</template>

<script>
import moment from 'moment'
import Pagination from '@/components/Pagination'
import { recommendContentList, addColumn } from '@/api/marketing/popularize'
export default {
  components: {
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: Number,
      default: 1
    },
    id: {
      type: String,
      default: ''
    },
    excludedIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      listQuery: {
        condition: {
          excludedIds: this.excludedIds,
          keyword: '',
          recommendType: this.type
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      tableData: [],
      tableColumn: [
        { prop: 'title', label: '内容名称' },
        { prop: 'articleType', label: '内容类型', width: '80' },
        { prop: 'author', label: '作者', width: '180' },
        { prop: 'createdName', label: '发布人', width: '100' },
        { prop: 'releaseTime', label: '发布时间', width: '140' },
        { prop: 'sourceString', label: '来源', width: '80' }
      ],
      total: 0,
      time: [],
      query: {
        bizId: null,
        id: null,
        recommendStartTime: '',
        recommendEndTime: '',
        recommendTitle: '',
        targetClickNum: null
      }
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        this.getList()
        this.query.id = this.id
      }
    },
    time(v) {
      if (v && v.length > 0) {
        this.query.recommendStartTime = v[0]
        this.query.recommendEndTime = v[1]
      } else {
        this.query.recommendStartTime = ''
        this.query.recommendEndTime = ''
      }
    },
    type(v) {
      this.listQuery.condition.recommendType = v
    },
    excludedIds(v) {
      this.listQuery.condition.excludedIds = v
    }
  },
  methods: {
    getList() {
      recommendContentList(this.listQuery).then(res => {
        res.records.forEach(i => {
          i.author = `${i.authorInfoResp.authorName} ${i.authorInfoResp.identityName} ${i.authorInfoResp.company} ${i.authorInfoResp.department}`
          i.sourceString = i.source === 0 ? '平台自建' : (i.source === 3 ? '平台推广' : '任务征集')
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      this.listQuery.condition.keyword = ''
      this.listQuery.pager.page = 1
      this.time = []
      this.query = {
        bizId: null,
        id: null,
        recommendStartTime: '',
        recommendEndTime: '',
        recommendTitle: '',
        targetClickNum: null
      }
    },
    select(row) {
      if ([1, 3].includes(row.source)) {
        this.time = [
          moment(row.releaseTime).startOf('minute').format('YYYY-MM-DD HH:mm:ss'),
          moment(row.recommendEndTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
        ]
        this.query.targetClickNum = row.targetClickNum
      } else {
        this.time = []
        this.query.targetClickNum = null
      }
      this.query.bizId = row.id
      this.query.recommendTitle = row.title
    },
    save() {
      if (this.query.recommendEndTime !== '' && !moment(this.query.recommendEndTime).isAfter()) {
        this.$message.error('结束时间不能早于当天')
      } else {
        addColumn(this.query).then(() => {
          this.handleClose()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.input-with-select {
  margin-bottom: 20px;
}
</style>
