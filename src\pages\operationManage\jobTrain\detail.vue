<template>
  <div class="app-container">
    <el-form ref="form" :model="form" label-width="90px" :rules="rules" :disabled="type === 'view'">
      <el-form-item label="名称:" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入名称"
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="培训周期:" prop="cycle">
        <el-input-number v-model="form.cycle" controls-position="right" :min="1" :max="60" step-strictly />&nbsp;&nbsp;&nbsp;个月
      </el-form-item>
      <el-form-item label="说明:" prop="description">
        <el-input
          v-model="form.description"
          :rows="6"
          type="textarea"
          placeholder="培训计划说明"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="培训目录:" required>
        <div v-for="(item,index) in contentList" :key="index" class="content-container">
          <h2>
            第{{ item.month }}个月
            <el-input
              v-model="contentList[index].name"
              placeholder="说明"
              maxlength="100"
            />
          </h2>
          <el-table :data="form.courseRequestDtoList | filterDateData(item.month)" fit size="mini" style="width: 100%; margin-bottom: 20px">
            <el-table-column align="center" type="index" label="序号" />
            <el-table-column align="center" prop="courseName" label="教程名称" />
            <el-table-column align="center" prop="courseDuration" label="时长" />
            <el-table-column align="center" label="正确率要求">
              <template slot-scope="{row}">
                <el-input-number v-model="row.courseAccuracy" :precision="0" controls-position="right" :min="10" :max="100" style="width: 120px" @change="inputChange(row)">
                  <template slot="append">%</template>
                </el-input-number>
                %
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="scope">
                <el-button v-if="scope.$index !== 0" type="info" @click="sortUp(scope.row.courseId)">
                  <i class="el-icon-arrow-up" />
                </el-button>
                <el-button v-if="scope.$index !== filterData(form.courseRequestDtoList,item.month).length - 1" type="info" @click="sortDown(scope.row.courseId)">
                  <i class="el-icon-arrow-down" />
                </el-button>
                <el-button type="danger" @click="handleDelCourse(scope.row)">
                  <i class="el-icon-delete" />
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item label class="text-center add-course-btn">
            <el-button v-if="filterData(form.courseRequestDtoList,item.month).length < 10" icon="el-icon-plus" @click="showDialogVisible(item.month, item.name)">添加</el-button>
          </el-form-item>
        </div>
      </el-form-item>

      <el-form-item label="可见单位" required>
        <el-radio v-model="form.isRestrict" :label="0">全部</el-radio>
        <el-radio v-model="form.isRestrict" :label="1">指定</el-radio>
        <div v-if="form.isRestrict === 1">
          <selectOrg
            v-if="form.id !== '' && type !== 'view'"
            ref="orgList"
            :type="0"
            :template-id="form.id"
            @change="handleOrgList()"
          />
          <selectOrg
            v-if="form.id !== ''"
            ref="selectOrgList"
            :type="1"
            :template-id="form.id"
            :copy-id="type === 'copy' ? $route.query.id : ''"
            @change="handleOrgList()"
          />
        </div>
      </el-form-item>
    </el-form>
    <div style="text-align: center">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button v-if="type !== 'view'" type="primary" @click="save()">保存</el-button>
    </div>

    <select-train-content
      :show.sync="dialogVisible"
      :month.sync="month"
      :name.sync="name"
      :selected-list="selectedList"
      @handleSelect="handleDialogSelect"
      @handleSubmit="handleDialogSubmit"
      @handleCancel="handleDialogCancel"
    />
  </div>
</template>

<script>
import selectTrainContent from './components/selectTrainContent' // 内容选择
import selectOrg from './components/selectOrg' // 单位选择
import { createPackagePre, editPackage, getPackageDetail, jobTrainSelectedOrgList } from '@/api/jobTrain'
export default {
  components: {
    selectTrainContent,
    selectOrg
  },
  filters: {
    filterTableIndex(val) {
      if (val === 0) {
        return '--'
      }
      return val
    },
    filterDateData(val, month) {
      const newVal = val.filter(item => item.month === month)
      return newVal
    }
  },
  data() {
    return {
      type: this.$route.query.type,
      form: {
        id: '',
        name: '',
        cycle: 1,
        description: '',
        isRestrict: 0,
        courseRequestDtoList: []
      },
      rules: {
        name: [
          { required: true, message: '请填写名称', trigger: 'blur' }
        ],
        cycle: [
          { required: true, message: '培训周期' }
        ]
      },
      month: 1,
      name: '',
      contentList: [],
      dialogVisible: false,
      selectedList: [], // 选中的任务内容列表
      selectedListTemp: [],
      selectedListInfoId: []
    }
  },
  watch: {
    'form.cycle': {
      handler(val) {
        const oldList = this.contentList
        this.contentList = []
        for (var i = 1; i <= val; i++) {
          this.contentList.push({
            month: i
          })
          this.$set(this.contentList[i - 1], 'name', oldList[i - 1] ? oldList[i - 1].name : '')
        }
        this.form.courseRequestDtoList = this.form.courseRequestDtoList.filter(v => v.month <= val)
        const courseIds = this.form.courseRequestDtoList.map(item => item.courseId)
        this.selectedList = this.selectedList.filter(v => courseIds.includes(v))
      },
      immediate: true
    }
  },
  created() {
    if (this.type === 'create' || this.type === 'copy') {
      this.$route.meta.title = '创建'
      createPackagePre().then(res => {
        this.form.id = res
      })
    } else {
      this.form.id = this.$route.query.id
    }
    if (this.type !== 'create') {
      this.$route.meta.title = '编辑'
      getPackageDetail(this.$route.query.id).then(res => {
        this.form.name = res.name
        this.form.cycle = res.cycle
        this.form.description = res.description
        this.contentList = res.examineContentList
        let courseList = []
        res.examineContentList.forEach((item, index) => {
          const list = item.coursesList.map(v => ({
            courseAccuracy: parseInt(v.courseAccuracy),
            courseDuration: v.courseDuration,
            courseId: v.courseId,
            courseInfoId: v.courseInfoId,
            courseName: v.name,
            courseOrder: v.courseOrder,
            month: item.month,
            name: item.name
          }))
          courseList = courseList.concat(list)
          if (index === res.examineContentList.length - 1) {
            this.selectedList = courseList.map(v => {
              return v.courseId
            })
          }
        })
        this.form.courseRequestDtoList = [...new Map(courseList.map(item => [item.courseId, item])).values()]
      })
    }
  },
  methods: {
    filterData(val, month) {
      const newVal = val.filter(item => item.month === month)
      return newVal
    },
    showDialogVisible(month, name) {
      this.month = month
      this.name = name
      this.dialogVisible = true
    },
    // 窗口选中教程/sop
    handleDialogSelect(val, isMessage) {
      this.selectedList.push(val)
      this.selectedListTemp.push(val)
      if (!isMessage) {
        this.$message.success('已添加该资源内容')
      }
    },
    // 教程去重
    removeRepeatCourse(remoteArr, localArr) {
      let repeat = false
      remoteArr.forEach(ra => {
        localArr.forEach((la, i) => {
          if (la.courseId === ra.courseId) {
            repeat = true
            this.$message({
              type: 'error',
              message: '您选中的教程或SOP包含了重复的教程"' + ra.courseName + '",已自动删除该重复教程!'
            })
            localArr.splice(i, 1)
            if (!repeat) {
              localArr.push(ra)
            }
          }
        })
      })
      repeat = false
      return localArr
    },
    // 窗口点击确定
    handleDialogSubmit(val) {
      const count = this.filterData(this.form.courseRequestDtoList, this.month).length + val.length
      if (count > 10) {
        this.$message.error('单月教程数最多10个, 已自动删除多余教程')
        val = val.splice(0, val.length - (count - 10))
      }
      if (val && val.length) {
        val = this.removeRepeatCourse(this.form.courseRequestDtoList, val)
        val.forEach(v => {
          this.form.courseRequestDtoList.push(v)
        })
      }
      this.form.courseRequestDtoList = this.reorderListOrder(this.form.courseRequestDtoList)
      const courseIds = this.form.courseRequestDtoList.map(item => item.courseId)
      this.selectedList = this.selectedList.filter(v => courseIds.includes(v))
      this.selectedListTemp = []
    },
    // 窗口点击取消
    handleDialogCancel() {
      const courseIds = this.form.courseRequestDtoList.map(item => item.courseId)
      this.selectedList = this.selectedList.filter(v => courseIds.includes(v))
      this.selectedListTemp = []
    },
    swapArray(arr, index1, index2) {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    },
    reorderListOrder(arr) {
      let date = ''
      let index = 1
      arr.map(v => {
        if (v.month !== date) {
          date = v.month
          index = 1
        } else {
          index = index + 1
        }
        v.courseOrder = index
      })
      return arr
    },
    inputChange(row) {
      if (!row.accuracy) {
        row.accuracy = 10
      }
    },
    sortUp(courseId) {
      const index = this.form.courseRequestDtoList.findIndex(v => v.courseId === courseId)
      this.swapArray(this.form.courseRequestDtoList, index, index - 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.courseRequestDtoList)
    },
    sortDown(courseId) {
      const index = this.form.courseRequestDtoList.findIndex(v => v.courseId === courseId)
      this.swapArray(this.form.courseRequestDtoList, index, index + 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.courseRequestDtoList)
    },
    handleDelCourse(row) {
      const list = this.form.courseRequestDtoList
      const courseId = row.courseId
      list.forEach((v, i) => {
        if (v.courseId === courseId) {
          list.splice(i, 1)
        }
      })

      this.selectedList.forEach((v, i) => {
        if (v.sopId) {
          // SOP
          v.children.forEach((vc, vi) => {
            if (vc === courseId) {
              v.children.splice(vi, 1)
            }
          })
        } else {
          // 教程
          this.selectedList = this.selectedList.filter(v => v !== courseId)
        }
      })
      this.selectedList = this.selectedList.filter(v => {
        if (v.sopId) {
          return v.sopId && v.children.length
        } else {
          return v
        }
      })

      this.form.courseRequestDtoList = this.reorderListOrder(this.form.courseRequestDtoList)
      this.$forceUpdate()
    },
    handleOrgList() {
      this.$refs.orgList.getList()
      this.$refs.selectOrgList.getList()
    },
    save() {
      let full = true
      let count = 0
      this.contentList.forEach(item => {
        if (!this.form.courseRequestDtoList.find(v => v.month === item.month)) {
          full = false
          this.$message.error(`第${item.month}个月培训内容不能为空`)
        }
        this.form.courseRequestDtoList.forEach(val => {
          if (val.month === item.month) {
            val.name = item.name
          }
        })
        count++
        if (count === this.contentList.length) {
          if (full) {
            this.$refs.form.validate(valid => {
              if (valid) {
                if (this.form.isRestrict === 1) {
                  jobTrainSelectedOrgList({
                    condition: {
                      keyword: '',
                      packageId: this.form.id,
                      areaId: [],
                      type: []
                    },
                    pager: {
                      page: 1,
                      pageSize: 10
                    }
                  }).then(res => {
                    if (res.total === 0) {
                      this.$message.error(`指定的培训单位不能为空`)
                    } else {
                      editPackage(this.form).then(() => {
                        this.$message.success(`${this.type === 'edit' ? '编辑' : '创建'}成功`)
                        this.$router.push({ name: 'JobTrainList' })
                      })
                    }
                  })
                } else {
                  editPackage(this.form).then(() => {
                    this.$message.success(`${this.type === 'edit' ? '编辑' : '创建'}成功`)
                    this.$router.push({ name: 'JobTrainList' })
                  })
                }
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .el-form-item {
    .el-input,
    .el-textarea {
      width: 500px;
    }

    .el-table {
      border: 1px solid #dfe6ec;
      border-bottom: none;
      margin-bottom: 0!important;
      &::v-deep .el-table__header {
        th {
          background: #f6f9fb;
        }
      }
    }
    .add-course-btn {
      margin-bottom: 0;
      padding-top: 20px;
      padding-bottom: 20px;
      border: 1px solid #dfe6ec;
      border-top: none;
      .el-button {
        width: 320px;
        height: 40px;
        background-color: #ffffff;
        border-radius: 3px;
        border: 1px dashed #409EFF;
        color: #409EFF;
      }
    }
  }
}
</style>
