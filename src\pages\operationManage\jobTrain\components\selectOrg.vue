<template>
  <div class="table">
    <h3>{{ `${type === 0 ? '可选' : '已选'}单位列表` }}</h3>
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="listQuery.condition.keyword" class="input-with-select" placeholder="名称" clearable @keyup.enter.native="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="listQuery.condition.areaId"
          placeholder="区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            emitPath: false,
            multiple: true,
            value:'areaId',
            label:'name',
            children:'childList'
          }"
          clearable
          @change="handleFilter"
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="listQuery.condition.type"
          placeholder="类型"
          :options="typeList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            emitPath: false,
            multiple: true,
            value:'orgTypeId',
            label:'name'
          }"
          clearable
          @change="handleFilter"
        />
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="batchAction">{{ type === 0 ? '添加全部' : "删除全部" }}</el-button>
      </div>
    </div>
    <!-- table -->
    <el-table
      :data="list"
      border
      :header-cell-style="{background:'#f9f9f9',color:'#333'}"
      style="width: 100%"
    >
      <el-table-column
        v-for="item in columns"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.width"
        align="center"
      />
      <el-table-column prop="handle" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="singleAction(scope.row.orgId)">{{ type === 0 ? '添加' : '删除' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination :page="listQuery.pager.page" :total="total" :auto-scroll="false" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getAreaTree } from '@/api/area'
import { getOrganTypeList } from '@/api/userManage'
import { jobTrainOrgList, jobTrainSelectedOrgList, jobTrainAddOrg, jobTrainDelOrg } from '@/api/jobTrain'

export default {
  name: 'SelectOrgList',
  components: {
    Pagination
  },
  props: {
    type: {
      // 0可选 1已选
      type: Number,
      default: 0
    },
    templateId: {
      type: String,
      default: ''
    },
    copyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      columns: [
        { label: 'ID', align: 'center', prop: 'orgId' },
        { label: '名称', align: 'center', prop: 'orgName' },
        { label: '类型', align: 'center', prop: 'orgTypeName' },
        { label: '层级', align: 'center', prop: 'orgLevelName' },
        { label: '区域', align: 'center', prop: 'orgArea' },
        { label: '创建时间', align: 'center', prop: 'orgCreateTime' }
      ],
      areaList: [],
      typeList: [],
      listQuery: {
        condition: {
          keyword: '',
          templateId: this.templateId,
          areaId: [],
          type: []
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  watch: {
    copyId: {
      handler(v) {
        if (v !== '') {
          const query = {
            orgAreaIds: this.listQuery.condition.areaId,
            orgType: this.listQuery.condition.type,
            templateId: this.listQuery.condition.templateId,
            copyId: this.copyId,
            queryWord: this.listQuery.condition.keyword
          }
          jobTrainAddOrg(query).then(() => {
            this.$emit('change')
          })
        }
      },
      immediate: true
    }

  },
  created() {
    // 获取区域树
    getAreaTree().then(res => {
      this.areaList = res
    })
    getOrganTypeList({ action: 0 }).then(res => {
      this.typeList = res
    })
    this.getList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    getList() {
      const listApi = this.type === 0 ? jobTrainOrgList : jobTrainSelectedOrgList
      listApi(this.listQuery).then(res => {
        this.list = res.records
        this.total = res.total
      })
    },
    // single action for user
    singleAction(orgId) {
      const handleApi = this.type === 0 ? jobTrainAddOrg : jobTrainDelOrg
      handleApi({ orgId, templateId: this.templateId }).then(() => {
        this.$emit('change')
        this.$message.success(`成功${this.type === 0 ? '添加' : '删除'}`)
      })
    },
    // batch action for user
    batchAction() {
      this.$confirm(`是否确定${this.type === 0 ? '添加全部' : '删除全部'}`, '确认信息', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const handleApi = this.type === 0 ? jobTrainAddOrg : jobTrainDelOrg
        const query = {
          orgAreaIds: this.listQuery.condition.areaId,
          orgType: this.listQuery.condition.type,
          templateId: this.listQuery.condition.templateId,
          queryWord: this.listQuery.condition.keyword
        }
        handleApi(query).then(() => {
          this.$emit('change')
          this.$message.success(`成功${this.type === 0 ? '添加' : '删除'}全部`)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table {
  margin: 0 auto 50px;
  padding: 0 20px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  .search-column__item {
    margin-left: 6px;
    margin-bottom: 10px;
    ::v-deep .el-input_inner {
      width: 180px;
    }
  }
}
</style>
