<template>
  <el-card class="card">
    <el-row style="width:100%;text-align:center">
      <h2>可选用户</h2>
    </el-row>
    <div class="search-column" style="display:flex">
      <div class="search-column__item" style="margin-right:10px">
        <el-input v-model="queryWord" placeholder="请输入搜索关键字" clearable>
          <el-select slot="prepend" v-model="queryType" style="width: 120px">
            <el-option v-for="item in queryTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <Search @search="search" @clear="clear" />
      <div class="search-column__item" style="margin-left:10px;">
        <el-button type="primary" @click="addAll()">添加全部</el-button>
      </div>
    </div>
    <br>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <span>{{ scope.row[col.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="120">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="viewPersonal(row.userId)">人员信息</el-button>
          <el-button size="mini" type="text" :disabled="flag" @click="add(row.userId)">添加</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="pag" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <DialogUserInfo :title="'人员信息'" :show.sync="dialogInfoVisible" :data="userInfoData" />

  </el-card>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { userList, addOrDelUser, batchAddOrDelUser } from '@/api/liveManage'
import { getPersonalDetail } from '@/api/userManage'

import DialogUserInfo from '@/pages/user/compontents/dialogUserInfo.vue'
import Search from '../components/search_1.vue'

export default {
  components: { Pagination, Search, DialogUserInfo },
  props: {
    liveId: {
      type: [Number, String],
      default: 0
    }
  },
  data() {
    return {
      queryWord: '',
      queryType: 1,
      queryTypeList: [
        { label: '个人账户', value: 1 },
        { label: '手机', value: 2 },
        { label: '用户姓名', value: 3 },
        { label: '员工账号', value: 4 },
        { label: '员工姓名', value: 5 }
      ],
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: 'UID', align: 'center', prop: 'userId', width: '180px' },
        { id: 2, label: '姓名', align: 'center', prop: 'realName' },
        { id: 3, label: '个人账号', align: 'center', prop: 'userName' },
        { id: 4, label: '手机', align: 'center', prop: 'phone' },
        { id: 5, label: '身份', align: 'center', prop: 'identity' },
        { id: 6, label: '专科', align: 'center', prop: 'majorName' },
        { id: 7, label: '职称', align: 'center', prop: 'academicName' },
        { id: 8, label: '区域', align: 'center', prop: 'areaName' },
        { id: 9, label: '注册时间', align: 'center', prop: 'regTime', width: '160px' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      dialogInfoVisible: false,
      userInfoData: {},
      flag: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search(condition) {
      if (condition) {
        this.tableQuery.condition = condition
      }
      this.tableQuery.condition.queryType = this.queryType
      this.tableQuery.condition.queryWord = this.queryWord
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.condition.queryWord = this.queryWord = ''
      this.tableQuery.pager.page = 1
      this.getList()
    },
    getList() {
      this.tableQuery.condition.liveId = this.liveId
      this.tableQuery.condition.selectType = 1
      userList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
        this.flag = false
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    viewPersonal(userId) {
      getPersonalDetail({ userId: userId }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
      })
    },
    add(userId) {
      const query = {
        liveId: this.liveId,
        operateType: 1,
        userId: userId
      }
      addOrDelUser(query).then(res => {
        this.$message.success('添加成功')
        this.flag = true
        this.$emit('init')
      })
    },
    addAll() {
      const query = {
        academicIds: this.tableQuery.condition.academicIds,
        areaIds: this.tableQuery.condition.areaIds,
        identityIds: this.tableQuery.condition.identityIds,
        liveId: this.liveId,
        majorIds: this.tableQuery.condition.majorIds,
        operateType: 1,
        queryType: this.queryType,
        queryWord: this.queryWord,
        selectType: 1
      }
      batchAddOrDelUser(query).then(res => {
        this.$message.success('添加成功')
        this.$emit('init')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card{
  overflow: hidden;
}
.pag{
  padding-top: 0;
  padding-bottom: 0;
}
</style>
