import request from '@/utils/request'

/**
 * 用户列表
 */

export default {
  activity: {
    // 活动用户列表
    list(param) {
      return request({
        url: `/activity/userList`,
        method: 'post',
        data: param
      })
    },

    // 添加/删除活动用户
    singleAction(param) {
      return request({
        url: `/activity/addOrDelUser`,
        method: 'post',
        data: param
      })
    },

    // 批量操作活动用户
    batchAction(param) {
      return request({
        url: `/activity/batchAddOrDelUser`,
        method: 'post',
        data: param
      })
    }
  },
  ad: {
    // 活动用户列表
    list(param) {
      return request({
        url: `/positive/userList`,
        method: 'post',
        data: param
      })
    },

    // 添加/删除活动用户
    singleAction(param) {
      return request({
        url: `/positive/addOrDelUser`,
        method: 'post',
        data: param
      })
    },

    // 批量操作活动用户
    batchAction(param) {
      return request({
        url: `/positive/batchAddOrDelUser`,
        method: 'post',
        data: param
      })
    }
  },
  message: {
    // 用户列表
    list(param) {
      return request({
        url: `/messageCenter/getMsgPersonList`,
        method: 'post',
        data: param
      })
    },

    // 添加/删除活动用户
    singleAction(param) {
      return request({
        url: `/messageCenter/addOrDelPerson`,
        method: 'post',
        data: param
      })
    },

    // 批量操作活动用户
    batchAction(param) {
      return request({
        url: `/messageCenter/batchAddOrDelMsgPerson`,
        method: 'post',
        data: param
      })
    }
  },
  credit: {
    // 用户列表
    list(param) {
      return request({
        url: `/user/tmpSelectUserList`,
        method: 'post',
        data: param
      })
    },

    // 添加/删除活动用户
    singleAction(param) {
      return request({
        url: `/user/addOrDelTmpSelectUser`,
        method: 'post',
        data: param
      })
    },

    // 批量操作活动用户
    batchAction(param) {
      return request({
        url: `/user/batchAddOrDelTmpSelectUser`,
        method: 'post',
        data: param
      })
    }
  }
}
