<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="condition.type" filterable clearable>
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="根据左侧查询方式对应关键字" clearable @change="init">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-cascader
          ref="elcascader"
          placeholder="资源分类"
          :options="treeList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            emitPath:false,
            expandTrigger:'hover',
            multiple: true,
            value:'categoryId',
            label:'name',
            children:'children'
          }"
          clearable
          filterable
          @change="changeHandler"
        />
      </div>
      <div class="fr">
        <div class="search-column__item">
          <el-button :disabled="!selectedList.length" type="primary" @click="del">删除</el-button>
          <el-button type="primary" @click="recommend">推荐课程</el-button>
        </div>
      </div>
    </div>

    <a-table :columns="columns" :data="list" border stripe @selection-change="onSelectChange">
      <template slot="cover" slot-scope="{row}">
        <img style="width:90px;display:block;" :src="row.imgUrl" @click="handlePreview(row.imgUrl)">
      </template>
      <template slot="categoryName" slot-scope="{row}">
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px;">
            {{ row.categoryName }}
          </div>
          <span>{{ row.categoryName | filterFont }}</span>
        </el-tooltip>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="setSort(row)">设置排序值</el-button>
        <el-button type="text" @click="del(row)">删除</el-button>
      </template>
    </a-table>

    <Pagination :total="total" :page="pager.page" @pagination="handlePagination" />

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

    <el-dialog title="设置排序值" :visible.sync="dialogSortShow" :close-on-click-modal="false">
      <el-form ref="sortForm" :model="sortForm" :rules="sortRules">
        <el-form-item label="排序值" prop="sort">
          <el-input-number v-model="sortForm.sort" :min="0" maxlength="6" @change="onSortChange($event,'sortForm')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSetSort">取 消</el-button>
        <el-button type="primary" @click="confirmSetSort">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="推荐课程" :visible.sync="dialogRecommendShow" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="推荐栏目">App首页推荐课程</el-form-item>
        <el-form-item label="推荐栏目" prop="course">
          <template v-if="curCourse.coursePackId">
            <el-button type="danger" size="small" @click="delCourse">移除</el-button>
            <p>{{ curCourse.name }}</p>
          </template>
          <template v-else>
            <el-button type="primary" @click="chooseCourse">选择课程</el-button>
          </template>
        </el-form-item>
        <el-form-item label="排序" prop="listOrder">
          <el-input-number v-model="form.listOrder" :min="0" maxlength="6" @change="onSortChange($event,'form')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelRecommend">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="confirmRecommend">确 定</el-button>
      </div>
    </el-dialog>

    <dialog-course :tree-list="treeList" :visible.sync="dialogCourseShow" @selectCourse="selectCourse" />
  </div>
</template>

<script>
import ATable from '@/components/ATable'
import Pagination from '@/components/Pagination'
import DialogCourse from './components/dialogCourse'
import { recommendList, recommendDel, recommendAdd, recommendEdit } from '@/api/course'
import { getCategoryTreeList } from '@/api/category'

export default {
  name: 'LessonRecommend',
  components: { ATable, Pagination, DialogCourse },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  data() {
    const validateCourse = (rule, value, callback) => {
      if (!this.curCourse.coursePackId) {
        callback(new Error('请选择课程'))
      } else {
        callback()
      }
    }
    return {
      condition: {
        type: '1',
        keyword: '',
        list: []
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      treeList: [],
      typeList: Object.freeze([
        { value: '1', name: '课程名称' },
        { value: '2', name: '作者' }
      ]),
      columns: Object.freeze([
        { props: { type: 'selection', align: 'center' }},
        { props: { label: '排序', align: 'center', prop: 'listOrder' }},
        { props: { label: 'ID', align: 'center', prop: 'recommendId' }},
        { props: { label: '课程名称', align: 'center', prop: 'courseName' }},
        {
          props: { label: '封面', align: 'center', width: '110' },
          slot: 'cover'
        },
        { props: { label: '资源分类', align: 'center', prop: 'categoryName' }, slot: 'categoryName' },
        { props: { label: '章节数', align: 'center', prop: 'chapterCount' }},
        { props: { label: '作者', align: 'center', prop: 'doctorName' }},
        {
          props: { align: 'center', label: '操作', width: '130' },
          slot: 'actions'
        }
      ]),
      list: [],
      selectedList: [],
      total: 0,
      dialogImageUrl: '',
      dialogVisible: false,
      dialogSortShow: false,
      dialogRecommendShow: false,
      dialogCourseShow: false,
      sortForm: {
        sort: 0
      },
      form: {
        courseId: '',
        // sort: 0
        listOrder: 0
      },
      rules: Object.freeze({
        listOrder: [{ required: true, message: '请输入', trigger: 'blur' }],
        course: [{ validator: validateCourse, trigger: 'blur' }]
      }),
      sortRules: Object.freeze({
        sort: [{ required: true, message: '请输入', trigger: 'blur' }]
      }),
      curCourse: {},
      curObj: {},
      loading: false
    }
  },
  created() {
    // 获取资源分类树数据
    getCategoryTreeList(0).then(res => {
      this.treeList = res
    })
    this.init()
  },
  methods: {
    init(reset = true) {
      reset && (this.pager.page = 1)
      const params = {
        condition: this.condition,
        pager: this.pager
      }
      recommendList(params).then(res => {
        this.list = res.records
        this.total = res.total
      })
    },
    changeHandler() {
      // 获取全部选中节点，包括父节点
      let checkedList = this.$refs.elcascader.getCheckedNodes()
      checkedList = checkedList.map(v => v.value)
      this.condition.list = checkedList
      this.init()
    },
    del(row) {
      this.$confirm('确认要删除推荐课程？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let recommendIds = []
        if (row.recommendId) {
          recommendIds = [row.recommendId]
        } else {
          recommendIds = this.selectedList.map(v => v.recommendId)
        }
        recommendDel({ recommendIds }).then(() => {
          this.$message.success('操作成功')
          this.init()
        })
      }).catch(console.log)
    },
    recommend() {
      this.dialogRecommendShow = true
    },
    handlePagination(val) {
      this.pager = val
      this.init(false)
    },
    onSelectChange(v) {
      this.selectedList = v
    },
    handlePreview(url) {
      this.dialogImageUrl = url
      this.dialogVisible = true
    },
    cancelSetSort() {
      this.$refs.sortForm.resetFields()
      this.dialogSortShow = false
    },
    confirmSetSort() {
      this.$refs.sortForm.validate(valid => {
        if (valid) {
          const params = Object.assign({}, this.curObj, { listOrder: this.sortForm.sort })
          recommendEdit(params).then(() => {
            this.cancelSetSort()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    onSortChange(v, form) {
      if (!v) {
        this.$nextTick(() => {
          this[form].sort = 0
        })
      }
    },
    chooseCourse() {
      this.dialogCourseShow = true
    },
    cancelRecommend() {
      this.$refs.form.resetFields()
      this.curCourse = {}
      this.dialogRecommendShow = false
    },
    confirmRecommend() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          recommendAdd(this.form).then(() => {
            this.cancelRecommend()
            this.init()
            this.$refs['form'].resetFields()
            this.curCourse = {}
          }).finally(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    },
    setSort(row) {
      this.dialogSortShow = true
      this.sortForm.sort = row.listOrder
      this.curObj = row
    },
    // 选择课程
    selectCourse(v) {
      this.curCourse = v
      this.form.courseId = v.coursePackId
      this.dialogCourseShow = false
    },
    // 移除已选
    delCourse() {
      this.curCourse = {}
      this.dialogCourseShow = true
    }
  }
}
</script>
