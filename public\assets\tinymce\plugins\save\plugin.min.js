!function(){"use strict";var n=tinymce.util.Tools.resolve("tinymce.PluginManager"),t=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),a=tinymce.util.Tools.resolve("tinymce.util.Tools"),o=function(n){return n.getParam("save_enablewhendirty",!0)},c=function(n){return!!n.getParam("save_onsavecallback")},i=function(n){return!!n.getParam("save_oncancelcallback")},r=function(n,e){n.notificationManager.open({text:n.translate(e),type:"error"})},e=function(n){var e;if(e=t.DOM.getParent(n.id,"form"),!o(n)||n.isDirty()){if(n.save(),c(n))return n.execCallback("save_onsavecallback",n),void n.nodeChanged();e?(n.setDirty(!1),e.onsubmit&&!e.onsubmit()||("function"==typeof e.submit?e.submit():r(n,"Error: Form submit field collision.")),n.nodeChanged()):r(n,"Error: No form element found.")}},l=function(n){var e=a.trim(n.startContent);i(n)?n.execCallback("save_oncancelcallback",n):(n.setContent(e),n.undoManager.clear(),n.nodeChanged())},d=function(n){n.addCommand("mceSave",function(){e(n)}),n.addCommand("mceCancel",function(){l(n)})},s=function(t){return function(n){var e=n.control;t.on("nodeChange dirty",function(){e.disabled(o(t)&&!t.isDirty())})}},u=function(n){n.addButton("save",{icon:"save",text:"Save",cmd:"mceSave",disabled:!0,onPostRender:s(n)}),n.addButton("cancel",{text:"Cancel",icon:!1,cmd:"mceCancel",disabled:!0,onPostRender:s(n)}),n.addShortcut("Meta+S","","mceSave")};n.add("save",function(n){u(n),d(n)})}();