<template>
  <section class="publish-task">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form" :disabled="$route.query.type === 'read'">
      <el-form-item label="产品:" prop="productId" class="product">
        <el-select
          v-model="form.serviceProviderOrgId"
          placeholder="请选择服务商"
          @change="changeServiceProvider()"
        >
          <el-option
            v-for="v in serviceList"
            :key="v.serviceOrgId"
            :label="v.serviceName"
            :value="v.serviceOrgId"
          />
        </el-select>
        <el-select
          v-model="form.orgzId"
          placeholder="请选择企业"
          @change="getProductList()"
        >
          <el-option
            v-for="v in orgList"
            :key="v.orgId"
            :label="v.orgName"
            :value="v.orgId"
          />
        </el-select>
        <el-select v-model="form.productId" placeholder="请选择产品">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型:" prop="type">
        <el-select v-model="form.type" placeholder="请选择类型">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务名称:" prop="name">
        <el-input v-model="form.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="任务时间:" prop="time">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
        />
      </el-form-item>
      <el-form-item label="任务要求:" prop="requireDesc">
        <el-input
          v-model="form.requireDesc"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 8}"
          resize="none"
          placeholder="请输入任务要求"
        />
      </el-form-item>
      <el-form-item label="任务对象:" prop="users" required="users">
        <el-button type="primary" @click="selectUsers('CREATOR')">选择创作者</el-button>
        <el-button type="primary" @click="selectUsers('PROMOTER')">选择推广员</el-button>
        <select-users
          :id="form.productId"
          :dialog-visible.sync="selectVisible"
          :type="selectType"
          :list.sync="form.users"
        />
        <el-table
          :data="form.users"
          border
          style="width: 1200px"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column
            prop="userName"
            label="姓名"
            width="120"
            align="center"
          />
          <el-table-column
            prop="userType"
            label="类型"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.userType === 'CREATOR' ? '创作者' : '推广员' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="phone"
            label="手机"
            width="120"
            align="center"
          />
          <el-table-column
            prop="promotionBudget"
            label="推广预算 (元)"
            align="center"
          >
            <template slot-scope="scope">
              <el-input-number
                v-model="form.users[scope.$index].promotionBudget"
                :precision="2"
                :controls="false"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="serviceBudget"
            label="制作预算 (元)"
            align="center"
          >
            <template slot-scope="scope">
              <el-input-number
                v-model="form.users[scope.$index].serviceBudget"
                :precision="2"
                :controls="false"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="extraBonus"
            label="点击奖励"
            align="center"
          >
            <el-table-column
              prop="extraBonusLimit"
              label="奖励上限 (元)"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="form.users[scope.$index].extraBonusLimit"
                  :precision="2"
                  :controls="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="extraBonusPrice"
              label="单价 (元)"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="form.users[scope.$index].extraBonusPrice"
                  :precision="2"
                  :controls="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="targetClickNum"
              label="目标点击量"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="form.users[scope.$index].targetClickNum"
                  :precision="0"
                  :disabled="true"
                  :controls="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="settleClickNum"
              label="结算点击量"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="form.users[scope.$index].settleClickNum"
                  :precision="0"
                  :controls="false"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="extraBonusEffeday"
              label="有效期 (天)"
              align="center"
            >
              <template slot-scope="scope">
                <el-input-number
                  v-model="form.users[scope.$index].extraBonusEffeday"
                  :precision="0"
                  :controls="false"
                />
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item v-if="$route.query.type !== 'read'">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>
    <el-button v-if="$route.query.type === 'read'" @click="$router.go(-1)">返回</el-button>
  </section>
</template>

<script>
import { getUnitList } from '@/api/userManage'
import { serviceProviderList, productList, addDoulaTask, editDoulaTask, doulaTaskDetail } from '@/api/marketing/taskPromote'
import SelectUsers from '@/pages/marketing/task/components/selectUsers.vue'
export default {
  components: { SelectUsers },
  data() {
    return {
      form: {
        serviceProviderOrgId: null,
        orgzId: null,
        productId: null,
        name: '',
        time: [],
        requireDesc: '',
        type: '',
        users: []
      },
      rules: {
        productId: [{ required: true, message: '请选择产品', trigger: 'change' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        time: [{ required: true, message: '请选择任务周期', trigger: 'change' }],
        requireDesc: [{ required: true, message: '请输入任务要求', trigger: 'blur' }],
        users: [
          { required: true, message: '请选择任务对象', trigger: 'blur' }
        ]
      },
      serviceList: [],
      orgList: [],
      productList: [],
      typeList: [
        { name: '抖喇图文征集', value: 'DOULA_ARTICLE' },
        { name: '抖喇短视频征集', value: 'DOULA_VIDEO' }
      ],
      selectVisible: false,
      selectType: ''
    }
  },
  watch: {
    'form.users': {
      handler(newVal, oldVal) {
        newVal.forEach(item => {
          if (item.extraBonusLimit && item.extraBonusPrice) {
            item.targetClickNum = item.extraBonusLimit / item.extraBonusPrice
            item.settleClickNum = !item.settleClickNum ? item.targetClickNum : item.settleClickNum
          }
        })
      },
      deep: true
    }
  },
  created() {
    this.getService()
    if (this.$route.query.taskId) {
      doulaTaskDetail({ taskId: this.$route.query.taskId }).then(res => {
        this.form = res
        this.form.time = [res.startTime, res.endTime]
        this.getOrg()
        this.getProductList('init')
      })
    }
  },
  methods: {
    getService() {
      serviceProviderList().then(res => {
        this.serviceList = res
      })
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002,
          serviceProviderOrgId: this.form.serviceProviderOrgId
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getProductList(init) {
      if (!init) {
        this.form.productId = null
      }
      productList({ orgzId: this.form.orgzId }).then(res => {
        this.productList = res
      })
    },
    changeServiceProvider() {
      this.getOrg()
      this.form.orgzId = null
      this.form.productId = null
      this.form.users = []
    },
    handleDelete(index, row) {
      this.form.users.splice(index, 1)
    },
    selectUsers(type) {
      if (!this.form.productId) {
        this.$message.error('请先选择产品')
      } else {
        this.selectVisible = true
        this.selectType = type
      }
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const query = JSON.parse(JSON.stringify(this.form))
          query.startTime = query.time[0]
          query.endTime = query.time[1]
          if (this.$route.query.taskId) {
            query.taskId = this.$route.query.taskId
            editDoulaTask(query).then(() => {
              this.$message.success('编辑成功')
              this.$router.push(
                { name: 'MarketingTaskDoula' }
              )
            })
          } else {
            addDoulaTask(query).then(() => {
              this.$message.success('发布成功')
              this.$router.push(
                { name: 'MarketingTaskDoula' }
              )
            })
          }
        }
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        } else if ([3, 4, 5].includes(index)) {
          const values = data.map(item => Number(item[column.property]))
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)
            sums[index] += ' 元'
          }
        }
      })
      return sums
    }
  }
}
</script>

<style lang="scss" scoped>
.publish-task {
  height: calc(100% - 50px);
  padding: 50px 100px;
  .product {
    .el-select{
      width: 250px;
      margin-right: 15px;
    }
  }
  .el-select,
  .el-date-editor,
  .el-textarea,
  .el-input {
    width: 520px;
  }
  ::v-deep .el-table {
    margin-top: 10px;
    th {
      padding: 0;
    }
    .el-input {
      width: 90px;
    }
  }
  table {
    width: 600px;
    border: 1px solid #ebeef5;
    border-spacing: 0;
    th,
    td {
      text-align: center;
      border-right: 1px solid #ebeef5;
      &:nth-last-child(1) {
        border-right: none;
      }
    }
    th {
      color: #909399;
      font-size: 14px;
    }
    td {
      border-top: 1px solid #ebeef5;
    }
    .el-input {
      width: 120px;
    }
  }
}
</style>
