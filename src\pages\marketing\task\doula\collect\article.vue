<template>
  <section class="articleEdit">
    <div class="articleEdit-title">
      {{ $route.query.id ? '编辑图文': '发布图文' }}
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="文字内容" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入内容"
          :maxlength="2000"
          show-word-limit
          style="width: 520px"
        />
      </el-form-item>
      <el-form-item label="链接">
        <add-link :link.sync="form.content.url" :link-name.sync="form.content.urlTitle" />
      </el-form-item>
      <el-form-item label="图片" required prop="coverIds">
        <el-upload
          ref="uploadFile"
          list-type="picture-card"
          :class="{ hideUpdate: form.fileList.length >= 9 }"
          :action="uploadFileApi"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess"
          :on-remove="removePic"
          :file-list="form.fileList"
        >
          <span slot="default" class="uploadStyle">
            <i class="el-icon-plus" />
            <span>添加图片</span>
          </span>
        </el-upload>
        最多9张
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="作者">
        <el-select v-if="info.userType === 'CREATOR'" v-model="form.authorId" disabled>
          <el-option :label="info.userName" :value="form.authorId" />
        </el-select>
        <div v-if="info.userType !== 'CREATOR'">
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" type="doula" />
        </div>
      </el-form-item>
      <el-form-item label="关联任务">
        <el-input v-model="info.taskName" disabled />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { saveArticleDoula, lookArticleDoula } from '@/api/marketing/taskExecute'
import Author from '@/pages/marketing/task/components/author.vue'
import AddLink from '@/pages/marketing/task/components/addLink.vue'
export default {
  components: {
    Author,
    AddLink
  },
  data() {
    return {
      info: this.$route.params.info,
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      form: {
        articleId: null,
        authorId: null,
        taskUserId: null,
        authorName: '',
        content: {
          url: '',
          urlTitle: ''
        },
        contentType: 'DOULA_ARTICLE',
        categoryIds: [],
        coverIds: [],
        coverUrls: [],
        fileList: []
      },
      authorInfo: {},
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        description: [
          { required: true, message: '请输入文字内容', trigger: 'blur' },
          { min: 1, max: 2000, message: '长度在 2000 个字符内', trigger: 'blur' }
        ],
        categoryIds: [
          { required: true, message: '请选择专科分类', trigger: 'change' }
        ],
        coverIds: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ]
      },
      authorDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
      }
    }
  },
  mounted() {
    if (this.$route.params.type && this.$route.params.type === 'edit') {
      lookArticleDoula({ articleId: this.info.articleId }).then(res => {
        this.form.articleId = res.id
        this.form.authorId = res.authorId
        this.form.authorName = res.authorName
        this.authorInfo.authorName = res.authorName
        this.form.description = res.description
        this.form.contentType = res.contentType
        this.form.content = JSON.parse(res.content)
        this.form.categoryIds = res.categoryIds
        this.form.coverIds = res.coverIds
        this.form.coverUrls = res.covers
        this.form.fileList = res.coverUrls.map(v => {
          return { url: v }
        })
      })
    }
    // 获取专科分类
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
    if (this.info.userId !== '' && this.info.userType === 'CREATOR') {
      this.form.authorId = this.info.userId
      this.form.authorName = this.info.userName
    }
  },
  methods: {
    uploadSuccess(val) {
      this.form.coverIds.push(val.data.id)
      this.form.fileList.push({
        url: val.data.url,
        name: val.data.id
      })
    },
    removePic(val) {
      const index = this.form.fileList.findIndex(item => item.uid === val.uid)
      this.form.coverIds.splice(index, 1)
      this.form.fileList.splice(index, 1)
      this.form.coverUrls.splice(index, 1)
    },
    async beforeUpload(val) {
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      const isLt50M = val.size / 1024 / 1024 < 50
      if (!JPG && !PNG && !JPEG) {
        const uid = val.uid // 关键作用代码，去除文件列表失败文件
        const index = this.$refs.uploadFile.uploadFiles.findIndex(item => item.uid === uid) // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）
        this.$refs.uploadFile.uploadFiles.splice(index, 1) // 关键作用代码，去除文件列表失败文件
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过50M')
        return
      }
      const param = {
        filename: val.name,
        size: val.size,
        type: val.type
      }
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.coverIds.length === 0) {
          return this.$message.error('请上传图片')
        }
        if (valid) {
          const param = {
            authorId: this.authorInfo.authorId || this.form.authorId,
            categoryIds: this.form.categoryIds,
            description: this.form.description,
            content: JSON.stringify(this.form.content),
            contentType: this.form.contentType,
            coverIds: this.form.coverIds,
            taskUserId: this.info.id
          }
          if (this.form.articleId) {
            param.id = this.form.articleId
          }
          saveArticleDoula(param).then(res => {
            this.agreementDialogVisible = true
            if (this.form.articleId) {
              this.$message.success('图文编辑完成,进入审核流程')
            } else {
              this.$message.success('图文发布完成,请提醒作者收到消息后在app完成授权协议签署以进入后续审核流程')
            }
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.articleEdit {
  background-color: #fff;
  width: 1173px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  ::v-deep .el-form {
    margin-left: 20px;
    &-item {
      &__label {
        color: #666;
      }
      .pdf-success,.uploadPdf {
        width: 520px;
        height: 184px;
        background: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
        color: #999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        h2 {
          margin: 0 auto;
          font-size: 22px;
          color: #000;
        }
      }
      .pdf-success {
        position: relative;
        img{
          width: 60px;
          &.pdf-success-del {
            position: absolute;
            top: 14px;
            right: 14px;
            width: 24px;
            cursor: pointer;
          }
        }
      }
      .el-radio-button {
        &__inner {
          margin-right: 10px;
          padding: 7px 0;
          width: 52px;
          height: 28px;
          border-radius: 14px;
        }
      }
      .el-upload-list__item {
        margin-top: 18px;
      }
      .el-upload--picture-card {
        margin-top: 18px;
        width: 146px;
        height: 146px;
        .uploadStyle {
          display: flex;
          flex-direction: column;
          margin-top: 50px;
          opacity: .6;
          i {
            font-size: 26px;
          }
          span{
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
      .el-cascader,
      .el-select {
        width: 520px;
      }
      .el-tag {
        max-width: 180px;
        height: 36px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .editor-wrapper {
      width: 948px;
    }
  }
}
.hideUpdate ::v-deep .el-upload--picture-card {
    display: none;
}
.hideContentUpdate {
  ::v-deep .el-upload {
    display: none;
  }
}
</style>
