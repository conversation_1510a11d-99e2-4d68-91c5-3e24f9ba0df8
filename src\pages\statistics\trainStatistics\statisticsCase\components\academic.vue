<template>
  <div class="identContain">
    <div class="search-column">
      <div class="search-column__item"><span class="record-text" />用户职称分布</div>
      <div class="search-column__item fr">
        <el-button v-show="isShow === 2" type="text" @click="handlerCutChart(1)"><i class="el-icon-s-fold" style="margin-right:10px" />列表</el-button>
        <el-button v-show="isShow === 1" type="text" @click="handlerCutChart(2)"><i class="el-icon-s-data" style="margin-right:10px" />图表</el-button>
        <el-button v-show="isShow!==3" type="text" @click="handleDownload"> <i class="el-icon-download" style="margin-right:10px" />下载</el-button>
      </div>
    </div>
    <div>
      <div v-show="isShow===3" class="empty">暂无数据</div>
      <div v-show="isShow===2" id="academicChart" class="academicChart" />
      <!-- 列表 -->
      <div v-show="isShow===1">
        <el-table :data="tableData" stripe class="academicTable" border>
          <el-table-column
            prop="name"
            label="类型"
            align="center"
          />
          <el-table-column
            prop="num"
            :label="labelName"
            align="center"
          >
            <template slot-scope="{row}">
              {{ row.num }}{{ [8, 9, 10, 11, 13].includes(query.type) ? '%' : '' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="zhanbi"
            label="占比"
            align="center"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
const echarts = require('echarts/lib/echarts')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/legend')
require('echarts/lib/chart/pie')
import request from '@/api/dataStatistics/statisticsCase'
export default {
  name: 'StatisticsAcademic',
  props: {
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null,
      request,
      isShow: 2,
      tableData: [],
      sum: 0
    }
  },
  computed: {
    labelName() {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[this.query.type]
    }
  },
  watch: {
    query: {
      handler() {
        this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.myChart = echarts.init(document.getElementById('academicChart'))
        window.addEventListener('resize', () => {
          this.myChart.resize()
        })
      })
    },
    async getList() {
      const query = JSON.parse(JSON.stringify(this.query))
      const params = {}
      params.areaId = query.areaId
      params.areaType = query.areaType
      params.endTime = query.endTime
      params.startTime = query.startTime
      params.type = query.type

      if (query.identityIds.length === 0) {
        // 空身份时，请求多个身份的接口，加个单值为-1的参数
        params.identityId = -1
        params.identityIds = []
        await this.request.academics(params).then(res => {
          this.jurgement(res)
        })
      } else if (query.identityIds.length === 1 && query.identityIds[0] === '0') {
        // 请求多个身份的接口
        params.identityIds = ['0']
        await this.request.academics(params).then(res => {
          this.jurgement(res)
        })
      } else if (query.identityIds.length === 1 && query.identityIds[0] !== '0') {
        // 请求一个身份的接口
        params.identityId = Number(query.identityIds[0])
        await this.request.academic(params).then(res => {
          this.jurgement(res)
        })
      } else {
        // 请求多个身份的接口
        params.identityIds = query.identityIds
        await this.request.academics(params).then(res => {
          this.jurgement(res)
        })
      }
    },
    jurgement(res) {
      let data = []
      if (JSON.stringify(res) == '{}' || JSON.stringify(res) == '[]') {
        this.isShow = 3
        return
      } else {
        // 更换num为value
        data = JSON.parse(JSON.stringify(res).replace(/num/g, 'value'))
        // 求和，并且对人数方面的数值做取整
        let sum = 0
        if ([8, 9, 10, 11, 13].includes(this.query.type)) {
          res.forEach(item => {
            sum += item.num
          })
        } else {
          res.forEach(item => {
            item.num = parseInt(item.num)
            sum += item.num
          })
        }
        // 求占比
        res.forEach(item => {
          if (sum === 0) {
            item.zhanbi = '0%'
          } else {
            item.zhanbi = (item.num / sum * 100).toFixed(2) + '%'
          }
        })
        // 对sum的小数点后几位做处理
        if (JSON.stringify(sum).indexOf('.') !== -1) {
          this.sum = sum.toFixed(2)
        } else {
          this.sum = sum
        }
        this.tableData = res
        this.isShow = 2
        this.initCharts()
      }
      this.setOptions(data)
    },
    // type 1/列表 2/图表
    handlerCutChart(type) {
      this.isShow = type
    },
    handleDownload() {
      // 将tabel表格转换为execl数据表，并下载为execl文件
      if (this.isShow === 1) {
        if (this.tableData.length < 1) {
          this.$message.info('当前数据为空')
          return
        }
        this.getExportParams()
      }
      // 将echarts图表转换为canvas,并将canvas下载为图片
      if (this.isShow === 2) {
        const aLink = document.createElement('a')
        const blob = this.base64ToBlob()
        const evt = document.createEvent('HTMLEvents')
        evt.initEvent('click', true, true)
        aLink.download = '用户职称分布'
        aLink.href = URL.createObjectURL(blob)
        aLink.click()
      }
    },
    getExportParams() {
      const query = JSON.parse(JSON.stringify(this.query))
      const params = {}
      params.areaId = query.areaId
      params.areaType = query.areaType
      params.endTime = query.endTime
      params.startTime = query.startTime
      params.type = query.type

      if (query.identityIds.length === 0) {
        // 空身份时，请求多个身份的接口，加个单值为-1的参数
        params.identityId = -1
        params.identityIds = []
        this.export2(params)
      } else if (query.identityIds.length === 1 && query.identityIds[0] === '0') {
        // 请求多个身份的接口
        params.identityIds = ['0']
        this.export2(params)
      } else if (query.identityIds.length === 1 && query.identityIds[0] !== '0') {
        // 请求一个身份的接口
        params.identityId = Number(query.identityIds[0])
        this.export1(params)
      } else {
        // 请求多个身份的接口
        params.identityIds = query.identityIds
        this.export2(params)
      }
    },
    export1(params) {
      this.request.exportAcad(params).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    export2(params) {
      this.request.exportAcademic(params).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    exportImg() { // echarts返回一个 base64的URL
      const myChart = echarts.init(
        document.getElementById('academicChart')
      )
      return myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
    },
    base64ToBlob() { // 将base64转换blob
      const img = this.exportImg()
      const parts = img.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    setOptions(data) {
      this.myChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: `{b0}: {c0}${[8, 9, 10, 11, 13].includes(this.query.type) ? '%' : ''}`
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '6%',
          right: '14%'
        },
        series: [
          {
            name: '用户职称分布',
            type: 'pie',
            radius: ['50%', '86%'],
            center: ['36%', '46%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              formatter: `${[4, 8, 9, 10, 11, 13].includes(this.query.type) ? '' : this.sum}`,
              fontSize: '36',
              fontWeight: 'bold'
            },
            data: data
          }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.identContain{
  border-radius: 5px;
  background: #fff;
  box-shadow: 0 0 8px rgb(167, 165, 165);

  .search-column {
    height: 60px;
    line-height: 60px;
    border-radius: 5px;
    padding: 5px 20px;
  }

  .empty{
    text-align: center;
    width: 100%;
    height: 300px;
    line-height: 150px;
    color: #999;
    font-size: 24px;
  }

  .academicChart{
    width: 100%;
    height: 300px;
  }

  .academicTable{
    font-size: 15px;
    width: 100%;
    height: 300px;
    overflow-y: scroll;
    overflow-x: hidden;
  }
}
.record-text {
  margin-bottom: 10px;
  padding-left: 15px;
  border-left: 2px solid #4F8EF8;
}
</style>
