<template>
  <div>
    <div class="btn">
      <el-button type="primary" @click="dialogVisible = true">选择活动</el-button>
      <el-input
        :placeholder="title"
        disabled
        style="width: 400px"
      />
    </div>
    <div @click="dialogVisible = true"><slot /></div>
    <el-dialog title="选择活动" :visible.sync="dialogVisible" width="900px" class="selectTrainContent" center>
      <div class="search-column">
        <div class="search-column__item">
          <el-input v-model="condition.keyword" placeholder="请输入搜索关键字" />
        </div>
        <div class="search-column__item">
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        </div>
      </div>
      <!-- body -->
      <a-table :columns="columns" fit :data="list" border stripe max-height="400" @selection-change="onSelectChange">
        <template slot="time" slot-scope="{row}">
          <span>{{ row.duration|formatSeconds }}</span>
        </template>
        <template slot="actions" slot-scope="{row}">
          <el-button size="mini" type="text" @click="close(row.activityId)">选择</el-button>
        </template>
      </a-table>
      <!-- pagination -->
      <Pagination :auto-scroll="false" class="text-center page" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    </el-dialog>
  </div>
</template>

<script>
import request from '@/api/activity'
import table from '@/mixins/table'
import { formatSeconds } from '@/utils/index'

const columns = [
  // { props: { type: 'index', width: '55', label: '排序', align: 'center' }},
  { props: { label: '活动名称', align: 'center', prop: 'title' }},
  // { props: { label: '活动时长', align: 'center', width: '180' }, slot: 'time' },
  { props: { align: 'center', label: '操作', width: '100' }, slot: 'actions' }
]

export default {
  filters: {
    formatSeconds,
    typeFmt(v) {
      const arr = ['图片广告', '活动广告', '活动广告', '课程广告']
      return arr[v - 1]
    },
    placeFmt(v) {
      const arr = ['App开屏广告', 'App首页banner']
      return arr[Number(v) - 1]
    },
    statusFmt(v) {
      const arr = ['下架', '上架']
      return arr[v - 1]
    }
  },
  mixins: [table],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    activityId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      columns,
      request,
      videoTypeList: [
        { label: '活动名称', value: 1 },
        { label: '创建者名称', value: 2 },
        { label: '单位名称', value: 3 }
      ],
      title: '请选择活动',
      // select list
      adStateList: [
        { value: 1, name: '下架' },
        { value: 2, name: '上架' }
      ],
      condition: {
        type: 1,
        keyword: '',
        status: 1
      },
      mainKey: 'advertisementId',
      editPath: 'addAd',
      showInput: false,
      dialogVisible: false,
      initList: false
    }
  },
  watch: {
    dialogVisible(v) {
      v && this.getList()
    },
    activityId(v) {
      if (v !== '') {
        this.getDetail(v)
      }
    }
  },
  created() {
    // get select list
    if (!this.$slots.default) {
      this.showInput = true
    } else {
      this.showInput = false
    }
  },
  methods: {
    getDetail(v) {
      request.detail(v).then(res => {
        this.$emit('checkedActivity', res)
        this.title = res.title
        this.dialogVisible = false
      })
    },
    close(activityId) {
      this.$emit('update:activityId', activityId)
    },
    setList(arr) {
      this.list = arr
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header{
  display: flex;
  align-items: center;
  justify-content: center;
}
.cover-img{
  width: 100px;
  height: auto;
  object-fit: contain;
}
.page{
  padding: 0;
}
.btn {
  font-size: 0;
  .el-button {
    border-radius: 0;
  }
  .el-input__inner {
    border-radius: 0;
  }
}
</style>
