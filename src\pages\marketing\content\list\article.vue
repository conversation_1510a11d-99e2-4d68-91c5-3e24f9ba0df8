<template>
  <section class="articleEdit">
    <div class="articleEdit-title">
      {{ $route.query.id ? '编辑文章': '发布文章' }}
    </div>
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="文章标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入文章标题 (5~100个字)"
          :maxlength="100"
          style="width: 520px"
        />
      </el-form-item>
      <el-form-item label="内容类型" required>
        <el-radio-group v-model="form.contentType">
          <el-radio label="RICH_TEXT">直接编辑</el-radio>
          <el-radio label="PDF">PDF</el-radio>
        </el-radio-group>
        <editor
          v-if="form.contentType === 'RICH_TEXT'"
          v-model="form.content"
          height="356"
        />
        <el-upload
          v-if="form.contentType === 'PDF'"
          accept="application/pdf"
          :show-file-list="false"
          :class="{ hideContentUpdate: form.contentList.length >= 1 }"
          :action="uploadFileApi"
          :data="uploadData"
          :limit="1"
          :file-list="form.contentList"
          :before-upload="handleBeforeUpload"
          :on-success="handleSuccess"
        >
          <div class="uploadPdf">
            <h2>
              <i class="el-icon-plus avatar-uploader-icon" /> 上传PDF
            </h2>
            大小不超过50M
          </div>
        </el-upload>
        <div v-if="form.contentType === 'PDF' && form.contentList.length >= 1" class="pdf-success">
          <img src="@/assets/images/fabu_icon_pdf.png">
          <div class="pdf-name">{{ form.content }}</div>
          <img src="@/assets/images/fabu_icon_del.png" class="pdf-success-del" @click="handleContentRemove">
        </div>
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="封面设置" required prop="imgSum">
        <el-radio-group v-model="form.imgSum" @input="clearImg">
          <el-radio-button :label="1">单图</el-radio-button>
          <el-radio-button :label="3">三图</el-radio-button>
        </el-radio-group>
        <UploadPic v-if="form.imgSum === 1" :key="form.coverUrls[0]" tips="添加封面图" :pic-id.sync="form.coverIds[0]" :url="form.coverUrls[0]" />
        <el-upload
          v-if="form.imgSum === 3"
          ref="uploadFile"
          list-type="picture-card"
          :class="{ hideUpdate: form.coverIds.length >= 3 }"
          :action="uploadFileApi"
          :data="uploadData"
          :before-upload="beforeUpload"
          :on-success="uploadSuccess"
          :on-remove="removePic"
          :file-list="form.fileList"
        >
          <span slot="default" class="uploadStyle">
            <i class="el-icon-plus" />
            <span>添加封面图</span>
          </span>
        </el-upload>
      </el-form-item>
      <el-form-item label="作者" required>
        <div>
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" />
        </div>
      </el-form-item>
      <el-form-item v-if="form.source && form.source === 1" label="任务">
        {{ form.taskName }}
      </el-form-item>
      <el-form-item v-if="form.source && form.source === 1" label="执行人">
        {{ form.taskUserName + '/' + form.taskUserPhone }}
      </el-form-item>
      <el-form-item>
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import Editor from '@/components/wangEditor'
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { saveOrUpdate, contentDetail } from '@/api/marketing/contentManage'
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Author from '@/pages/marketing/task/components/author.vue'
export default {
  components: {
    Editor,
    UploadPic,
    Author
  },
  data() {
    return {
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      form: {
        contentId: null,
        authorId: null,
        authorName: '',
        title: '',
        contentType: 'RICH_TEXT',
        content: '',
        contentList: [],
        categoryIds: [],
        imgSum: 1,
        coverIds: [],
        coverUrls: [],
        fileList: []
      },
      authorInfo: {},
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        title: [
          { required: true, message: '请输入文章标题', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        categoryIds: [
          { type: 'array', required: true, message: '请选择专科分类', trigger: 'change' }
        ]
      },
      pdfName: '',
      detail: {},
      authorDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
      }
    }
  },
  mounted() {
    if (this.$route.query.id) {
      contentDetail(this.$route.query.id).then(res => {
        res.contentList = this.form.contentList
        if (res.contentType === 'PDF') {
          res.contentList.push(
            {
              url: res.content,
              name: res.fileName
            })
        }
        this.form = res
        this.form.contentId = res.id
        this.authorInfo.authorName = res.authorName
        this.authorInfo.phone = res.authorPhone
        this.$set(this.form, 'categoryIds', res.cateIds)
        this.$set(this.form, 'coverIds', JSON.parse(res.coverImgIds).map(String))
        this.form.coverUrls = res.coverImgUrls
        this.form.fileList = res.coverImgUrls.map(v => {
          return { url: v }
        })
        this.form.imgSum = this.form.coverIds.length === 1 ? 1 : 3
      })
    }
    // 获取专科分类
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
  },
  methods: {
    async handleBeforeUpload(val) {
      const param = {
        filename: val.name,
        size: val.size,
        type: 'adv'
      }
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        this.form.content = res.data.url
        this.form.contentList = [
          {
            url: this.form.content,
            name: this.fileName
          }
        ]
      }
    },
    handleRemove() {
      this.form.fileList = []
    },
    handleContentRemove() {
      this.form.contentList = []
      this.form.content = ''
    },
    uploadSuccess(val) {
      this.form.coverIds.push(val.data.id)
      this.form.fileList.push({
        url: val.data.url,
        name: val.data.id
      })
    },
    clearImg() {
      this.form.coverIds = []
      this.form.fileList = []
      this.form.coverUrls = []
    },
    removePic(val) {
      const index = this.form.fileList.findIndex(item => item.uid === val.uid)
      this.form.coverIds.splice(index, 1)
      this.form.fileList.splice(index, 1)
      this.form.coverUrls.splice(index, 1)
    },
    async beforeUpload(val) {
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      const isLt50M = val.size / 1024 / 1024 < 50
      if (!JPG && !PNG && !JPEG) {
        const uid = val.uid // 关键作用代码，去除文件列表失败文件
        const index = this.$refs.uploadFile.uploadFiles.findIndex(item => item.uid === uid) // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）
        this.$refs.uploadFile.uploadFiles.splice(index, 1) // 关键作用代码，去除文件列表失败文件
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return
      }
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过50M')
        return
      }
      const param = {
        filename: val.name,
        size: val.size,
        type: val.type
      }
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.imgSum === 3 && this.form.coverIds.length !== 3) {
          return this.$message.error('请上传三张封面图')
        } else if (this.form.imgSum === 1 && this.form.coverIds.length !== 1) {
          return this.$message.error('请上传封面图')
        }
        if (!this.form.authorId) {
          this.$message.error('未选择作者')
          return
        }
        if (valid) {
          const param = {
            articleType: 'ARTICLE',
            contentId: this.form.contentId,
            authorId: this.authorInfo.authorId || this.form.authorId,
            categoryIds: this.form.categoryIds,
            content: this.form.content,
            contentType: this.form.contentType,
            coverIds: this.form.coverIds,
            title: this.form.title
          }
          saveOrUpdate(param).then(res => {
            if (this.form.contentId) {
              this.$message.success('文章编辑完成')
            } else {
              this.$message.success('文章发布完成')
            }
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.articleEdit {
  width: 1173px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  ::v-deep .el-form {
    margin-left: 20px;
    &-item {
      &__label {
        color: #666;
      }
      .pdf-success,.uploadPdf {
        width: 520px;
        min-height: 184px;
        padding: 20px 10px;
        background: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
        color: #999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        h2 {
          margin: 0 auto;
          font-size: 22px;
          color: #000;
        }
        .pdf-name {
          margin-top: 10px;
          width: 100%;
          word-wrap: break-word;
          line-height: 24px;
        }
      }
      .pdf-success {
        position: relative;
        img{
          width: 60px;
          &.pdf-success-del {
            position: absolute;
            top: 14px;
            right: 14px;
            width: 24px;
            cursor: pointer;
          }
        }
      }
      .el-radio-button {
        &__inner {
          margin-right: 10px;
          padding: 7px 0;
          width: 52px;
          height: 28px;
          border-radius: 14px;
        }
      }
      .el-upload-list__item {
        margin-top: 18px;
      }
      .el-upload--picture-card {
        margin-top: 18px;
        width: 146px;
        height: 146px;
        .uploadStyle {
          display: flex;
          flex-direction: column;
          margin-top: 50px;
          opacity: .6;
          i {
            font-size: 26px;
          }
          span{
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
      .el-cascader,
      .el-select {
        width: 520px;
      }
      .el-tag {
        max-width: 180px;
        height: 36px;
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .editor-wrapper {
      width: 948px;
    }
  }
}
.hideUpdate ::v-deep .el-upload--picture-card {
    display: none;
}
.hideContentUpdate {
  ::v-deep .el-upload {
    display: none;
  }
}
</style>
