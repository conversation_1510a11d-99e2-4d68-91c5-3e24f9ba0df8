<template>
  <div>
    <el-input v-if="!this.$slots.default" v-model="dvalue" placeholder="请选择视频" @focus="dialogVisible = true" />
    <div @click="dialogVisible = true"><slot /></div>
    <el-dialog title="选择视频" :visible.sync="dialogVisible" width="900px" class="selectTrainContent" center>
      <div class="search-column">
        <!-- <div class="search-column__item fr">
              <el-select v-model="condition.type">
                <el-option
                  v-for="item in videoTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div> -->
        <div class="search-column__item">
          <el-input v-model="condition.keyword" placeholder="请输入搜索关键字" />
        </div>
        <div class="search-column__item">
          <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        </div>
      </div>
      <!-- body -->
      <a-table :columns="columns" fit :data="list" border stripe max-height="400" @selection-change="onSelectChange">
        <template slot="time" slot-scope="{row}">
          <span>{{ row.duration|formatSeconds }}</span>
        </template>
        <template slot="actions" slot-scope="{row}">
          <el-button size="mini" type="text" @click="choose(row)">选择</el-button>
        </template>
      </a-table>
      <!-- pagination -->
      <Pagination :auto-scroll="false" class="text-center page" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    </el-dialog>
  </div>
</template>

<script>
import request from '@/api/SelectContent/Video'
import table from '@/mixins/table'
import { formatSeconds } from '@/utils/index'

const columns = [
  { props: { type: 'index', width: '55', label: '排序', align: 'center' }},
  { props: { label: '视频名称', align: 'center', prop: 'name' }},
  { props: { label: '视频时长', align: 'center', width: '180' }, slot: 'time' },
  { props: { align: 'center', label: '操作', width: '100' }, slot: 'actions' }
]

export default {
  filters: {
    formatSeconds,
    typeFmt(v) {
      const arr = ['图片广告', '活动广告', '视频广告', '课程广告']
      return arr[v - 1]
    },
    placeFmt(v) {
      const arr = ['App开屏广告', 'App首页banner']
      return arr[Number(v) - 1]
    },
    statusFmt(v) {
      const arr = ['下架', '上架']
      return arr[v - 1]
    }
  },
  mixins: [table],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      columns,
      request,
      videoTypeList: [
        { label: '视频名称', value: 1 },
        { label: '创建者名称', value: 2 },
        { label: '单位名称', value: 3 }
      ],
      dvalue: '',
      // select list
      adStateList: [
        { value: 1, name: '下架' },
        { value: 2, name: '上架' }
      ],
      condition: {
        // type: 1,
        keyword: ''
      },
      mainKey: 'advertisementId',
      editPath: 'addAd',
      showInput: false,
      dialogVisible: false,
      initList: false
    }
  },
  watch: {
    value(v) {
      this.dvalue = v
    },
    dialogVisible(v) {
      v && this.getList()
    }
  },
  created() {
    // get select list
    if (!this.$slots.default) {
      this.showInput = true
    } else {
      this.showInput = false
    }
  },
  methods: {
    choose(row) {
      this.dialogVisible = false
      const { videoId, videoInfoId, videoFileId } = row
      if (!videoId || !videoInfoId || !videoFileId) return this.$message.error('该视频无法播放, 请选择其他视频')
      this.$emit('change', videoFileId)
      this.$emit('choose', row)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header{
  display: flex;
  align-items: center;
  justify-content: center;
}
.cover-img{
  width: 100px;
  height: auto;
  object-fit: contain;
}
.page{
  padding: 0;
}
</style>
