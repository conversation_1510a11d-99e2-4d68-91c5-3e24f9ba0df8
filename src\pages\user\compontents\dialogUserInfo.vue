<template>
  <div class="app-container">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="700px"
      lock-scroll
      top="8vh"
      :append-to-body="isAppend"
      @close="close()"
    >
      <el-tabs v-model="activeName" style="padding:20px">
        <el-tab-pane label="个人信息" name="userData">
          <el-form label-width="120px" size="mini">
            <el-form-item label="账号名：">
              {{ datas.userPersonalResponseDto.username }}
            </el-form-item>
            <el-form-item label="姓名：">
              {{ datas.userPersonalResponseDto.realName }}
            </el-form-item>
            <el-form-item label="手机号：">
              {{ datas.userPersonalResponseDto.phone }}
            </el-form-item>
            <el-form-item label="身份：">
              {{ datas.userPersonalResponseDto.identityName }}
            </el-form-item>
            <el-form-item label="职称：">
              {{ datas.userPersonalResponseDto.academicName }}
            </el-form-item>
            <el-form-item label="关注的专科：">
              {{ datas.userPersonalResponseDto.majorName }}
            </el-form-item>
            <el-form-item label="工作单位：">
              {{ datas.userPersonalResponseDto.company }}
            </el-form-item>
            <el-form-item label="部门科室：">
              {{ datas.userPersonalResponseDto.department }}
            </el-form-item>
            <el-form-item label="所在城市：">
              {{ datas.userPersonalResponseDto.provinceName }} -
              {{ datas.userPersonalResponseDto.cityName }} -
              {{ datas.userPersonalResponseDto.areaName }}
            </el-form-item>
            <el-form-item label="擅长：">
              <div style="margin-right:30px;">
                {{ datas.userPersonalResponseDto.skill }}
              </div>
            </el-form-item>
            <el-form-item label="个人简介：">
              <div style="margin-right:30px;">
                {{ datas.userPersonalResponseDto.introduction }}
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="员工信息" name="staffData">
          <div v-for="item in datas.userStaffList" :key="item.uid">
            <el-form label-width="120px">
              <el-form-item label="单位名称：">
                {{ item.company }}
              </el-form-item>
              <el-form-item label="员工号：">
                {{ item.username }}
              </el-form-item>
              <el-form-item label="员工姓名：">
                {{ item.realName }}
              </el-form-item>
              <el-form-item label="联系手机：">
                {{ item.phone }}
              </el-form-item>
              <el-form-item label="身份：">
                {{ item.identityName }}
              </el-form-item>
              <el-form-item v-show="!['5','6'].includes(item.identityName)" label="职称：">
                {{ item.academicName }}
              </el-form-item>
              <el-form-item label="所属专科：">
                {{ item.majorName }}
              </el-form-item>
              <el-form-item label="部门/科室：">
                {{ item.deptName }}
              </el-form-item>
              <el-form-item label="所在城市：">
                {{ item.city }}
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div slot="footer" style="text-align:center">
        <el-button @click="handleCosedialog">取 消</el-button>
        <el-button type="primary" @click="handleCosedialog">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name: 'DialogUserInfo',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '查看用户个人信息'
    },
    data: {
      type: Object,
      default: () => {
        return {
          userPersonalResponseDto: {},
          userStaffList: []
        }
      }
    },
    isAppend: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: this.show,
      activeName: 'userData',
      datas: {
        userPersonalResponseDto: {},
        userStaffList: []
      }
    }
  },
  watch: {
    show() {
      this.visible = this.show
    },
    data() {
      this.datas = this.data
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false)
      this.activeName = 'userData'
    },
    handleCosedialog() {
      this.close()
    }
  }
}
</script>
