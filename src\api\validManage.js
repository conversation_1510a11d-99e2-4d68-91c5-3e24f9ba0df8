import request from '@/utils/request'

// 视频上下架列表
export function videoList(params) {
  return request({
    url: '/video/putaway/list',
    method: 'post',
    data: params
  })
}

// 设置视频上下架
export function changeVideoStatus(params) {
  return request({
    url: `/video/putaway/${params.videoId}/${params.videoInfoId}/${params.status}`,
    method: 'post'
  })
}

// 获取视频详情
export function getVideoDetail(params) {
  return request({
    url: `/video/web/detail/${params.videoId}/${params.videoSource}`,
    method: 'post',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// 播放视频
export function playVideoInfo(params) {
  return request({
    url: `/video/web/play/${params.videoId}/${params.videoInfoId}/${params.videoFileId}`,
    method: 'get',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// 视频审核列表
export function videoValidList(params) {
  return request({
    url: '/video/list',
    method: 'post',
    data: params
  })
}

// 视频审核详情
export function videoValidDetail(params) {
  return request({
    url: `/video/audit/${params.videoInfoId}`,
    method: 'post'
  })
}

// 视频审核历史
export function videoValidateHistory(params) {
  return request({
    url: '/video/audit/history',
    method: 'post',
    data: params
  })
}

// 视频审核
export function videoValidate(params) {
  return request({
    url: '/video/audit/pass',
    method: 'post',
    data: params
  })
}

// 教程审核列表
export function courseList(params) {
  return request({
    url: '/course/cms/course/list',
    method: 'post',
    data: params
  })
}

// 课程库列表
export function coursePack(params) {
  return request({
    url: '/coursePack/cmsList',
    method: 'post',
    data: params
  })
}

// 教程审核历史
export function courseValidateHistroy(params) {
  return request({
    url: '/course/audit/history',
    method: 'post',
    data: params
  })
}

// 教程审核详情
export function courseValidDetail(params) {
  return request({
    url: `/course/audit/course/${params.courseInfoId}`,
    method: 'post'
  })
}

// 教程审核
export function courseValid(params) {
  return request({
    url: '/course/audit/course/pass',
    method: 'post',
    data: params
  })
}

// sop审核列表
export function sopList(params) {
  return request({
    url: '/sop/cms/sop/list',
    method: 'post',
    data: params
  })
}

// sop审核历史
export function sopValidateHistroy(params) {
  return request({
    url: '/sop/audit/history',
    method: 'post',
    data: params
  })
}

// sop审核详情
export function sopValidDetail(params) {
  return request({
    url: `/sop/audit/sop/${params.sopInfoId}`,
    method: 'post'
  })
}

// sop-course审核详情
export function sopCourseValidDetail(params) {
  return request({
    url: `/sop/course/${params.courseId}/${params.courseInfoId}`,
    method: 'post'
  })
}

// sop审核
export function sopValid(params) {
  return request({
    url: '/sop/audit/sop/pass',
    method: 'post',
    data: params
  })
}
// 视频设置虚拟量
export function setVideoVirtual(params) {
  return request({
    url: '/video/update/virtual/value',
    method: 'post',
    data: params
  })
}

/* -------------------- 视频 ------------------   */
// 添加视频/修改分类
export function addVideo(params) {
  return request({
    url: '/video/category/edit',
    method: 'post',
    data: params
  })
}

// 修改排序值
export function setVideoSort(params) {
  return request({
    url: '/video/sort/edit',
    method: 'post',
    data: params
  })
}

// 播放视频次数+1
export function displayHit(videoId) {
  return request({
    url: `/video/cms/display/hit/${videoId}`,
    method: 'post'
  })
}

/* -------------------- 课程 ------------------   */
// 添加课程/修改分类
export function addCoursePack(params) {
  return request({
    url: '/coursePack/category/edit',
    method: 'post',
    data: params
  })
}

// 修改排序值
export function setCoursePackSort(params) {
  return request({
    url: '/coursePack/sort/edit',
    method: 'post',
    data: params
  })
}

/* -------------------- 教程 ------------------   */
// 教程列表
export function getCourseList(params) {
  return request({
    url: '/course/vantage/list',
    method: 'post',
    data: params
  })
}

// 添加教程/修改分类
export function addCourse(params) {
  return request({
    url: '/course/vantage/category/edit',
    method: 'post',
    data: params
  })
}

// 查看详情
export function getCourseDetail(courseInfoId) {
  return request({
    url: `/course/view/detail/${courseInfoId}`,
    method: 'get'
  })
}

// 上下架操作
export function coursePutaway(courseId, status) {
  return request({
    url: `/course/putaway/${courseId}/${status}`,
    method: 'post'
  })
}

// 修改排序值
export function setCourseSort(params) {
  return request({
    url: '/course/vantage/sort/edit',
    method: 'post',
    data: params
  })
}
/* -------------------- sop ------------------   */
// sop列表
export function getSopList(params) {
  return request({
    url: '/sop/putaway/list',
    method: 'post',
    data: params
  })
}

// 添加sop/修改分类
export function addSop(params) {
  return request({
    url: '/sop/category/edit',
    method: 'post',
    data: params
  })
}

// 查看详情
export function getSopDetail(sopInfoId) {
  return request({
    url: `/sop/view/detail/${sopInfoId}`,
    method: 'get'
  })
}

// 打开课程
export function openCourse(courseInfoId) {
  return request({
    url: `/sop/open/course/${courseInfoId}`,
    method: 'get'
  })
}

// 修改排序值
export function setSopSort(params) {
  return request({
    url: '/sop/sort/edit',
    method: 'post',
    data: params
  })
}

// 上下架操作
export function sopPutaway(sopId, status) {
  return request({
    url: `/sop/putaway/${sopId}/${status}`,
    method: 'post'
  })
}

// 纠错记录列表
export function redressList(params) {
  return request({
    url: '/errorCorrection/pageList',
    method: 'post',
    data: params
  })
}

// 纠错处理
export function redressHandle(params) {
  return request({
    url: '/errorCorrection/handle',
    method: 'post',
    data: params
  })
}

// 设置视频会员等级
export function setVideoMemberLevel(params) {
  return request({
    url: '/video/setVideoMemberLevel',
    method: 'post',
    data: params
  })
}

// 设置教程会员等级
export function setCourseMemberLevel(params) {
  return request({
    url: '/course/setCourseMemberLevel',
    method: 'post',
    data: params
  })
}

// 设置SOP会员等级
export function setSopMemberLevel(params) {
  return request({
    url: '/sop/setSopMemberLevel',
    method: 'post',
    data: params
  })
}
