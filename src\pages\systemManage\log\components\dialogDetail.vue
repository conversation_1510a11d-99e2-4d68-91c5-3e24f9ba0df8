<template>
  <el-dialog title="操作数据" :visible.sync="show" :before-close="beforeClose" top="50px" width="1200px">

    <el-table v-if="detail.arr&&detail.arr.length>0" :data="detail.arr">
      <el-table-column v-for="item in columns" :key="item.prop" :label="item.label" :prop="item.prop" align="center" />
    </el-table>

    <div v-if="detail.faceDetail&&detail.faceDetail.length>0" class="s_title">人脸认证详情</div>
    <el-row v-if="detail.faceDetail&&detail.faceDetail.length>0">
      <el-table :data="detail.faceDetail" tooltip-effect="dark" :header-cell-style="{background:'#ECF0F1'}">
        <el-table-column label="教程ID" align="center" prop="courseId" />
        <el-table-column label="教程名称" align="center" prop="courseName" />
        <el-table-column label="本人人脸" align="center" prop="faceImgUrl">
          <template slot-scope="{ row }">
            <el-image style="width: 100px; height: 100px" :src="row.faceImgUrl" fit="cover">
              <div slot="error" class="image_error">未认证</div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="认证人脸" align="center" prop="contrastFaceImgUrl">
          <template slot-scope="{ row }">
            <el-image style="width: 100px; height: 100px" :src="row.contrastFaceImgUrl" fit="cover">
              <div slot="error" class="image_error">未认证</div>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="contrastResult">
          <template slot="header">
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">
                <span>匹配：认证人脸与本人人脸对比符合；</span><br>
                <span>不匹配：认证人脸与本人人脸对比不符合；</span><br>
                <span>特殊情况：首次人脸识别只采集本人人脸数据，不做人脸认证，认证人脸为空，对比结果也为“匹配”。</span>
              </div>
              <span>认证结果 <i class="el-icon-question" /></span>
            </el-tooltip>
          </template>
          <template slot-scope="{ row }">
            <el-tag :type="row.contrastResult|resultType" size="medium">{{ row.contrastResult | resultStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="operationTime" />
      </el-table>
    </el-row><br>

    <div v-if="detail.lookPaperRecords&&detail.lookPaperRecords.length>0" class="s_title">查看考题记录</div>
    <el-row v-if="detail.lookPaperRecords&&detail.lookPaperRecords.length>0" class="row1">
      <el-table :data="detail.lookPaperRecords" tooltip-effect="dark" :header-cell-style="{background:'#ECF0F1'}">
        <el-table-column label="章节" align="center" prop="chapterName" />
        <el-table-column label="试卷" align="center" prop="paperGroupName" />
        <el-table-column label="试卷时间点" align="center" prop="timeSpot">
          <template slot-scope="{ row }">
            <span>{{ row.timeSpot | formatSeconds }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作时间" align="center" prop="operationTime">
          <template slot-scope="{ row }">
            <span>{{ row.operationTime | parseTime }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

  </el-dialog>
</template>

<script>
import { formatSeconds, parseTime } from '@/utils'

const resultStatusOpt = [
  { type: 'warning', name: '未匹配' },
  { type: '', name: '匹配' },
  { type: 'danger', name: '不匹配' }
]

export default {
  name: 'DialogOperation',
  filters: {
    resultStatus(val) {
      return resultStatusOpt[val].name
    },
    resultType(val) {
      return resultStatusOpt[val].type
    },
    formatSeconds,
    parseTime
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      columns: [
        { label: '字段', prop: 'fieldName' },
        { label: '原值', prop: 'oldValue' },
        { label: '新值', prop: 'newValue' }
      ]
    }
  },
  methods: {
    beforeClose(done) {
      this.$emit('update:show', false)
    }
  }
}
</script>
<style lang="scss" >
.s_title{
  font-size: 18px;
  padding: 10px;
}
.image_error{
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  color: #c0c4cc;
  background-color: #f5f7fa;

}
.row1{
  min-height: 200px;
  max-height: 500px;
  overflow-y: auto;
}
</style>
