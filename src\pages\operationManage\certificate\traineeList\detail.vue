<template>
  <div class="app-container">
    <div class="por">
      <div class="search-column">
        <span>学员: {{ $route.query.name }}</span>
        <span>手机: {{ $route.query.phone }}</span>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0)"
        :data="tableData"
        :header-cell-style="{background:'#ECF0F1'}"
        :border="true"
        :row-class-name="getRowClass"
        @expand-change="expandOne"
      >
        <!-- 账号+人员+专科+所在单位列表 -->
        <el-table-column v-for="(item,index) in personCol" :key="index" align="center" :prop="item.prop" :label="item.label" :min-width="item.width" />
        <!-- 操作 -->
        <el-table-column type="expand" label="操作" align="center" width="100">
          <template slot-scope="{row}">
            <el-table :data="row.insideTable" @expand-change="expandSecond">
              <el-table-column v-for="(item,index) in insideTableCol" :key="index" align="center" :prop="item.prop" :label="item.label" />
              <!-- 操作 -->
              <el-table-column type="expand" label="操作" align="center" width="100">
                <template slot-scope="{row}">
                  <el-table :data="row.chapterTable" @expand-change="expandAnswerRecord">
                    <el-table-column v-for="(item,index) in chapterTableCol" :key="index" align="center" :prop="item.prop" :label="item.label" />
                    <!-- 操作 -->
                    <el-table-column type="expand" label="操作" align="center" width="100">
                      <template slot-scope="thirdRow">
                        <!-- 试题 -->
                        <div v-for="(testItem,index) in thirdRow.row.testList" :key="index" class="test-item">
                          <i :class="testItem.passed ? 'el-icon-success' : 'el-icon-error'" />
                          <div class="test-title">{{ index+1|indexFmt }}. {{ testItem.title }} （{{ testItem.submitAnswer }}）？（{{ testItem.questionType }}）</div>
                          <div v-for="(option,oIndex) in testItem.options" :key="oIndex" class="test-option">
                            <i :class="option.isAnswer ? 'el-icon-check' : 'place-icon'" />
                            {{ option.index }}、{{ option.content }}
                          </div>
                          <div class="test-tip">【正确答案】{{ testItem.standardAnswer }}</div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
// import { getStaffAssessRecord, examRecordDetail } from '@/api/trainData/hospital'
import { directoryList, courseList, chapterList, answerRecordList } from '@/api/certificate'
export default {
  name: 'ExmineDetail',
  filters: {
    faceResultFmt(val) {
      const arr = ['无人脸', '匹配', '不匹配']
      return arr[val]
    },
    statusFmt(val) {
      const arr = ['未通过', '通过', '未参加', '考核中']
      return arr[val]
    },
    resultFmt(val) {
      const arr = ['未通过', '通过', '未参加', '考核中']
      return arr[val]
    },
    indexFmt(val) {
      return val < 10 ? '0' + val : val
    }
  },
  data() {
    return {
      // 表格
      tableData: [],
      // 人员统计列
      personCol: [
        { prop: 'examineDirectoryNo', label: '序号', width: '20' },
        { prop: 'examineDirectoryName', label: '章', width: '200' },
        { prop: 'statusDesc', label: '考核结果', width: '30' },
        { prop: 'examineNum', label: '考核次数', width: '30' }
      ],
      // 人员统计展开列
      insideTableCol: [
        { prop: 'courseSeq', label: '序号' },
        { prop: 'courseName', label: '教程' },
        { prop: 'statusDesc', label: '考核结果' },
        { prop: 'examineNum', label: '考核次数', width: '20' }
      ],
      chapterTableCol: [
        { prop: 'serial', label: '考核次序' },
        { prop: 'insterTime', label: '考核时间' },
        { prop: 'chapterName', label: '考核章节' },
        { prop: 'assessTimeStr', label: '考核时长', width: '20' },
        { prop: 'correctScale', label: '正确率', width: '20' },
        { prop: 'assessResultStr', label: '考核结果', width: '20' }
      ],
      loading: true
    }
  },
  created() {
    this.getExamineDetailList()
  },
  methods: {
    // 跳转人脸记录
    goToFace(userName) {
      this.$router.push({ name: 'TrainingLog', query: { 'userName': userName }})
    },
    // 人员统计展开
    expandOne(row) {
      row.expandFlag = !row.expandFlag
      if (row.expandFlag) {
        row.insideTable = []
        courseList({ directoryRecordId: row.id }).then(res => {
          res.forEach(v => {
            v.testList = []
            v.expandFlag = false
          })
          row.insideTable.push(...res)
        })
      }
    },
    expandSecond(row) {
      row.expandFlag = !row.expandFlag
      if (row.expandFlag) {
        row.chapterTable = []
        chapterList({ courseRecordId: row.id }).then(res => {
          res.forEach(v => {
            v.testList = []
            v.expandFlag = false
          })
          row.chapterTable.push(...res)
        })
      }
    },
    expandAnswerRecord(row) {
      row.expandFlag = !row.expandFlag
      if (row.expandFlag) {
        answerRecordList({ examineRecordId: row.assessRecordId }).then(res => {
          row.testList = []
          row.testList = res
        })
      }
    },
    // 考核详情列表
    getExamineDetailList() {
      directoryList({ examineJoinId: this.$route.query.id }).then(res => {
        res.forEach(v => {
          v.expandFlag = false
          v.insideTable = []
        })
        this.tableData = res
        this.loading = false
      })
    },
    getRowClass({ row }) {
      if (row.status === 0) {
        return 'hide-expand'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.app-container{
  .title {
    position: relative;
    border-bottom: 2px solid #409EFF;
    .el-button {
      position: absolute;
      right: 0;
      bottom: 6px;
    }
  }
  .por{
    position: relative;
    padding: 0 22px;
    background-color: #fff;
    min-height: 65vh;
    .back-btn{
      position: absolute;
      right: 0;
      top: 0;
      z-index: 10;
    }
    .dateTitle {
      padding: 10px;
      margin: 0;
      background-color: #fff;
    }
  }

  ::v-deep .el-table .hide-expand .cell .el-table__expand-icon {
    display: none;
  }

  ::v-deep .el-tabs__header {
    border-bottom: 2px solid #409EFF !important;

    .el-tabs__nav-wrap {
      margin-bottom: 0;
    }

    .el-tabs__item{
      background: #E2EBE8;
      color: #999;
      &.is-active{
        color: #fff;
        background: #409EFF;
        border-bottom-color: #409EFF;
      }
    }
  }
  ::v-deep .el-tabs__content {
    height: 630px;
    overflow-y: scroll
  }
  ::v-deep .el-tabs__content::-webkit-scrollbar {
    display: none;
  }
  .el-tab-pane{
    padding: 29px 25px;
  }
  .search-column {
    padding: 0 0 24px;
    background: #fff;
    span {
      margin-right: 20px;
    }
  }

  .paper-list {
    ::v-deep .el-collapse-item__header {
      padding-left: 22px;
      &.is-active {
        border-bottom-color: #e6ebf5;
      }
    }
    ::v-deep .el-collapse-item__content {
      padding: 10px;
    }
    ::v-deep .question-list {
      .el-card {
        border-color: transparent;
        .el-card__header {
          padding: 18px 40px 0;
          border: none;
        }
        .el-card__body {
          padding: 20px 40px;
        }
      }
    }
  }
}
.rank {
    cursor: pointer;
}
.test-item{
      position: relative;
      font-size: 14px;
      color: #333;
      margin-bottom: 45px;

      >i{
        position: absolute;
        right: 30px;
        top: 10px;
        font-size: 40px;
        color:#46b94b;
        &.el-icon-error{
          color: #f56c6c;
        }
      }

      .test-title{
        font-weight: bold;
        margin-bottom: 14px;
      }
      .test-option{
        line-height: 30px;
        .place-icon{
          display: inline-block;
          width: 14px;
        }
      }
      .test-tip{
        margin-top: 16px;
      }
    }
</style>
