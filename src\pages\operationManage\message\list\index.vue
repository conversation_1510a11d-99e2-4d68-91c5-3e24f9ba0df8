<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.title"
        class="group"
        placeholder="请输入搜索关键字"
        clearable
        @change="search"
        @clear="clearKeyWord"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>

      <el-select
        v-model="listQuery.condition.status"
        placeholder="状态"
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.channel"
        placeholder="消息通道"
        @change="search"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      消息列表
      <div>
        <el-button type="primary" @click="sendPop">发送</el-button>
        <el-button type="primary" @click="$router.push({ name: 'MessageCreator' })">创建</el-button>
      </div>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
        />
        <el-table-column prop="title" label="消息标题">
          <template slot-scope="scope">
            <el-tooltip v-if="scope.row.title.length>18" class="item" popper-class="desc-tool-tip" effect="dark" :content="scope.row.title" placement="top-start">
              <el-button
                type="text"
                size="mini"
                @click="edit(scope.row, 'detail')"
              >{{ scope.row.title }}</el-button>
            </el-tooltip>
            <el-button
              v-else
              type="text"
              size="mini"
              @click="edit(scope.row, 'detail')"
            >{{ scope.row.title }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === 1"
              type="text"
              size="mini"
              @click="send(scope.row.id)"
            >发送</el-button>
            <el-button
              v-if="scope.row.status === 1"
              type="text"
              size="mini"
              @click="edit(scope.row, 'edit')"
            >编辑</el-button>
            <el-button
              v-if="[1, 3].includes(scope.row.status)"
              type="text"
              size="mini"
              @click="del(scope.row.id)"
            >删除</el-button>
            <el-button
              v-if="scope.row.status === 2"
              type="text"
              size="mini"
              @click="withdraw(scope.row.id, 1)"
            >撤回</el-button>
            <el-button
              v-if="[2, 4].includes(scope.row.status)"
              type="text"
              size="mini"
              @click="statistics(scope.row)"
            >消息统计</el-button>
            <!-- <el-button
              v-if="scope.row.status === 2"
              type="text"
              size="mini"
              @click="edit(scope.row, 'add')"
            >追加消息对象</el-button> -->
            <!-- <el-button
              v-if="scope.row.status === 3"
              type="text"
              size="mini"
              @click="withdraw(scope.row.id, 2)"
            >取消撤回</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <el-dialog
      title="发送方式"
      :visible.sync="sendDialogVisible"
      width="20%"
      center
    >
      <el-form :model="sendQuery" style="height: 300px">
        <el-form-item label="发送时间" required>
          <el-radio-group v-model="now">
            <el-radio :label="true">立即发送</el-radio>
            <el-radio :label="false">定时发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!now">
          <el-date-picker
            v-model="sendQuery.sendTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择发送时间"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="send()">确认</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getMessageTaskList, sendMsg, updateWithdraw, delMessageTask } from '@/api/message'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      stateOptions: [
        { label: '全部状态', value: null },
        { label: '待发送', value: 1 },
        { label: '已发送', value: 2 },
        { label: '已撤回', value: 3 },
        { label: '发送中', value: 4 }
      ],
      typeOptions: [
        { label: '全部消息通道', value: null },
        { label: '站内信', value: 'STATION_MSG' },
        { label: 'App push', value: 'APP_PUSH' }
      ],
      listQuery: {
        condition: {
          status: null,
          channel: null,
          title: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'msgType', label: '消息类型', width: '60' },
        { prop: 'channel', label: '消息通道', width: '80' },
        { prop: 'statusStr', label: '消息状态', width: '80' },
        { prop: 'sendTime', label: '发送时间', width: '80' },
        { prop: 'userName', label: '创建人', width: '50' },
        { prop: 'createdTime', label: '创建时间', width: '80' }
      ],
      tableData: [],
      sendDialogVisible: false,
      now: true,
      sendQuery: {
        msgIds: [],
        sendTime: null
      }
    }
  },
  watch: {
    now(v) {
      if (v) {
        this.sendQuery.sendTime = null
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleSelectionChange(v) {
      this.sendQuery.msgIds = v.map(v => {
        if (v.status === 1) { return v.id }
      })
    },
    sendPop() {
      if (this.sendQuery.msgIds.length >= 1) {
        this.sendDialogVisible = true
      } else { this.$message.error('请先选择需要发送的消息') }
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    getList() {
      getMessageTaskList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.channel = v.appPushFlag && v.stationMsgFlag ? '站内信, App push' : (!v.appPushFlag && v.stationMsgFlag ? '站内信' : 'App push')
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    clearKeyWord() {
      this.listQuery.condition.title = ''
      this.getList()
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    send(id) {
      this.sendDialogVisible = false
      if (id) {
        console.log(1)
        this.sendQuery = {
          msgIds: [id]
        }
      }
      sendMsg(this.sendQuery).then(() => {
        this.$message.success('发送成功')
        this.getList()
      }).catch(err => {
        this.$message.error(err)
      })
    },
    del(id) {
      this.$confirm('请慎重进行删除操作，以免造成数据损失，仍要删除请确定', '确认信息', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delMessageTask(id).then(() => { this.getList() })
      })
    },
    withdraw(id, type) {
      if (type === 1) {
        this.$confirm('确定撤回消息吗?', '确认信息', {
          distinguishCancelAndClose: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          updateWithdraw({ msgId: id, operateType: type }).then(() => { this.getList() })
        })
      } else {
        updateWithdraw({ msgId: id, operateType: type }).then(() => { this.getList() })
      }
    },
    edit(row, type) {
      this.$router.push({
        name: 'MessageCreator',
        query: { id: row.id, type }
      })
    },
    statistics(row) {
      this.$router.push({
        name: 'MessageStatistics',
        params: row
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: #eaeaee;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
