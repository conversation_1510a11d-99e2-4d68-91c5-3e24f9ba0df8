<template>
  <el-dialog :visible.sync="visible" width="40%" top="8vh">
    <div slot="title" class="header-title">
      <h3>详情
        <el-tooltip class="item" effect="dark" content="统计用户点击广告的详情数据" placement="top-start">
          <i class="el-icon-warning" />
        </el-tooltip>
      </h3>
    </div>

    <el-table border stripe :data="tableData" height="350" style="width: 100%">
      <el-table-column label="序号" type="index" width="100" align="center" />
      <el-table-column label="点击时间" prop="clickTime" align="center" />
    </el-table>
    <!-- page -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
// import getList from '@/api/'

export default {
  name: 'DetailDialog',
  components: { Pagination },
  props: {
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      tableData: [],
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // page
      layout: 'total, prev, pager, next, jumper',
      total: 0
    }
  },
  watch: {
    visible: {
      handler(v) {
        // getList().then(res => {
        //   this.tableData = res
        // })
      },
      immediate: true
    }
  },
  methods: {
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUserList()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  .header-title {
    h3 {
      margin: 0;
    }
  }
}
</style>
