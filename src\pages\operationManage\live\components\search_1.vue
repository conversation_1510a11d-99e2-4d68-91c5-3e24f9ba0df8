<template>
  <div>
    <!-- 区域选择器 -->
    <div class="search-column__item">
      <el-cascader
        ref="cascader1"
        v-model="condition.areaIds"
        placeholder="请选择区域"
        :options="areaList"
        collapse-tags
        :show-all-levels="false"
        :props="{
          value:'areaId',
          label:'name',
          children:'childList',
          multiple:true,
          emitPath:false
        }"
        clearable
      />
    </div>
    <!-- 身份选择器 -->
    <div class="search-column__item">
      <el-cascader
        v-model="condition.identityIds"
        placeholder="请选择身份"
        :options="identityTree"
        collapse-tags
        :show-all-levels="false"
        :props="{
          multiple: true,
          value:'identityId',
          label:'name',
          children:'childList',
          emitPath: false
        }"
        clearable
        @change="identityChange"
      />
    </div>
    <!-- 专科选择器 -->
    <div class="search-column__item">
      <el-cascader
        v-model="condition.majorIds"
        placeholder="请选择专科"
        :options="majorList"
        collapse-tags
        :props="{
          multiple: true,
          value:'majorId',
          label:'name',
          children:'childList',
          emitPath: false
        }"
        clearable
      />
    </div>
    <!-- 职称选择器 -->
    <div class="search-column__item">
      <el-cascader
        v-model="condition.academicIds"
        placeholder="请选择职称"
        :options="academicList"
        collapse-tags
        :props="{
          multiple: true,
          value:'academicId',
          label:'name',
          children:'childList',
          emitPath: false,
          checkStrictly: true
        }"
        clearable
      />
    </div>
    <div class="search-column__item">
      <el-button type="primary" @click="init()">查询</el-button>
      <el-button type="primary" @click="clear()">重置</el-button>
    </div>
  </div>
</template>

<script>
import { identityTreeList, majorTreeList } from '@/api/category' // 身份树，随身份联动的专科树
import { getAreaTree } from '@/api/area' // 区域树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树

export default {
  data() {
    return {
      condition: {
        identityIds: [],
        majorIds: [],
        academicIds: [],
        areaIds: []
      },
      areaList: [],
      areaId: [],
      identityTree: [],
      majorList: [],
      academicList: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ]
    }
  },
  created() {
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    // 获取专科树
    treeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', majorId: '0' })
      this.majorList = newArr
    })
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      this.areaList = this.clearNullChildList(res, 'childList')
    })
  },
  methods: {
    init() {
      this.$emit('search', this.condition)
    },
    clear() {
      this.condition.identityIds = []
      this.condition.majorIds = []
      this.condition.academicIds = []
      this.majorList = []
      this.academicList = []
      this.condition.areaIds = []
      this.$emit('clear', this.condition)
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      this.condition.majorIds = []
      this.condition.academicIds = []
      if (e.length === 0) {
        this.majorList = []
        this.academicList = []
      }
      if (e.length === 1 && e[0] === '0') {
        // 身份选'无'，专科职称也为无
        this.majorList = []
        this.academicList = []
        this.condition.majorIds = []
        this.condition.academicIds = []
        return
      }
      if (e.length > 0) {
        majorTreeList({ identityIds: e }).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: 0 })
          this.majorList = newArr
        })
      }
      if (e.length > 1) {
        this.academicList = JSON.parse(JSON.stringify(this.academicList2))
      }
      if (e.length === 1) {
        academicTreeListById(e[0]).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: 0 })
          this.academicList = newArr
        })
      }
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style>

</style>
