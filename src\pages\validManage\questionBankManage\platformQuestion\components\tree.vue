<template>
  <div class="app-container">
    <el-row>
      <el-input v-show="showSearch" v-model="ifShowSearch" size="mini" placeholder="搜索" clearable />
      <div v-if="activeName === 'orgContacts'">
        <el-tree
          ref="tree"
          class="mycs-tree"
          :data="treeData"
          node-key="questionCategoryId"
          :filter-node-method="filterNode"
          :highlight-current="onHighlightCurrent"
          :props="{ label: 'name', children: 'childList' }"
          expand-on-click-node
          :draggable="allowDrag"
          @node-click="handleClick"
          @node-drag-start="handleNodeDragStart"
          @node-drag-end="handleNodeDragEnd"
          @node-expand="addDefaultKeys"
          @node-collapse="delDefaultKeys"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="showMenu(node,data)">
            <span class="over-text" :title="node.label">{{ node.label }}</span>
            <span
              v-show="nodeIdCur == data.questionCategoryId"
              class="fr tree-option"
              @click.stop="() => setNodeIdCur(data)"
            >
              <i class="el-icon-more" />
              <div
                v-show="nodeIdCur == data.questionCategoryId && memuShow"
                :style="{ 'margin-top': -(data.st)+'px' }"
                @mouseenter="handleMouseEnter"
                @mouseleave="handleMouseLeave"
              >
                <div class="option-mask" :class="isBottom?'bottom':''">
                  <div
                    v-show="node.level > 0"
                    class="list-item"
                    @click.stop="() => appendSameLevel(data)"
                  >
                    <svg-icon icon-class="memberGroup" />添加同级
                  </div>
                  <div
                    class="list-item"
                    @click.stop="() => appendChildLevel(data)"
                  >
                    <svg-icon icon-class="memberGroup" />添加下级
                  </div>
                  <div
                    v-show="node.level > 0 && isOpen"
                    class="list-item"
                    @click.stop="() => editThisLevel(data)"
                  >
                    <svg-icon icon-class="setting" />编辑
                  </div>
                  <div
                    v-show="node.level > 0 && isOpen"
                    class="list-item"
                    @click.stop="() => deleteThisLevel(node, data)"
                  >
                    <svg-icon icon-class="deleteDept" />删除
                  </div>
                </div>
              </div>
            </span>
          </span>
        </el-tree>
      </div>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'OTree',
  props: {
    tree: {
      type: Array,
      default: function() {
        return []
      }
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    allowDrag: {
      type: Boolean,
      default: false
    },
    filterTextModel: {
      type: String,
      default: ''
    },
    defaultExpandedKeys: {
      type: Array,
      default: function() {
        return []
      }
    },
    highlightCurrent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      onHighlightCurrent: this.highlightCurrent,
      isBottom: false,
      isOpen: true,
      timmer: null,
      memuTimmer: null,
      show: true,
      activeName: 'orgContacts',
      nodeIdCur: -1,
      memuShow: false,
      filterText: '',
      listQuery: {
        condition: {
          params: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      treeData: []
    }
  },
  computed: {
    ifShowSearch() {
      return this.showSearch ? this.filterText : this.filterTextModel
    }
  },
  watch: {
    highlightCurrent() {
      this.onHighlightCurrent = this.highlightCurrent
    },
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    filterTextModel(val) {
      this.$refs.tree.filter(val)
    },
    tree: {
      handler: function(val) {
        this.treeData = val
      },
      immediate: true
    }
  },
  methods: {
    addDefaultKeys(data) {
      // this.defaultKeys.push(data.questionCategoryId)
    },
    delDefaultKeys(data) {
      // this.defaultKeys = this.defaultKeys.filter((item) => data.questionCategoryId !== item)
    },
    ellipsisFont(font) {
      return font.length >= 8 ? font.substring(0, 7) + '...' : font
    },

    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    },
    // 关于菜单操作
    showMenu(node, data) {
      const scrollT = window.document.getElementsByClassName('el-scrollbar__wrap')[4].scrollTop
      const scrollT2 = window.document.getElementsByClassName('el-scrollbar__wrap')[1].scrollTop
      if (window.event.y + 220 < document.body.clientHeight) {
        data.st = scrollT + scrollT2
        this.isBottom = false
      } else {
        data.st = 90 + scrollT + scrollT2
        this.isBottom = true
      }
      clearTimeout(this.memuTimmer)
      this.nodeIdCur = data.questionCategoryId
    },
    hideMenu() {
      this.nodeIdCur = -1
    },
    setNodeIdCur(data) {
      this.memuShow = true
    },
    // 收起/展开
    optionClick() {
      this.isOpen = !this.isOpen
    },
    // 拖拽开始
    handleNodeDragStart(node, event) {
      const returnObj = { node, event }
      this.$emit('nodeDragStart', returnObj)
    },
    // 拖拽结束
    handleNodeDragEnd(node, enterNode, event) {
      const returnObj = { node, enterNode, event }
      this.$emit('nodeDragEnd', returnObj)
    },
    // 鼠标进入
    handleMouseEnter() {
      clearTimeout(this.timmer)
    },
    // 鼠标移开
    handleMouseLeave() {
      this.timmer = setTimeout(() => {
        this.nodeIdCur = -1
        this.memuShow = false
        // this.isOpen = false
      }, 500)
    },

    // 添加试题分类
    appendSameLevel(data) {
      this.$emit('appendSameLevel', data)
    },

    // 添加下级试题分类
    appendChildLevel(data) {
      this.$emit('appendChildLevel', data)
    },

    // 节点点击
    handleClick(data, node) {
      this.onHighlightCurrent = true
      data.level = node.level
      this.$emit('handleClick', data, node)
    },

    // 删除试题分类
    deleteThisLevel(node, data) {
      this.$emit('deleteThisLevel', node, data)
    },

    // 编辑试题分类
    editThisLevel(data) {
      this.$emit('editThisLevel', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.mycs-tree ::v-deep .el-tree-node > .el-tree-node__children,
.mycs-tree ::v-deep .el-tree-node.is-expanded > .el-tree-node__children {
  overflow: visible;
}
.mycs-tree ::v-deep .el-tree-node.is-drop-inner>.el-tree-node__content .over-text{
  background: #4AB9A3;
}
.el-input {
  margin-bottom: 14px;
}
.btn-group {
  /*margin-bottom: 20px;*/
  .el-button {
    flex: 1;
    justify-content: center;
  }
}
.svg-icon{
  margin-top: 5px;
  margin-right: 5px;
}
.custom-tree-node {
  display: flex;
  position: relative;
  width: 100%;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  overflow: hidden;
}
.tree-option {
  position: absolute;
  top: 0;
  right: 20px;
  height: 24px;
  width: 20px;
  margin-right: -25px;
  .option-mask {
    padding: 10px 20px;
    position: fixed;
    margin-left: 30px;
    margin-top: -45px;
    background: #ffffff;
    width: 200px;
    z-index: 99;
    border-radius: 3px;
    box-shadow: 1px 1px 15px 0 rgba($color: #000000, $alpha: 0.2);
    .list-item {
      line-height: 2;
      color: #666;
    }
    &.bottom{
      &:after{
        top: 115px;
      }
    }
    &:after{
      content: '';
      position: absolute;
      top: 28px;
      left: -4px;
      display: block;
      border: 4px solid #ffffff;
      width: 0;
      height: 0;
      transform: rotate(45deg);
      box-shadow: -1px 1px 20px 0 rgba($color: #000000, $alpha: 0.2);
    }
  }
}
.over-text{
  font-size: 14px;
  margin-right: 20px;
  flex: 1;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.list-tree,
.list-table {
  height: calc(100vh - 337px);
  border: 1px solid #dfe6ec;
}

.list-tree {
  width: 240px;
  margin-right: 16px;

  .content-container {
    width: 100%;
    height: 100%;
    padding: 0;

    &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
      overflow-x: hidden;
    }
    &::v-deep .el-scrollbar__view.p20-scrollbar__view {
      padding: 20px;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
    }
    &::v-deep .el-scrollbar__bar.is-vertical {
      width: 10px;
    }
  }
}
.list-table {
  flex: 1;
  width: 1px;
  overflow: auto;
}
</style>
