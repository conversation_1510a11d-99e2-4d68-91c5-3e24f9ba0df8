<template>
  <el-dialog title="创建单位" :visible.sync="visible" width="720px" top="8vh" :close-on-click-modal="false" :before-close="beforeClose">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="单位logo" prop="logo">
        <singleImage ref="upload" v-model="form.logo" width="200px" height="100px" type="org_logo" @input="getLogoId" />
      </el-form-item>
      <el-form-item label="单位主题" prop="subjuctCode">
        <div class="color_box">
          <div class="c_img" @click="chooseColor('green')">
            <img src="@/assets/images/colorSetting/img_green.png">
            <i v-if="form.subjuctCode==='green'" class="el-icon-check active" style="color:#30B08F" />
          </div>
          <div class="c_img" @click="chooseColor('blue')">
            <img src="@/assets/images/colorSetting/img_blue.png">
            <i v-if="form.subjuctCode==='blue'" class="el-icon-check active" style="color:#348FE4" />
          </div>
          <div class="c_img" @click="chooseColor('red')">
            <img src="@/assets/images/colorSetting/img_red.png">
            <i v-if="form.subjuctCode==='red'" class="el-icon-check active" style="color:#C20505" />
          </div>
          <div class="c_img" @click="chooseColor('darkGray')">
            <img src="@/assets/images/colorSetting/img_gray.png">
            <i v-if="form.subjuctCode==='darkGray'" class="el-icon-check active" style="color:#2C3544" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="单位类型">
        <span>企业</span>
      </el-form-item>
      <el-form-item label="单位名称">
        <span>{{ detail.orgName }}</span>
      </el-form-item>
      <el-form-item label="单位层级">
        <span>默认</span>
      </el-form-item>
      <el-form-item label="所属区域">
        <span>{{ detail.areaName }}</span>
      </el-form-item>
      <el-form-item>
        <!-- 详细地址 -->
        <span>{{ detail.address }}</span>
      </el-form-item>
      <el-form-item label="单位联系人">
        <span>{{ detail.contacts }}</span>
      </el-form-item>
      <el-form-item label="联系电话">
        <span>{{ detail.phone }}</span>
      </el-form-item>
      <h3 style="margin:40px 0">单位服务配置</h3>
      <el-form-item prop="shortCode" label="单位前缀">
        <el-input v-model="form.shortCode" placeholder="请输入单位前缀" />
      </el-form-item>
      <el-form-item label="员工限额" prop="maxStaffCount">
        <el-input v-model.number="form.maxStaffCount" placeholder="" style="width:120px">
          <template slot="append">人</template>
        </el-input>
      </el-form-item>
      <el-form-item label="视频空间容量" prop="maxSizeCount">
        <el-input v-model.number="form.maxSizeCount" placeholder="" style="width:120px">
          <template slot="append">G</template>
        </el-input>
      </el-form-item>
      <el-form-item label="启用服务">
        <el-radio-group v-model="form.status">
          <el-radio v-for="item in statusList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="人脸识别">
        <el-radio-group v-model="form.faceFlag">
          <el-radio v-for="item in faceFlagList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="组卷服务">
        <el-radio-group v-model="form.examFlag">
          <el-radio v-for="item in faceFlagList" :key="item.key" :label="item.key">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内容扩展方直播">
        <el-radio-group v-model="radio" disabled>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleSaveForm">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import singleImage from '@/components/SingleImage'

export default {
  name: 'DialogUnitCreate',
  components: { singleImage },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const shortCodeValid = (rules, value, callback) => {
      if (value === '') {
        callback(new Error('请输入单位前缀'))
      } else if (!/^[A-Za-z0-9]+$/.test(value)) {
        callback(new Error('单位前缀只支持英文+数字的组合'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        shortCode: [
          { validator: shortCodeValid, required: true, trigger: 'blur' }
        ],
        maxStaffCount: [
          {
            required: true,
            type: 'number',
            message: '请输入员工限额',
            trigger: 'blur'
          }
        ],
        maxSizeCount: [
          {
            required: true,
            type: 'number',
            message: '请输入视频空间容量',
            trigger: 'blur'
          }
        ],
        status: [
          { required: true, message: '请选择是否启用服务', trigger: 'change' }
        ]
      },
      form: {
        logo: '',
        subjuctCode: 'green',
        banner: '',
        shortCode: '',
        maxStaffCount: '',
        maxSizeCount: '',
        status: 1,
        faceFlag: 1,
        examFlag: 1
      },
      // 单位结构类型
      typeList: [],
      // 启用服务
      statusList: [
        { label: '启用', key: 1 },
        { label: '停用', key: 0 }
      ],
      // 人脸识别
      faceFlagList: [
        { label: '开启', key: 1 },
        { label: '关闭', key: 0 }
      ],
      radio: 1

    }
  },
  watch: {
    visible(v) {
      !v && this.$refs.upload.rmImage()
    }
  },
  methods: {
    chooseColor(color) {
      this.form.subjuctCode = color
    },
    getLogoId(logoId) {
      this.form.logo = logoId || '0'
    },
    close() {
      this.$refs.form.resetFields()
      this.$emit('update:visible', false)
      this.$emit('handleCancel')
    },
    handleSaveForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$emit('handleSave', this.form)
        } else {
          return false
        }
      })
    },
    beforeClose(done) {
      this.$refs.form.resetFields()
      this.close()
      this.$nextTick(() => {
        done()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.color_box{
  width: 400px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;

  .c_img{
    position: relative;
    width: 80px;
    height: 80px;
    cursor: pointer;
    img{
      width: 100%;
      height: 100%;
    }
    .active{
      position: absolute;
      top: 40%;
      left: 40%;
      font-size: 24px;
      font-weight: 700;
    }
  }
}
</style>
