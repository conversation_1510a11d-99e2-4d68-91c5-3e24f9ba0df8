const provinceList = [{ 'areaId': '70001', 'code': '110000', 'name': '北京市', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70002', 'code': '120000', 'name': '天津市', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70003', 'code': '130000', 'name': '河北省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70004', 'code': '140000', 'name': '山西省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70005', 'code': '150000', 'name': '内蒙古自治区', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70006', 'code': '210000', 'name': '辽宁省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70007', 'code': '220000', 'name': '吉林省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70008', 'code': '230000', 'name': '黑龙江省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70009', 'code': '310000', 'name': '上海市', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70010', 'code': '320000', 'name': '江苏省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70011', 'code': '330000', 'name': '浙江省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70012', 'code': '340000', 'name': '安徽省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70013', 'code': '350000', 'name': '福建省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70014', 'code': '360000', 'name': '江西省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70015', 'code': '370000', 'name': '山东省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70016', 'code': '410000', 'name': '河南省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70017', 'code': '420000', 'name': '湖北省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70018', 'code': '430000', 'name': '湖南省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70019', 'code': '440000', 'name': '广东省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70020', 'code': '450000', 'name': '广西壮族自治区', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70021', 'code': '460000', 'name': '海南省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70022', 'code': '500000', 'name': '重庆市', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70023', 'code': '510000', 'name': '四川省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70024', 'code': '520000', 'name': '贵州省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70025', 'code': '530000', 'name': '云南省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70026', 'code': '540000', 'name': '西藏自治区', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70027', 'code': '610000', 'name': '陕西省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70028', 'code': '620000', 'name': '甘肃省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70029', 'code': '630000', 'name': '青海省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70030', 'code': '640000', 'name': '宁夏回族自治区', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70031', 'code': '650000', 'name': '新疆维吾尔自治区', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70032', 'code': '710000', 'name': '台湾省', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70033', 'code': '810000', 'name': '香  港', 'wholeName': '', 'parentCode': '', 'level': 1 }, { 'areaId': '70034', 'code': '820000', 'name': '澳  门', 'wholeName': '', 'parentCode': '', 'level': 1 }]
const cityList = [{ 'areaId': '80001', 'code': '110100', 'name': '市辖区', 'wholeName': '', 'parentCode': '110000', 'level': 2 }]
const areaList = [{ 'areaId': '90001', 'code': '110101', 'name': '东城区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90002', 'code': '110102', 'name': '西城区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90003', 'code': '110105', 'name': '朝阳区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90004', 'code': '110106', 'name': '丰台区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90005', 'code': '110107', 'name': '石景山区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90006', 'code': '110108', 'name': '海淀区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90007', 'code': '110109', 'name': '门头沟区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90008', 'code': '110111', 'name': '房山区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90009', 'code': '110112', 'name': '通州区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90010', 'code': '110113', 'name': '顺义区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90011', 'code': '110114', 'name': '昌平区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90012', 'code': '110115', 'name': '大兴区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90013', 'code': '110116', 'name': '怀柔区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90014', 'code': '110117', 'name': '平谷区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90015', 'code': '110118', 'name': '密云区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }, { 'areaId': '90016', 'code': '110119', 'name': '延庆区', 'wholeName': '', 'parentCode': '110100', 'level': 3 }]

export default [
  {
    url: '/area/areaList',
    type: 'get',
    response: config => {
      return {
        code: 1,
        data: provinceList
      }
    }
  }
]
