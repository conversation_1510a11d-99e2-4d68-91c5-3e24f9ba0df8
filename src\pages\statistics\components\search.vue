<template>
  <div>
    <div class="search-column__item" style="width: 250px;">
      <el-input v-model="keyword" placeholder="请输入关键字" class="input-with-select">
        <el-select slot="prepend" v-model="keyType" icon="el-icon-search">
          <el-option
            v-for="item in keyTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-input>
    </div>
    <!-- 身份选择器 -->
    <div class="search-column__item">
      <el-cascader
        v-model="condition.identityId"
        placeholder="请选择身份"
        :options="identityTree"
        collapse-tags
        :show-all-levels="false"
        :props="{
          value:'identityId',
          label:'name',
          children:'childList',
          emitPath: false,
          checkStrictly: true
        }"
        clearable
        @change="identityChange"
      />
    </div>
    <!-- 专科选择器 -->
    <div class="search-column__item">
      <el-cascader
        ref="majorCascader"
        v-model="condition.majorId"
        placeholder="请选择专科"
        :options="majorList"
        collapse-tags
        :props="{
          value:'majorId',
          label:'name',
          children:'childList',
          emitPath: false,
          checkStrictly: true
        }"
        clearable
        @change="majorChange"
      />
    </div>
    <!-- 职称选择器 -->
    <div class="search-column__item">
      <el-cascader
        v-model="condition.academicId"
        placeholder="请选择职称"
        :options="academicList"
        collapse-tags
        :props="{
          value:'academicId',
          label:'name',
          children:'childList',
          emitPath: false,
          checkStrictly: true
        }"
        clearable
      />
    </div>
    <!-- 区域选择器 -->
    <div class="search-column__item">
      <el-cascader
        ref="areaCascader"
        v-model="condition.areaId"
        placeholder="请选择区域"
        :options="areaList"
        collapse-tags
        :show-all-levels="false"
        :props="{
          value:'areaId',
          label:'name',
          children:'childList',
          checkStrictly: true,
          emitPath:false
        }"
        clearable
        @change="areaChange"
      />
    </div>
    <div class="search-column__item">
      <el-date-picker
        v-if="timeSelect"
        v-model="time"
        value-format="yyyy-MM-dd HH:mm:ss"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </div>
    <div class="search-column__item">
      <el-button type="primary" @click="init()">查询</el-button>
      <el-button @click="clear()">重置</el-button>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { identityTreeList, majorTreeList } from '@/api/category' // 身份树，随身份联动的专科树
import { getAreaTree } from '@/api/area' // 区域树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树

export default {
  props: {
    timeSelect: {
      type: Boolean,
      default: false
    },
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      condition: {
        areaLevel: 4,
        majorLevel: null,
        identityId: '',
        majorId: '',
        academicId: '',
        areaId: '',
        startTime: '',
        endTime: ''
      },
      time: [],
      keyword: '',
      keyType: 'realName',
      keyTypeOptions: [
        { label: '姓名', value: 'realName' },
        { label: '手机', value: 'phone' },
        { label: '单位', value: 'orgName' }
      ],
      areaList: [],
      areaId: [],
      identityTree: [],
      majorList: [],
      academicList: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ]
    }
  },
  watch: {
    keyword(v) {
      this.condition[this.keyType] = v
    },
    keyType() {
      this.condition.realName = ''
      this.condition.phone = ''
      this.condition.orgName = ''
      this.keyword = ''
    },
    time(v) {
      this.condition.startTime = v[0]
      this.condition.endTime = moment(v[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }
  },
  created() {
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    // 获取专科树
    treeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', majorId: '0' })
      this.majorList = newArr
      this.condition.majorId = this.query.majorId
    })
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      res.unshift({ name: '全国', areaId: '0', level: 4 })
      this.areaList = this.clearNullChildList(res, 'childList')
      this.condition.areaId = this.query.areaId
    })
  },
  methods: {
    init() {
      this.$emit('search', this.condition)
    },
    clear() {
      this.condition.identityId = ''
      this.condition.majorId = ''
      this.condition.academicId = ''
      this.majorList = []
      this.academicList = []
      this.condition.areaId = ''
      this.condition.startTime = ''
      this.condition.endTime = ''
      this.condition.realName = ''
      this.condition.phone = ''
      this.condition.orgName = ''
      this.$emit('clear', this.condition)
    },
    areaChange() {
      this.condition.areaLevel = this.$refs.areaCascader.getCheckedNodes()[0].data.level
    },
    majorChange() {
      this.condition.majorLevel = this.$refs.majorCascader.getCheckedNodes()[0].data.level
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      this.condition.majorId = ''
      this.condition.academicId = ''
      if (e === '0' || e === null) {
        // 身份选'无'，专科职称也为无
        treeList().then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: '0' })
          this.majorList = newArr
        })
        this.academicList = [
          { academicId: 1004, name: '正高级' },
          { academicId: 1003, name: '副高级' },
          { academicId: 1002, name: '中级' },
          { academicId: 1001, name: '初级' },
          { academicId: 0, name: '无' }
        ]
        this.condition.majorId = ''
        this.condition.academicId = ''
        return
      } else {
        majorTreeList({ identityIds: [e] }).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: 0 })
          this.majorList = newArr
        })
        academicTreeListById(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: 0 })
          this.academicList = newArr
        })
      }
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.search-column__item {
    .el-input {
      width: 250px;
      .el-select {
        width: 80px;
      }
    }
    ::v-deep .el-autocomplete-suggestion{
      width: auto !important;
    }
    &:nth-last-child(1) {
      margin-left: 70px;
    }
    &:nth-last-child(2) {
      width: 250px;
      .el-date-editor {
        width: 250px;
      }
    }
}
</style>
