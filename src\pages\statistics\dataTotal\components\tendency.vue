<template>
  <div class="tendency" style="cursor: pointer" @click="skip">
    <h2>用户活跃趋势</h2>
    <div id="chart" class="chart" />
  </div>
</template>

<script>
var echarts = require('echarts/lib/echarts')
require('echarts/lib/chart/line')
require('echarts/lib/chart/bar')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/legend')
import { GridComponent, ToolboxComponent } from 'echarts/components'
echarts.use([GridComponent, ToolboxComponent])

export default {
  name: 'Trend',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    explain: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(v) {
        this.myChart.dispose()
        this.myChart = echarts.init(document.getElementById('chart'))
        this.setOptions(v)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(document.getElementById('chart'))
      this.setOptions(this.data)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    })
  },
  methods: {
    skip() {
      this.$router.push({
        name: 'StatisticsActive'
      })
    },
    setOptions(data) {
      this.myChart.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: '{b0}<br /> {a0}:{c0}<br />{a1}: {c1}%'
        },
        legend: {
          bottom: -4,
          itemWidth: 25,
          itemHeight: 10,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          data: ['活跃数', '活跃率']
        },
        color: ['#ff9', '#e1f415'],
        grid: {
          top: '14%',
          left: '3%',
          right: '5%',
          bottom: '6%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#606266'
            }
          },
          axisLabel: {
            margin: 17,
            color: '#fff',
            fontSize: 9,
            rotate: -45
          },
          boundaryGap: true,
          alignWithLabel: true,
          data: data.date
        },
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#606266'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            }
          },
          {
            type: 'value',
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: '#606266'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
            axisLabel: {
              formatter: '{value}%',
              align: 'left'
            }
          }
        ],
        series: [
          {
            name: '活跃数',
            yAxisIndex: 0,
            type: 'bar',
            symbol: 'circle',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0.2, 0.7, 1, [
                { offset: 1, color: '#096cf2' },
                { offset: 0.6, color: '#3183e9' },
                { offset: 0.4, color: '#1f81f9' },
                { offset: 0, color: '#75f4db' }
              ])
            },
            data: data.count || []
          },
          {
            name: '活跃率',
            yAxisIndex: 1,
            type: 'line',
            symbol: 'circle',
            data: data.ratio || []
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.myChart) {
        return
      }
      this.myChart.dispose()
      this.myChart = null
    }
  }
}
</script>

<style scoped lang="scss">
.tendency {
  h2 {
    margin-bottom: 0;
    font-size: 24px
  }
  .chart {
    height: 290px;
  }
}
</style>
