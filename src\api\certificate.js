import request from '@/utils/request'

// 证书列表
export function certExamineList(params) {
  return request({
    url: '/certExamine/pageList',
    method: 'post',
    data: params
  })
}

// 证书操作
export function certExamineOperate(params) {
  return request({
    url: `/certExamine/operate`,
    method: 'put',
    params
  })
}

// 证书创建
export function certExamineCreate(params) {
  return request({
    url: '/certExamine/create',
    method: 'post',
    data: params
  })
}

// 证书编辑
export function certExamineEdit(params) {
  return request({
    url: '/certExamine/edit',
    method: 'post',
    data: params
  })
}

// 证书详情
export function certExamineDetail(id) {
  return request({
    url: `/certExamine/detail/${id}`,
    method: 'get'
  })
}

// 证书列表
export function certSelectList() {
  return request({
    url: '/certExamine/certSelectList',
    method: 'get'
  })
}

// 学员列表
export function studentPageList(params) {
  return request({
    url: '/certExamine/studentPageList',
    method: 'post',
    data: params
  })
}

// 证书列表
export function userDetail(params) {
  return request({
    url: '/user/userDetail',
    method: 'get',
    params
  })
}

// 证书培训-考核详情-目录列表
export function directoryList(params) {
  return request({
    url: '/certExamine/examineDetail/directoryList',
    method: 'get',
    params
  })
}
// 证书培训-考核详情-教程列表
export function courseList(params) {
  return request({
    url: '/certExamine/examineDetail/courseList',
    method: 'get',
    params
  })
}
// 证书培训-考核详情-教程章节列表
export function chapterList(params) {
  return request({
    url: '/certExamine/examineDetail/chapterList',
    method: 'get',
    params
  })
}

// 证书培训-考核详情-教程章节列表
export function answerRecordList(params) {
  return request({
    url: '/certExamine/examineDetail/answerRecordList',
    method: 'get',
    params
  })
}

// 申领证书列表
export function applyPageList(params) {
  return request({
    url: '/certExamineApply/pageList',
    method: 'post',
    data: params
  })
}

// 证书寄件
export function sending(params) {
  return request({
    url: `/certExamineApply/sending`,
    method: 'put',
    params
  })
}
