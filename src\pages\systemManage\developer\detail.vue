<template>
  <div class="app-container">
    <el-form ref="ruleForm" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="选择单位：" prop="company">
        <el-input v-model="form.orgName" placeholder="请点击" clearable @focus="handleFocus" />
      </el-form-item>
      <el-form-item label="选择接入类型：" required>
        <el-select v-model="form.company" placeholder="请选择接入类型">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="AppID：" required>
        <div class="text">
          <i class="el-icon-warning-outline" />保存后自动生成AppID，用于对接调用
        </div>
      </el-form-item>
      <el-form-item label="秘钥：" required>
        <div class="text">
          <i class="el-icon-warning-outline" />保存后自动生成秘钥，用于对接调用
        </div>
      </el-form-item>
      <el-form-item label="关联管理员：" required="">
        <div class="text">
          <i class="el-icon-warning-outline" />展示单位管理员列表，选择对1个或多个管理员进行关联，填写“关联唯一标识”用于对接第三方用户
        </div>
        <a-table ref="multipleTable" :columns="columns" fit :data="list" border stripe>
          <template slot="actions" slot-scope="{row}">
            <el-input v-model="form.clientOpenid" :disabled="disabled" @blur="handleBlur(row)" />
          </template>
        </a-table>
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm('ruleForm')">取消</el-button>
        <el-button type="primary" :disabled="disabled" @click="submitForm('ruleForm')">保存</el-button>
      </el-form-item>
    </el-form>

    <!-- 单位弹窗 -->
    <div>
      <searchOrg :show="showSearchDialog" @confirm="handleConfirm" @close="close" />
    </div>
  </div>
</template>
<script>
import { checkRelation } from '@/api/developer'
import { getOrganAdminListByOrgId } from '@/api/userManage'
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件
import searchOrg from './components/searchOrg.vue'

const columns = [
  { props: { label: '手机号', align: 'center', prop: 'phone' }},
  { props: { label: '姓名', align: 'center', prop: 'realName' }},
  { props: { label: '用户账号', align: 'center', prop: 'userAccounts' }},
  { props: { label: '员工账号', align: 'center', prop: 'username' }},
  {
    props: { align: 'center', label: '操作', width: '130' },
    slot: 'actions'
  }
]

export default {
  name: 'DeveloperDetail',
  components: {
    ATable,
    searchOrg
  },
  data() {
    return {
      columns,
      disabled: false,
      options: [
        { value: 1, name: 'SDK' }
      ],
      form: {
        orgName: '',
        orgId: '',
        name: '',
        clientOpenid: ''
      },
      rules: {
        company: [{ required: true, message: '请选择单位', trigger: 'blur' }],
        name: [
          { required: true, message: '请输入管理员唯一标识', trigger: 'blur' }
        ]
      },
      list: [],
      showSearchDialog: false
    }
  },
  created() {
    this.setManager(this.form.orgId)
  },
  methods: {
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$router.go(-1)
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.request
            .updateMedia(this.form)
            .then(() => {
              this.$message.success('操作成功')
              this.$router.go(-1)
            })
            .catch(err => {
              this.$message.error(err)
            })
        } else {
          return false
        }
      })
    },
    setManager(id) {
      getOrganAdminListByOrgId(id).then(res => {
        this.list = res.records
        this.dialogGetManagerShow = true
      })
    },
    handleFocus() {
      this.showSearchDialog = true
    },
    handleBlur(row) {
      console.log(row)
      checkRelation(row.orgId).then(res => {

      }).catch(err => {
        this.$message.error(err)
      })
    },
    handleConfirm(val) {
      this.form.orgName = val.orgName
      this.form.orgId = val.orgId
      this.showSearchDialog = false
      this.setManager(this.form.orgId)
    },
    close(val) {
      console.log(val)
      this.showSearchDialog = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  width: 920px;
  margin: 0 auto;

  .text {
    color: #a9a9a9;
  }
}
</style>
