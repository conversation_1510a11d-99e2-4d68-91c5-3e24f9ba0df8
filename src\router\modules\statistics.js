import Layout from '@/layout'

const statisticsRouter = {
  path: '/statistics',
  component: Layout,
  name: 'Statistics',
  redirect: { name: 'trainIndex' },
  meta: { title: '统计分析', icon: 'peoples' },
  children: [
    {
      path: 'dataTotal',
      component: () => import('@/pages/statistics/dataTotal/index'),
      name: 'StatisticsDataTotal',
      meta: {
        title: '数据总览',
        target: '_blank'
      }
    },
    {
      path: 'user',
      component: () => import('@/pages/statistics/user/index'),
      name: 'StatisticsUser',
      meta: {
        title: '用户分析'
      }
    },
    {
      path: 'user/detail',
      component: () => import('@/pages/statistics/user/detail.vue'),
      name: 'StatisticsUserDetail',
      meta: {
        title: '用户明细'
      }
    },
    {
      path: 'active',
      component: () => import('@/pages/statistics/active/index'),
      name: 'StatisticsActive',
      meta: {
        title: '活跃分析'
      }
    },
    {
      path: 'active/detail',
      component: () => import('@/pages/statistics/active/detail.vue'),
      name: 'StatisticsActiveDetail',
      meta: {
        title: '活跃明细'
      }
    },
    {
      path: 'course',
      component: () => import('@/pages/statistics/course/index'),
      name: 'StatisticsCourse',
      meta: {
        title: '课程学习分析'
      },
      children: [
        {
          path: 'detail',
          name: 'StatisticsCourseDetail',
          component: () => import('@/pages/statistics/course/detail.vue'),
          meta: { title: '学习详情' }
        }
      ]
    },
    {
      path: '/trainStatistics',
      name: 'DataStatistics',
      component: { render: e => e('router-view') },
      alwaysShow: true,
      meta: { title: '培训统计' },
      children: [
        {
          path: 'statisticsCase',
          component: () =>
            import('@/pages/statistics/trainStatistics/statisticsCase/index'),
          name: 'StatisticsCase',
          meta: {
            title: '统计概况'
          }
        },
        {
          path: 'staticRegionDetail',
          component: () =>
            import('@/pages/statistics/trainStatistics/regionDetail'),
          name: 'RegionDetail',
          meta: {
            title: '区域详情'
          }
        },
        {
          path: 'staticIdentityDetail',
          component: () =>
            import('@/pages/statistics/trainStatistics/identityDetail'),
          name: 'IdentityDetail',
          meta: { title: '身份详情' }
        },
        {
          path: 'staticMajorDetail',
          component: () =>
            import('@/pages/statistics/trainStatistics/majorDetail'),
          name: 'MajorDetail',
          meta: { title: '专科详情' }
        },
        {
          path: 'staticAcademicDetail',
          component: () =>
            import('@/pages/statistics/trainStatistics/academicDetail'),
          name: 'AcademicDetail',
          meta: { title: '职称详情' }
        },
        {
          path: 'staticPersonDetail',
          component: () =>
            import('@/pages/statistics/trainStatistics/personDetail'),
          name: 'PersonDetail',
          meta: { title: '人员详情' }
        },
        {
          path: 'statisticsParams',
          component: () =>
            import('@/pages/statistics/trainStatistics/statisticsParams'),
          name: 'StatisticsParams',
          meta: { title: '统计参数' }
        }
      ]
    },
    {
      path: 'dataParams',
      component: () => import('@/pages/statistics/dataParams/index'),
      name: 'StatisticsDataParams',
      meta: {
        title: '数据参数'
      }
    }
  ]
}

export default statisticsRouter
