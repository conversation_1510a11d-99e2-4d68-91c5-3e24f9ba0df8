<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    :show="show"
    width="800px"
    @close="close()"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item prop="name" label="热门词">
        <el-input v-model="form.name" maxlength="10" />
      </el-form-item>
      <el-form-item prop="listOrder" label="排序">
        <el-input v-model="form.listOrder" max="9999999" type="number" min="0" />
      </el-form-item>
      <el-form-item prop="phone" label="状态">
        <el-radio v-model="form.state" :label="0">有效</el-radio>
        <el-radio v-model="form.state" :label="1">无效</el-radio>
      </el-form-item>
    </el-form>

    <span slot="footer">
      <el-button @click="handleCloseDialog">取 消</el-button>
      <el-button type="primary" @click="handleSaveForm">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>

export default {
  name: 'DialogUserEdit',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增热门'
    },
    data: {
      type: Object,
      default: () => {
        return {
          name: '',
          listOrder: '',
          state: 0
        }
      }
    }
  },
  data() {
    return {
      rules: {
        name: [
          { required: true, message: '请输入热词', trigger: 'blur' },
          { min: 2, max: 10, message: '名称长度应该在2到10个字符之间', trigger: 'blur' }
        ]
      },
      visible: this.show,
      form: this.data,
      // 职称相关
      academicId: '',
      academicList: [],
      // 科室相关
      majorId: '',
      majorList: []
    }
  },
  watch: {
    show() {
      this.visible = this.show
    },
    data: {
      handler(val) {
        this.form = val
      },
      deep: true
    }
  },
  created() {
  },
  methods: {
    handleCloseDialog() {
      this.$emit('update:show', false)
      this.$emit('handleCancel')
    },
    close() {
      this.$emit('update:show', false)
    },
    handleSaveForm() {
      if (this.form.listOrder > 9999999) {
        this.form.listOrder = 9999999
      }
      if (this.form.listOrder < 0) {
        this.form.listOrder = 0
      }
      this.$emit('handleSave', this.form)
    }
  }
}
</script>

<style>

</style>
