import request from '@/utils/request'

// 内容列表
export function contentList(params) {
  return request({
    url: '/promoteContent/getPageList',
    method: 'post',
    data: params
  })
}

// 内容删除
export function contentDel(params) {
  return request({
    url: `/promoteContent/deleteById?id=${params}`,
    method: 'post'
  })
}

// 内容上下架
export function contentUpOrDown(params) {
  return request({
    url: '/promoteContent/upOrDown',
    method: 'post',
    data: params
  })
}

// 内容新增
export function saveOrUpdate(params) {
  return request({
    url: '/promoteContent/saveOrUpdate',
    method: 'post',
    data: params
  })
}

// 内容详情
export function contentDetail(id) {
  return request({
    url: `/promoteContent/detail/${id}`,
    method: 'get'
  })
}

// 抖喇内容管理
// 内容列表
export function contentListDoula(params) {
  return request({
    url: '/promoteDoulaContent/getPageList',
    method: 'post',
    data: params
  })
}

// 内容删除
export function contentDelDoula(params) {
  return request({
    url: `/promoteDoulaContent/deleteById?id=${params}`,
    method: 'post'
  })
}

// 内容上下架
export function contentUpOrDownDoula(params) {
  return request({
    url: '/promoteDoulaContent/upOrDown',
    method: 'post',
    data: params
  })
}

// 内容新增
export function saveOrUpdateDoula(params) {
  return request({
    url: '/promoteDoulaContent/saveOrUpdate',
    method: 'post',
    data: params
  })
}

// 内容详情
export function contentDetailDoula(id) {
  return request({
    url: `/promoteDoulaContent/detail/${id}`,
    method: 'get'
  })
}

// 会议素材列表
export function meetingRecordList(params) {
  return request({
    url: '/meetingRecord/pageList',
    method: 'post',
    data: params
  })
}

// 会议素材列表导出
export function meetingRecordExport(params) {
  return request({
    url: '/meetingRecord/export',
    method: 'post',
    data: params
  })
}

// 会议素材删除
export function meetingRecordDel(params) {
  return request({
    url: '/meetingRecord/delete',
    method: 'post',
    params
  })
}

// 会议素材详情
export function meetingRecordDetail(params) {
  return request({
    url: '/meetingRecord/detail',
    method: 'post',
    params
  })
}

// 创建/编辑会议素材
export function addOrModify(params) {
  return request({
    url: '/meetingRecord/addOrModify',
    method: 'post',
    data: params
  })
}
