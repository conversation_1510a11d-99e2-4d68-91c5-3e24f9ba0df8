<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item" style="padding-left:50px;width:350px;">
        <el-input v-model="keyword" placeholder="请输入关键字" maxlength="50" clearable @keydown.enter.native="search" @clear="search" />
      </div>
      <div class="search-column__item">
        <el-button size="medium" type="primary" @click="search">查询</el-button>
      </div>
    </div>
    <div style="margin-left:50px;">
      <CategoryList
        v-for="index in 4"
        :key="index"
        :level="index"
        :list="dataList(index)"
        @selectCategory="selectCategory"
        @getList="getList"
      />
    </div>
  </div>
</template>

<script>
import CategoryList from './components/CategoryList'

import { cateList } from '@/api/resourceCategory'

export default {
  name: 'CategoryIndex',
  components: { CategoryList },
  data() {
    return {
      category1: [],
      category2: [],
      category3: [],
      category4: [],
      keyword: '',
      query: {
        parentId: 0,
        keyword: ''
      }
    }
  },
  computed: {
    dataList() {
      return index => this[`category${index}`]
    }
  },
  created() {
    this.getCategory()
  },
  methods: {
    search() {
      this.query.parentId = 0
      this.query.keyword = this.keyword
      this.category1 = []
      this.category2 = []
      this.category3 = []
      this.category4 = []
      this.getCategory()
    },
    getCategory() {
      cateList(this.query).then(res => {
        this.category1 = res
      })
    },
    selectCategory(havChild, id, level) {
      this.query.parentId = id
      if (level === 1) {
        this.category2 = []
        this.category3 = []
        this.category4 = []
        if (havChild) {
          cateList(this.query).then(res => {
            this.category2 = res
          })
        }
      } else if (level === 2) {
        this.category3 = []
        this.category4 = []
        if (havChild) {
          cateList(this.query).then(res => {
            this.category3 = res
          })
        }
      } else if (level === 3) {
        this.category4 = []
        if (havChild) {
          cateList(this.query).then(res => {
            this.category4 = res
          })
        }
      }
    },
    getList(pid, level) {
      if (level === 1) {
        this.category2 = []
        this.category3 = []
        this.category4 = []
      } else if (level === 2) {
        this.category3 = []
        this.category4 = []
      } else if (level === 3) {
        this.category4 = []
      }
      this.query.parentId = pid
      cateList(this.query).then(res => {
        this[`category${level}`] = res
      })
    }
  }
}
</script>

