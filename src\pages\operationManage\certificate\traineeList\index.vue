<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        class="group"
        placeholder="姓名/手机"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>

      <el-select
        v-model="listQuery.condition.certId"
        placeholder="全部证书"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in certOptions"
          :key="item.certId"
          :label="item.certName"
          :value="item.certId"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.examineStatus"
        placeholder="考核结果"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      学员列表
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="lookInfo(row.userId)"
            >查看信息</el-button>
            <el-button
              type="text"
              size="mini"
              @click="toDetail(row)"
            >考核详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <InfoDetail :dialog-visible.sync="infoDialogVisible" :user-id="userId" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import InfoDetail from '@/pages/operationManage/certificate/components/infoDetail'
import { studentPageList, certSelectList } from '@/api/certificate'
export default {
  name: 'CertificateTraineeList',
  components: {
    Pagination,
    InfoDetail
  },
  data() {
    return {
      certOptions: [],
      stateOptions: [
        { label: '未参加', value: 0 },
        { label: '考核中', value: 3 },
        { label: '已通过', value: 1 },
        { label: '不通过', value: 2 }
      ],
      listQuery: {
        condition: {
          keyword: '',
          certId: this.$route.query.id || '',
          examineStatus: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'userName', label: '学员姓名', width: '40' },
        { prop: 'userPhone', label: '手机', width: '40' },
        { prop: 'certName', label: '证书名称', width: '70' },
        { prop: 'examineResult', label: '考核结果', width: '30' },
        { prop: 'progress', label: '考核进度', width: '30' },
        { prop: 'avgAccuracy', label: '平均正确率', width: '40' },
        { prop: 'lastCreateTimeStr', label: '最后考核完成时间', width: '50' },
        { prop: 'getCert', label: '是否获得证书', width: '40' },
        { prop: 'createTimeStr', label: '报名时间', width: '50' }
      ],
      tableData: [],
      infoDialogVisible: false,
      userId: ''
    }
  },
  mounted() {
    certSelectList().then(res => {
      this.certOptions = res
    })
    this.getStudentPageList()
  },
  methods: {
    toDetail(row) {
      this.$router.push({
        name: 'CertificateTraineeExamineDetail',
        query: {
          id: row.id,
          name: row.userName,
          phone: row.userPhone
        }
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getStudentPageList()
    },
    getStudentPageList() {
      studentPageList(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getStudentPageList()
    },
    lookInfo(id) {
      this.infoDialogVisible = true
      this.userId = id
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding-top: 25px;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
