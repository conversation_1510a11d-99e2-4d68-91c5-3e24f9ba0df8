<template>
  <div class="app-container">
    <div class="search-column">
      <el-button type="primary" @click="">首页顶部banner</el-button>
      <el-button type="primary" @click="">无忧学院</el-button>
      <el-button type="primary" @click="">首页视频推荐</el-button>
      <el-button type="primary" @click="">个人中心底部推荐</el-button>
      <el-button type="primary" @click="">启动页大图</el-button>
    </div>
    <el-table :data="tableList" border stripe>
      <el-table-column
        v-for="col in columns"
        :key="col.id"
        :prop="col.id"
        :label="col.label"
        :width="col.width"
      />
    </el-table>

  </div>
</template>

<script>
import { getBannerList } from '@/api/appBanner'
export default {
  data() {
    return {
      tableQuery: {
        condition: {
          id: 1
        },
        orderBys: [
          {
            asc: true,
            column: ''
          }
        ],
        pager: {
          page: 0,
          pageSize: 15
        }
      },
      tableList: [],
      toatal: 0
    }
  },
  created() {
    this.getBannerList()
  },
  methods: {
    getBannerList() {
      getBannerList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.toatal = res.toatal
      })
    }
  }
}
</script>

<style>

</style>
