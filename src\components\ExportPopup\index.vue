<template>
  <el-dialog
    :visible.sync="centerDialogVisible"
    width="400px"
    center
    class="export"
  >
    <div class="export-icon"><i class="el-icon-success" /> 导出任务已执行</div>
    <template v-if="status != 0">
      <p v-if="status != 0" class="export-line">请下载导出数据文件</p>
      <p class="export-line">导出的数据文件，可在“<span class="el-button--text" @click="$router.push('/export')">导出结果</span>”栏中查询。</p>
    </template>
    <p v-else class="export-line">正在导出的数据文件，请稍后在“<span class="el-button--text" @click="$router.push('/export')">导出结果</span>”栏中查询。</p>
    <span slot="footer" class="dialog-footer">
      <el-button v-if="status != 0" type="primary" @click="download">下载文件</el-button>
      <el-button v-else type="primary" @click="centerDialogVisible = false">知道了</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getExportStatus } from '@/api/export'

export default {
  name: 'ExportPopup',
  props: {
    eid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      centerDialogVisible: false,
      timeout: null,
      fileUrls: [],
      status: 0
    }
  },
  watch: {
    eid(v) {
      this.centerDialogVisible = !!v
      this.status = 0
      if (this.centerDialogVisible) {
        this.getStatus()
      }
    },
    centerDialogVisible(v) {
      if (!v) {
        this.$emit('update:eid', '')
      }
    }
  },
  beforeDestroy() {
    clearTimeout(this.timeout)
  },
  methods: {
    getStatus() {
      clearTimeout(this.timeout)
      this.timeout = setTimeout(res => {
        if (this.centerDialogVisible) {
          getExportStatus(this.eid).then(res => {
            if (this.centerDialogVisible) {
              this.fileUrls = res.fileUrls
              this.status = res.status
              if (res.status === 0) {
                this.getStatus()
              }
            }
          }, () => {
            if (this.centerDialogVisible) {
              this.getStatus()
            }
          })
        }
      }, 2000)
    },
    download() {
      this.fileUrls.forEach(link => {
        this.centerDialogVisible = false
        window.open(link)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.export {
  ::v-deep {
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      padding-bottom: 0;
    }
  }
  .export-icon {
    text-align: center;
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 20px;
    .el-icon-success {
      font-size: 40px;
      color: #44b9a2;
      vertical-align: top;
    }
  }
  .export-line {
    color: #999999;
      padding-bottom: 8px;
  }
}
.el-button--text {
  cursor: pointer;
}
</style>
