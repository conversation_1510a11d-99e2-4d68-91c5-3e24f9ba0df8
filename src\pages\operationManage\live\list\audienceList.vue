<template>
  <div>
    <div class="search-column" style="display:flex">
      <div class="search-column__item" style="margin:0 10px 0 20px">
        <el-input v-model="queryWord" placeholder="请输入搜索关键字" clearable>
          <el-select slot="prepend" v-model="queryType" style="width: 120px">
            <el-option v-for="item in queryTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item" style="margin-right:10px">
        <el-select v-model="watchFrom" placeholder="请选择观众来源">
          <el-option label="推广" :value="2" />
          <el-option label="自来" :value="1" />
          <el-option label="分享" :value="3" />
        </el-select>
      </div>
      <Search @search="search" @clear="clear" />
      <div class="search-column__item" style="margin-left:10px;">
        <el-button type="primary" @click="exportFile()">导出</el-button>
      </div>
    </div>
    <br>

    <el-row style="margin-left:20px">
      <el-table :data="tableList" border stripe>
        <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
          <template slot-scope="scope">
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="120">
          <template slot-scope="{row}">
            <el-button type="text" @click="viewPersonal(row.userId)">人员信息</el-button>
            <el-button type="text" @click="lookDetail(row.userId)">观看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>

    <Pagination class="pag" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <DialogUserInfo :title="'人员信息'" :show.sync="dialogInfoVisible" :data="userInfoData" :is-append="true" />

    <LookDetail :visible.sync="lookDetailVisible" :people-detail="peopleDetail" :watch-detail="watchDetail" :comment-detail="commentDetail" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getLiveAudience, getLiveWatchDetail, exportLiveUser } from '@/api/liveManage'
import { getPersonalDetail } from '@/api/userManage'

import DialogUserInfo from '@/pages/user/compontents/dialogUserInfo.vue'
import Search from '@/pages/operationManage/live/components/search_1.vue'
import LookDetail from './lookDetail.vue'

export default {
  components: { Pagination, Search, DialogUserInfo, LookDetail },
  props: {
    liveId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryWord: '',
      queryType: 1,
      queryTypeList: [
        { label: '个人账户', value: 1 },
        { label: '手机', value: 2 },
        { label: '用户姓名', value: 3 },
        { label: '员工账号', value: 4 },
        { label: '员工姓名', value: 5 }
      ],
      watchFrom: null,
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: '观众来源', align: 'center', prop: 'watchFrom' },
        { id: 1, label: '观众姓名', align: 'center', prop: 'realName' },
        { id: 2, label: '个人账号', align: 'center', prop: 'userName' },
        { id: 3, label: '手机', align: 'center', prop: 'phone' },
        { id: 4, label: '身份', align: 'center', prop: 'identity' },
        { id: 5, label: '专科', align: 'center', prop: 'majorName' },
        { id: 6, label: '职称', align: 'center', prop: 'academicName' },
        { id: 7, label: '区域', align: 'center', prop: 'areaName' },
        { id: 8, label: '首次进入观看时间', align: 'center', prop: 'firstEnterTime', width: '160px' },
        { id: 9, label: '累计观看时长', align: 'center', prop: 'totalDuration' },
        { id: 10, label: '评论数', align: 'center', prop: 'comments' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      dialogInfoVisible: false,
      userInfoData: {},
      peopleDetail: [],
      watchDetail: [],
      commentDetail: [],
      lookDetailVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search(condition) {
      if (condition) {
        this.tableQuery.condition = condition
      }
      this.tableQuery.condition.queryType = this.queryType
      this.tableQuery.condition.queryWord = this.queryWord
      this.tableQuery.condition.watchFrom = this.watchFrom
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.condition.queryWord = this.queryWord = ''
      this.tableQuery.condition.watchFrom = this.watchFrom = null
      this.tableQuery.pager.page = 1
      this.getList()
    },
    getList() {
      this.tableQuery.condition.liveId = this.liveId
      getLiveAudience(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    viewPersonal(userId) {
      getPersonalDetail({ userId: userId }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
        this.lookDetailVisible = false
      })
    },
    lookDetail(userId) {
      getLiveWatchDetail(this.liveId, userId).then(res => {
        this.peopleDetail.length = 0
        this.peopleDetail.push(res)
        this.watchDetail = res.watchDetailResponseDtos
        this.commentDetail = res.commentResponseDtos
        this.lookDetailVisible = true
        this.dialogInfoVisible = false
      })
    },
    exportFile() {
      exportLiveUser(this.liveId).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pag{
  padding-top: 0;
  padding-bottom: 0;
}
</style>
