<template>
  <div class="app-container">
    <!-- header -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.name" placeholder="部门库名称" @change="onInput">
          <i slot="suffix" class="el-input__icon el-icon-search" @click="onInput" />
        </el-input>
      </div>
      <div class="fr">
        <el-button type="primary" @click="addDept">添加部门库</el-button>
      </div>
    </div>
    <!-- body -->
    <el-table fit :data="deptList" border stripe>
      <el-table-column v-for="col in tableCols" :key="col.id" :prop="col.prop" :label="col.label" align="center" />
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import { deptList, delDept } from '@/api/dept'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission'

export default {
  name: 'DeptIndex',
  components: { Pagination },
  directives: { permission },
  data() {
    return {
      // search params
      tableQuery: {
        condition: {
          name: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // table field
      tableCols: [
        { label: '部门库名称', prop: 'name' },
        { label: '部门关键字', prop: 'packKeyword' },
        { label: '创建人', prop: 'createUserName' },
        { label: '创建时间', prop: 'createTime' }
      ],
      // table list
      deptList: [],
      // pagination
      total: 0,
      layout: 'total, prev, pager, next, jumper'
    }
  },
  created() {
    this.getDeptList()
  },
  methods: {
    onInput() {
      this.tableQuery.pager.page = 1
      this.getDeptList()
    },
    addDept() {
      this.$router.push('addDept')
    },
    // get dept list
    getDeptList() {
      deptList(this.tableQuery).then(res => {
        this.deptList = res.records
        this.total = res.total
      })
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getDeptList()
    },
    // delete row
    deleteRow(row) {
      this.$confirm(`是否删除 "` + row.packKeyword + '" 这个部门库？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDept({ depaPackId: row.departmentPackId }).then(() => {
          this.$message.success(`删除成功`)
          this.getDeptList()
        })
      })
    },
    // edit row
    editRow(row) {
      this.$router.push({
        path: 'addDept',
        query: {
          detail: row,
          isEdit: true
        }
      })
    }
  }
}
</script>
