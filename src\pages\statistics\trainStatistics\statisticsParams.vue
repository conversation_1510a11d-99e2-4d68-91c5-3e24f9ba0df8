<template>
  <!-- 统计参数 -->
  <div class="app-container">
    <div class="top">
      <div><i class="el-icon-warning-outline" />
        按区域设置每个数据指标的倍数，设置后，全国数据按全国倍数扩充，省份及其所有下级区域数据按省倍数扩充
      </div>
      <div>
        <span>数据扩充</span>
        <el-switch
          v-model="status"
          inactive-text=""
          active-color="#409EFF"
          @change="handleSwitchChange"
        />
      </div>
    </div>

    <!-- table -->
    <vTable :columns="column" :data-ary="dataAry" :has-index="true" style="margin-bottom:20px">
      <div slot="handleColumn">
        <el-table-column
          fixed="right"
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleClick(scope.row)">设置倍数</el-button>
          </template>
        </el-table-column>
      </div>
    </vTable>

    <!-- 设置倍数 弹窗 -->
    <multiple :visible.sync="multipleVisible" :multiple-id="multipleId" @close="close" />
  </div>
</template>

<script>
import vTable from './components/vTable.vue'
import multiple from './components/multipleEdit.vue'

import {
  list,
  enlargeStatus,
  editStatus
} from '@/api/dataStatistics/statisticsParams'

// 设置表头信息 hasSort：是否排序 isShow：是否展示 label：表头标签 prop：属性名 align：表头对应方式(left/center/right)
const column = [
  { hasSort: false, isShow: true, label: '区域', align: 'center', prop: 'areaName' },
  { hasSort: false, isShow: true, label: '用户数', align: 'center', prop: 'staffAmount' },
  { hasSort: false, isShow: true, label: '活跃数', align: 'center', prop: 'activeAmount' },
  { hasSort: false, isShow: true, label: '培训人数', align: 'center', prop: 'trainingAmount' },
  { hasSort: false, isShow: true, label: '培训任务数', align: 'center', prop: 'trainingTaskAmount' },
  { hasSort: false, isShow: true, label: '培训人次', align: 'center', prop: 'receivedPersonNum' },
  { hasSort: false, isShow: true, label: '参与人次', align: 'center', prop: 'joinedPersonNum' },
  { hasSort: false, isShow: true, label: '通过人次', align: 'center', prop: 'passedPersonNum' },
  { hasSort: false, isShow: true, label: '培训时长', align: 'center', prop: 'examineTime' },
  { hasSort: false, isShow: true, label: '自学时长', align: 'center', prop: 'studyTime' }
]
export default {
  name: 'StatisticsParams',
  components: {
    vTable,
    multiple
  },
  data() {
    return {
      multipleVisible: false,
      status: false, // 滑块开关
      // 数据扩充请求参数
      switcher: {
        id: '',
        status: ''
      },
      column,
      dataAry: [], // 表格数据
      multipleId: ''
    }
  },
  created() {
    enlargeStatus().then(res => {
      this.switcher.id = res.id
      this.switcher.status = res.status
      this.status = Boolean(res.status)
      editStatus(this.switcher).then(res => {
        this.getList()
      })
    })
  },
  methods: {
    getList() {
      list().then(res => {
        this.dataAry = JSON.parse(JSON.stringify(res))
      })
    },
    // 表格操作
    handleClick({ multipleId }) {
      this.multipleVisible = true
      this.multipleId = multipleId
    },
    // 数据扩充
    handleSwitchChange(e) {
      this.switcher.status = e ? 1 : 0
      editStatus(this.switcher).then(res => {
        this.getList()
      })
    },
    close(e) {
      if (e && e !== '') {
        this.getList()
      }
      this.multipleVisible = false
    }
  }
}
</script>

<style>
.top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
