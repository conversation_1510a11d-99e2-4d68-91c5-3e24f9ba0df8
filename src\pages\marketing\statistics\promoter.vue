<template>
  <section class="promoters">
    <div v-if="!$route.path.includes('promoter/detail')">
      <h2 v-if="!isTotal">
        推广员列表
        <el-button type="primary" @click="exportList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="!isTotal ? tableData : tableData.slice(0,4) "
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column prop="promoters" label="推广员" width="360">
            <template slot-scope="scope">
              <span @click="toDetail(scope.row.userId)">{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
      </div>
      <Pagination v-if="!isTotal" :page="pager.page" :page-size="pager.pageSize" :total="total" @pagination="handlePagination" />
    </div>
    <router-view />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { userAnalysis, userAnalysisExport } from '@/api/marketing/promoteArticle'
import { deleteEmptyProperty } from '@/utils/index'
export default {
  components: {
    Pagination
  },
  props: {
    isTotal: {
      type: Boolean,
      default: false
    },
    searchParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableColumn: [
        { prop: 'serviceProvider', label: '服务商', width: '130' },
        { prop: 'orgName', label: '企业', width: '130' },
        { prop: 'productDesc', label: '关联产品', width: '360' },
        { prop: 'phone', label: '手机', width: '' },
        { prop: 'articleNum', label: '文章数', width: '' },
        { prop: 'videoNum', label: '视频数', width: '' },
        { prop: 'doulaNum', label: '抖喇数', width: '' },
        { prop: 'visitNum', label: '拜访数', width: '' },
        { prop: 'costWithUnit', label: '总费用', width: '' }
      ],
      tableData: [],
      total: 0,
      pager: {
        page: 1,
        pageSize: 10
      },
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          orgId: null,
          orgzIds: null,
          serviceProviderOrgId: null,
          productId: null,
          categoryId: null,
          keyword: null,
          userType: 'PROMOTER'
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.queryParams.pager.page = newVal.pager.page
        if (this.$route.path.includes('promoter/detail')) return
        this.getUserAnalysis()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toDetail(userId) {
      this.$router.push({
        name: 'MarketingStatisticsPromoterDetail',
        query: {
          userId
        }
      })
    },
    getUserAnalysis() {
      const param = JSON.parse(JSON.stringify(this.queryParams))
      deleteEmptyProperty(param)
      userAnalysis(param).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportList() {
      if (this.total <= 0) {
        this.$message.error('暂无可导出数据')
        return
      }
      const param = JSON.parse(JSON.stringify(this.queryParams.condition))
      deleteEmptyProperty(param)
      userAnalysisExport(param).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getUserAnalysis()
    }
  }
}
</script>

<style lang="scss" scoped>
.promoters {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  h2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
    .el-button {
      width: 80px;
      height: 35px;
    }
  }
  .table {
    padding: 25px 20px 0;
    .cell {
      span {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}
</style>
