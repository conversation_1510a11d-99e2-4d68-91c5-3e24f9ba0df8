<template>
  <div class="contain">
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="keywordName" filterable placeholder="请选择类型" style="width: 130px">
          <el-option v-for="item in keywordType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="根据左侧类型进行对应关键字查询" clearable style="width: 300px" @keyup.enter.native="search" @clear="search">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="search" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="areaIdArr"
          style="width: 300px"
          placeholder="请选择省市区"
          :options="areaList"
          :props="prop"
          clearable
          @change="search"
        />
      </div>
      <!-- <div class="search-column__item">
        <el-button type="primary" @click="search">查询</el-button>
      </div> -->
      <div class="search-column__item fr">
        <el-button type="primary" @click="exportRecord">导出</el-button>
      </div>
    </div>

    <!-- body -->
    <el-table :data="records" border stripe>
      <el-table-column align="center" label="用户ID" width="180px">
        <template slot-scope="{row}">{{ row.userId }}</template>
      </el-table-column>
      <el-table-column align="center" label="员工姓名" width="180px">
        <template slot-scope="{row}">{{ row.staffName }}</template>
      </el-table-column>
      <el-table-column align="center" label="员工账号" width="240px">
        <template slot-scope="{row}">{{ row.username }}</template>
      </el-table-column>
      <el-table-column align="center" label="所属单位" width="240px">
        <template slot-scope="{row}">{{ row.orgName }}</template>
      </el-table-column>
      <el-table-column align="center" label="所在地区" width="240px">
        <template slot-scope="{row}">{{ row.areaName }}</template>
      </el-table-column>
      <el-table-column align="center" label="考试名称" width="240px">
        <template slot-scope="{row}">{{ row.useRefName }}</template>
      </el-table-column>
      <el-table-column align="center" label="使用题目" width="150px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="detail(row)">{{ row.subjectNum }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="使用时间" width="240px">
        <template slot-scope="{row}">{{ row.usedTime }}</template>
      </el-table-column>
    </el-table>

    <!-- pagination -->
    <Pagination
      :total="total"
      :page="pager.page"
      @pagination="pagination"
    />
  </div>
</template>

<script>
import { exportRecord, getRecordList, getArea } from '@/api/questions'
import Pagination from '@/components/Pagination'
export default {
  components: { Pagination },
  data() {
    return {
      pager: { page: 1, pageSize: 10 },
      total: 0,
      keywordType: [
        { value: 'userId', name: '用户ID' },
        { value: 'staffName', name: '员工姓名' },
        { value: 'username', name: '员工账号' },
        { value: 'orgName', name: '单位名称' }
      ],
      keywordName: '',
      keyword: '',
      records: [],
      areaIdArr: [],
      areaList: [],
      prop: {
        label: 'name',
        value: 'areaId',
        children: 'childList',
        expandTrigger: 'hover'
      },
      condition: {
        areaId: '',
        orgName: '',
        staffName: '',
        userId: '',
        username: ''
      }
    }
  },
  created() {
    this.getList()
    getArea().then(res => {
      this.areaList = res
    })
  },
  methods: {
    // 获取列表
    getList() {
      this.condition.areaId = this.areaIdArr[this.areaIdArr.length - 1]
      getRecordList({ pager: this.pager, condition: this.condition }).then(res => {
        this.total = res.total
        this.records = res.records
      })
    },
    // 查询
    search() {
      this.pager.page = 1
      this.condition[this.keywordName] = this.keyword
      this.getList()
    },
    // 题目详情
    detail(row) {
      if (row.useType === 1) {
        this.$router.push({
          name: 'UsedRecordDetail',
          query: {
            taskId: row.useRefId,
            originId: row.dataOrigin
          }
        })
      }
    },
    // 导出
    exportRecord() {
      if (this.total < 1) {
        this.$message.info('当前数据为空')
        return
      }
      exportRecord({ ...this.condition, total: this.total }).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    pagination(pager) {
      this.pager = pager
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-column{
  margin-left: 20px;
}
.fr{
  margin-right: 20px;
}
</style>
