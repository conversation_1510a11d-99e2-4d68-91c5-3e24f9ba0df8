<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">{{ auditDetail.name }}</header>
    </div>
    <!-- body -->
    <div class="video-detail">
      <h3 class="detail-title">教程名称：{{ auditDetail.name }}</h3>
      <div class="detail-content">
        <!-- chapter -->
        <div
          v-for="(chapter, chapterIndex) in auditDetail.chapterResponseDtos"
          :key="chapter.chapterId"
          class="detail-item"
          :name="chapter.name"
        >
          <div class="paper-head" @click.stop="handleClickCollapse(chapter)">
            <div class="paper-title" :class="chapter.view? 'bg-success':''">章节{{ chapterIndex | filtersIndex }}：{{ chapter.name }}
              <i v-if="chapter.view" class="el-icon-success ic-success fr">已查阅</i>
            </div>
            <i class="paper-icon" :class="activeIndex === chapterIndex?'el-icon-arrow-down':'el-icon-arrow-right'" />
          </div>
          <!-- content -->
          <div v-if="activeIndex === chapterIndex">
            <!-- player -->
            <ali-player
              ref="aliplayer"
              :play-style="aliPlayerConfig.playStyle"
              :source="aliPlayerConfig.source"
              :cover="aliPlayerConfig.cover"
              :height="aliPlayerConfig.height"
              :skin-layout="aliPlayerConfig.skinLayout"
              @ready="handleReadyVideo"
              @pause="handlePauseVideo"
              @error="handleError"
            />
            <div
              v-for="(test, testIndex) in chapter.webTestPaperGroupDtos"
              :key="test.id"
              class="test-content"
            >
              <div class="test-title">试题{{ testIndex | filtersIndex }}: {{ test.name }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;插入时间：{{ test.timeSpot | parseSeconds }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;答题时间：{{ test.answerTime }}秒</div>
              <div v-for="item in test.webTestPaperDtos" :key="item.id" class="test-item">
                <div class="item-title">试题名称：{{ item.name }}</div>
                <div class="item-content">
                  <div v-for="option in item.webPaperItemDtos" :key="option.id" class="option">
                    {{ option.order | filtersEnIndex }}、{{ option.content }} <i v-if="option.answerStatus" class="el-icon-success" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- bottom button -->
    <div class="fixed-btn-group">
      <div class="group-content">
        <el-button type="default" @click="handleValidVideo(0)">驳回</el-button>
        <el-button :disabled="btnComfirmDisabled" type="primary" @click="handleValidVideo(1)">通过</el-button>
      </div>
    </div>
    <!-- dialog -->
    <el-dialog
      :title="dialogValidVideo.title"
      :visible.sync="dialogValidVideo.visible"
      width="600px"
      @close="handleCloseDialog()"
    >
      <el-form ref="form" :model="validVideoFrom" label-width="120px">
        <el-form-item label="教程名称">
          <span>{{ auditDetail.name }}</span>
        </el-form-item>
        <el-form-item label="教程说明">
          <span>{{ auditDetail.instruction }}</span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="validVideoFrom.opinion" type="textarea" maxlength="30" placeholder="请输入审核理由" />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="handleCloseDialog()">取 消</el-button>
        <el-button type="primary" @click="handleSubmitFrom()">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import AliPlayer from '@/components/Aliplayer/index'
import { courseValidDetail, courseValid } from '@/api/validManage'
import { parseSeconds, filtersIndex, filtersEnIndex } from '@/utils'
export default {
  name: 'CourseDetail',
  components: {
    AliPlayer
  },
  filters: {
    parseSeconds,
    filtersIndex,
    filtersEnIndex
  },
  mixins: [],
  props: {

  },
  data() {
    return {
      activeIndex: -1,
      // 已查看的内容
      viewList: [],
      // 按钮
      btnComfirmDisabled: true,
      // 阿里云配置
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            'name': 'bigPlayButton',
            'align': 'cc'
          },
          {
            'name': 'H5Loading',
            'align': 'cc'
          },
          {
            'name': 'errorDisplay',
            'align': 'tlabs',
            'x': 0,
            'y': 0
          },
          {
            'name': 'infoDisplay'
          },
          {
            'name': 'tooltip',
            'align': 'blabs',
            'x': 0,
            'y': 56
          },
          {
            'name': 'controlBar',
            'align': 'blabs',
            'x': 0,
            'y': 0,
            'children': [
              {
                'name': 'progress',
                'align': 'blabs',
                'x': 0,
                'y': 44
              },
              {
                'name': 'playButton',
                'align': 'tl',
                'x': 15,
                'y': 12
              },
              {
                'name': 'timeDisplay',
                'align': 'tl',
                'x': 10,
                'y': 7
              },
              {
                'name': 'fullScreenButton',
                'align': 'tr',
                'x': 10,
                'y': 12
              },
              {
                'name': 'volume',
                'align': 'tr',
                'x': 5,
                'y': 10
              }
            ]
          }
        ]
      },
      // 审核详情
      auditDetail: {},
      // 通过/驳回
      dialogValidVideo: {
        visible: false,
        title: '确认驳回审核？'
      },
      // 提交审核表单
      validVideoFrom: {
        opinion: '',
        result: null, // 0--不通过 1--通过
        courseId: '',
        courseInfoId: ''
      }
    }
  },
  created() {
    const courseInfoId = this.$route.params.courseInfoId
    courseValidDetail({ courseInfoId: courseInfoId }).then(res => {
      if (res.chapterResponseDtos.length) {
        res.chapterResponseDtos.forEach(v => {
          v.view = false
        })
        this.auditDetail = res
      }
    })
  },
  methods: {
    // 点击查看时
    handleClickCollapse(val) {
      if (val) {
        // 动态index
        if (this.activeIndex === val.listOrder - 1) {
          this.activeIndex = -1
        } else {
          this.activeIndex = val.listOrder - 1
        }
        // 审核通过按钮判定
        if (!this.viewList.includes(val.chapterId)) {
          this.viewList.push(val.chapterId)
          this.auditDetail.chapterResponseDtos.forEach(v => {
            if (v.chapterId === val.chapterId) {
              v.view = true
            }
          })
          if (this.viewList.length === this.auditDetail.chapterResponseDtos.length) {
            this.btnComfirmDisabled = false
          } else {
            this.btnComfirmDisabled = true
          }
        }
        // 视频播放
        if (this.activeIndex > -1) {
          if (this.auditDetail.chapterResponseDtos[this.activeIndex].videoUrl) {
            const sourceURL = {}
            this.auditDetail.chapterResponseDtos[this.activeIndex].videoUrl.reverse().map(v => {
              sourceURL[v.definition] = v.playURL
            })
            this.aliPlayerConfig.source = JSON.stringify(sourceURL)
            this.$nextTick(() => {
              this.$refs.aliplayer[0].init()
            })
          } else {
            this.$message.error('视频源获取失败，请刷新重试！')
          }
        }
      }
    },
    // 通过/驳回
    handleValidVideo(type) {
      if (type) {
        this.dialogValidVideo.title = '确认通过审核？'
      } else {
        this.dialogValidVideo.title = '确认驳回审核？'
      }
      this.validVideoFrom.courseId = this.auditDetail.courseId
      this.validVideoFrom.courseInfoId = this.auditDetail.courseInfoId
      this.validVideoFrom.result = type
      this.dialogValidVideo.visible = true
    },
    handleCloseDialog() {
      this.dialogValidVideo.visible = false
    },
    handleSubmitFrom() {
      courseValid(this.validVideoFrom).then(res => {
        let str = ''
        if (this.validVideoFrom.result) {
          str = '已通过审核！'
        } else {
          str = '已驳回审核！'
        }
        this.$message.success(str)
        setTimeout(() => {
          this.$router.go(-1)
        }, 300)
      })
    },
    // 阿里云播放器事件
    handleReadyVideo(val) {
      this.player = val
    },
    handlePauseVideo() {
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    }
  }
}
</script>

<style lang="scss" scoped>
  .head-level-second{
    text-indent: 20px;
  }
  .head-level-thrid{
    text-indent: 40px;
  }
  .bg-success{
    background: rgba($color: #67C23A, $alpha: 0.4);
  }
  .ic-success{
    font-size: 14px;
    line-height: 50px;
    color: #67C23A;
  }
  .video-detail{
    border: 1px solid #e4e4e4;
    margin: 0 auto 100px;
    width: 800px;
    .detail-title{

      margin: 0;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 600;
      line-height: 50px;
      background: #f2f5f9;
    }
    .detail-item{

    }
    .paper-head{
      display: flex;
      margin-bottom: -1px;
      line-height: 48px;
      height: 48px;
      border-bottom: 1px solid #e4e4e4;
    }
    .paper-icon{
      margin-right: 8px;
      line-height: 48px;
      font-size: 12px;
    }
    .paper-title{
      flex: 12;
      padding: 0 10px;
      font-size: 14px;
      width: 100%;
    }
    .test-content{
      background: #f2f5f9;
      .test-title, .test-item{
        padding: 0 10px;
      }
      .test-item{
        background: #fff;
      }
      .test-title, .item-title{
        border-bottom: 1px solid #e4e4e4;
        font-size: 14px;
        line-height: 40px;
      }
       .item-title{
         border-bottom: 0;
       }
      .item-content{
        text-indent: 20px;
        .option{
          font-size: 14px;
          line-height: 30px;
        }
      }
    }
  }
  .fixed-btn-group{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    .group-content{
      padding: 20px 0;
      text-align: center;
      background: #fff;
    }
  }
</style>
