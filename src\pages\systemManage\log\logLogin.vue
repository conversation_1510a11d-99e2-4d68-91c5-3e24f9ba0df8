<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" value-format="timestamp" :picker-options="pickerOptions" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onTimeChange" />
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="账号/姓名" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.system" clearable placeholder="请选择系统">
          <el-option v-for="item in osList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.operatorType" clearable placeholder="请选择类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.authType" clearable placeholder="请选择入口类型">
          <el-option v-for="item in authTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button @click="reset">重置</el-button>
      </div>
    </div>

    <!-- body -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="system" slot-scope="{row}">
        <span>{{ row.system|systemFmt }}</span>
      </template>
      <template slot="authType" slot-scope="{row}">
        <span>{{ row.authType|authTypeFmt }}</span>
      </template>
      <template slot="operatorType" slot-scope="{row}">
        <span>{{ row.operatorType|typeFmt }}</span>
      </template>
      <template slot="time" slot-scope="{row}">
        <span>{{ row.createTime|dateFmt }}</span>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import { parseTime, getDayLastSecond } from '@/utils'
import request from '@/api/logLogin'
import table from '@/mixins/table'

const columns = [
  {
    props: { label: '类型', align: 'center' },
    slot: 'operatorType'
  },
  {
    props: { label: '账号', align: 'center', prop: 'username' }
  },
  {
    props: { label: '姓名', align: 'center', prop: 'realName' }
  },
  {
    props: { label: 'IP', align: 'center', prop: 'userIp' }
  },
  {
    props: { label: '系统', align: 'center' },
    slot: 'system'
  },
  {
    props: { label: '入口类型', align: 'center' },
    slot: 'authType'
  },
  {
    props: { label: '时间', align: 'center' },
    slot: 'time'
  }
]

export default {
  name: 'LogLogin',
  filters: {
    systemFmt(v) {
      const arr = ['', 'cms', 'saas', '安卓', 'ios']
      return arr[v]
    },
    typeFmt(v) {
      const arr = ['', '登录', '退出']
      return arr[v]
    },
    authTypeFmt(v) {
      const arr = ['', '名医传世', 'SDK', '钉钉']
      return arr[v]
    },
    dateFmt(v) {
      return parseTime(v)
    }
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      timeRange: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      osList: [
        { value: 1, label: 'cms' },
        { value: 2, label: 'saas' },
        { value: 3, label: '安卓' },
        { value: 4, label: 'ios' }
      ],
      typeList: [
        { value: 1, label: '登录' },
        { value: 2, label: '退出' }
      ],
      authTypeList: [
        { value: 1, label: '名医传世' },
        { value: 2, label: 'SDK' },
        { value: 3, label: '钉钉' }
      ],
      conditionWatch: {
        operatorType: '',
        system: '',
        authType: '',
        startTime: '',
        endTime: ''
      },
      condition: {
        keyword: ''
      }
    }
  },
  methods: {
    onTimeChange(v) {
      this.conditionWatch.startTime = v ? v[0] : ''
      this.conditionWatch.endTime = v ? getDayLastSecond(v[1]) : ''
    }
  }
}
</script>
