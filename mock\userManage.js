const dataList = {
  records: [
    {
      additional: '',
      agroupId: 184,
      applyCode: '',
      createUid: '',
      email: '',
      homeInfo: '',
      mobile: '13719018851',
      password: '',
      personTag: '',
      platformAuth: '',
      realname: '陈恢秀',
      regFrom: '',
      regIp: '',
      regTime: '2020-03-10 17:13',
      salt: '',
      sorts: '',
      status: 1,
      userAdmin: '',
      userAdvertiser: '',
      userAgency: '',
      userAgroupIntStr: '',
      userDoctor: '',
      userEnterprise: '',
      userHospital: '',
      userId: '953955',
      userIp: '',
      userPersonal: '',
      userRoleStr: '',
      userStaff: '',
      userType: 3,
      userTypeInt: 3,
      userTypeIntStr: '企业员工',
      username: 'lczhyc:030'
    },
    {
      additional: '',
      agroupId: 184,
      applyCode: '',
      createUid: '',
      email: '',
      homeInfo: '',
      mobile: '13719018851',
      password: '',
      personTag: '',
      platformAuth: '',
      realname: '陈恢秀',
      regFrom: '',
      regIp: '',
      regTime: '2020-03-10 17:13',
      salt: '',
      sorts: '',
      status: 1,
      userAdmin: '',
      userAdvertiser: '',
      userAgency: '',
      userAgroupIntStr: '',
      userDoctor: '',
      userEnterprise: '',
      userHospital: '',
      userId: '953955',
      userIp: '',
      userPersonal: '',
      userRoleStr: '',
      userStaff: '',
      userType: 3,
      userTypeInt: 3,
      userTypeIntStr: '企业员工',
      username: 'lczhyc:030'
    },
    {
      additional: '',
      agroupId: 184,
      applyCode: '',
      createUid: '',
      email: '',
      homeInfo: '',
      mobile: '13719018851',
      password: '',
      personTag: '',
      platformAuth: '',
      realname: '陈恢秀',
      regFrom: '',
      regIp: '',
      regTime: '2020-03-10 17:13',
      salt: '',
      sorts: '',
      status: 1,
      userAdmin: '',
      userAdvertiser: '',
      userAgency: '',
      userAgroupIntStr: '',
      userDoctor: '',
      userEnterprise: '',
      userHospital: '',
      userId: '953955',
      userIp: '',
      userPersonal: '',
      userRoleStr: '',
      userStaff: '',
      userType: 3,
      userTypeInt: 3,
      userTypeIntStr: '企业员工',
      username: 'lczhyc:030'
    },
    {
      additional: '',
      agroupId: 184,
      applyCode: '',
      createUid: '',
      email: '',
      homeInfo: '',
      mobile: '13719018851',
      password: '',
      personTag: '',
      platformAuth: '',
      realname: '陈恢秀',
      regFrom: '',
      regIp: '',
      regTime: '2020-03-10 17:13',
      salt: '',
      sorts: '',
      status: 1,
      userAdmin: '',
      userAdvertiser: '',
      userAgency: '',
      userAgroupIntStr: '',
      userDoctor: '',
      userEnterprise: '',
      userHospital: '',
      userId: '953955',
      userIp: '',
      userPersonal: '',
      userRoleStr: '',
      userStaff: '',
      userType: 3,
      userTypeInt: 3,
      userTypeIntStr: '企业员工',
      username: 'lczhyc:030'
    }
  ],
  total: parseInt(10)
}

const wjwList = {
  total: parseInt(10),
  records: [
    {
      bindUid: '',
      deptId: '133399',
      deptName: '海伦市海伦镇卫生院',
      directEntOrgId: '127625',
      keyword: '黑龙江',
      level: 0,
      listOrder: 0,
      page: 1,
      parentId: '127625',
      parentOrgIds: '127631,127625,',
      srcId: '',
      srcParentId: '',
      status: 2,
      type: 2
    },
    {
      bindUid: '',
      deptId: '8280709',
      deptName: '卫生站',
      directEntOrgId: '284521',
      keyword: '黑龙江',
      level: 0,
      listOrder: 13142,
      page: 1,
      parentId: '284521',
      parentOrgIds: '127631,284511,284521,',
      srcId: '',
      srcParentId: '',
      status: 1,
      type: 4
    },
    {
      bindUid: '',
      deptId: '139995',
      deptName: '富裕县繁荣乡中心卫生院',
      directEntOrgId: '127583',
      keyword: '黑龙江',
      level: 0,
      listOrder: 0,
      page: 1,
      parentId: '127583',
      parentOrgIds: '127631,127583,',
      srcId: '',
      srcParentId: '',
      status: 2,
      type: 2
    }
  ]
}

export default [
  {
    url: '/vue-element-admin/user/list',
    type: 'post',
    response: config => {
      return {
        code: 1,
        data: dataList
      }
    }
  },
  {
    url: '/vue-element-admin/user/doctor',
    type: 'post',
    response: config => {
      return {
        code: 1,
        data: dataList
      }
    }
  },
  {
    url: '/wei/list',
    type: 'get',
    response: config => {
      return {
        code: 1,
        data: wjwList
      }
    }
  }
]
