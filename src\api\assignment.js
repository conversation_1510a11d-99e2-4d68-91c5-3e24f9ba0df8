import request from '@/utils/request'

// 增加指派任务
export function addAssignment(params) {
  const { uid, name } = params
  return request({
    url: `/addAssignment/${uid}/${name}`,
    method: 'get'
  })
}

// 指派列表
export function assignmentList() {
  return request({
    url: '/assignmentList',
    method: 'get'
  })
}

// 删除指派
export function deleteAssignment(params) {
  const { assignmentId } = params
  return request({
    url: `/deleteByAssignmentId/${assignmentId}`,
    method: 'get'
  })
}

// 选择医院
export function getHospitalList(params) {
  return request({
    url: '/hospitalList',
    method: 'post',
    data: params
  })
}

// 启用、禁用
export function modifyAssignmentStatus(params) {
  const { assignmentId, status } = params
  return request({
    url: `/modify/${assignmentId}/${status}`,
    method: 'post'
  })
}
