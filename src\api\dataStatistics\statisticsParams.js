import request from '@/utils/request'

/**
 * 统计参数
 */

// 查询统计倍数列表信息
export function list() {
  return request({
    url: `/multiple/list`,
    method: 'post'
  })
}

// 扩充状态
export function enlargeStatus() {
  return request({
    url: `/multiple/enlarge/status`,
    method: 'post'
  })
}

// 状态改变
export function editStatus(params) {
  return request({
    url: `/multiple/edit/status`,
    method: 'post',
    data: params
  })
}

// 倍数设置
export function editMultiple(params) {
  return request({
    url: '/multiple/edit',
    method: 'post',
    data: params
  })
}

// 获取区域列表
export function multipleDetail(id) {
  return request({
    url: `/multiple/detail/${id}`,
    method: 'get'
  })
}
