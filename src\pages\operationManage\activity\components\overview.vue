<template>
  <div class="overview">
    <h3>活动概况<i class="el-icon-warning" @click="dialogShow = true" />
      <el-button class="fr detail" type="text" @click="viewDetail">明细</el-button>
    </h3>
    <div class="exposure">
      <h4>曝光概况</h4>
      <div class="data-row">
        <div>
          <span>曝光次数</span>
          <span>曝光人数</span>
          <span>人均曝光次数</span>
          <span>曝光率</span>
        </div>
        <div><span v-for="(v,i) in data.exposure" :key="i" class="data-num">{{ v }}</span></div>
      </div>
    </div>
    <div class="click-exposure">
      <h4>点击概况</h4>
      <div class="data-row">
        <div>
          <span>点击次数</span>
          <span>点击人数</span>
          <span>人均点击次数</span>
          <span>点击率</span>
        </div>
        <div><span v-for="(v,i) in data.click" :key="i" class="data-num">{{ v }}</span></div>
      </div>
    </div>
    <div class="exposure">
      <h4>参与概况</h4>
      <div class="data-row">
        <div>
          <span>视频答题次数</span>
          <span>视频答题人数</span>
          <span>人均视频答题次数</span>
          <span>转化率</span>
        </div>
        <div><span v-for="(v,i) in data.participate" :key="i" class="data-num">{{ v }}</span></div>
        <div>
          <span>视频播放次数</span>
          <span>视频播放人数</span>
          <span>人均视频播放次数</span>
        </div>
        <div><span v-for="(v,i) in data.video" :key="i" class="data-num">{{ v }}</span></div>
      </div>
    </div>

    <el-dialog title="规则说明" :visible.sync="dialogShow" width="800px" top="8vh">
      <div>
        <div class="text">
          <p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">说明：根据所选的活动，统计活动曝光概况、点击概况、参与概况数据</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">规则：</span></p><p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">1、曝光概况</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 曝光概况：包括曝光次数、曝光人数、人均曝光次数</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 1）曝光次数：所有用户查看到活动总次数</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 2）曝光人数：所有查看到活动的总人数，相同用户只计1次</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 3）人均曝光次数：单个用户平均看到活动的次数，人均曝光次数=曝光次数/曝光人数（保留整数）</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 4）曝光率：曝光用户占活动用户的比例，曝光率=曝光人数/投放总人数*100%（保留两位小数）</span></p><p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">2、点击概况</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 曝光概况：包括点击次数、点击人数、人均点击次数、点击率</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 1）点击次数：所有用户点击活动进入活动详情的总次数，包括在活动列表点击、在广告点击进入活动详情</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 2）点击人数：所有点击进入活动详情的总人数，相同用户只计1次</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 3）人均点击次数：单个用户平均点击进入活动详情的次数，人均点击次数=点击次数/点击人数（保留整数）</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 4）点击率：点击人数在总的曝光人数的占比，点击率=点击人数/曝光人数*100%（保留两位小数）</span></p><p><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;font-weight:700;">3、参与概况</span><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;" /></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 参与概况：包括视频答题次数、视频答题人数、人均视频答题次数、转化率</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 1）视频答题次数：所有用户进行视频答题的总次数，包括已完成和未完成的</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 2）视频答题人数：所有进行视频答题的总人数，相同用户只计1次</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 3）人均视频答题次数：单个用户平均进行视频答题的次数，人均视频答题次数=视频答题次数/视频答题人数（保留整数）</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 4）转化率：视频答题人数在总的曝光人数的占比，转化率=视频答题人数/曝光人数*100%（保留两位小数）</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 5）视频播放次数：所有用户进行视频播放的总次数，包括视频答题、视频播放</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 6）视频播放人数：所有进行视频播放的总人数（包括视频答题的人），相同用户只计1次</span></p><p><span style="font-family:'Arial Normal', 'Arial', sans-serif;font-weight:400;">&nbsp;&nbsp; &nbsp;&nbsp; 7）人均视频播放次数：单个用户评价进行视频播放的次数，人均视频播放次数=视频播放次数/视频播放人数（保留整数）</span></p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogShow = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Overview',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    actId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false
    }
  },
  methods: {
    viewDetail() {
      this.$router.push({
        path: 'statisticsDetail',
        query: {
          id: this.actId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.overview {
  h3 {
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
    i {
      cursor: pointer;
      margin-left: 10px;
      color: #3bb19c;
    }
    .detail{
      margin-top: 6px;
      margin-right: 20px;
    }
  }
  .exposure,
  .click-exposure {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h4 {
      width: 20%;
    }
  }
  .data-row {
    flex: 1;
    margin: 14px 0;
    div {
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        width: 25%;
        text-align: left;
      }
      .data-num{
        line-height: 1.6;
        font-size: 30px;
      }
    }
  }
}
</style>
