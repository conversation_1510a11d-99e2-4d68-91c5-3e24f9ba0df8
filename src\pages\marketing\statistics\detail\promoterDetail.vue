<template>
  <section class="detail">
    <div class="module">
      <h2>
        推广员概况
      </h2>
      <ul>
        <li v-for="item in roughly" :key="item.label">
          <p>{{ item.label }}</p>
          {{ item.value }}
        </li>
      </ul>
    </div>
    <div class="module">
      <article-list :search-param="queryParams" :user-type="queryParams.condition.userType" />
    </div>
    <div class="module">
      <video-list :search-param="queryParams" :user-type="queryParams.condition.userType" />
    </div>
    <div class="module">
      <doula-list :search-param="queryParams" :user-type="queryParams.condition.userType" />
    </div>
    <div class="module">
      <visit-list :search-param="queryParams" :user-type="queryParams.condition.userType" />
    </div>
  </section>
</template>

<script>
import ArticleList from '../article.vue'
import VideoList from '../video.vue'
import DoulaList from '../doula.vue'
import VisitList from '../visit.vue'
import { userAnalysisDetailSurvey } from '@/api/marketing/promoteArticle'
export default {
  components: {
    ArticleList,
    VideoList,
    DoulaList,
    VisitList
  },
  data() {
    return {
      roughly: [],
      tableColumn: [
        { prop: 'productName', label: '产品', width: '280' },
        { prop: 'releaseTime', label: '发布时间', width: '280' },
        { prop: 'viewNum', label: '浏览量', width: '280' },
        { prop: 'peopleViewNum', label: '浏览人数', width: '280' },
        { prop: 'totalFee', label: '费用(元)', width: '' }
      ],
      articleTableData: [],
      videoTableData: [],
      queryParams: {
        condition: {
          keywordId: this.$route.query.userId,
          userId: this.$route.query.userId,
          userType: 'PROMOTER'
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  created() {
    this.getUserAnalysisDetailSurvey()
  },
  methods: {
    getUserAnalysisDetailSurvey() {
      userAnalysisDetailSurvey(this.queryParams.condition).then(res => {
        this.roughly = [
          { label: '推广员', value: res.userName },
          { label: '关联产品', value: res.productDesc },
          { label: '手机', value: res.phone },
          { label: '文章数', value: res.articleNum },
          { label: '视频数', value: res.videoNum },
          { label: '抖喇数', value: res.doulaNum },
          { label: '拜访数', value: res.visitNum },
          { label: '总费用', value: res.costWithUnit }
        ]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  .module {
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      font-size: 20px;
      line-height: 55px;
      background-color: #f9f9f9;
      .el-button {
        height: 35px;
      }
    }
    ul {
      display: flex;
      justify-content: space-between;
      padding: 38px 0 0 24px;
      height: 145px;
      font-size: 20px;
      font-weight: bold;
      li {
        p {
          font-size: 18px;
          color: #666;
          font-weight: 400;
          margin-bottom: 20px;
        }
      }
    }
    .preview {
      height: 125px;
      padding-left: 24px;
      line-height: 125px;
      a {
        color: #409eff;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .table {
      padding: 18px 24px 0;
      .pagination-container {
        padding: 30px 20px;
      }
      a {
        color: #409eff;
        font-size: 14px;
      }
    }
  }
}
</style>
