<template>
  <div>
    <!-- 查看导入数据 -->
    <el-dialog
      :visible.sync="verifyDialogVisible"
      width="700px"
      @close="close()"
    >
      <el-table :data="viewDataList" border style="width: 100%">
        <el-table-column align="center" width="180px" prop="createTime" label="导入日期" />
        <el-table-column align="center" prop="username" label="操作人" />
        <el-table-column align="center" prop="orgTypeName" label="导入类型" />
        <el-table-column align="center" prop="statusName" label="处理状态" />
        <el-table-column align="center" width="150px" label="操作">
          <template slot-scope="{row}">
            <el-button v-if="row.status!==1" type="text" @click="handleView(row)">查看</el-button>
            <el-button v-if="row.isRowErrorData" type="text" @click="exportIssue(row)">导出问题</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-if="viewDataList.length > 0"
        class="text-center"
        :layout="layout"
        :page="viewDataQuery.pager.page"
        :total="pageTotal"
        @pagination="handlePagination"
      />
    </el-dialog>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import Pagination from '@/components/Pagination'
import request from '@/api/wjw'

export default {
  name: 'WjwImport',
  components: {
    Pagination
  },
  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'p'
    }
  },
  data() {
    return {
      // 导入弹层
      viewDataQuery: {
        condition: {},
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      viewDataList: [],
      pageTotal: 100,
      layout: 'total, prev, pager, next, jumper',
      verifyDialogVisible: false
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.verifyDialogVisible = true
        this.viewData()
      }
    }
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit('update:show', false)
    },
    // 查看导出问题
    exportIssue(row) {
      window.location.href = request.getDownloadRecordMessage({ recordId: row.recordId }, getToken())
    },
    handlePagination(val) {
      this.viewDataQuery.pager.page = val.page
      this.viewData()
    },
    // 获取列表
    viewData() {
      request.getImportHistory(this.viewDataQuery).then(res => {
        this.viewDataList = res.records
        this.pageTotal = res.total
      })
    },
    // 查看导入结果
    handleView(row) {
      this.$emit('error', {
        title: '导入结果：' + row.statusName,
        message: row.message,
        batchNo: row.batchNo
      })
      // if (row.status === 2) {
      //   // this.$alert(`${row.createTime}成功导入${row.total}条数据记录`, '导入成功', {
      //   //   confirmButtonText: '确定'
      //   // })
      //   this.$emit('error', {
      //     title: '导入结果：' + row.statusName,
      //     message: `${row.createTime} 成功导入${row.orgTypeName}单位 ${row.total}条数据`
      //   })
      // } else {
      //   this.$emit('error', {
      //     title: '导入结果：' + row.statusName,
      //     message: row.message,
      //     batchNo: row.batchNo
      //   })
      // }
    }
  }
}
</script>

