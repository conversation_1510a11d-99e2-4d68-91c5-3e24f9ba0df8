<template>
  <div class="contain">
    <el-row>
      <div class="condition">
        <span>推广类型：</span><el-tag style="margin-right:100px;font-size:18px">{{ promotionType }}</el-tag>
        <span>推广人数：</span><el-tag style="font-size:18px">{{ promotionNum }}</el-tag>
        <div v-show="promotionType==='精准推广'" style="display:flex;align-items:center">
          <span>推广条件：</span>
          <el-table :data="liveScopeCondition">
            <el-table-column prop="weight" label="优先级" width="100px" />
            <el-table-column prop="val" label="条件">
              <template slot-scope="{row}">
                <template v-if="row.property==='area'">
                  <el-cascader
                    ref="cascader1"
                    v-model="row.ids"
                    :options="areaList"
                    disabled
                    :props="{
                      value:'areaId',
                      label:'name',
                      children:'childList',
                      emitPath: false,
                      multiple:true
                    }"
                    style="width:70vw"
                  />
                </template>
                <template v-else>
                  <span>{{ row.val }}</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-row>
    <el-row style="width:1600px">
      <confirmUserList ref="confirmUserList" :live-id="liveId" @init="initUserList()" />
    </el-row>
    <br>
    <el-row style="width:1600px">
      <ableAddUserList ref="ableAddUserList" :live-id="liveId" @init="initUserList()" />
    </el-row>
    <div class="footerBtn">
      <el-button @click="cancle()">取 消</el-button>
      <el-button type="primary" @click="confirm()">确 定</el-button>
    </div>
  </div>
</template>

<script>
import ableAddUserList from './ableAddUserList.vue'
import confirmUserList from './confirmUserList.vue'
import { liveAuditFinish } from '@/api/liveManage'
import { getAreaTree } from '@/api/area' // 区域树

export default {
  name: 'CheckDetail',
  components: { confirmUserList, ableAddUserList },
  data() {
    return {
      liveId: this.$route.query.liveId,
      promotionType: this.$route.query.promotionType,
      promotionNum: this.$route.query.promotionNum,
      liveScopeCondition: this.$route.query.liveScopeCondition || [],
      areaList: []
    }
  },
  created() {
    getAreaTree().then(res => {
      this.areaList = this.clearNullChildList(res, 'childList')
    })
  },
  methods: {
    initUserList() {
      this.$refs.confirmUserList.getList()
      this.$refs.confirmUserList.flag = true
      this.$refs.ableAddUserList.getList()
      this.$refs.ableAddUserList.flag = true
    },
    cancle() {
      this.$confirm('是否取消', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$router.go(-1)
      })
    },
    confirm() {
      liveAuditFinish(this.$route.query.liveId).then(res => {
        this.$message.success('审核成功')
        this.$router.push('/liveManage')
      })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  margin: 30px;
}
.condition{
  margin-bottom: 20px;
}
.footerBtn{
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
</style>
