<template>
  <el-dialog top="8vh" title="选择单位" :visible.sync="visible" center :before-close="beforeClose" width="800px">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="搜索单位" clearable @change="init">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
        </el-input>
      </div>
    </div>

    <a-table :columns="columns" :data="list" border stripe max-height="500px" size="mini">
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="toggleSelect(row)">{{ selectedIds.includes(row.orgId) ? '取消选择' : '选择' }}</el-button>
      </template>
    </a-table>

    <Pagination :total="total" :page="pager.page" @pagination="handlePagination" />
  </el-dialog>
</template>

<script>
import ATable from '@/components/ATable'
import Pagination from '@/components/Pagination'
import { orgList, orgDetail } from '@/api/course'

export default {
  name: 'DialogUnit',
  components: { ATable, Pagination },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      condition: {
        keyword: ''
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      columns: Object.freeze([
        { props: { label: 'ID', align: 'center', prop: 'orgId', width: '100' }},
        { props: { label: '类型', align: 'center', prop: 'typeName', width: '100' }},
        { props: { label: '名称', align: 'center', prop: 'orgName' }},
        {
          props: { align: 'center', label: '操作', width: '100' },
          slot: 'actions'
        }
      ]),
      list: [],
      total: 0,
      selectedIds: []
    }
  },
  watch: {
    visible(v) {
      v && this.init()
    }
  },
  methods: {
    init(reset = true) {
      reset && (this.pager.page = 1)
      const params = {
        condition: this.condition,
        pager: this.pager
      }
      orgList(params).then(res => {
        this.total = res.total
        this.list = res.records
      })
    },
    handlePagination(val) {
      this.pager = val
      this.init(false)
    },
    toggleSelect(row) {
      if (this.selectedIds.includes(row.orgId)) {
        const I = this.selectedIds.findIndex(v => v === row.orgId)
        this.selectedIds.splice(I, 1)
      } else {
        this.selectedIds = [row.orgId]
      }
      orgDetail(row.orgId).then(res => {
        this.$emit('selectUnit', Object.assign({}, row, res))
      }).catch(() => {
        this.$message.error('获取详情信息失败')
      })
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    }
  }
}
</script>
