import Layout from '@/layout'

const systemManageRouter = {
  path: '/systemManage',
  component: Layout,
  name: 'SystemManage',
  meta: { title: '系统管理', icon: 'dept', breadcrumb: false },
  children: [
    {
      path: 'platformParams',
      component: () => import('@/pages/systemManage/platformParams'),
      name: 'PlatformParams',
      meta: { title: '平台参数' }
    },
    {
      path: 'appVersionManage',
      component: () => import('@/pages/systemManage/appVersionManage'),
      name: 'AppVersionManage',
      meta: { title: 'App版本管理' }
    },
    {
      path: 'createVersion',
      component: () => import('@/pages/systemManage/createVersion'),
      name: 'CreateVersion',
      meta: { title: '创建版本' },
      hidden: true
    },
    {
      path: 'editVersion',
      component: () => import('@/pages/systemManage/editVersion'),
      name: 'EditVersion',
      meta: { title: '编辑版本' },
      hidden: true
    },
    {
      path: 'deptIndex',
      component: () => import('@/pages/dept/index'),
      name: 'DeptIndex',
      meta: { title: '部门库' }
    },
    {
      path: 'addDept',
      component: () => import('@/pages/dept/addDept'),
      name: 'AddDept',
      meta: { title: '添加部门库' },
      hidden: true
    },
    {
      path: '/authoritySaas',
      component: { render: (e) => e('router-view') },
      name: 'SaasAuthority',
      redirect: { name: 'SaasRole' },
      meta: { title: 'Saas权限管理' },
      children: [
        {
          path: 'saasRole',
          component: () => import('@/pages/systemManage/authoritySaas/role'),
          name: 'SaasRole',
          meta: { title: '角色管理' }
        },
        {
          path: 'saasRoleAdd',
          component: () => import('@/pages/systemManage/authoritySaas/roleAdd'),
          name: 'SaasRoleAdd',
          meta: { title: '添加角色' },
          hidden: true
        },
        {
          path: 'saasMenu',
          component: () => import('@/pages/systemManage/authoritySaas/menu'),
          name: 'SaasMenu',
          meta: { title: '菜单管理' }
        },
        {
          path: 'saasMenuAdd',
          component: () => import('@/pages/systemManage/authoritySaas/menuAdd'),
          name: 'SaasMenuAdd',
          meta: { title: '添加菜单' },
          hidden: true
        }
      ]
    },
    {
      path: '/authorityCms',
      component: { render: (e) => e('router-view') },
      name: 'CmsAuthority',
      redirect: { name: 'CmsRole' },
      meta: { title: 'CMS权限管理' },
      children: [
        {
          path: 'cmsRole',
          component: () => import('@/pages/systemManage/authorityCms/role'),
          name: 'CmsRole',
          meta: { title: '角色管理' }
        },
        {
          path: 'cmsRoleAdd',
          component: () => import('@/pages/systemManage/authorityCms/roleAdd'),
          name: 'CmsRoleAdd',
          meta: { title: '添加角色' },
          hidden: true
        },
        {
          path: 'cmsMenu',
          component: () => import('@/pages/systemManage/authorityCms/menu'),
          name: 'CmsMenu',
          meta: { title: '菜单管理' }
        },
        {
          path: 'cmsMenuAdd',
          component: () => import('@/pages/systemManage/authorityCms/menuAdd'),
          name: 'CmsMenuAdd',
          meta: { title: '添加菜单' },
          hidden: true
        }
      ]
    },
    {
      path: '/log',
      component: { render: (e) => e('router-view') },
      name: 'Log',
      redirect: { name: 'LoginLog' },
      meta: { title: '日志管理' },
      children: [
        {
          path: 'logLogin',
          component: () => import('@/pages/systemManage/log/logLogin'),
          name: 'LoginLog',
          meta: { title: '登录日志' }
        },
        {
          path: 'logOperation',
          component: () => import('@/pages/systemManage/log/logOperation'),
          name: 'OperationLog',
          meta: { title: '操作日志' }
        }
      ]
    },
    {
      path: 'thirdparty',
      component: () => import('@/pages/systemManage/developer/index'),
      name: 'Thirdparty',
      meta: { title: '第三方接入' }
    }
  ]
}

export default systemManageRouter
