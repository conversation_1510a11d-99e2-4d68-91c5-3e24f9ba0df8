<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.name"
        placeholder="证书名称"
        clearable
        @change="search"
        @clear="clearKeyWord"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>
      <el-select
        v-model="listQuery.condition.isRecommend"
        placeholder="是否推荐"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in recommendOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.status"
        placeholder="状态"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      证书列表
      <div>
        <el-button type="primary" @click="toDetail('create')">创建证书培训</el-button>
      </div>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column prop="cover" label="封面" min-width="50">
          <template slot-scope="{row}">
            <el-image
              class="cover-img"
              :src="row.coverImgUrl"
              :preview-src-list="[row.coverImgUrl]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="证书名称">
          <template slot-scope="{row}">
            <el-tooltip v-if="row.name.length>18" class="item" popper-class="desc-tool-tip" effect="dark" :content="row.name" placement="top-start">
              <el-button
                type="text"
                size="mini"
                @click="toDetail('view', row.certId)"
              >{{ row.name }}</el-button>
            </el-tooltip>
            <el-button
              v-else
              type="text"
              size="mini"
              @click="toDetail('view', row.certId)"
            >{{ row.name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
          align="center"
        >
          <template slot-scope="{row}">
            <el-button v-if="item.prop === 'userNum'" type="text" @click="toTrainee(row.certId)">
              {{ row[item.prop] }}
            </el-button>
            <span v-else>
              {{ row[item.prop] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="operate(row, 'status')"
            >{{ [0, 2].includes(row.status) ? '上架' : '下架' }}</el-button>
            <el-button
              v-if="row.status === 1"
              type="text"
              size="mini"
              @click="operate(row, 'recommend')"
            >{{ row.isRecommend === 0 ? '推荐' : '取消推荐' }}</el-button>
            <el-button
              v-if="row.status === 0"
              type="text"
              size="mini"
              @click="toDetail('edit', row.certId)"
            >编辑</el-button>
            <el-button
              v-if="row.status === 0"
              type="text"
              size="mini"
              @click="operate(row, 'del')"
            >删除</el-button>
            <el-button
              v-if="row.status === 1"
              type="text"
              size="mini"
              @click="toApply()"
            >纸质证书申领学员</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { certExamineList, certExamineOperate } from '@/api/certificate'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      stateOptions: [
        { label: '待上架', value: 0 },
        { label: '已上架', value: 1 },
        { label: '已下架', value: 2 }
      ],
      recommendOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      listQuery: {
        condition: {
          status: null,
          isRecommend: null,
          name: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'endTime', label: '截止日期', width: '40' },
        { prop: 'limitDaysStr', label: '考核期限', width: '40' },
        { prop: 'joinFeeStr', label: '价格', width: '40' },
        { prop: 'limitUsersStr', label: '培训名额', width: '40' },
        { prop: 'userNum', label: '学员数量', width: '40' },
        { prop: 'statusStr', label: '状态', width: '40' },
        { prop: 'isRecommendStr', label: '推荐', width: '40' },
        { prop: 'creator', label: '创建人', width: '40' },
        { prop: 'createTime', label: '创建时间', width: '80' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    getList() {
      certExamineList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.limitDaysStr = v.limitDays + '天'
          v.statusStr = this.stateOptions.find(i => i.value === v.status).label
          v.isRecommendStr = this.recommendOptions.find(i => i.value === v.isRecommend).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    clearKeyWord() {
      this.listQuery.condition.name = ''
      this.getList()
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    operate(row, handle) {
      const type = handle === 'status' ? 1 : (handle === 'recommend' ? 2 : 3)
      if (type !== 2) {
        this.$confirm(`此操作将${type === 3 ? '删除' : (row.status === 1 ? '下架' : '上架')}该证书培训, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          certExamineOperate({ certId: row.certId, type }).then(() => {
            this.getList()
          })
        })
      } else {
        certExamineOperate({ certId: row.certId, type }).then(() => {
          this.getList()
        })
      }
    },
    toDetail(type, id) {
      this.$router.push({
        name: 'CertificateCreator',
        query: {
          type,
          id
        }
      })
    },
    toTrainee(id) {
      this.$router.push({
        name: 'CertificateTraineeList',
        query: {
          id
        }
      })
    },
    toApply() {
      this.$router.push({
        name: 'CertificateApply'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  min-height: 95vh;
  border-radius: 4px;
  overflow: hidden;
  background-color: #eaeaee;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
  .cover-img{
    width: 100px;
    height: auto;
    object-fit: cover;
  }
}
</style>
