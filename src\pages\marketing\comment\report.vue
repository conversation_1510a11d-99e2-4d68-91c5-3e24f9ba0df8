<template>
  <section class="report">
    <div class="screen">
      <el-input
        v-model="searchKeyword"
        class="group"
        placeholder="请输入搜索关键字"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
        <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="clearKeyWord">
          <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-input>

      <el-select
        v-model="listQuery.condition.isVerified"
        placeholder="状态"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.type"
        placeholder="举报类型"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.code"
          :label="item.value"
          :value="item.code"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.contentType"
        placeholder="内容类型"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in contentTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>举报管理</h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn.slice(0,5)"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="举报数">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="showDialog(scope.row.id, scope.row.contentType)"
            >{{ scope.row.reportNum }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          v-for="item in tableColumn.slice(5,8)"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template v-if="scope.row.isVerified === '未处理'" slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="auditReport(scope.row.id,scope.row.contentType,1)"
            >属实</el-button>
            <el-button
              type="text"
              size="mini"
              @click="auditReport(scope.row.id,scope.row.contentType,2)"
            >不属实</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <el-dialog title="举报明细" :visible.sync="dialogVisible">
      <el-table
        :data="reportDetail"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
      >
        <el-table-column
          v-for="item in detailTableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
      </el-table>
      <Pagination :page="detailListQuery.pager.page" :total="detailTotal" @pagination="detailHandlePagination" />
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { reportTypeList, reportList, reportDetailList, reportAudit } from '@/api/marketing/commentPromote'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      searchType: 'userName',
      searchTypeList: [
        { label: '发布人', value: 'userName' },
        { label: '发布人账号', value: 'userAccount' },
        { label: '内容', value: 'content' }
      ],
      searchKeyword: '',
      stateOptions: [
        { label: '未处理', value: 0 },
        { label: '属实', value: 1 },
        { label: '不属实', value: 2 }
      ],
      typeOptions: [],
      contentTypeOptions: [
        { label: '评论', value: 'COMMENT' },
        { label: '抖喇图文', value: 'DOULA_ARTICLE' },
        { label: '抖喇短视频', value: 'DOULA_VIDEO' }
      ],
      listQuery: {
        condition: {
          isVerified: null,
          type: null,
          contentType: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'id', label: '内容ID', width: '60' },
        { prop: 'userName', label: '发布人', width: '60' },
        { prop: 'userAccount', label: '发布人账号', width: '60' },
        { prop: 'content', label: '发布内容', width: '200' },
        { prop: 'createdTime', label: '发布时间', width: '120' },
        { prop: 'contentTypeString', label: '内容类型', width: '120' },
        { prop: 'lastReportTime', label: '最后举报时间', width: '120' },
        { prop: 'isVerified', label: '状态', width: '60' }
      ],
      tableData: [],
      dialogVisible: false,
      reportDetail: [],
      detailTableColumn: [
        { prop: 'userName', label: '举报人', width: '60' },
        { prop: 'userAccount', label: '举报人账号', width: '60' },
        { prop: 'reportType', label: '举报类型', width: '60' },
        { prop: 'reportTime', label: '举报时间', width: '' }
      ],
      detailListQuery: {
        condition: {
          commentId: 0,
          contentType: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      detailTotal: 0
    }
  },
  mounted() {
    reportTypeList({ type: 'COMMENT_REPORT_TYPE' }).then(res => {
      this.typeOptions = res
    })
    this.getList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    detailHandlePagination(v) {
      this.detailListQuery.pager = v
      this.getDetailList()
    },
    getList() {
      reportList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.isVerified = this.stateOptions.find(i => i.value === v.isVerified).label
          v.contentTypeString = this.contentTypeOptions.find(i => i.value === v.contentType).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    clearKeyWord() {
      this.searchKeyword = ''
      this.listQuery.condition = {
        isVerified: this.listQuery.condition.isVerified,
        type: this.listQuery.condition.type,
        contentType: this.listQuery.condition.contentType
      }
      this.getList()
    },
    search() {
      this.listQuery.pager.page = 1
      if (this.searchKeyword === '') {
        this.listQuery.condition = {
          isVerified: this.listQuery.condition.isVerified,
          type: this.listQuery.condition.type,
          contentType: this.listQuery.condition.contentType
        }
      } else {
        this.listQuery.condition[this.searchType] = this.searchKeyword
      }
      this.getList()
    },
    auditReport(commentId, contentType, isVerified) {
      reportAudit({ commentId, contentType, isVerified }).then(() => {
        this.getList()
      })
    },
    showDialog(id, type) {
      this.detailListQuery.condition.commentId = id
      this.detailListQuery.condition.contentType = type
      this.dialogVisible = true
      this.getDetailList()
    },
    getDetailList() {
      reportDetailList(this.detailListQuery).then(res => {
        this.reportDetail = res.records
        this.detailTotal = res.total
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.report {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
