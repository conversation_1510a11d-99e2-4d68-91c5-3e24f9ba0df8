<template>
  <div class="app-container">
    <div class="search-column">
      <Search :has-audit-status="true" @search="search" />
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='auditStatus'">
            <span>{{ scope.row[col.prop] | auditStatusFilter }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="100">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus===1" type="text" @click="checkFn(row.liveId)">审核</el-button>
          <el-button type="text" @click="checkHistoryFn(row.liveId)">审核历史</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <!-- 审核弹窗 -->
    <el-dialog title="直播审核" :visible.sync="checkVisible" width="1000px" top="20px">
      <div class="liveCheck">
        <h3>直播方式</h3>
        <el-card v-if="liveDetailData.liveMethod==='网页直播'" style="width:200px;margin-left:120px;">
          <div slot="header">
            <span>网页直播</span>
          </div>
          <div>实时网页直播，支持课件讲课、屏幕共享，适用于知识讲解类的网页直播</div>
        </el-card>
        <el-card v-else style="width:200px;margin-left:120px;">
          <div slot="header">
            <span>OBS/推流直播</span>
          </div>
          <div>适用于活动拍摄、门诊拍摄、查房拍摄、会场拍摄、录播直播等直播</div>
        </el-card>
        <br>
        <h3>基本信息</h3>
        <el-form label-width="120px">
          <el-form-item label="讲师：">
            <table class="costRulesTable" border cellpadding="0" style="border-collapse:collapse;">
              <tr style="background-color:#d7d7d7;">
                <td>讲师</td>
                <td>职称</td>
                <td>单位</td>
                <td>科室/部门</td>
              </tr>
              <tr>
                <td>{{ liveDetailData.lecturer.realName }}</td>
                <td>{{ liveDetailData.lecturer.academic }}</td>
                <td>{{ liveDetailData.lecturer.company }}</td>
                <td>{{ liveDetailData.lecturer.department }}</td>
              </tr>
            </table>
          </el-form-item>
          <el-form-item label="直播类型：">{{ liveDetailData.liveType }}</el-form-item>
          <el-form-item label="直播主题：">{{ liveDetailData.title }}</el-form-item>
          <el-form-item label="直播封面：">
            <el-image style="width: 300px" fit="cover" :src="liveDetailData.coverImg" :preview-src-list="[liveDetailData.coverImg]" />
          </el-form-item>
          <el-form-item label="直播时间：">{{ liveDetailData.startTime }}</el-form-item>
          <el-form-item label="APP 直播详情：">
            <el-image v-for="item in liveDetailData.content" :key="item.picId" style="width: 300px" fit="cover" :src="item.picImg" :preview-src-list="[item.picImg]" />
          </el-form-item>
          <el-form-item label="分享海报：">
            <el-image style="width: 300px" fit="cover" :src="liveDetailData.shareBehindImg" :preview-src-list="[liveDetailData.shareBehindImg]" />
          </el-form-item>
        </el-form>
        <br>
        <h3>推广设置</h3>
        <el-form label-width="120px">
          <el-form-item label="推广类型：">{{ liveDetailData.promotionType }}</el-form-item>
          <el-form-item label="推广人数：">{{ liveDetailData.promotionNum }}</el-form-item>
          <el-form-item v-if="liveDetailData.liveScopeCondition&&liveDetailData.liveScopeCondition.length" label="推广条件：">
            <el-table :data="liveDetailData.liveScopeCondition" style="width: 750px;">
              <el-table-column prop="weight" label="优先级" width="80" />
              <el-table-column prop="val" label="条件" width="670">
                <template slot-scope="{row}">
                  <template v-if="row.property==='area'">
                    <el-cascader
                      ref="cascader1"
                      v-model="row.ids"
                      :options="areaList"
                      disabled
                      :props="{
                        value:'areaId',
                        label:'name',
                        children:'childList',
                        emitPath: false,
                        multiple:true
                      }"
                      style="width:650px"
                    />
                  </template>
                  <template v-else>
                    <span>{{ row.val }}</span>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align:center;">
        <el-button @click="checkVisible = false">取 消</el-button>
        <el-button type="danger" @click="checkRejuct()">不通过</el-button>
        <el-button type="primary" @click="checkPass()">通 过</el-button>
      </div>
    </el-dialog>

    <!-- 审核不通过弹窗 -->
    <el-dialog title="审核说明" :visible.sync="rejuctVisible" width="600px">
      <div>
        <el-form label-width="80px">
          <el-form-item label="审核说明:">
            <span><i class="el-icon-warning-outline" />  请填写不通过的说明，以便修正</span>
            <el-input
              v-model="explain"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="rejuctVisible = false">取 消</el-button>
        <el-button type="primary" @click="rejuctConfirm()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 审核历史 弹窗 -->
    <el-dialog title="审核历史" :visible.sync="hisVisible" width="90vw">
      <el-table :data="hisList" border stripe>
        <el-table-column v-for="col in hisListColumnList" :key="col.id" v-bind="col">
          <template slot-scope="scope">
            <template v-if="col.prop==='res'">
              <span>{{ scope.row[col.prop]===0?'不通过':'通过' }}</span>
            </template>
            <template v-else>
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <Pagination class="text-center" :page-size="hisQuery.pager.pageSize" :total="hisTotal" :page="hisQuery.pager.page" @pagination="hisPagination" />

    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { liveAuditList, liveDetail, liveAudit, liveAuditRecordList } from '@/api/liveManage'
import Search from '../components/search.vue'
import { getAreaTree } from '@/api/area' // 区域树

export default {
  name: 'LiveCheck',
  components: { Pagination, Search },
  filters: {
    auditStatusFilter(val) {
      const arr = ['', '待审核', '审核不通过', '审核通过']
      return arr[val]
    }
  },
  data() {
    return {
      tableColumnList: Object.freeze([
        { id: 0, label: '直播ID', align: 'center', prop: 'liveId', width: '100px' },
        { id: 2, label: '直播主题', align: 'center', prop: 'title' },
        { id: 3, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 4, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 5, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 6, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 7, label: '推广类型', align: 'center', prop: 'promotionType' },
        { id: 8, label: '推广人数', align: 'center', prop: 'promotionNum' },
        { id: 9, label: '直播时间', align: 'center', prop: 'startTime', sortable: true, width: '160px' },
        { id: 10, label: '提交审核时间', align: 'center', prop: 'createTime', sortable: true, width: '160px' },
        { id: 11, label: '审核状态', align: 'center', prop: 'auditStatus', sortable: true }
      ]),
      hisListColumnList: Object.freeze([
        { id: 2, label: '直播主题', align: 'center', prop: 'title' },
        { id: 3, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 4, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 5, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 6, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 7, label: '推广类型', align: 'center', prop: 'promotionType' },
        { id: 8, label: '推广人数', align: 'center', prop: 'promotionNum' },
        { id: 9, label: '直播时间', align: 'center', prop: 'startTime', width: '160px' },
        { id: 10, label: '提交审核时间', align: 'center', prop: 'createTime', width: '160px' },
        { id: 11, label: '审核人', align: 'center', prop: 'auditor' },
        { id: 12, label: '审核时间', align: 'center', prop: 'auditTime', width: '160px' },
        { id: 13, label: '审核结果', align: 'center', prop: 'res' },
        { id: 14, label: '审核说明', align: 'center', prop: 'opinion' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      // 直播审核弹窗
      liveId: '',
      checkVisible: false,
      liveDetailData: {
        lecturer: {}
      },
      rejuctVisible: false,
      explain: '',
      postData: {},
      // 请求参数
      hisQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      hisTotal: 0,
      hisList: [],
      hisVisible: false,
      areaList: []
    }
  },
  created() {
    this.getList()
    getAreaTree().then(res => {
      this.areaList = this.clearNullChildList(res, 'childList')
    })
  },
  methods: {
    search(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.pager.page = 1
      this.getList()
    },
    getList() {
      liveAuditList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    // 审核-打开弹窗
    checkFn(liveId) {
      this.liveId = liveId
      liveDetail(liveId).then(res => {
        this.liveDetailData = res
        this.postData.promotionType = res.promotionType
        this.postData.promotionNum = res.promotionNum
        this.postData.liveScopeCondition = res.liveScopeCondition
        this.checkVisible = true
      })
    },
    // 审核历史-打开弹窗
    checkHistoryFn(liveId) {
      this.hisQuery.condition.liveId = liveId
      this.hisQuery.pager.page = 1
      liveAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
        this.hisVisible = true
      })
    },
    // 审核历史-分页
    hisPagination(val) {
      this.hisQuery.pager = val
      liveAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
      })
    },
    // 审核-不通过
    checkRejuct() {
      this.explain = ''
      this.rejuctVisible = true
    },
    // 审核-通过
    checkPass() {
      const query = {
        liveId: this.liveId,
        status: 1
      }
      liveAudit(query).then(res => {
        this.$router.push({ name: 'CheckDetail', query: { liveId: this.liveId, ...this.postData }})
      })
    },
    // 不通过-提交
    rejuctConfirm() {
      if (this.explain) {
        const query = {
          explain: this.explain,
          liveId: this.liveId,
          status: 2
        }
        liveAudit(query).then(res => {
          this.rejuctVisible = false
          this.checkVisible = false
          this.getList()
        })
      } else {
        this.$message.warning('请填写审核说明')
      }
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.costRulesTable{
  td{
    width: 150px;
    text-align: center;
  }
}
.liveCheck{
  padding-left: 50px;
  height: 70vh;
  overflow-y: auto;
}
</style>
