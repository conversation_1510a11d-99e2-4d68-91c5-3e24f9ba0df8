<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="5">
        <div class="content-left">
          <div class="tree-title">
            <span>资源分类</span>
            <div v-if="showSelect">
              <el-button type="text" @click="cancelSelect">取消选择</el-button>
            </div>
          </div>
          <el-scrollbar wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container">
            <Tree
              ref="tree"
              :type="categoryType"
              :platform-type="tableQuery.condition.platform"
              :highlight-current="highlightCurrent"
              @nodeAddPlatForm="handleNodeAddPlatForm"
              @nodeClick="handleTreeNodeClick"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <div class="search-column">
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.type">
              <el-option v-for="item in courseTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-input v-model="tableQuery.condition.keyword" placeholder="根据左侧查询方式对应关键字" style="width: 240px" clearable @change="init">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
            </el-input>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.platform" @change="init">
              <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.status" @change="init">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.memberLevel" placeholder="会员等级" clearable @change="init">
              <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
            </el-select>
          </div>
          <div class="search-column__item fr">
            <el-button v-if="selectedList.length" type="primary" @click="batchSetMemberLevel">批量设置会员等级</el-button>
            <el-button v-if="tableQuery.condition.platform ===1 && havChild" type="primary" @click="handleAddcourse">增加教程</el-button>
            <el-button v-if="[1, 2].includes(tableQuery.condition.platform)" :disabled="!selectedList.length" type="primary" @click="editCategory({})">修改分类</el-button>
          </div>
        </div>

        <el-table :data="courseList" border stripe @selection-change="onSelectChange">
          <el-table-column type="selection" fixed="left" />
          <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.filter === 'name'">
                <span class="course-links" @click="handleCourseDetail(scope.row)">{{ scope.row[col.prop] }}</span>
              </template>
              <template v-else-if="col.filter === 'cateName'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop] | filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else-if="col.filter == 'introduction'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop] | filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else-if="col.type==='image'">
                <el-image style="width: 100px" :src="scope.row[col.prop]" @click="preview(scope.row[col.prop])" />
              </template>
              <template v-else-if="col.filter==='platform'">
                <span>{{ scope.row.platform|platformFmt }}</span>
              </template>
              <template v-else-if="col.filter==='status'">
                <span>{{ scope.row.status|statusFmt }}</span>
              </template>
              <template v-else>
                <span>
                  {{ scope.row[col.prop] }}
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="200">
            <template slot-scope="{row}">
              <el-button v-if="row.status === 1 || row.status === 3" size="mini" type="text" @click="handleUpdateStatus(row)">上架</el-button>
              <el-button v-if="row.status === 2 && tableQuery.condition.platform ===1" size="mini" type="text" @click="handleUpdateStatus(row)">下架</el-button>
              <el-button size="mini" type="text" @click="setMemberLevel(row)">设置会员等级</el-button>
              <el-button v-if="[1, 2].includes(tableQuery.condition.platform)" size="mini" type="text" @click="editCategory(row)">修改分类</el-button>
              <el-button v-if="tableQuery.condition.platform ===1 && havChild" size="mini" type="text" @click="setSort(row)">排序</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-dialog :visible.sync="dialogImgShow">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

    <el-dialog title="增加教程" width="800px" :visible.sync="addcourseVisible" :close-on-click-modal="false" :destroy-on-close="true" @close="addcourseCancel">
      <div class="search-column">
        <div class="search-column__item">
          <div class="search-column__label">教程名称：</div>
          <div class="search-column__inner">
            <el-input v-model="addcourseTableQuery.condition.keyword" placeholder="请输入教程名称" @change="search" @keydown="search" />
          </div>
        </div>
      </div>
      <div v-if="noQuery" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
        <span>请输入教程名称查询</span>
      </div>
      <div v-else>
        <el-table ref="singleTable" :data="addcourseTableData" border>
          <el-table-column v-for="col in addcourseTableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.filter === 'cateName'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop] | filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else-if="col.filter==='status'">
                <span>{{ scope.row.status|statusFmt }}</span>
              </template>
              <template v-else>
                <span>
                  {{ scope.row[col.prop] }}
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="100">
            <template slot-scope="{row}">
              <el-button v-if="courseIdList.includes(row.courseId)" size="mini" type="text" @click="handleCloseSelect(row)">已选择</el-button>
              <el-button v-else size="mini" type="text" @click="handleSelect(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- pagination -->
        <Pagination class="text-center" :total="addcourseTotal" :page="addcourseTableQuery.pager.page" @pagination="handleAddcoursePagination" />

        <span slot="footer" class="dialog-footer">
          <el-button @click="addcourseCancel">取 消</el-button>
          <el-button type="primary" @click="addcourseConfirm">确 定</el-button>
        </span>
      </div>
    </el-dialog>

    <el-dialog title="修改分类" width="600px" :visible.sync="editCategoryVisible" :destroy-on-close="true">
      <el-form ref="categoryForm" :model="categoryForm" label-width="120px">
        <el-form-item v-if="showCheckCategory" label="已选分类：" style="line-height: 25px;">
          <div v-for="item in categoryNameArr" :key="item+1" style="line-height: 20px !important;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 400px;">
                {{ item }}{{ item?'；': '' }}
              </div>
              <span>{{ item | filterFont }}{{ item?'；': '' }}</span>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="资源分类：" prop="sort">
          <el-cascader
            ref="elcascader"
            v-model="categoryForm.cateIdList"
            placeholder="选择分类"
            :options="platformTreeList"
            :props="platformProps"
            :show-all-levels="true"
            clearable
            filterable
            collapse-tags
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editCategoryCancel">取 消</el-button>
        <el-button type="primary" @click="editCategoryConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改排序值" width="500px" :visible.sync="editSortVisible" :destroy-on-close="true">
      <el-form ref="sortForm" :model="sortForm" label-width="120px">
        <el-form-item label="排序值" prop="sort">
          <el-input-number v-model="sortForm.sort" :min="0" :max="99999" @change="onNumChange($event,'sort')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sortCancel">取 消</el-button>
        <el-button type="primary" @click="sortConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="isBatchSetMemberLevel ? '批量设置会员等级' : '设置会员等级'" width="500px" :visible.sync="memberLevelDialogVisible" :destroy-on-close="true">
      <el-form ref="memberLevelForm" :model="memberLevelForm" label-width="120px">
        <el-form-item label="会员等级" prop="memberLevel" :rules="[{ required: true, message: '请选择会员等级', trigger: 'change' }]">
          <el-select v-model="memberLevelForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isBatchSetMemberLevel && selectedList.length >= tableQuery.pager.pageSize" label="">
          <el-checkbox v-model="memberLevelForm.crossPage" label="跨页批量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="memberLevelCancel">取 消</el-button>
        <el-button type="primary" @click="memberLevelConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <Pagination class="text-center" :limit="tableQuery.limit" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <div>
      <CourseDetail :id="courseInfoId" :show.sync="showDetail" @close="closeDetail" />
    </div>
  </div>
</template>

<script>
import {
  getCourseList,
  addCourse,
  coursePutaway,
  setCourseSort,
  setCourseMemberLevel
} from '@/api/validManage'
import CourseDetail from './components/courseDetail.vue'
import Pagination from '@/components/Pagination'
import { getCategoryTreeList } from '@/api/category'
import { getVipLevelList } from '@/api/vip'
import Tree from '../components/platformTree.vue'

export default {
  name: 'CourseList',
  components: { Pagination, Tree, CourseDetail },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    },
    statusFmt(v) {
      const arr = ['', '草稿', '上架', '下架']
      return arr[v]
    },
    platformFmt(v) {
      const arr = ['', '平台资源', '单位资源']
      return arr[v]
    }
  },
  data() {
    return {
      // 教程搜索类型
      courseTypeList: Object.freeze([
        { label: '教程名称', value: 1 },
        { label: '创建者名称', value: 2 },
        { label: '单位名称', value: 3 }
      ]),
      // 资源类型
      platformList: Object.freeze([
        { label: '平台资源', value: 1 },
        { label: '单位资源', value: 2 }
      ]),
      // 状态搜索类型
      statusList: Object.freeze([
        { label: '上架', value: 2 },
        { label: '下架', value: 3 }
      ]),
      // 表格表头
      tableColumnList: [
        { id: 0, prop: 'courseId', label: 'ID', align: 'center' },
        {
          id: 1,
          prop: 'name',
          label: '教程名称',
          width: '200',
          align: 'center',
          filter: 'name'
        },
        {
          id: 2,
          prop: 'imgUrl',
          label: '封面',
          align: 'center',
          width: '110',
          type: 'image'
        },
        {
          id: 7,
          prop: 'cateName',
          label: '资源分类',
          width: '140',
          align: 'center',
          filter: 'cateName'
        },
        { id: 13, label: '资源类型', align: 'center', filter: 'platform' },
        { id: 15, label: '会员等级', align: 'center', prop: 'memberLevelName' },
        { id: 4, label: '教程简介', width: '140', align: 'center', prop: 'introduction', filter: 'introduction' },
        { id: 8, label: '创建者名称', align: 'center', prop: 'author' },
        { id: 9, label: '单位名称', align: 'center', prop: 'orgName' },
        { id: 12, label: '状态', align: 'center', filter: 'status' },
        { id: 10, label: '创建时间', align: 'center', prop: 'createTime' }
      ],
      // 请求参数
      tableQuery: {
        condition: {
          keyword: '',
          type: 1,
          status: 2,
          cateId: '',
          platform: 1,
          memberLevel: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 教程列表
      courseList: [],
      total: 0,
      dialogFormShow: false,
      selectedList: [],
      form: {
        basePraises: 0,
        baseHits: 0
      },
      dialogImageUrl: '',
      dialogImgShow: false,
      curTarget: {},
      categoryType: 2,
      highlightCurrent: false, // 是否开启高亮
      showSelect: false, // 是否选中分类
      showDetail: false,
      courseInfoId: '',
      sortForm: {
        sort: 0,
        courseId: '',
        cateId: ''
      },
      editSortVisible: false, // 排序弹窗
      showCheckCategory: false,
      categoryNameArr: [],
      categoryForm: {
        cateIdList: []
      },
      platformTreeList: [],
      platformProps: {
        label: 'name',
        value: 'categoryId',
        children: 'children',
        expandTrigger: 'hover',
        multiple: true,
        emitPath: false
      },
      havChild: false,
      editCategoryVisible: false, // 修改分类弹窗
      noQuery: true,
      addcourseTableData: [],
      // 表格表头
      addcourseTableColumnList: Object.freeze([
        { id: 1, label: '教程名称', align: 'center', prop: 'name' },
        {
          id: 2,
          prop: 'cateName',
          label: '教程分类',
          width: '140',
          align: 'center',
          filter: 'cateName'
        },
        { id: 3, label: '状态', align: 'center', filter: 'status' }
      ]),
      // 请求参数
      addcourseTableQuery: {
        condition: {
          type: 1,
          keyword: '',
          platform: 1,
          notInCateId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      addcourseTotal: 0,
      addcourseVisible: false,
      courseIdList: [], // 教程id 集合
      cateIdList: [], // 分类id 集合
      // 会员等级相关
      memberLevelList: [], // 会员等级列表
      memberLevelDialogVisible: false, // 设置会员等级弹窗
      memberLevelForm: {
        memberLevel: '',
        courseIds: [],
        crossPage: false
      },
      isBatchSetMemberLevel: false // 是否批量设置会员等级
    }
  },
  created() {
    // 获取资源分类树数据
    getCategoryTreeList(0).then(res => {
      this.platformTreeList = res
    })
    // 获取会员等级列表
    getVipLevelList().then(res => {
      this.memberLevelList = res || []
    })
    this.init()
  },
  methods: {
    // 上下架操作
    handleUpdateStatus(row) {
      this.$confirm(
        row.status === 2
          ? '下架以后该教程将不能正常播放，确认下架该教程？'
          : '确认上架该教程？',
        '温馨提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          closeOnPressEscape: false
        }
      ).then(() => {
        coursePutaway(row.courseId, row.status === 2 ? 3 : 2).then(res => {
          row.status === 2
            ? this.$message.success('下架成功')
            : this.$message.success('上架成功')
          this.init()
        })
      })
    },
    // 播放教程
    handleCourseDetail(row) {
      this.showDetail = true
      this.courseInfoId = row.courseInfoId
    },
    closeDetail(val) {
      this.showDetail = val
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.init(false)
    },
    // 教程列表
    init(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      getCourseList(this.tableQuery).then(res => {
        if (this.havChild) {
          const flag = this.tableColumnList.some(item => {
            return item.id === 14
          })
          if (!flag) {
            this.tableColumnList.unshift({ id: 14, label: '排序', prop: 'listOrder', align: 'center', filter: 'sort' })
          }
        } else {
          this.tableColumnList = this.tableColumnList.filter(item => {
            return item.id !== 14
          })
        }
        this.courseList = res.records
        // 添加会员等级名称显示
        this.courseList.forEach(v => {
          const memberLevelItem = this.memberLevelList.find(i => i.level === v.memberLevel)
          v.memberLevelName = memberLevelItem ? memberLevelItem.name : '-'
        })
        this.total = res.total
      })
    },
    onSelectChange(arr) {
      this.selectedList = arr
    },
    // 增加教程
    search(e) {
      if (!e) {
        this.noQuery = true
      } else {
        getCourseList(this.addcourseTableQuery).then(res => {
          this.noQuery = false
          this.addcourseTableData = res.records
          this.addcourseTotal = res.total
        })
      }
    },
    handleAddcourse() {
      this.addcourseTableQuery.condition.notInCateId = this.tableQuery.condition.cateId
      this.addcourseVisible = true
    },
    addcourseCancel() {
      this.addcourseTableQuery.condition.courseName = ''
      this.addcourseTableQuery.pager.page = 1
      this.addcourseTableQuery.pager.pageSize = 10
      this.addcourseTotal = 0
      this.noQuery = true
      this.courseIdList = []
      this.addcourseVisible = false
    },
    addcourseConfirm() {
      if (this.courseIdList.length < 1) { return this.$message.error('请选择教程') }
      if (this.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 1,
        courseIdList: this.courseIdList,
        cateIdList: this.cateIdList
      }
      addCourse(data).then(res => {
        this.$message.success('添加成功')
        this.addcourseCancel()
        this.init()
      })
    },
    handleAddcoursePagination(val) {
      this.addcourseTableQuery.pager = val
      getCourseList(this.addcourseTableQuery).then(res => {
        this.noQuery = false
        this.addcourseTableData = res.records
        this.addcourseTotal = res.total
      })
    },
    // 取消选择事件
    handleCloseSelect(row) {
      this.courseIdList = this.courseIdList.filter(item => {
        return item !== row.courseId
      })
    },
    // 选择事件
    handleSelect(row) {
      this.courseIdList.push(row.courseId)
    },
    // 修改分类
    editCategory(row) {
      this.courseIdList = []
      if (row && JSON.stringify(row) !== '{}') {
        this.showCheckCategory = true
        this.courseIdList.push(row.courseId)
        this.categoryNameArr = row.cateName.split(';')
        this.categoryForm.cateIdList = row.cateIds
      } else {
        this.showCheckCategory = false
        this.categoryNameArr = []
        this.categoryForm.cateIdList = []
        this.selectedList.forEach(item => {
          this.courseIdList.push(item.courseId)
        })
      }
      this.editCategoryVisible = true
    },
    editCategoryCancel() {
      this.courseIdList = []
      this.cateIdList = []
      this.categoryForm.cateIdList = []
      this.editCategoryVisible = false
    },
    editCategoryConfirm() {
      if (this.courseIdList.length < 1) { return this.$message.error('请选择教程') }
      if (this.categoryForm.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 2,
        courseIdList: this.courseIdList,
        cateIdList: JSON.parse(JSON.stringify(this.categoryForm.cateIdList))
      }
      addCourse(data).then(res => {
        this.$message.success('添加成功')
        this.editCategoryCancel()
        this.init()
      })
    },
    // 设置排序
    setSort(row) {
      this.sortForm.sort = row.listOrder
      this.sortForm.courseId = row.courseId
      this.editSortVisible = true
    },
    sortCancel() {
      this.sortForm.sort = 0
      this.sortForm.courseId = ''
      this.sortForm.cateId = ''
      this.$refs.sortForm.resetFields()
      this.editSortVisible = false
    },
    sortConfirm() {
      if (this.tableQuery.condition.cateId) {
        this.sortForm.cateId = this.tableQuery.condition.cateId
      } else {
        return this.$message.error('请选择分类')
      }
      this.$refs.sortForm.validate(valid => {
        if (valid) {
          setCourseSort(this.sortForm).then(() => {
            this.$message.success('操作成功')
            this.sortCancel()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    onNumChange(v, key) {
      if (key === 'sort') {
        if (!v) {
          this.$nextTick(() => {
            this.sortForm[key] = 0
          })
        }
      } else {
        if (!v) {
          this.$nextTick(() => {
            this.form[key] = 0
          })
        }
      }
    },
    preview(url) {
      this.dialogImageUrl = url
      this.dialogImgShow = true
    },
    cancelSelect() {
      const flag = this.tableColumnList.some(item => {
        return item.id === 14
      })
      if (flag) {
        this.tableColumnList.shift()
      }
      this.tableQuery.condition.cateId = ''
      this.highlightCurrent = false
      this.showSelect = false
      this.havChild = false
      this.init()
      // 刷新分类数据
      this.$refs.tree.onRegionHeaderRefresh()
    },
    // 资源分类添加事件
    handleNodeAddPlatForm(data) {
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.addcourseTableQuery.condition.notInCateId = data.categoryId
      this.addcourseVisible = true
    },
    // 获取当前试题内容
    handleTreeNodeClick(data, node) {
      if (!data.havChild) {
        this.havChild = true
      } else {
        this.havChild = false
      }
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.showSelect = true // 显示取消选择按钮
      this.highlightCurrent = true // 高亮所选中的分类
      this.tableQuery.condition.cateId = data.categoryId
      this.tableQuery.pager.page = 1
      this.init()
    },
    // 设置会员等级
    setMemberLevel(row) {
      this.isBatchSetMemberLevel = false
      this.memberLevelForm.courseIds = [row.courseId]
      this.memberLevelForm.memberLevel = row.memberLevel || ''
      this.memberLevelDialogVisible = true
    },
    // 批量设置会员等级
    batchSetMemberLevel() {
      if (this.selectedList.length === 0) {
        this.$message.warning('请选择要设置的教程')
        return
      }
      this.isBatchSetMemberLevel = true
      this.memberLevelForm.courseIds = this.selectedList.map(item => item.courseId)
      this.memberLevelForm.memberLevel = ''
      this.memberLevelForm.crossPage = false
      this.memberLevelDialogVisible = true
    },
    // 会员等级弹窗取消
    memberLevelCancel() {
      this.memberLevelForm = {
        memberLevel: '',
        courseIds: [],
        crossPage: false
      }
      this.memberLevelDialogVisible = false
    },
    // 会员等级弹窗确认
    memberLevelConfirm() {
      this.$refs.memberLevelForm.validate(valid => {
        if (valid) {
          const params = {
            ...this.tableQuery.condition,
            updateMemberLevel: this.memberLevelForm.memberLevel,
            courseIdList: this.memberLevelForm.courseIds,
            setType: this.memberLevelForm.crossPage ? 1 : 2
          }
          setCourseMemberLevel(params).then(() => {
            this.$message.success('设置成功')
            this.memberLevelCancel()
            this.init()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.course-links {
  color: #409eff;
  cursor: pointer;
}

.content-left {
  padding-top: 15px;
  height: calc(100vh + 220px);

  .tree-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-size: 22px;
      font-weight: bold;
      margin-right: 10px;
    }
  }
  .content-container {
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: #fff;
    &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
      overflow-x: hidden;
    }
    &::v-deep .el-scrollbar__view.p20-scrollbar__view {
      background-color: #fff;
      padding-right: 15px;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
    }
    &::v-deep .el-scrollbar__bar.is-vertical {
      width: 8px;
    }
  }
}
.content-right {
  flex: 8;
  padding-right: 20px;

}
::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
  display:none;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
