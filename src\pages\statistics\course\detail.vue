<template>
  <section class="detail">
    <div class="module">
      <h2>
        课程概况
      </h2>
      <ul>
        <li v-for="item in roughly" :key="item.label" class="roughlyLi">
          <p>{{ item.label }}</p>
          {{ item.value }}
        </li>
      </ul>
    </div>
    <div class="module">
      <h2>
        课程预览
      </h2>
      <div class="preview">
        <el-button type="text" @click="handlePlayVideo()">{{ $route.params.title }}</el-button>
      </div>
    </div>
    <div class="module">
      <h2>
        学习详情
        <el-button type="primary" @click="exportcourseDetailList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
        <Pagination
          :page="queryParams.pager.page"
          :page-size="queryParams.pager.pageSize"
          :total="total"
          @pagination="handlePagination"
        />
      </div>
    </div>

    <el-dialog class="playVideoDialog" :title="playerTitle" :visible.sync="dialogVisible" width="1024px" @close="handlePauseVideo">
      <div class="pos-re">
        <ali-player
          ref="aliplayer"
          :play-style="aliPlayerConfig.playStyle"
          :source="aliPlayerConfig.source"
          :cover="aliPlayerConfig.cover"
          :height="aliPlayerConfig.height"
          :skin-layout="aliPlayerConfig.skinLayout"
          @ready="handleReadyVideo"
          @pause="handlePauseVideo"
          @error="handleError"
        />
      </div>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import AliPlayer from '@/components/Aliplayer'
import { playVideoInfo } from '@/api/validManage'
import { courseDetailList, courseDetailListExport } from '@/api/statistics'
export default {
  components: {
    AliPlayer,
    Pagination
  },
  data() {
    return {
      roughly: [],
      tableColumn: [
        { prop: 'uid', label: 'UID', width: '140' },
        { prop: 'userInfoRespDto.name', label: '姓名', width: '80' },
        { prop: 'userInfoRespDto.phone', label: '手机', width: '100' },
        { prop: 'userInfoRespDto.identityName', label: '身份', width: '160' },
        { prop: 'userInfoRespDto.majorName', label: '专科', width: '160' },
        { prop: 'userInfoRespDto.academicName', label: '职称', width: '160' },
        { prop: 'userInfoRespDto.areaName', label: '区域', width: '160' },
        { prop: 'userInfoRespDto.orgName', label: '单位', width: '230' },
        { prop: 'studyTimeStr', label: '学习时间', width: '160' },
        { prop: 'ip', label: 'IP', width: '' }
      ],
      tableData: [],
      queryParams: {
        condition: 0,
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      // 阿里播放器设置
      dialogVisible: false,
      playerTitle: '',
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            name: 'bigPlayButton',
            align: 'cc'
          },
          {
            name: 'H5Loading',
            align: 'cc'
          },
          {
            name: 'errorDisplay',
            align: 'tlabs',
            x: 0,
            y: 0
          },
          {
            name: 'infoDisplay'
          },
          {
            name: 'tooltip',
            align: 'blabs',
            x: 0,
            y: 56
          },
          {
            name: 'controlBar',
            align: 'blabs',
            x: 0,
            y: 0,
            children: [
              {
                name: 'progress',
                align: 'blabs',
                x: 0,
                y: 44
              },
              {
                name: 'playButton',
                align: 'tl',
                x: 15,
                y: 12
              },
              {
                name: 'timeDisplay',
                align: 'tl',
                x: 10,
                y: 7
              },
              {
                name: 'fullScreenButton',
                align: 'tr',
                x: 10,
                y: 12
              },
              {
                name: 'volume',
                align: 'tr',
                x: 5,
                y: 10
              }
            ]
          }
        ]
      }
    }
  },
  created() {
    this.queryParams.condition = this.$route.params.videoInfoId
    this.roughly = [
      { label: '课程', value: this.$route.params.title },
      { label: '分类', value: this.$route.params.cate },
      { label: '作者', value: this.$route.params.authorInfo },
      { label: '学习人次', value: this.$route.params.studyTimes }
    ]
    this.getcourseDetailList()
  },
  methods: {
  // 播放视频
    handlePlayVideo() {
      playVideoInfo({
        videoId: 0,
        videoInfoId: 0,
        videoFileId: this.$route.params.videoFileId
      }).then(res => {
        if (res.playInfoList && res.playInfoList.length) {
          const sourceUrl = {}
          res.playInfoList.map(v => {
            sourceUrl[v.definition] = v.playURL
          })
          this.aliPlayerConfig.cover = res.coverUrl
          this.aliPlayerConfig.source = JSON.stringify(sourceUrl)
          this.playerTitle = res.videoName
          this.dialogVisible = true
          this.$nextTick(() => {
            this.$refs.aliplayer.init()
          })
        } else {
          this.$message.error('该视频暂时无法播放，请稍后重试！')
        }
      })
    },
    getcourseDetailList() {
      courseDetailList(this.queryParams).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportcourseDetailList() {
      courseDetailListExport(this.queryParams.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(res => {
        if (res !== null && (res.message === null || res.message === '')) {
          this.$message.error('导出失败')
        }
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getcourseDetailList()
    },
    // 阿里云播放器事件
    handleReadyVideo(val) {
      this.player = val
    },
    handlePauseVideo() {
      // this.init()
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  .module {
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      font-size: 20px;
      line-height: 55px;
      background-color: #f9f9f9;
      .el-button {
        height: 35px;
      }
    }
    ul {
      display: flex;
      padding: 24px;
      font-size: 20px;
      font-weight: bold;
      li {
        max-width: 200px;
        margin-right: 100px;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        p {
          font-size: 18px;
          color: #666;
          font-weight: 400;
          margin-bottom: 20px;
        }
      }
    }
    .preview {
      height: 125px;
      padding-left: 24px;
      line-height: 125px;
      .el-button {
        color: #409eff;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .table {
      padding: 18px 24px 0;
      .pagination-container {
        padding: 30px 20px;
      }
      a {
        color: #409eff;
        font-size: 14px;
      }
    }
  }
}
.roughlyLi {
  width: 10%;;
}
</style>
