<template>
  <el-dialog
    title="编辑题目"
    :close-on-click-modal="false"
    :visible.sync="visible"
    width="750px"
    class="exam-dialog"
    :show-close="false"
    @close="close()"
  >
    <el-form :model="form" :rules="rules">
      <el-form-item label="题目标题" prop="title" :label-width="formLabelWidth">
        <el-input v-model="form.title" autocomplete="off" class="bg" maxlength="120" />
      </el-form-item>
      <el-form-item label="题目类型" class="bg" :label-width="formLabelWidth">
        <el-select v-model="form.type" placeholder="请选择题目类型" class="bg" @change="nodeSelectChange">
          <el-option
            v-for="item in questionType"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="题目答案" :label-width="formLabelWidth">
        <div
          v-for="(item, index) in form.paperItems"
          :key="index"
          class="exam-option"
        >
          <el-form-item :prop="`paperItems.${index}.content`" :rules="rules.content">
            <span class="option-index">{{ paperOptionsList[index] }}.</span>
            <el-input v-if="form.type!='RadioJudge'" v-model="item.content" class="option-int" />
            <el-input v-else v-model="item.content" class="option-int" disabled />
            <el-checkbox v-model="item.answerYesNo" @change="handleCheck(form.type, item, index)" />
            <i
              v-if="form.type!='RadioJudge'"
              class="el-icon-remove-outline option-delete"
              @click="deleteOptionItem(index)"
            />
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item v-if="form.type!='RadioJudge'">
        <div class="add-option text-center">
          <el-button icon="el-icon-plus" @click="addOptionItem()">添加题目答案</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { setPaperOptionList } from '@/utils'

export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      paperOptionsList: [],
      form: {
        title: '',
        type: 'RadioButton',
        listOrder: 0,
        paperItems: [
          { content: '', name: 'A', answerYesNo: 0, listOrder: 0 },
          { content: '', name: 'B', answerYesNo: 0, listOrder: 1 },
          { content: '', name: 'C', answerYesNo: 0, listOrder: 2 },
          { content: '', name: 'D', answerYesNo: 0, listOrder: 3 }
        ]
      },
      rules: {
        title: [
          { required: true, message: ' ', trigger: 'blur' }
        ],
        content: [
          { required: true, message: ' ', trigger: 'blur' }
        ]
      },
      visible: false,
      formLabelWidth: '80px',
      optionIndex: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      opIndexArray: [],
      optionIndexLength: 0,
      questionType: [
        { label: '单选题', value: 'RadioButton' },
        { label: '多选题', value: 'CheckBox' },
        { label: '判断题', value: 'RadioJudge' }
      ]
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (this.isEdit) {
          if (val.paperItems && val.paperItems.length) {
            val.paperItems.forEach(item => {
              item.answerYesNo = item.answerYesNo === 1
            })
          }
          this.form = JSON.parse(JSON.stringify(val))
          this.opIndexArray = setPaperOptionList()
          this.optionIndexLength = this.form.paperItems.length
        }
      }
    }
  },
  created() {
    this.paperOptionsList = setPaperOptionList()
  },
  methods: {
    // 勾选答案
    handleCheck(type, item, index) {
      if (type !== 'CheckBox') {
        this.form.paperItems.forEach((paper, itemIndex) => {
          paper.answerYesNo = index === itemIndex
        })
      }
    },
    close() {
      this.form = {
        title: '',
        type: 'RadioButton',
        listOrder: 0,
        paperItems: [
          { content: '', name: 'A', answerYesNo: 0, listOrder: 0 },
          { content: '', name: 'B', answerYesNo: 0, listOrder: 1 },
          { content: '', name: 'C', answerYesNo: 0, listOrder: 2 },
          { content: '', name: 'D', answerYesNo: 0, listOrder: 3 }
        ]
      }
      this.visible = false
    },
    handleCancel() {
      this.$emit('cancelPaperItem')
      this.visible = false
    },
    // 选择题目类型
    nodeSelectChange(data) {
      if (data !== this.formData.type) {
        if (data !== 'RadioJudge') {
          if (this.form.paperItems.length) {
            this.form.paperItems.forEach(item => {
              item.answerYesNo = 0
              item.content = ''
            })
          } else {
            this.form.paperItems = [
              { content: '', name: 'A', answerYesNo: 0, listOrder: 0 },
              { content: '', name: 'B', answerYesNo: 0, listOrder: 1 }
            ]
          }
        } else {
          this.form.paperItems = [
            { content: '对', name: 'A', answerYesNo: 0, listOrder: 0 },
            { content: '错', name: 'B', answerYesNo: 0, listOrder: 1 }
          ]
        }
      } else {
        if (this.formData.paperItems.length === 0) {
          if (data !== 'RadioJudge') {
            this.form.paperItems = [
              { content: '', name: 'A', answerYesNo: 0, listOrder: 0 },
              { content: '', name: 'B', answerYesNo: 0, listOrder: 1 }
            ]
          } else {
            this.form.paperItems = [
              { content: '对', name: 'A', answerYesNo: 0, listOrder: 0 },
              { content: '错', name: 'B', answerYesNo: 0, listOrder: 1 }
            ]
          }
        } else {
          this.form.paperItems = this.formData.paperItems
        }
      }
    },
    addOptionItem() {
      if (this.form.paperItems.length >= 26) {
        this.$message.warning('已超选项上限')
        return
      }
      const _questionOption = this.form.paperItems
      _questionOption.push({

        content: '',
        name: this.optionIndex[_questionOption.length],
        listOrder: _questionOption.length,
        answerYesNo: 0
      })
      // this.$emit("update:form.paperItems", _questionOption);
      this.optionIndexLength++
    },
    deleteOptionItem(index) {
      const _questionOption = this.form.paperItems
      _questionOption.splice(index, 1)
      this.optionIndexLength--
      for (let i = index; i < this.optionIndexLength; i++) {
        _questionOption[i].name = this.opIndexArray[i]
      }
      // this.$emit("update:form.paperItems", _questionOption);
    },
    handleSave() {
      // 校验标题不能为空
      if (this.form.title.replace(/\s*/g, '').length === 0) {
        this.$message.error('题目标题不能为空')
        return
      }
      // 校验多选题答案个数
      if (this.form.type === 'CheckBox') {
        let checkNum = 0
        this.form.paperItems.forEach(item => {
          if (item.answerYesNo) {
            checkNum++
          }
        })
        if (checkNum < 2) {
          this.$message.error('多选题正确答案的个数不能小于两个')
          return
        }
      } else {
        // 单选判断选中个数
        let checkNum = 0
        this.form.paperItems.forEach(item => {
          if (item.answerYesNo) {
            checkNum++
          }
        })
        if (checkNum !== 1) {
          this.$message.error('请选择一个正确答案')
          return
        }
      }
      // 校验答案不能为空
      const checkContent = this.form.paperItems.some(item => item.content === '')
      if (checkContent) {
        this.$message.closeAll()
        this.$message.error('答案不能为空')
        return
      }
      if (this.form.paperItems.some(item => item.content.replace(/\s*/g, '').length === 0)) {
        this.$message.error('答案不能为空')
        return
      }
      // 校验答案不能重复
      let checkContentRepert = false
      const repeatArr = this.form.paperItems.sort()
      checkContentRepert = this.isRepeat(repeatArr)
      if (checkContentRepert) {
        this.$message.closeAll()
        this.$message.error('答案不能重复')
        return
      }
      this.form.paperItems.forEach(item => {
        item.answerYesNo = item.answerYesNo ? 1 : 0
        item.content = item.content.trim()
      })
      this.$emit('savePaperItem', this.form)
    },
    isRepeat(repeatArr) {
      var hash = {}
      for (var i in repeatArr) {
        if (hash[repeatArr[i].content]) {
          return true
        }
        hash[repeatArr[i].content] = true
      }
      return false
    }
  }
}
</script>

<style lang="scss" scope>
.exam-dialog {
  &::v-deep .el-dialog__footer {
    background: #f0f2f5;
  }
  .bg {
    width: 100%;
    input {
      background: #f5f7fa;
    }
  }
  .el-form-item {
    margin-bottom: 18px;
  }
  .add-option {
    button {
      border: 1px dashed #44b9a2;
      color: #44b9a2;
    }
  }
}
.exam-option {
  margin-bottom: 10px;
  .option-int {
    margin-right: 10px;
    width: 530px;
  }
  .option-int-err {
    margin-right: 10px;
    width: 530px;
    border: 1px solid red;
    border-radius: 5px;
  }
  .option-index {
    display: inline-block;
    width: 12px;
    margin-right: 10px;
  }
  .option-delete {
    vertical-align: middle;
    margin-left: 5px;
    margin-top: -2px;
    font-size: 16px;
    color: #fe3737;
  }
}
</style>
