<template>
  <div class="app-container">
    <el-tree
      ref="tree"
      class="mycs-tree"
      node-key="categoryId"
      :highlight-current="onHighlightCurrent"
      :props="{ label: 'name', isLeaf:'isLeaf' }"
      lazy
      :load="loadNode"
      :accordion="true"
      :expand-on-click-node="false"
      @node-click="handleClick"
    />
  </div>
</template>

<script>
export default {
  name: 'NewCategoryTree',
  props: {
    highlightCurrent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      onHighlightCurrent: this.highlightCurrent
    }
  },
  watch: {
    highlightCurrent() {
      this.onHighlightCurrent = this.highlightCurrent
    }
  },
  methods: {
    // 懒加载
    loadNode(node, resolve) {
      this.$emit('loadNode', node, resolve)
    },
    // 节点点击
    handleClick(data, node) {
      this.onHighlightCurrent = true
      data.level = node.level
      this.$emit('nodeClick', data, node)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
}

.mycs-tree ::v-deep .el-tree-node > .el-tree-node__children,
.mycs-tree ::v-deep .el-tree-node.is-expanded > .el-tree-node__children {
  overflow: visible;
}

.mycs-tree ::v-deep .el-tree-node__content{
  height: 32px;
}

.mycs-tree ::v-deep .el-tree-node__expand-icon{
  font-size: 18px;
}
</style>
