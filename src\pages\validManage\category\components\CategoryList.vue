<template>
  <div>
    <el-card v-if="list.length>0" class="category-card">
      <h3>{{ level | levelFilter }}级分类</h3>
      <ul>
        <li
          v-for="(item, index) in list"
          :key="index"
          :class="{'active': index === currentIndex ,'isHighlight':item.isHighlight}"
          @click="selectCategory(item, index)"
          @mousemove="hoverIndex=index"
        >
          <div>
            <el-tooltip effect="dark" :content="item.name" placement="right">
              <span>{{ item.name | nameFmt }} ({{ item.videoNum }})</span>
            </el-tooltip>
            <div>
              <i v-show="index === hoverIndex || index === currentIndex" style="font-size:16px;" class="el-icon-more" @click.stop="menu(item,index)" />
              <i v-if="item.havChild" class="el-icon-arrow-right" />
            </div>
          </div>
          <div @click.stop="">
            <el-switch
              v-model="item.showStatus"
              :active-value="1"
              :inactive-value="0"
              @change="updateShow($event,item.categoryId)"
            />
          </div>
        </li>
      </ul>
    </el-card>

    <!-- 菜单 -->
    <el-dialog
      :title="info.name"
      :visible.sync="rcDialog"
      width="20%"
      center
    >
      <RightClick :info="info" @getList="getList" @changeOrder="changeOrder" />
    </el-dialog>

    <!-- 修改排序 -->
    <el-dialog
      title="修改排序"
      :visible.sync="orderDialog"
      width="400px"
      top="50px"
      center
    >
      <div class="orderListBox">
        <h3>当前分类：{{ info.name }}</h3>
        <ul>
          <li
            v-for="(item, index) in list"
            :key="index+'order'"
          >
            <el-tooltip effect="dark" :content="item.name" placement="top">
              <span>{{ item.name | nameFmt }}</span>
            </el-tooltip>
            <el-button v-if="info.categoryId!==item.categoryId" size="mini" @click="choseOrder(item,index)">选择</el-button>
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import RightClick from './rightClickDialog'
import { cateList, cateChangeOrder, updateShowStatus } from '@/api/resourceCategory'

export default {
  name: 'CategoryList',
  components: { RightClick },
  filters: {
    levelFilter(val) {
      const arr = ['', '一', '二', '三', '四']
      return arr[val]
    },
    nameFmt(v) {
      if (v.length > 16) {
        return v.substring(0, 15) + '..'
      } else {
        return v
      }
    }
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      currentIndex: '',
      hoverIndex: -1,
      rcDialog: false,
      orderDialog: false,
      info: {},
      infoIndex: 0,
      choseIndex: 0

    }
  },
  methods: {
    selectCategory(item, i) {
      this.currentIndex = i
      this.$emit('selectCategory', item.havChild, item.categoryId, this.level)
    },
    menu(info, index) {
      this.info = info
      this.infoIndex = index
      this.rcDialog = true
    },
    getList(pid, level, type) {
      // 添加同级分类时，添加成功后做排序操作再刷新列表
      if (type && type === '添加同级分类') {
        cateList({ parentId: pid, keywork: '' }).then(res => {
          this.add_and_order(res)
        })
      } else {
        this.$emit('getList', pid, level)
        this.rcDialog = false
      }
    },
    add_and_order(list) {
      const arr = list.map((item, index) => {
        return { categoryId: item.categoryId, listOrder: index }
      })
      const infoObj = arr[arr.length - 1]
      arr.pop()
      arr.splice(this.infoIndex + 1, 0, infoObj)
      arr.forEach((item, index) => {
        item.listOrder = index
      })
      cateChangeOrder(arr).then(() => {
        this.rcDialog = false
        this.$emit('getList', this.info.parentId, this.info.level)
      })
    },
    changeOrder() {
      this.rcDialog = false
      this.orderDialog = true
    },
    choseOrder(item, index) {
      const arr = this.list.map((item, index) => {
        return { categoryId: item.categoryId, listOrder: index }
        // return { name: item.name, categoryId: item.categoryId, listOrder: index }
      })
      const infoObj = arr[this.infoIndex]
      arr.splice(this.infoIndex, 1)
      if (this.infoIndex > index) {
        arr.splice(index + 1, 0, infoObj)
      } else {
        arr.splice(index, 0, infoObj)
      }
      arr.forEach((item, index) => {
        item.listOrder = index
      })
      this.$confirm(`是否将当前分类移至（${item.name}）后面`, '提示', {
        confirmButtonText: '确 定',
        cancelButtonText: '取 消',
        type: 'warning'
      }).then(() => {
        cateChangeOrder(arr).then(() => {
          this.$message.success('修改成功')
          this.orderDialog = false
          this.$emit('getList', this.info.parentId, this.info.level)
        })
      })
    },
    updateShow(e, id) {
      updateShowStatus({ id }).then(() => {
        //
      })
    }
  }
}
</script>

<style scoped lang="scss">
.category-card {
  width: 300px;
  margin-top: 20px;
  margin-right: 50px;
  float: left;
}
.orderListBox{
  width: 360px;
  height: calc(80vh);
  ul{
    width: 340px;
    margin-left: 10px;
  }
}

ul {
  font-size: 14px;
  padding: 0;
  height: calc(70vh);
  overflow-y: auto;
  li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-right: 3px solid transparent;
    &:hover,&.active {
      cursor: pointer;
      background-color: #ecf8ff;
      color: #409eff;
    }
    &.isHighlight{
      color: #F56C6C;
    }
  }

  &::-webkit-scrollbar {
    width: 6px;
  }
  &:hover::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #79bbff;
  }
  &:hover::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background: rgba(242, 248, 247, 0.904);
  }
}

.hoverBtn{
  font-size: 20px;
  font-weight: bold;
  // height: 10px;
}
</style>
