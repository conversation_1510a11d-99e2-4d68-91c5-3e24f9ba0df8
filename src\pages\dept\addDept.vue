<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="部门库名称" prop="name">
        <el-input v-model="form.name" maxlength="10" clearable placeholder="请输入部门库名称" />
      </el-form-item>
      <el-form-item label="部门库关键字" prop="packKeyword">
        <el-input v-model="form.packKeyword" type="textarea" clearable placeholder="请输入部门关键字，可模糊匹配，多个以逗号分隔" />
      </el-form-item>
      <el-form-item>
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { addDept, editDept } from '@/api/dept'

export default {
  name: 'AddDept',
  data() {
    return {
      isEdit: this.$route.query.isEdit,
      form: {
        departmentPackId: '',
        name: '',
        packKeyword: ''
      },
      rules: {
        name: [{ required: true, message: '请输入部门库名称', trigger: 'blur' }],
        packKeyword: [{ required: true, message: '请输入关键字，可模糊匹配，多个以逗号分隔', trigger: 'blur' }]
      }
    }
  },
  created() {
    if (this.isEdit) {
      this.form = this.$route.query.detail
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },
    submitForm(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          const API = this.isEdit ? editDept : addDept
          API(this.form).then(() => {
            this.$message.success('操作成功')
            this.goBack()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding-top: 80px;
  display: flex;
  justify-content: center;
  .el-input {
    width: 500px;
  }
}
</style>
