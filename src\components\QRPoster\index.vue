<template>
  <el-dialog title="海报" :before-close="beforeClose" :visible.sync="visible" top="8vh" style="text-align:center">
    <div>

      <div ref="canvasDom" class="canvasDom">
        <img id="bgc" ref="bgc" class="bgc">
        <div class="qr">
          <div id="qrcode" />
          <div>
            长按扫码或扫描二维码<br>
            查看详情
          </div>
        </div>
      </div>

    </div>
    <span slot="footer">
      <el-button @click="cancle">关闭</el-button>
      <el-button type="primary" @click="download">下 载</el-button>
    </span>
  </el-dialog>

</template>

<script>
import html2canvas from 'html2canvas'
import QRCode from 'qrcodejs2'

export default {
  props: {
    url: {
      type: String,
      default: ''
    },
    qrurl: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    picTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getImage(this.url, 'bgc')
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.qrcode()
      this.getImage(this.url, 'bgc')
    })
  },
  methods: {
    qrcode() {
      const qrcode = new QRCode('qrcode', {
        width: 110,
        height: 110,
        text: this.qrurl, // 二维码地址
        colorDark: '#000',
        colorLight: '#fff'
      })
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    },
    cancle() {
      this.$emit('update:visible', false)
    },
    getImage(url, imgId) {
      var xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      xhr.responseType = 'blob'
      xhr.setRequestHeader('cache-control', 'no-cache')
      xhr.send()
      xhr.onload = function() {
        if (this.status === 200) {
          document.getElementById(imgId).src = URL.createObjectURL(
            this.response
          )
        }
      }
    },
    download() {
      // 设置放大倍数
      const scale = window.devicePixelRatio
      // 传入节点原始宽高
      const width = this.$refs.canvasDom.offsetWidth
      const height = this.$refs.canvasDom.offsetHeight
      // return
      // html2canvas配置项
      const ops = {
        scale,
        width,
        height,
        useCORS: true,
        allowTaint: false
      }
      html2canvas(this.$refs.canvasDom, ops).then(canvas => {
        // 返回图片的二进制数据
        const imgurl = canvas.toDataURL('image/png')

        const a = document.createElement('a')
        a.setAttribute('href', imgurl)
        a.setAttribute('download', `${this.picTitle}.png`)
        a.click()
        this.$message.success('下载成功')
        this.$emit('update:visible', false)
      })
    }
  }
}
</script>

<style lang="scss">
.canvasDom {
  max-width: 375px;
  margin: 10px auto;
  border: 1px solid #efefef;
  .bgc {
    max-width: 375px;
    // width: 100%;
    // height: 600px;
    // object-fit: cover;
    // object-position: center;
  }
  .qr {
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    div {
      margin-left: 10px;
      font-size: 18px;
      font-weight: bold;
      color: #333333;
    }
  }
}
.el-dialog .el-dialog__body{
  padding: 0;
}
</style>
