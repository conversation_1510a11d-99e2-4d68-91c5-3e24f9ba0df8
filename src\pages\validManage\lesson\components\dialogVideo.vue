<template>
  <el-dialog top="7vh" title="选择视频" :visible.sync="visible" :before-close="beforeClose" width="850px">
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="condition.type" filterable clearable @change="init">
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="根据左侧查询方式对应关键字" clearable @change="init">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
        </el-input>
      </div>
    </div>

    <a-table :columns="columns" :data="list" border stripe max-height="450px" size="mini">
      <template slot="status" slot-scope="{row}">
        {{ row.status|statusFmt }}
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="toggleSelect(row)">{{ judge(row) ? '取消选择' : '选择' }}</el-button>
      </template>
    </a-table>

    <Pagination :total="total" :page="pager.page" @pagination="handlePagination" />

    <div slot="footer" class="dialog-footer text-center">
      <el-button size="small" @click="cancel">取 消</el-button>
      <el-button size="small" type="primary" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import ATable from '@/components/ATable'
import Pagination from '@/components/Pagination'
import { courseVideoList } from '@/api/course'

export default {
  name: 'DialogVideo',
  components: { ATable, Pagination },
  filters: {
    statusFmt(v) {
      switch (v) {
        case 2:
          return '上架'
        case 3:
          return '下架'
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      condition: {
        type: '2',
        keyword: ''
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      typeList: Object.freeze([
        { value: '1', name: '视频ID' },
        { value: '2', name: '视频名称' }
      ]),
      columns: Object.freeze([
        {
          props: {
            label: '视频ID',
            align: 'center',
            prop: 'videoId',
            width: '150'
          }
        },
        { props: { label: '视频名称', align: 'center', prop: 'name' }},
        {
          props: { label: '状态', align: 'center', width: '90' },
          slot: 'status'
        },
        {
          props: { align: 'center', label: '操作', width: '100' },
          slot: 'actions'
        }
      ]),
      list: [],
      total: 0,
      selectedIds: [],
      tempSelectedIds: []
    }
  },
  watch: {
    visible(v) {
      if (v) {
        this.init()
        this.tempSelectedIds = JSON.parse(JSON.stringify(this.selectedIds))
      }
    }
  },
  methods: {
    init(reset = true) {
      reset && (this.pager.page = 1)
      const params = {
        condition: this.condition,
        pager: this.pager
      }
      courseVideoList(params).then(res => {
        this.total = res.total
        this.list = res.records
      })
    },
    handlePagination(val) {
      this.pager = val
      this.init(false)
    },
    toggleSelect(row) {
      const I = this.tempSelectedIds.findIndex(v => v.videoId === row.videoId)
      if (I !== -1) {
        this.tempSelectedIds.splice(I, 1)
      } else {
        row.chapterName = row.name // 接口用
        this.tempSelectedIds.push(row)
      }
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    },
    cancel() {
      this.tempSelectedIds = []
      this.$emit('update:visible', false)
    },
    confirm() {
      this.selectedIds = JSON.parse(JSON.stringify(this.tempSelectedIds))
      this.$emit('selectVideo', this.selectedIds)
    },
    judge(row) {
      const I = this.tempSelectedIds.findIndex(v => v.videoId === row.videoId)
      return I !== -1
    }
  }
}
</script>
