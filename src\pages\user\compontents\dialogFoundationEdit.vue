<template>
  <el-dialog :title="title" :visible="show" width="700px" top="8vh" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <el-form ref="form" :disabled="disable" :rules="rules" size="mini" :model="form" label-width="120px">

      <el-form-item label="项目" prop="projectName">
        <el-input v-model="form.projectName" placeholder="请输入项目名称" maxlength="50" />
      </el-form-item>
      <el-form-item label="单位" prop="orgId">
        <el-select v-model="foundationForm.type" clearable placeholder="请选择单位类型" @change="handleChangeType">
          <el-option
            v-for="item in typeList"
            :key="item.orgTypeId"
            :label="item.name"
            :value="item.orgTypeId"
          />
        </el-select>
        <el-select v-model="form.orgId" placeholder="请选择单位" clearable filterable remote :remote-method="remoteMethod">
          <el-option v-for="item in foundationList" :key="item.orgId" :label="item.projectName" :value="item.orgId" />
        </el-select>
      </el-form-item>
      <el-form-item prop="unit" label="项目权限">
        <el-input v-model="filterText" />
        <!-- tree -->
        <div class="scroll-tree">
          <el-scrollbar>
            <el-tree
              ref="tree"
              node-key="orgId"
              show-checkbox
              :data="unitList"
              :filter-node-method="filterNode"
              lazy
              :load="handleLoadList"
              :props="defaultProps"
              :default-checked-keys="defaultCheckedKeys"
              @check-change="handleCheckChange"
            />
          </el-scrollbar>
        </div>
      </el-form-item>
      <el-form-item label="项目简介">
        <el-input v-model="form.introduce" type="textarea" />
      </el-form-item>
    </el-form>
    <span slot="footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleSaveForm">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import {
  getProjectFoundationList,
  getProjectUintList,
  pitchList,
  getOrganizationType
} from '@/api/userManage'
import { getChildrenByOrgId } from '@/api/wjw'

export default {
  name: 'DialogFoundationedit',
  props: {
    action: {
      type: String,
      default: 'add'
    },
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '创建项目'
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      disable: false,
      form: this.data,
      rules: {
        projectName: [
          { required: true, message: '请输入项目', trigger: 'blur' }
        ],
        orgId: [{ required: true, message: '请选择单位', trigger: 'change' }],
        unit: [
          { required: true, message: '请选择项目权限', trigger: 'change' }
        ]
      },
      // tree
      defaultProps: {
        children: 'childList',
        label: 'orgName',
        isLeaf: 'leaf'
      },
      filterText: '',
      defaultCheckedKeys: [], // 选中数组
      defaultCheckedKeysP: [], // 半选数组(push方案)
      // halfKeys: [], // 半选中数组(fake click方案)
      unitList: [],
      // 基金会列表
      foundationForm: {
        keyWord: '',
        type: ''
      },
      foundationList: [],
      typeList: []
    }
  },
  watch: {
    filterText(v) {
      this.$refs.tree.filter(v)
    },
    show(v) {
      if (v && this.action === 'add') {
        this.getProjectUintList()
        this.form = {
          createUid: null, // 创建人id
          introduce: '', // 简介
          orgId: '', // 基金会id
          projectName: '', // 资助项目
          unit: '' // 被资助单位
        }
        this.foundationForm.keyWord = ''
        this.foundationForm.type = ''
        this.foundationList = []
        this.$nextTick(() => {
          this.$refs.form.clearValidate()
        })
      }
    },
    data() {
      if (this.show) {
        this.form = JSON.parse(JSON.stringify(this.data))
        this.foundationForm.keyWord = ''
        console.log(this.form)
        if (this.form.type) {
          this.foundationForm.type = this.form.type.toString()
          this.foundationForm.keyWord = this.form.orgName
          this.getProjectFoundationList()
        } else {
          this.foundationForm.type = ''
          this.foundationList = []
        }
        pitchList({ id: this.data.id }).then(res => {
          if (res.length) {
            // 有父级
            this.defaultCheckedKeys = this.data.unit.split(',')
            this.defaultCheckedKeysP = res
          } else {
            this.defaultCheckedKeys = this.data.unit.split(',')
            this.defaultCheckedKeysP = []
          }
          this.getProjectUintList()
        })
      }
    },
    action: {
      handler(v) {
        this.disable = v === 'view'
        if (v === 'add') {
          this.defaultCheckedKeys = []
        } else {
          this.$nextTick(() => {
            this.$refs.tree.setCheckedKeys(this.defaultCheckedKeys)
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.getProjectFoundationList()
    this.getTypeList()
  },
  methods: {
    // 获取单位类型
    getTypeList() {
      getOrganizationType().then(res => {
        const typeList = []
        Object.keys(res).forEach(i => {
          typeList.push({
            orgTypeId: i,
            name: res[i]
          })
        })
        this.typeList = typeList
      })
    },
    // 切换单位类型
    handleChangeType() {
      this.form.orgId = ''
      this.getProjectFoundationList()
    },
    remoteMethod(key) {
      this.foundationForm.keyWord = key
      this.getProjectFoundationList()
    },
    // tree lazy load
    handleLoadList(node, resolve) {
      if (node.level === 0) return resolve([])
      getChildrenByOrgId(node.data.orgId)
        .then(res => {
          resolve(res)
          this.$nextTick(() => {
            this.hsChildren(node.data.childList)
          })
        })
        .catch(res => {
          resolve([])
        })
    },
    // 创建时 - 基金会
    getProjectFoundationList() {
      if (this.foundationForm.type) {
        getProjectFoundationList(this.foundationForm).then(res => {
          this.foundationList = res
        })
      }
    },
    // tree lazy load
    setLoadList(key) {
      getChildrenByOrgId(key).then(res => {
        const nodes = res.map(e => ({ ...e, id: e.orgId }))
        nodes.forEach(v => {
          this.$refs.tree.append(v, this.$refs.tree.getNode(key))
        })
        this.$nextTick(() => {
          this.$refs.tree.setCheckedKeys(this.defaultCheckedKeys)
          this.hsChildren(nodes)
        })
      })
    },
    hsChildren(list) {
      if (this.defaultCheckedKeysP.length > 0) {
        const parentNode = this.defaultCheckedKeysP
        // 遍历一级数组找出与parentNode半选中数组的匹配项
        const TopArr = list.filter(
          v => parentNode.findIndex(sv => sv === v.orgId) > -1
        )
        this.$nextTick(() => {
          TopArr.forEach(v => {
            this.setLoadList(v.orgId)
          })
        })
      }
    },
    // 创建时 - 单位
    getProjectUintList() {
      getProjectUintList().then(res => {
        this.unitList = res.map(e => ({ ...e, id: e.orgId }))
        this.hsChildren(this.unitList)
      })
    },
    close() {
      this.defaultCheckedKeys = []
      this.$refs.tree.setCheckedKeys([])
      this.$refs.form.resetFields()
      this.$emit('update:show', false)
      this.$emit('handleCancel')
      this.form = {}
    },
    handleSaveForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$emit('handleSave', this.form)
        } else {
          return false
        }
      })
    },
    getCheckedKeys(node) {
      let arr = []
      const arrNode = []
      node.forEach(i => {
        if (i.checked) {
          arr.push(i.data.orgId)
          console.log(i.childNodes.length)
          if (i.childNodes.length) {
            arrNode.push({
              orgId: i.data.orgId,
              childNodes: i.childNodes
            })
          }
        }
      })
      console.log(arrNode)
      arrNode.forEach(i => {
        i.childNodes.forEach(j => {
          arr = arr.filter(item => item !== j.data.orgId)
        })
      })
      return arr
    },
    // tree 复选框选中
    handleCheckChange(data, checked, indeterminate) {
      const selectedArr = this.getCheckedKeys(this.$refs.tree.store._getAllNodes())
      console.log(selectedArr)
      this.form.unit = selectedArr.join(',')
      this.defaultCheckedKeys = selectedArr
    },
    // filter tree node
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-tree {
  height: 350px;
  overflow: hidden;
  .el-scrollbar {
    height: 100%;
    ::v-deep .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  ::v-deep {
    .el-checkbox__input.is-disabled {
      &.is-checked .el-checkbox__inner,
      &.is-indeterminate .el-checkbox__inner {
        background-color: #409eff;
        border-color: #409eff;
      }
    }
  }
}
</style>
