import request from '@/utils/request'

/**
 * 视频推荐配置相关
 */

export default {
  // 创建视频推荐配置
  create(param) {
    return request({
      url: '/cms/hotwords/add',
      method: 'post',
      data: param
    })
  },
  putaway(params) {
    return request({
      url: '/cms/hotwords/setUpState',
      method: 'get',
      params
    })
  },
  // 编辑视频推荐配置
  edit(param) {
    return request({
      url: '/cms/hotwords/edit',
      method: 'post',
      data: param
    })
  },
  // 删除视频推荐配置
  del(data) {
    return request({
      url: `/cms/hotwords/delete`,
      method: 'post',
      data
    })
  },
  // 视频推荐配置列表
  list(param) {
    return request({
      url: '/cms/hotwords/list',
      method: 'post',
      data: param
    })
  },
  setListOrder(params) {
    return request({
      url: '/cms/hotwords/setListOrder',
      method: 'get',
      params
    })
  }
}
