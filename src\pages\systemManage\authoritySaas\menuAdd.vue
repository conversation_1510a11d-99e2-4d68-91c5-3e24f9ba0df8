<template>
  <div class="app-container">
    <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="100px" class="form">
      <el-form-item label="类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" show-word-limit maxlength="20" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="上级" prop="parentId">
        <el-cascader
          v-model="form.parentId"
          :options="menuList"
          :props="{
            emitPath:false,
            checkStrictly: true,
            children:'childList' ,
            label:'name',
            value:'id'
          }"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item label="code" prop="code">
        <el-input v-model="form.code" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="适用范围" prop="useScope">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="checkAllScope">全选</el-checkbox>
        <el-checkbox-group v-model="form.useScopeArr" @change="useScopeChange">
          <el-checkbox v-for="item in alluseScope" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox>
        </el-checkbox-group>

      </el-form-item>

      <el-form-item label="排序号" prop="listOrder">
        <el-input-number v-model="form.listOrder" placeholder="请输入" clearable :min="0" @change="onOrderChange" />
      </el-form-item>
      <el-form-item label="url" prop="uri">
        <el-input v-model="form.uri" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input v-model="form.description" type="textarea" maxlength="100" show-word-limit placeholder="请输入" clearable />
      </el-form-item>
    </el-form>

    <div>
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/api/menuSaas'

const alluseScope = [
  { name: '名医传世', id: 1 },
  { name: 'SDK', id: 2 },
  { name: '钉钉', id: 3 }
]

export default {
  name: 'MenuAdd',
  data() {
    const useScopeValid = (rule, value, cb) => {
      if (!this.form.useScopeArr.length) {
        cb(new Error('请选择适用范围'))
      } else {
        cb()
      }
    }
    return {
      readOnly: this.$route.query.readOnly || false,
      form: {
        name: '',
        parentId: '',
        code: '',
        description: '',
        listOrder: '',
        type: '',
        uri: '',
        useScopeArr: []
      },
      rules: {
        code: [{ required: true, message: '请输入code', trigger: 'blur' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        listOrder: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ],
        useScope: [{ validator: useScopeValid, trigger: 'change' }]
      },
      menuList: [],
      typeList: [
        { value: 1, label: '菜单' },
        { value: 2, label: '操作' }
      ],
      detail: {},
      alluseScope: alluseScope,
      useScope: [],
      checkAll: false,
      isIndeterminate: true
    }
  },
  created() {
    if (this.$route.query.id) {
      this.detail = this.$route.query
      this.detail.listOrder += ''
      this.form = { ...this.detail }
      console.log(this.form)
    }
    request.list().then(res => {
      this.menuList = res
    })
  },
  methods: {
    useScopeChange(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === alluseScope.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < alluseScope.length
      this.form.useScopeArr = value
    },
    checkAllScope(val) {
      this.form.useScopeArr = val ? [1, 2, 3] : []
      this.isIndeterminate = false
    },
    cancel() {
      this.$router.go(-1)
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.detail.id) {
            request.edit(this.form).then(res => {
              this.$message.success('编辑成功')
              this.$router.go(-1)
            })
          } else {
            request.create(this.form).then(res => {
              this.$message.success('添加成功')
              this.$router.go(-1)
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    onOrderChange(v) {
      !v && (this.form.listOrder = 0)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .form {
    width: 700px;
  }
}
</style>
