<template>
  <section class="task">
    <div class="task-header">
      <el-input
        v-model="listQuery.condition.keyword"
        placeholder="执行人姓名/手机"
        @change="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.status" clearable placeholder="执行状态" @change="handleFilter()">
        <el-option
          v-for="item in executeStatusList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.condition.payStatus" clearable placeholder="结算状态" @change="handleFilter()">
        <el-option
          v-for="item in payStatusList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="task-main">
      <h2>
        执行列表
        <div>
          <el-button type="primary" @click="exportZip()">导出执行数据包</el-button>
        </div>
      </h2>
      <div class="table">
        <el-table
          :data="tableData"
          border
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.width"
          />
          <el-table-column label="操作">
            <template v-if="scope.row.status !== 0" slot-scope="scope">
              <el-button type="text" size="mini" @click="pdfPreview(scope.row.acceptAssignmentPdf)">查看任务确认单</el-button>
              <el-button
                v-if="scope.row.status < 2"
                type="text"
                size="mini"
                @click="reSign(scope.row.id,'task')"
              >重签任务确认单</el-button>
              <el-button
                v-if="scope.row.status > 2"
                type="text"
                size="mini"
                @click="pdfPreview(scope.row.authorAuthorizePdf)"
              >查看作者授权协议</el-button>
              <el-button
                v-if="scope.row.status === 3"
                type="text"
                size="mini"
                @click="reSign(scope.row.id,'author')"
              >重签作者授权协议</el-button>
              <template v-if="$route.query.type === 'DOULA_ARTICLE'">
                <el-button
                  v-if="scope.row.status === 1"
                  type="text"
                  size="mini"
                  @click="articleEdit(scope.row)"
                >发布图文抖喇</el-button>
                <el-button
                  v-if="[3, 6].includes(scope.row.status)"
                  type="text"
                  size="mini"
                  @click="articlePreview(scope.row.articleId,'audit')"
                >审核图文抖喇</el-button>
                <el-button
                  v-if="scope.row.status >= 4"
                  type="text"
                  size="mini"
                  @click="articlePreview(scope.row.articleId,'look')"
                >查看图文抖喇</el-button>
                <el-button
                  v-if="scope.row.status === 5"
                  type="text"
                  size="mini"
                  @click="articleEdit(scope.row, 'edit')"
                >编辑图文抖喇</el-button>
              </template>
              <template v-if="$route.query.type === 'DOULA_VIDEO'">
                <el-button
                  v-if="scope.row.status === 1"
                  type="text"
                  size="mini"
                  @click="videoEdit(scope.row)"
                >发布短视频抖喇</el-button>
                <el-button
                  v-if="[3, 6].includes(scope.row.status)"
                  type="text"
                  size="mini"
                  @click="articlePreview(scope.row.articleId,'audit')"
                >审核短视频抖喇</el-button>
                <el-button
                  v-if="scope.row.status >= 4"
                  type="text"
                  size="mini"
                  @click="articlePreview(scope.row.articleId,'look')"
                >查看短视频抖喇</el-button>
                <el-button
                  v-if="scope.row.status === 5"
                  type="text"
                  size="mini"
                  @click="videoEdit(scope.row, 'edit')"
                >编辑短视频抖喇</el-button>
              </template>
              <el-button
                v-if="scope.row.status === 2"
                type="text"
                size="mini"
                @click="offline(scope.row.id)"
              >线下授权</el-button>
              <el-button
                v-if="[0,1].includes(scope.row.payStatus) && scope.row.status === 4"
                type="text"
                size="mini"
                @click="calculation(scope.row.id,scope.row.checkingFee)"
              >核算结算费用</el-button>
              <el-button
                v-if="scope.row.payStatus > 1 && scope.row.status === 4"
                type="text"
                size="mini"
                @click="pdfPreview(scope.row.confirmFeePdf)"
              >查看结算确认单</el-button>
              <el-button
                v-if="scope.row.payStatus === 2 && scope.row.status === 4"
                type="text"
                size="mini"
                @click="reSign(scope.row.id,'confirmFee')"
              >重签结算确认单</el-button>
              <el-button
                v-if="scope.row.payStatus === 2 && scope.row.status === 4"
                type="text"
                size="mini"
                @click="pay(scope.row)"
              >支付</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    </div>

    <!-- 确认单 -->
    <pdf-preview :dialog-visible.sync="pdfPreviewVisible" :pdf="pdfUrl" />
    <calculation :id="taskUserId" :dialog-visible.sync="calculationVisible" :checking-fee="calculationTotalFee" type="doula" />
    <pay :id="taskUserId" :platform-ids="canSelectplatformIds" :old-bank-info="bankInfo" :dialog-visible.sync="payVisible" type="doula" />
    <offline :id="taskUserId" :dialog-visible.sync="offlineVisible" type="doula" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import Calculation from '@/pages/marketing/task/components/calculation'
import pdfPreview from '@/pages/marketing/task/components/pdfPreview.vue'
import Pay from '@/pages/marketing/task/components/pay'
import Offline from '@/pages/marketing/task/components/offline'
import {
  executeListDoula,
  exportTaskUserExecZipDoula,
  reSignAuthorizationAgreementDoula,
  reSignCostConfirmationDoula,
  reSignTaskConfirmationDoula
} from '@/api/marketing/taskExecute'
export default {
  name: 'MarketingTaskExecute',
  components: {
    Pagination,
    Calculation,
    pdfPreview,
    Pay,
    Offline
  },
  data() {
    return {
      listQuery: {
        condition: {
          keyword: '',
          payStatus: null,
          status: null,
          taskId: this.$route.query.id
        },
        orderBys: [
          {
            asc: false,
            column: 'status'
          }
        ],
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      executeStatusList: [
        { name: '待接受', value: 0 },
        { name: '待执行', value: 1 },
        { name: '待授权', value: 2 },
        { name: '待审核', value: 3 },
        { name: '待平台审核', value: 6 },
        { name: '已完成', value: 4 },
        { name: '已退回', value: 5 }
      ],
      payStatusList: [
        { name: '待核算', value: 0 },
        { name: '待确认费用', value: 1 },
        { name: '待支付', value: 2 },
        { name: '支付中', value: 4 },
        { name: '已支付', value: 3 }
      ],
      tableColumn: [
        { prop: 'userName', label: '执行人', width: '25' },
        { prop: 'phone', label: '手机', width: '25' },
        { prop: 'statusName', label: '执行状态', width: '25' },
        { prop: 'payStatusName', label: '结算状态', width: '25' },
        { prop: 'promotionBudget', label: '推广预算', width: '25' },
        { prop: 'budgetFee', label: '制作预算', width: '25' },
        { prop: 'extraFee', label: '点击奖励', width: '25' },
        { prop: 'checkingFee', label: '核算费用', width: '25' },
        { prop: 'totalFee', label: '结算费用', width: '25' },
        { prop: 'platform', label: '代征平台', width: '25' }
      ],
      tableData: [],
      pdfUrl: '',
      pdfPreviewVisible: false,
      calculationVisible: false,
      calculationTotalFee: undefined,
      taskUserId: null,
      payVisible: false,
      offlineVisible: false,
      canSelectplatformIds: [],
      bankInfo: {}
    }
  },
  mounted() {
    this.getExecuteList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getExecuteList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getExecuteList()
    },
    getExecuteList() {
      executeListDoula(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.payStatusName = this.payStatusList.find(i => i.value === v.payStatus).name
          v.statusName = this.executeStatusList.find(i => i.value === v.status).name
          v.taskName = this.$route.query.name
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    pdfPreview(url) {
      if (url !== '') {
        window.open(url)
      } else {
        this.$message.error('暂无相关文件')
      }
    },
    calculation(id, checkingFee) {
      this.taskUserId = id
      this.calculationTotalFee = checkingFee
      this.calculationVisible = true
    },
    pay(data) {
      this.taskUserId = data.id
      this.canSelectplatformIds = data.canSelectplatformIds
      this.bankInfo = {
        bankCard: data.bankCard,
        bankName: data.bankName,
        bankSubName: data.bankSubName,
        bankBindPhone: data.bankBindPhone
      }
      this.payVisible = true
    },
    offline(id) {
      this.taskUserId = id
      this.offlineVisible = true
    },
    exportZip() {
      exportTaskUserExecZipDoula({ taskId: this.$route.query.id }).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    articleEdit(row, type) {
      this.$router.push(
        {
          name: 'MarketingTaskDoulaArticleCollect',
          params: {
            info: row,
            type
          }
        }
      )
    },
    videoEdit(row, type) {
      this.$router.push(
        {
          name: 'MarketingTaskDoulaVideoCollect',
          params: {
            info: row,
            type
          }
        }
      )
    },
    articlePreview(id, type) {
      this.$router.push(
        {
          name: 'MarketingTaskDoulaArticlePreview',
          query: {
            articleId: id,
            type
          }
        }
      )
    },
    reSign(id, type) {
      this.$confirm('确认后,将发回签署人重新电签', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (type === 'author') {
          reSignAuthorizationAgreementDoula(id).then(() => { this.getExecuteList() })
        } else if (type === 'task') {
          reSignTaskConfirmationDoula(id).then(() => { this.getExecuteList() })
        } else {
          reSignCostConfirmationDoula(id).then(() => { this.getExecuteList() })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.task {
  height: calc(100vh - 50px);
  padding: 20px 20px;
  background-color: #eaeaee;
  &-header {
    .el-input {
      width: 200px;
    }
  }
  &-main {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      background: #f9f9f9;
    }
    .table {
      padding: 16px 16px 0px;
      ::v-deep .el-table {
        color: #333;
        thead {
          color: #333;
        }
        .el-button--mini {
          position: relative;
          &:before {
            content: ' ';
            display: inline-block;
            position: absolute;
            top: 7px;
            right: -8px;
            width: 1px;
            height: 14px;
            background-color: #44b9a2;
          }
          &:nth-last-child(1) {
            &:before {
              display: none;
            }
          }
        }
      }
    }
    ::v-deep .pagination-container {
      margin: 0 auto;
    }
  }
}
</style>
