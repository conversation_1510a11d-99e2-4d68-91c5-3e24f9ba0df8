<template>
  <el-dialog title="设置CMS角色" :visible.sync="show" :before-close="beforeClose">
    <el-form :model="form">
      <el-form-item label="设置CMS角色" :label-width="formLabelWidth">
        <el-select v-model="form.roleId" placeholder="请选择角色" filterable clearable>
          <el-option v-for="item in roleList" :key="item.roleId" :label="item.name" :value="item.roleId" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import requset from '@/api/roleCms'

export default {
  name: 'DialogSetRole',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    uid: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        roleId: ''
      },
      roleList: [],
      formLabelWidth: '120px'
    }
  },
  watch: {
    show(v) {
      v && this.getRole()
    }
  },
  created() {
    requset.roleUserList().then(res => {
      this.roleList = res
    })
  },
  methods: {
    getRole() {
      requset.roleGetUser(this.uid).then(res => {
        this.form.roleId = res.id || ''
      })
    },
    cancel() {
      this.form.roleId = ''
      this.$emit('update:show', false)
    },
    confirm() {
      if (this.form.roleId) {
        const param = {
          roleId: this.form.roleId,
          userId: this.uid
        }
        requset.roleSetUser(param).then(res => {
          this.cb()
        })
      } else {
        requset.roleShut(this.uid).then(res => {
          this.cb()
        })
      }
    },
    cb() {
      this.$message.success('设置成功')
      this.$parent.getUserList()
      this.cancel()
    },
    beforeClose(done) {
      this.cancel()
    }
  }
}
</script>
