import Layout from '@/layout'

const operationManage = {
  path: '/operationManage',
  component: Layout,
  name: 'OperationManage',
  redirect: { name: 'OperationManage' },
  alwaysShow: true,
  meta: { title: '运营管理', icon: 'table' },
  children: [
    {
      path: 'credit',
      component: { render: e => e('router-view') },
      name: 'Credit',
      redirect: { name: 'StudentList' },
      meta: { title: '学分管理' },
      children: [
        {
          path: 'studentList',
          component: () => import('@/pages/operationManage/credit/studentList'),
          name: 'StudentList',
          meta: { title: '学员列表' }
        },
        {
          path: 'creditDistribution',
          component: () =>
            import('@/pages/operationManage/credit/creditDistribution'),
          name: 'CreditDistribution',
          meta: { title: '学分派发' }
        },
        {
          path: 'batchDistribute',
          component: () =>
            import('@/pages/operationManage/credit/creditDistribution/batch'),
          name: 'BatchDistribute',
          meta: { title: '学分批量派发' }
        },
        {
          path: 'creditPrice',
          component: () => import('@/pages/operationManage/credit/creditPrice'),
          name: 'CreditPrice',
          meta: { title: '学分单价' }
        }
      ]
    },
    {
      path: 'certificate',
      component: { render: e => e('router-view') },
      name: 'Certificate',
      redirect: { name: 'CertificateList' },
      meta: { title: '证书培训' },
      children: [
        {
          path: 'list',
          component: () => import('@/pages/operationManage/certificate/list'),
          name: 'CertificateList',
          meta: { title: '证书列表' }
        },
        {
          path: 'creator',
          component: () =>
            import('@/pages/operationManage/certificate/list/creator'),
          name: 'CertificateCreator',
          meta: { title: '证书创建' }
        },
        {
          path: 'traineeList',
          component: () =>
            import('@/pages/operationManage/certificate/traineeList'),
          name: 'CertificateTraineeList',
          meta: { title: '学员列表' }
        },
        {
          path: 'traineeExamineDetail',
          component: () =>
            import('@/pages/operationManage/certificate/traineeList/detail'),
          name: 'CertificateTraineeExamineDetail',
          meta: { title: '考核详情' }
        },
        {
          path: 'apply',
          component: () => import('@/pages/operationManage/certificate/apply'),
          name: 'CertificateApply',
          meta: { title: '申领证书' }
        }
      ]
    },
    {
      path: 'jobTrain',
      component: { render: e => e('router-view') },
      name: 'JobTrain',
      redirect: { name: 'JobTrainList' },
      meta: { title: '培训计划' },
      children: [
        {
          path: 'list',
          component: () => import('@/pages/operationManage/jobTrain/list'),
          name: 'JobTrainList',
          meta: { title: '' }
        },
        {
          path: 'detail',
          component: () => import('@/pages/operationManage/jobTrain/detail'),
          name: 'JobTrainDetail',
          meta: { title: '创建' },
          hidden: true
        }
      ]
    },
    {
      path: '/ad',
      component: { render: e => e('router-view') },
      name: 'Ad',
      redirect: { name: 'AdIndex' },
      meta: { title: '广告管理' },
      children: [
        {
          path: 'list',
          component: () => import('@/pages/operationManage/ad/list.vue'),
          name: 'AdIndex',
          meta: { title: '广告列表' }
        },
        {
          path: 'addAd',
          component: () => import('@/pages/operationManage/ad/addAd'),
          name: 'AddAd',
          meta: { title: '添加广告' },
          hidden: true
        },
        {
          path: 'statistics',
          component: () => import('@/pages/operationManage/ad/statistics'),
          name: 'AdStatistics',
          meta: { title: '广告统计' }
        },
        {
          path: 'statisticsDetail',
          component: () =>
            import('@/pages/operationManage/ad/statisticsDetail'),
          name: 'StatisticsDetail',
          meta: { title: '统计明细' },
          hidden: true
        },
        {
          path: 'statisticsPersonalDetail',
          component: () =>
            import('@/pages/operationManage/ad/statisticsPersonalDetail'),
          name: 'StatisticsDetail',
          meta: { title: '人员详情' },
          hidden: true
        },
        {
          path: 'personDetail',
          component: () => import('@/pages/operationManage/ad/personDetail'),
          name: 'SersonDetail',
          meta: { title: '人员明细' },
          hidden: true
        }
      ]
    },
    {
      path: '/activity',
      component: { render: e => e('router-view') },
      name: 'Activity',
      redirect: { name: 'ActivityIndex' },
      meta: { title: '活动管理' },
      children: [
        {
          path: 'list',
          component: () => import('@/pages/operationManage/activity/list'),
          name: 'ActivityIndex',
          meta: { title: '活动列表' }
        },
        {
          path: 'addActivity',
          component: () =>
            import('@/pages/operationManage/activity/addActivity'),
          name: 'AddActivity',
          meta: { title: '添加活动' },
          hidden: true
        },
        {
          path: 'statistics',
          component: () =>
            import('@/pages/operationManage/activity/statistics'),
          name: 'ActivityStatistics',
          meta: { title: '活动统计' }
        },
        {
          path: 'statisticsDetail',
          component: () =>
            import('@/pages/operationManage/activity/statisticsDetail'),
          name: 'ActivityStatisticsDetail',
          meta: { title: '统计明细' },
          hidden: true
        },
        {
          path: 'participationDetail',
          component: () =>
            import('@/pages/operationManage/activity/participationDetail'),
          name: 'ActivityParticipationDetail',
          meta: { title: '参与明细' },
          hidden: true
        },
        {
          path: 'answerDetail',
          component: () =>
            import('@/pages/operationManage/activity/answerDetail'),
          name: 'ActivityAnswerDetail',
          meta: { title: '答题详情' },
          hidden: true
        }
      ]
    },
    {
      path: '/liveManage',
      component: { render: e => e('router-view') },
      name: 'LiveManage',
      redirect: { name: 'Check' },
      meta: { title: '直播管理' },
      children: [
        {
          path: 'check',
          component: () => import('@/pages/operationManage/live/check/index'),
          name: 'Check',
          meta: { title: '直播审核' }
        },
        {
          // 直播设置是 直播审核-审核-通过-所跳转的页面
          path: '/checkDetail',
          component: () => import('@/pages/operationManage/live/check/detail'),
          name: 'CheckDetail',
          meta: { title: '直播设置' },
          hidden: true
        },
        {
          path: 'list',
          component: () => import('@/pages/operationManage/live/list/index'),
          name: 'List',
          meta: { title: '直播列表' }
        },
        {
          path: 'costRules',
          component: () => import('@/pages/operationManage/live/costRules'),
          name: 'CostRules',
          meta: { title: '费用规则' }
        },
        {
          path: 'faceRecord',
          component: () => import('@/pages/operationManage/live/faceRecord'),
          name: 'FaceRecord',
          meta: { title: '人脸记录' }
        }
      ]
    },
    {
      path: '/message',
      component: { render: e => e('router-view') },
      name: 'Message',
      redirect: { name: 'MessageList' },
      meta: { title: '消息管理' },
      alwaysShow: true,
      children: [
        {
          path: 'list',
          component: () => import('@/pages/operationManage/message/list/index'),
          name: 'MessageList',
          meta: { title: '消息列表' }
        },
        {
          path: 'creator',
          component: () =>
            import('@/pages/operationManage/message/list/creator'),
          name: 'MessageCreator',
          meta: { title: '创建消息' }
        },
        {
          path: 'statistics',
          component: () =>
            import('@/pages/operationManage/message/statistics/index'),
          name: 'MessageStatistics',
          meta: { title: '消息统计' }
        }
      ]
    },
    {
      path: '/information',
      component: () => import('@/pages/operationManage/information/index'),
      name: 'InformationManage',
      meta: { title: '资讯管理' },
      children: [
        {
          path: 'detail',
          component: () => import('@/pages/operationManage/information/detail'),
          name: 'ArticleDetail',
          meta: { title: '编辑文章' },
          hidden: true
        }
      ]
    },
    {
      path: '/vipManage',
      component: () => import('@/pages/operationManage/vipManage/index'),
      name: 'VipManage',
      meta: { title: '会员体系' }
    },
    {
      path: '/appManage',
      component: { render: e => e('router-view') },
      name: 'AppManage',
      redirect: { name: 'AppHotwords' },
      meta: { title: 'APP管理' },
      alwaysShow: true,
      children: [
        {
          path: 'hotwords',
          component: () =>
            import('@/pages/operationManage/appManage/hotwords/index'),
          name: 'AppHotwords',
          meta: {
            title: 'APP搜索热门推荐'
          }
        },
        {
          path: 'feedback',
          component: () =>
            import('@/pages/operationManage/appManage/feedback/index'),
          name: 'FeedbackIndex',
          meta: { title: 'APP用户意见反馈' }
        }
      ]
    },
    {
      path: 'resourceRecommend',
      component: () =>
        import('@/pages/operationManage/h5/resourceRecommend/index'),
      name: 'Config',
      meta: { title: 'H5视频推荐' }
    },
    {
      path: 'orderManage',
      component: () => import('@/pages/operationManage/order/index.vue'),
      name: 'OrderManage',
      meta: { title: '收入订单' }
    }
  ]
}

export default operationManage
