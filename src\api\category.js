import request from '@/utils/request'

// 新增分类
export function addCategory(params) {
  return request({
    url: '/category/add',
    method: 'post',
    data: params
  })
}

// 删除分类
export function deleteCategory(params) {
  return request({
    url: '/category/delete',
    method: 'get',
    params: params
  })
}

// 编辑分类
export function updateCategory(params) {
  return request({
    url: '/category/update',
    method: 'post',
    data: params
  })
}

// 查询视频栏目
export function getVideoTreeList() {
  return request({
    url: '/category/videoTreeList',
    method: 'get'
  })
}

// 分类/身份
export function identityList() {
  return request({
    url: '/identity/identityList',
    method: 'get'
  })
}

// 身份分类树
export function identityTreeList() {
  return request({
    url: '/identity/identityTreeList',
    method: 'get'
  })
}

// 专科树
export function getTreeList(query) {
  return request({
    url: '/major/treeList',
    method: 'get'
  })
}

// 专科分类树  根据身份集合获取
export function majorTreeList(data) {
  return request({
    url: '/major/treeListByIdentityIds',
    method: 'post',
    data
  })
}

// 专科分类树  根据身份集合获取
export function majorTreeListId(id) {
  return request({
    url: `/major/treeListByIdentityId?identityId=${id}`,
    method: 'get'
  })
}

// 分类列表（树结构）
export function getCategoryTreeList(parentId) {
  return request({
    url: `/category/tree/${parentId}`,
    method: 'get'
  })
}

// 视频、课程、SOP资源相关分类列表
// parentId 父级id（为0时，查所有一级分类）
// type 类型：1-视频,2-课程，3-sop
export function getCategoryList(parentId, type) {
  return request({
    url: `/category/resource/${parentId}/${type}`,
    method: 'get'
  })
}

// 分类列表 公共接口
export function listByParentId(parentId) {
  return request({
    url: `/category/listByParentId/${parentId}`,
    method: 'get'
  })
}

// 获取 新版 分类 数据 (单级)
export function getNewCategory(parentId) {
  return request({
    url: '/category/list/' + parentId,
    method: 'get',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}
