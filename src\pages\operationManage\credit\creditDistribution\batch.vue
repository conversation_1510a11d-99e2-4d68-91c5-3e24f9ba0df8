<template>
  <div class="app-container">
    <el-form ref="form" style="width: 932px;" :model="form" label-width="120px" class="form">
      <el-form-item label="派发学分:" required label-width="120">
        <el-input-number v-model="form.score" :controls="false" :min="1" :precision="0" />
      </el-form-item>
    </el-form>

    <template v-if="form.batchId">
      <user-list
        :batch-id="form.batchId"
        api-type="credit"
      />
    </template>
    <div>
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm('form')">保存</el-button>
    </div>
  </div>
</template>

<script>
import userList from '@/components/PublishUserList/index.vue'
import { getTmpUserBatchId, distributeCredits } from '@/api/credit'

export default {
  name: 'BatchDistribute',
  components: { userList },
  data() {
    return {
      form: {
        score: 1,
        batchId: ''
      }
    }
  },
  created() {
    getTmpUserBatchId().then(res => {
      this.form.batchId = res
    })
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          distributeCredits(this.form).then(() => {
            this.$message.success('派发成功')
            this.$router.go(-1)
          }).catch(error => {
            this.$message.error(error)
          })
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  align-items: center;
  flex-direction: column
}
</style>
