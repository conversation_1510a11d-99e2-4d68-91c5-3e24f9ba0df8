import request from '@/utils/request'

// 学员列表
export function getStudentList(params) {
  return request({
    url: '/creditUser/studentList',
    method: 'post',
    data: params
  })
}

// 学员列表导出
export function exportStudentList(params) {
  return request({
    url: '/creditUser/studentListExport',
    method: 'post',
    data: params
  })
}

// 学分卡列表
export function creditCardList(params) {
  return request({
    url: '/creditUser/creditCardList',
    method: 'post',
    data: params
  })
}

// 派发学分
export function distributeCredits(params) {
  return request({
    url: '/creditUser/distributeCredits',
    method: 'post',
    data: params
  })
}

// 派发记录
export function distributeRecord(id) {
  return request({
    url: `/creditUser/distributeRecord/${id}`,
    method: 'get'
  })
}

// 抵扣记录
export function deductionRecord(id) {
  return request({
    url: `/creditUser/deductionRecord/${id}`,
    method: 'get'
  })
}

export function creditAreaPriceList() {
  return request({
    url: '/creditAreaPrice/list',
    method: 'get'
  })
}

export function getTmpUserBatchId() {
  return request({
    url: '/user/getTmpUserBatchId',
    method: 'get'
  })
}

export function setCreditPrice(params) {
  return request({
    url: '/creditAreaPrice/setPrice',
    method: 'post',
    data: params
  })
}
