import Layout from '@/layout'

const validManageRouter = {
  path: '/validManage',
  component: Layout,
  name: 'ValidManage',
  redirect: { name: 'CategoryIndex' },
  alwaysShow: true,
  meta: { title: '资源管理', icon: 'review' },
  children: [
    {
      path: 'category/index', // 新增
      component: () => import('@/pages/validManage/category/index'),
      name: 'CategoryIndex',
      meta: { title: '资源分类' }
    },
    {
      path: 'video/index',
      component: () => import('@/pages/validManage/video/index'),
      name: 'VideoIndex',
      meta: { title: '视频列表' }
    },
    {
      path: 'lesson/list',
      component: () => import('@/pages/validManage/lesson/list'),
      name: 'LessonList',
      meta: { title: '课程列表' }
    },
    {
      path: 'lesson/recommend',
      component: () => import('@/pages/validManage/lesson/recommend'),
      name: 'LessonRecommend',
      meta: { title: '课程推荐' }
    },
    {
      path: 'lesson/edit',
      component: () => import('@/pages/validManage/lesson/edit'),
      name: 'LessonEdit',
      meta: { title: '编辑' },
      hidden: true
    },
    {
      path: 'course/list', // 新增
      component: () => import('@/pages/validManage/course/list'),
      name: 'CourseList',
      meta: { title: '教程列表' }
    },
    {
      path: 'sop/list', // 新增
      component: () => import('@/pages/validManage/sop/list'),
      name: 'SopList',
      meta: { title: 'sop列表' }
    },
    {
      path: 'video/valid',
      component: () => import('@/pages/validManage/video/valid'),
      name: 'VideoValid',
      meta: { title: '视频审核' }
    },
    {
      path: 'video/detail/:videoInfoId',
      component: () => import('@/pages/validManage/video/detail'),
      name: 'VideoDetail',
      meta: { title: '视频审核详情' },
      hidden: true
    },
    {
      path: 'course/index',
      component: () => import('@/pages/validManage/course/index'),
      name: 'CourseValid',
      meta: { title: '教程审核' }
    },
    {
      path: 'course/detail/:courseInfoId',
      component: () => import('@/pages/validManage/course/detail'),
      name: 'CourseDetail',
      meta: { title: '教程审核详情' },
      hidden: true
    },
    {
      path: 'sop/index',
      component: () => import('@/pages/validManage/sop/index'),
      name: 'SopValid',
      meta: { title: 'sop审核' }
    },
    {
      path: 'sop/detail/:sopInfoId',
      component: () => import('@/pages/validManage/sop/detail'),
      name: 'SopDetail',
      meta: { title: 'sop审核详情' },
      hidden: true
    },
    {
      path: 'redress/index',
      component: () => import('@/pages/validManage/redress/index'),
      name: 'RedressRecord',
      meta: { title: '纠错记录' }
    },
    {
      path: '/questionBankManage',
      component: () => import('@/pages/validManage/questionBankManage'),
      name: 'QuestionBankManage',
      meta: { title: '题库管理' },
      children: [
        // 小林
        {
          path: 'platformQuestion',
          component: () => import('@/pages/validManage/questionBankManage/platformQuestion'),
          name: 'PlatformQuestion',
          meta: {
            title: '平台试题'
          }
        },
        {
          path: 'createQuestion',
          component: () => import('@/pages/validManage/questionBankManage/platformQuestion/createQuestion'),
          name: 'CreateQuestion',
          meta: {
            title: '创建试题'
          },
          hidden: true
        },
        // 一池
        {
          path: 'unitQuestions',
          component: () => import('@/pages/validManage/questionBankManage/unitQuestions/index'),
          name: 'UnitQuestions',
          meta: { title: '单位试题' }
        },
        {
          path: 'usedRecord',
          component: () => import('@/pages/validManage/questionBankManage/usedRecord/index'),
          name: 'UsedRecord',
          meta: { title: '使用记录' }
        },
        {
          path: 'usedRecordDetail',
          component: () => import('@/pages/validManage/questionBankManage/usedRecord/detail'),
          name: 'UsedRecordDetail',
          meta: { title: '使用题目' },
          hidden: true
        }
      ]
    }
  ]
}

export default validManageRouter
