/**
 * Created by PanJia<PERSON>hen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  )
}

/**
 *
 * @param {string} seconds(second)
 * @returns {string}
 */
export function parseSeconds(seconds) {
  let changeSeconds = 0
  if (!seconds) changeSeconds = 0
  if (typeof seconds === 'string') changeSeconds = parseFloat(seconds)
  if (typeof seconds === 'number') changeSeconds = seconds
  let [theTime, minute, hour] = [changeSeconds, 0, 0]
  if (theTime >= 60) {
    minute = parseInt(theTime / 60)
    theTime = parseInt(theTime % 60)
    if (minute >= 60) {
      hour = parseInt(minute / 60)
      minute = parseInt(minute % 60)
    }
  }
  let result =
    parseInt(theTime) > 9 ? '' + parseInt(theTime) : '0' + parseInt(theTime)
  result =
    (parseInt(minute) > 9 ? '' + parseInt(minute) : '0' + parseInt(minute)) +
    ':' +
    result
  result =
    (parseInt(hour) > 9 ? '' + parseInt(hour) : '0' + parseInt(hour)) +
    ':' +
    result
  return result
}

export function isEmptyObject(obj) {
  return Object.keys(obj).length > 0
}

/**
 * filter aduit status
 * @param {number} val
 * @returns {string}
 */
export function filterAduitStatus(val) {
  // 0–待提交审核，1–待审核,2审核通过,3审核不通过
  switch (val) {
    case 0:
      return '待提交审核'
    case 1:
      return '待审核'
    case 2:
      return '审核通过'
    case 3:
      return '审核不通过'
    default:
      return '未知状态'
  }
}

/**
 * filter aduit history status
 * @param {number} val
 * @returns {string}
 */
export function filterHistoryAduitStatus(val) {
  // 0–待提交审核，1–待审核,2审核通过,3审核不通过
  switch (val) {
    case 0:
      return '不通过'
    case 1:
      return '审核通过'
    default:
      return '未知状态'
  }
}

/**
 * filter ext permission status
 * @param {number} val
 * @returns {string}
 */
export function filterExtPermission(val) {
  // 0:平台不公开 1:平台公开
  switch (val) {
    case 0:
      return '平台不公开'
    case 1:
      return '平台公开'
    default:
      return '未知权限'
  }
}

/**
 * filter int permission status
 * @param {number} val
 * @returns {string}
 */
export function filterIntPermission(val) {
  switch (val) {
    case 0:
      return '内部不公开'
    case 1:
      return '单位内公开'
    default:
      return '未知权限'
  }
}

/**
 * filter index
 * @param {number} val
 * @returns {string}
 */
export function filtersIndex(val) {
  const arr = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十']
  return arr[val]
}

/**
 * filter index to english
 * @param {number} val
 * @returns {string}
 */
export function filtersEnIndex(val) {
  const arr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')
  return arr[val]
}

/**
 * totalSeconds to 'hh:mm:ss'
 * @params {seconds}
 * return {string}
 */
export function formatSeconds(seconds) {
  let changeSeconds = 0
  if (!seconds) changeSeconds = 0
  if (typeof seconds === 'string') changeSeconds = parseFloat(seconds)
  if (typeof seconds === 'number') changeSeconds = seconds
  let [theTime, minute, hour] = [changeSeconds, 0, 0]
  if (theTime >= 60) {
    minute = parseInt(theTime / 60)
    theTime = parseInt(theTime % 60)
    if (minute >= 60) {
      hour = parseInt(minute / 60)
      minute = parseInt(minute % 60)
    }
  }
  let result =
    parseInt(theTime) > 9 ? '' + parseInt(theTime) : '0' + parseInt(theTime)
  result =
    (parseInt(minute) > 9 ? '' + parseInt(minute) : '0' + parseInt(minute)) +
    ':' +
    result
  result =
    (parseInt(hour) > 9 ? '' + parseInt(hour) : '0' + parseInt(hour)) +
    ':' +
    result
  return result
}

/**
 * 'hh:mm:ss' to totalSeconds
 * @params {seconds}
 * return {string}
 */
export function formatTotalSeconds(string) {
  const arr = string.split(':')
  return parseInt(arr[0]) * 3600 + parseInt(arr[1]) * 60 + parseInt(arr[2])
}

export function setPaperOptionList() {
  return 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')
}

export function getDayLastSecond(params) {
  var todayYear = (new Date(params)).getFullYear()
  var todayMonth = (new Date(params)).getMonth()
  var todayDay = (new Date(params)).getDate()
  var todayTime = (new Date(todayYear, todayMonth, todayDay, '23', '59', '59')).getTime()// 毫秒
  return todayTime
}

// 删除json中值为空串或者为null的元素
export function deleteEmptyProperty(object) {
  for (var i in object) {
    var value = object[i]
    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        if (value.length == 0) {
          delete object[i]
          continue
        }
      }
      deleteEmptyProperty(value)
      if (isEmpty(value)) {
        delete object[i]
      }
    } else if (value === '' || value === null || value === undefined) {
      delete object[i]
    }
  }
}
export function isEmpty(object) {
  for (var name in object) {
    return false
  }
  return true
}

export function isEmptyStr(str) {
  if (str === null || str === '' || str === undefined) {
    return true
  }
  return false
}

const designWidth = 1920
// px to rem
export function px2rem(px) {
  return px * 320 / designWidth / 20 + 'rem'
}

export function resetRem() {
  // 基准大小
  const baseSize = 100
  const basePc = baseSize / 1920 // 表示1920的设计图,使用100PX的默认值
  const vW = window.innerWidth // 当前窗口的宽度

  const rem = vW * basePc // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值
  document.documentElement.style.fontSize = `${rem}px`
}
