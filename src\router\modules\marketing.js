import Layout from '@/layout'

const marketingRouter = {
  path: '/marketing',
  component: Layout,
  name: 'Marketing',
  redirect: { name: 'MarketingStatistics' },
  meta: { title: '数字营销', icon: '' },
  alwaysShow: true,
  children: [
    {
      path: 'statistics',
      name: 'MarketingStatistics',
      component: () => import('@/pages/marketing/statistics'),
      redirect: { name: 'MarketingStatisticsTotal' },
      meta: { title: '统计分析', noCache: true },
      children: [
        {
          path: 'total',
          name: 'MarketingStatisticsTotal',
          component: () => import('@/pages/marketing/statistics/total.vue'),
          meta: { title: '总体分析' }
        },
        {
          path: 'promotion',
          name: 'MarketingStatisticsPromotion',
          component: () => import('@/pages/marketing/statistics/promotion.vue'),
          meta: { title: '推广分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsPromotionDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/promotionDetail.vue'),
              meta: { title: '推广详情' }
            }
          ]
        },
        {
          path: 'article',
          name: 'MarketingStatisticsArticle',
          component: () => import('@/pages/marketing/statistics/article.vue'),
          meta: { title: '文章分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsArticleDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/articleDetail.vue'),
              meta: { title: '文章详情' }
            }
          ]
        },
        {
          path: 'video',
          name: 'MarketingStatisticsVideo',
          component: () => import('@/pages/marketing/statistics/video.vue'),
          meta: { title: '视频分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsVideoDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/videoDetail.vue'),
              meta: { title: '视频详情' }
            }
          ]
        },
        {
          path: 'creator',
          name: 'MarketingStatisticsCreator',
          component: () => import('@/pages/marketing/statistics/creator.vue'),
          meta: { title: '创作者分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsCreatorDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/creatorDetail.vue'),
              meta: { title: '创作者详情' }
            }
          ]
        },
        {
          path: 'promoter',
          name: 'MarketingStatisticsPromoter',
          component: () => import('@/pages/marketing/statistics/promoter.vue'),
          meta: { title: '推广员分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsPromoterDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/promoterDetail.vue'),
              meta: { title: '推广员详情' }
            }
          ]
        },
        {
          path: 'visit',
          name: 'MarketingStatisticsVisit',
          component: () => import('@/pages/marketing/statistics/visit.vue'),
          meta: { title: '拜访分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsVisitDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/visitDetail.vue'),
              meta: { title: '拜访详情' }
            }
          ]
        },
        {
          path: 'service',
          name: 'MarketingStatisticsService',
          component: () => import('@/pages/marketing/statistics/service.vue'),
          meta: { title: '服务商分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsServiceDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/serviceDetail.vue'),
              meta: { title: '服务商详情' }
            }
          ]
        },
        {
          path: 'doula',
          name: 'MarketingStatisticsDoula',
          component: () => import('@/pages/marketing/statistics/doula.vue'),
          meta: { title: '抖喇分析' },
          children: [
            {
              path: 'detail',
              name: 'MarketingStatisticsDoulaDetail',
              component: () =>
                import('@/pages/marketing/statistics/detail/doulaDetail.vue'),
              meta: { title: '抖喇详情' }
            }
          ]
        }
      ]
    },
    {
      path: 'task',
      name: 'MarketingTask',
      component: () => import('@/pages/marketing/task'),
      redirect: { name: 'MarketingTaskCreator' },
      meta: { title: '任务管理', noCache: true },
      children: [
        {
          path: 'promote',
          name: 'MarketingTaskPromote',
          component: () => import('@/pages/marketing/task/promote'),
          meta: { title: '平台推广' },
          children: [
            {
              path: 'add',
              name: 'MarketingTaskPromoteAdd',
              component: () =>
                import('@/pages/marketing/task/promote/add.vue'),
              meta: { title: '新增推广内容' }
            }
          ]
        },
        {
          path: 'content',
          name: 'MarketingTaskContent',
          component: () => import('@/pages/marketing/task/content'),
          redirect: { name: 'MarketingTaskContentList' },
          meta: { title: '内容审核' },
          children: [
            {
              path: 'list',
              name: 'MarketingTaskContentList',
              component: () =>
                import('@/pages/marketing/task/content/list.vue'),
              meta: { title: '' }
            },
            {
              path: 'preview',
              name: 'MarketingTaskContentPreview',
              component: () =>
                import('@/pages/marketing/task/creator/collect/preview.vue'),
              meta: { title: '文章详情' }
            },
            {
              path: 'doulaPreview',
              name: 'MarketingTaskContentDoulaPreview',
              component: () =>
                import('@/pages/marketing/task/doula/collect/preview.vue'),
              meta: { title: '文章详情' }
            }
          ]
        },
        {
          path: 'creator',
          name: 'MarketingTaskCreator',
          component: () => import('@/pages/marketing/task/creator'),
          meta: { title: '创作任务' },
          children: [
            {
              path: 'execute',
              name: 'MarketingTaskExecute',
              component: () => import('@/pages/marketing/task/creator/execute'),
              meta: { title: '执行管理' }
            },
            {
              path: 'publish',
              name: 'MarketingTaskPublish',
              component: () => import('@/pages/marketing/task/creator/publish'),
              meta: { title: '任务详情' }
            },
            {
              path: 'articleCollect',
              name: 'MarketingTaskArticleCollect',
              component: () =>
                import('@/pages/marketing/task/creator/collect/article.vue'),
              meta: { title: '文章发布' }
            },
            {
              path: 'videoCollect',
              name: 'MarketingTaskVideoCollect',
              component: () =>
                import('@/pages/marketing/task/creator/collect/video.vue'),
              meta: { title: '视频发布' }
            },
            {
              path: 'articlePreview',
              name: 'MarketingTaskArticlePreview',
              component: () =>
                import('@/pages/marketing/task/creator/collect/preview.vue'),
              meta: { title: '文章详情' }
            }
          ]
        },
        {
          path: 'doula',
          name: 'MarketingTaskDoula',
          component: () => import('@/pages/marketing/task/doula'),
          meta: { title: '抖喇任务' },
          children: [
            {
              path: 'execute',
              name: 'MarketingTaskDoulaExecute',
              component: () => import('@/pages/marketing/task/doula/execute'),
              meta: { title: '执行管理' }
            },
            {
              path: 'publish',
              name: 'MarketingTaskDoulaPublish',
              component: () => import('@/pages/marketing/task/doula/publish'),
              meta: { title: '任务详情' }
            },
            {
              path: 'articleCollect',
              name: 'MarketingTaskDoulaArticleCollect',
              component: () =>
                import('@/pages/marketing/task/doula/collect/article.vue'),
              meta: { title: '文章发布' }
            },
            {
              path: 'videoCollect',
              name: 'MarketingTaskDoulaVideoCollect',
              component: () =>
                import('@/pages/marketing/task/doula/collect/video.vue'),
              meta: { title: '视频发布' }
            },
            {
              path: 'articlePreview',
              name: 'MarketingTaskDoulaArticlePreview',
              component: () =>
                import('@/pages/marketing/task/doula/collect/preview.vue'),
              meta: { title: '文章详情' }
            }
          ]
        },
        {
          path: 'visit',
          name: 'MarketingTaskVisit',
          component: () => import('@/pages/marketing/task/visit'),
          meta: { title: '拜访任务' },
          children: [
            {
              path: 'execute',
              name: 'MarketingTaskVisitExecute',
              component: () => import('@/pages/marketing/task/visit/execute'),
              meta: { title: '执行管理' }
            },
            {
              path: 'publish',
              name: 'MarketingTaskVisitPublish',
              component: () => import('@/pages/marketing/task/visit/publish'),
              meta: { title: '任务详情' }
            }
          ]
        },
        {
          path: 'param',
          name: 'MarketingTaskParam',
          component: () => import('@/pages/marketing/task/param'),
          meta: { title: '参数设置' }
        }
      ]
    },
    {
      path: 'content',
      name: 'MarketingContent',
      component: () => import('@/pages/marketing/content'),
      redirect: { name: 'MarketingContentList' },
      meta: { title: '内容管理', noCache: true },
      children: [
        {
          path: 'videoMaterial',
          name: 'MarketingContentVideoMaterial',
          component: () =>
            import('@/pages/marketing/content/videoMaterial/index.vue'),
          meta: { title: '视频素材库' }
        },
        {
          path: 'list',
          name: 'MarketingContentList',
          component: () => import('@/pages/marketing/content/list/index.vue'),
          meta: { title: '文章视频列表' }
        },
        {
          path: 'articleAdd',
          name: 'MarketingContentArticleAdd',
          component: () => import('@/pages/marketing/content/list/article.vue'),
          meta: { title: '新增文章' }
        },
        {
          path: 'videoAdd',
          name: 'MarketingContentVideoAdd',
          component: () => import('@/pages/marketing/content/list/video.vue'),
          meta: { title: '新增视频' }
        },
        {
          path: 'popularize',
          name: 'MarketingContentPopularize',
          component: () => import('@/pages/marketing/content/popularize.vue'),
          meta: { title: '文章视频投放管理' }
        },
        {
          path: 'doulaList',
          name: 'MarketingContentDoulaList',
          component: () =>
            import('@/pages/marketing/content/doulaList/index.vue'),
          meta: { title: '抖喇列表' }
        },
        {
          path: 'doulaArticleAdd',
          name: 'MarketingContentDoulaArticleAdd',
          component: () =>
            import('@/pages/marketing/content/doulaList/article.vue'),
          meta: { title: '新增图文' }
        },
        {
          path: 'doulaVideoAdd',
          name: 'MarketingContentDoulaVideoAdd',
          component: () =>
            import('@/pages/marketing/content/doulaList/video.vue'),
          meta: { title: '新增短视频' }
        },
        {
          path: 'doulaPopularize',
          name: 'MarketingContentDoulaPopularize',
          component: () =>
            import('@/pages/marketing/content/doulaPopularize.vue'),
          meta: { title: '抖喇投放管理' }
        },
        {
          path: 'conference',
          name: 'MarketingConference',
          component: () =>
            import('@/pages/marketing/content/conference/index.vue'),
          meta: { title: '会议素材' }
        },
        {
          path: 'conferenceDetail',
          name: 'MarketingConferenceDetail',
          component: () =>
            import('@/pages/marketing/content/conference/detail.vue'),
          meta: { title: '会议编辑' }
        },
        {
          path: 'articleAdd',
          name: 'MarketingContentArticleAdd',
          component: () => import('@/pages/marketing/content/list/article.vue'),
          meta: { title: '新增文章' }
        }
      ]
    },
    {
      path: 'user',
      name: 'MarketingUser',
      component: () => import('@/pages/marketing/user'),
      redirect: { name: 'MarketingUserAudit' },
      meta: { title: '用户管理', noCache: true },
      children: [
        {
          path: 'audit',
          name: 'MarketingUserAudit',
          component: () => import('@/pages/marketing/user/audit.vue'),
          meta: { title: '实名审核' }
        },
        {
          path: 'creator',
          name: 'MarketingUserCreator',
          component: () => import('@/pages/marketing/user/creator.vue'),
          meta: { title: '创作者管理' }
        },
        {
          path: 'creatorAdd',
          name: 'MarketingUserCreatorAdd',
          component: () => import('@/pages/marketing/user/creatorAdd.vue'),
          meta: { title: '添加创作者' }
        },
        {
          path: 'promoter',
          name: 'MarketingUserPromoter',
          component: () => import('@/pages/marketing/user/promoter.vue'),
          meta: { title: '推广员管理' }
        },
        {
          path: 'unit',
          name: 'MarketingUserUnit',
          component: () => import('@/pages/marketing/user/unit.vue'),
          meta: { title: '单位管理' }
        }
      ]
    },
    {
      path: 'comment',
      name: 'MarketingComment',
      component: () => import('@/pages/marketing/comment'),
      redirect: { name: 'MarketingCommentList' },
      meta: { title: '评论管理', noCache: true },
      children: [
        {
          path: 'list',
          name: 'MarketingCommentList',
          component: () => import('@/pages/marketing/comment/list.vue'),
          meta: { title: '评论列表' }
        },
        {
          path: 'report',
          name: 'MarketingCommentReport',
          component: () => import('@/pages/marketing/comment/report.vue'),
          meta: { title: '举报管理' }
        }
      ]
    }
  ]
}

export default marketingRouter
