<template>
  <el-dialog title="选择任务内容" :visible.sync="dialogVisible" width="1300px" top="60px" class="selectTrainContent" center @close="handleClose">
    <div style="height: 700px;">
      <el-col :span="7">
        <div class="content-left">
          <div class="tree-title">
            <span>资源分类</span>
            <el-button v-if="showSelect" type="text" @click="cancelSelect">取消选择</el-button>
          </div>
          <Tree
            ref="tree"
            :highlight-current="highlightCurrent"
            @nodeClick="handleTreeNodeClick"
            @loadNode="handleLoadNode(arguments)"
          />
        </div>
      </el-col>
      <el-col :span="17">
        <div>
          <div class="content-type">
            <el-button-group>
              <el-button :type="activeName === 'course' ? 'primary' : ''" @click="swithCourseType('course')">教程</el-button>
              <el-button :type="activeName === 'sop' ? 'primary' : ''" @click="swithCourseType('sop')">SOP</el-button>
            </el-button-group>
          </div>
          <el-row class="search-column">
            <!-- 搜索条件 -->
            <div class="search-column__item">
              <el-input v-model="listQuery.condition.keyword" v-search="searchOption" size="small" placeholder="关键字搜索">
                <i slot="suffix" class="el-input__icon el-icon-search pointer" @click="getData({page: 1, pageSize: 10})" />
              </el-input>
            </div>
          </el-row>
          <el-row>
            <el-col>
              <el-table class="listTable" :data="tableData" size="mini" fit height="480" style="width: 100%">
                <el-table-column align="center" type="index" label="序号" />
                <el-table-column align="center" :label="activeName === 'sop'? 'sop名称':'教程名称'" width="240px">
                  <template slot-scope="{row}">
                    <span>{{ row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" :label="activeName === 'sop'? 'sop分类':'教程分类'">
                  <template slot-scope="{row}">
                    <el-tooltip class="item" popper-class="desc-tool-tip" effect="dark" placement="top">
                      <div slot="content"><div class="cateToolTip">{{ row.cateName }}</div></div>
                      <p class="desc-tip">{{ row.cateName }}</p>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column v-if="activeName === 'sop'" align="center" prop="courseNum" label="包含课程数" />
                <el-table-column v-if="activeName === 'course'" align="center" label="播放时长" width="80px">
                  <template v-if="activeName === 'course'" slot-scope="{row}">{{ formatSeconds(row.duration) }}</template>
                </el-table-column>
                <el-table-column align="center" :label="activeName === 'sop'? 'sop简介':'教程简介'" width="260">
                  <template slot-scope="{row}">
                    <el-tooltip class="item" popper-class="desc-tool-tip" effect="dark" :content="row.desc" placement="top">
                      <p class="desc-tip">{{ row.desc }}</p>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="80px">
                  <template slot-scope="{row}">
                    <el-button class="primary_span_color" type="text" @click="isSelected(row)?'':handleSelect(row)">{{ isSelected(row)? '已选':'选择' }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
          <pagination v-if="activeName === 'course'" :page="listQuery.pager.page" :total="total" @pagination="getData" />
          <pagination v-if="activeName === 'sop'" :page="listQuery.pager.page" :total="total" @pagination="getData" />
        </div>
        <div style="text-align:center;margin-top: 30px">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </el-col>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getTaskCourseList, getTaskSOPList, getCourseDetail, getSopCourseDetail } from '@/api/material'
import { formatSeconds } from '@/utils'
import { getNewCategory } from '@/api/category'
import Tree from '@/components/newCategory'

export default {
  name: 'SelectTrainContent',
  components: { Pagination, Tree },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    // 选中的条目
    selectedList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 选中的条目的版本id
    selectedListInfoId: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 不同页面进入时做区分 暂时先用这样做不同处理
    enterType: {
      type: String,
      default: 'materialDetail'
    },
    month: {
      type: Number,
      default: 1
    },
    name: {
      type: String,
      default: ''
    },
    certificate: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchOption: {
        delay: 500,
        fn: this.getData
      },
      formatSeconds,
      dialogVisible: this.show,
      activeName: 'course',
      listQuery: {
        condition: {
          cateId: null,
          keyword: '',
          materialType: 1, // 类型 course--1 sop--2
          gainWay: 1, // 本单位-1 平台-2
          deptId: '', // 专科
          categoryId: '' // 教程类型
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      tableData: [],
      total: 0,
      selectedCourseItemList: [], // 选择之后的教程表
      deptList: [],
      // 左侧分类
      showSelect: false,
      highlightCurrent: false, // 是否开启高亮
      infoIdList: [] // 教程的版本id，用于区分相同教程的不同版本
    }
  },
  watch: {
    show() {
      this.dialogVisible = this.show
    },
    selectedList: {
      handler(val) {
        this.infoIdList = val
      },
      immediate: true
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    // 左侧分类-------
    cancelSelect() {
      this.listQuery.condition.cateId = ''
      this.highlightCurrent = false
      this.showSelect = false
      this.getData()
    },
    // 获取当前试题内容
    handleTreeNodeClick(data, node) {
      this.showSelect = true
      this.highlightCurrent = true
      this.listQuery.condition.cateId = data.categoryId
      this.getData()
    },
    handleLoadNode([Node, resolve]) {
      // 如果展开第一级节点，从后台加载一级节点列表
      if (Node.level === 0) {
        getNewCategory(0).then(res => {
          res.forEach(item => {
            item.isLeaf = !item.havChild
          })
          resolve(res)
        })
      }
      // 如果展开其他级节点，动态从后台加载下一级节点列表
      if (Node.level >= 1) {
        getNewCategory(Node.data.categoryId).then(res => {
          res.forEach(item => {
            item.isLeaf = !item.havChild
          })
          resolve(res)
        })
      }
    },
    // 左侧分类-------
    isSelected(row) {
      if (this.selectedList && this.selectedList.length) {
        if (this.activeName === 'course') {
          // 教程 tab进入
          if (this.selectedList.includes(row.id)) {
            if (this.infoIdList.includes(row.id)) {
              return true
            }
          } else {
            return false
          }
        } else {
          // SOP tab进入
          let flag = false
          this.selectedList.forEach(v => {
            if (v.sopId === row.id) {
              if (v.children.length) {
                flag = true
              } else {
                flag = false
              }
            }
          })
          return flag
        }
      }
    },
    // 获取教程主方法
    getData(val) {
      if (val) this.listQuery.pager = val
      this.tableData = []
      if (this.listQuery.condition.materialType === 1) {
        getTaskCourseList(this.listQuery).then(res => {
          this.tableData = res.records
          this.total = res.total || 0
        })
      } else {
        getTaskSOPList(this.listQuery).then(res => {
          const tableDataTemp = []

          if (res.records.length) {
            res.records.forEach(v => {
              tableDataTemp.push({
                name: v.name,
                cateName: v.cateName,
                courseNum: v.courseNum,
                desc: v.introduction,
                id: v.id,
                sid: v.sid
              })
            })
          }
          this.tableData = tableDataTemp
          this.total = res.total || 0
        })
      }
    },
    // 切换教程类型
    swithCourseType(type) {
      if (this.activeName === type) return
      if (type === 'course') {
        this.listQuery.condition.materialType = 1
      } else {
        this.listQuery.condition.materialType = 2
      }
      this.listQuery.condition.gainWay = 1
      this.activeName = type
      this.total = 0
      this.listQuery.pager.page = 1
      this.getData()
    },
    handleClose() {
      this.$emit('update:show', false)
      this.$emit('handleClose')
    },
    // 当选中时
    handleSelect(row) {
      if (this.activeName === 'course') {
        // course
        const courseId = row.id
        const courseSource = this.listQuery.condition.gainWay
        getCourseDetail(`${courseId}/${courseSource}`).then(res => {
          const rowData = {
            courseId: res.courseId,
            courseInfoId: res.courseInfoId,
            courseName: res.name,
            courseOrder: '-',
            courseDuration: this.formatSeconds(row.duration),
            courseAccuracy: 70
          }
          if (!this.certificate) {
            rowData.month = this.month
            rowData.name = this.name
          }
          this.selectedCourseItemList.push(rowData)
          this.$emit('handleSelect', rowData.courseId)
          this.infoIdList.push(row.infoId)
        }).catch(() => {
          this.dialogVisible = false
        })
      } else {
        // sop
        const params = {
          sopId: row.id,
          sopSource: this.listQuery.condition.gainWay
        }
        getSopCourseDetail(params).then(res => {
          if (res.courseResponseDtos.length) {
            const selectedChildrenCourseIdList = [] // 选中的子级教程courseId集合
            let selectedflag = false
            let isMessage = false
            res.courseResponseDtos.forEach(v => {
              if (this.selectedCourseItemList && this.selectedCourseItemList.length) {
                selectedflag = this.selectedCourseItemList.some(sv => sv.courseId === v.courseId)
                if (selectedflag) {
                  isMessage = true
                } else {
                  const rowData = {
                    courseId: v.courseId,
                    courseInfoId: v.courseInfoId,
                    courseName: v.name,
                    courseOrder: v.listOrder,
                    courseAccuracy: v.accuracy,
                    courseDuration: this.formatSeconds(v.duration),
                    sopId: res.sid
                  }
                  if (!this.certificate) {
                    rowData.month = this.month
                    rowData.name = this.name
                  }
                  this.selectedCourseItemList.push(rowData)
                }
              } else {
                const rowData = {
                  courseId: v.courseId,
                  courseInfoId: v.courseInfoId,
                  courseName: v.name,
                  courseOrder: v.listOrder,
                  courseAccuracy: v.accuracy,
                  courseDuration: this.formatSeconds(v.duration),
                  sopId: res.sid
                }
                if (!this.certificate) {
                  rowData.month = this.month
                  rowData.name = this.name
                }
                this.selectedCourseItemList.push(rowData)
              }
              // sop里面的课程
              selectedflag = false
              selectedChildrenCourseIdList.push(v.courseId)
              return
            })
            if (isMessage) {
              return this.$message.error('包含两部相同的教程,系统默认仅适用一部')
            }
            // sop单条数据格式
            const selectedItem = { sopId: params.sopId, children: selectedChildrenCourseIdList }

            this.$emit('handleSelect', selectedItem, isMessage)
          } else {
            this.$message.warning('您选中的SOP暂无教程, 请重新选择')
          }
        }).catch(() => {
          this.dialogVisible = false
        })
      }
    },
    // 点击确定
    handleSubmit() {
      this.$emit('handleSubmit', this.selectedCourseItemList)
      this.selectedCourseItemList = [] // 清空已存在的对象
      this.dialogVisible = false
    },
    // 点击取消
    handleCancel() {
      this.selectedCourseItemList = []
      this.$emit('handleCancel')
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.selectTrainContent {
  &::v-deep .el-dialog__body {
    padding: 25px 35px;

    .el-button-group {
      margin-bottom: 23px;
    }
  }

  .content-type {
    display: flex;
    justify-content: center;
  }

  .search-column {
    height: 51px;
    line-height: 51px;
    padding: 0 15px;
    background: #dae5f1;
    margin-bottom: 17px;
  }

  .listTable {
    border-top: 1px solid #e4e4e4;
  }
}
.desc-tip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.content-left {
  height: 680px;
  margin-right: 10px;
  overflow-y: auto;

  .tree-title {
    height: 40px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-size: 22px;
      font-weight: bold;
      margin-right: 10px;
    }
  }
}
.cateToolTip{
  padding-right: 10px;
  min-height: 60px;
  max-height: 500px;
  overflow-y: auto;
}
</style>
