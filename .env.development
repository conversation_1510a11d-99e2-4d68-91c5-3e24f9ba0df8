# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/cmsapi'
VUE_APP_LOGIN_API = '/loginapi'
# VUE_APP_SAAS_URL = '/saasurl'
VUE_APP_SAAS_URL = 'https://localhost:9527/'
VUE_APP_SAAS_API_URL = '/saasapi'
VUE_APP_BIZ_URL = '/bizurl'
VUE_APP_H5_URL = 'http://testapph5.mycs.cn'
VUE_APP_FRONT_H5_URL = 'https://m-test.mycs.cn'

# VUE_APP_BASE_API = 'https://cmsapi.mycs.cn'
# VUE_APP_LOGIN_API = 'https://login.mycs.cn'
# VUE_APP_SAAS_URL = 'http://www.mycs.cn'
# VUE_APP_SAAS_API_URL = 'https://webapi.mycs.cn'

# VUE_APP_BASE_API = 'http://jianye.mycs.cn:8080'
# VUE_APP_LOGIN_API = 'http://jianye.mycs.cn:13700'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
