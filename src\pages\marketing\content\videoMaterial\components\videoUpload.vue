<template>
  <div ref="vodUploadInt" class="vod-upload">
    <el-badge v-if="fileProgressList.length > 0" :value="fileProgressList.length - successList.length">
      <el-button type="primary" plain class="select-btn" @click="uploadPanelShow = true">
        上传列表
      </el-button>
    </el-badge>
    <el-button v-else type="primary" class="select-btn">
      上传视频
      <label for="fileUpload">
        <input id="fileUpload" type="file" multiple @change="fileChange($event)">
      </label>
    </el-button>
    <transition>
      <div v-show="uploadPanelShow" class="upload-panel">
        <div class="panel-title"><i />上传列表 <i class="el-icon-close" @click="uploadPanelShow = false" /></div>
        <el-scrollbar wrap-class="panel-list">
          <div v-for="(file, index) in fileProgressList" :key="index" class="panel-item">
            <div class="panel-item-left">
              <div class="file-title">{{ file.name }}</div>
              <div class="file-progress">
                {{ file.totalSize ? (file.totalSize * file.progress / 100 / 1024 / 1024).toFixed(2) + ' MB/' + (file.totalSize / 1024 / 1024).toFixed(2) + ' MB' : '' }}
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                {{ file.pause ? '上传已暂停' : '' }}
              </div>
              <div class="file-tool">
                <div v-if="file.progress !== 100" class="file-progress-bg">
                  <div
                    class="file-progress"
                    :style="{ width: Math.floor(file.progress) + '%' }"
                    :class="{
                      'is-success': file.progress === 100
                    }"
                  />
                </div>
              </div>
            </div>
            <div v-if="file.progress !== 100" class="panel-item-right">
              <i v-if="!file.pause" class="mycs-icon mycs-icon-pause" @click.prevent="pause(index,0)" />
              <i v-if="file.pause" class="el-icon-caret-right" @click.prevent="pause(index,1)" />
              <i class="el-icon-close" @click.prevent="remove(index)" />
            </div>
          </div>
        </el-scrollbar>
      </div>
    </transition>
  </div>
</template>
<script>
import { preUploadVideoApi, refreshUploadAuthAli, getUploadAuth, preUpload, cancelPreupload } from '@/api/biz'
export default {
  name: 'VodUpload',
  data() {
    return {
      // 上传结果
      uploadResult: {
        fail: 0,
        cancel: 0,
        success: 0
      },
      // 上传面板
      uploadPanelShow: false,
      // 用于进度条展示
      fileProgressList: [],
      // 文件列表
      fileList: [],
      // 上传实例
      uploaders: [],
      // 上传组件配置
      vodUploadOption: {
        timeout: null,
        partSize: null,
        parallel: null,
        retryCount: null,
        retryDuration: null,
        region: null,
        userId: null
      },
      // 视频预上传参数 - vod
      preUploadVideoQuery: [
        // {
        //   filename: null,
        //   size: 0
        // }
      ],
      // 视频预上传列表 - 本地
      preUploadList: [],
      // 视频上传参数
      uploadVideoQuery: [
        // {
        //   converId: 0,
        //   data: null
        // }
      ],
      // 视频上传返回的文件凭证列表
      uploadVideoInfo: [],
      successList: []
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    // 选中文件
    fileChange(e) {
      // 清空已选择的文件内容
      this.fileList = []
      this.fileProgressList = []
      this.preUploadVideoQuery = []
      this.preUploadList = []
      // 检测是否有文件
      for (let i = 0; i < e.target.files.length; i++) {
        // 校验视频格式
        if (e.target.files[i].type !== 'video/mp4') {
          this.$message.error('上传文件不是MP4格式，请重新选择上传！')
          this.fileList = []
          this.fileProgressList = []
          this.preUploadVideoQuery = []
          this.preUploadList = []
          return
        }
        this.fileList.push(e.target.files[i])
        this.fileProgressList.push({ name: e.target.files[i].name, pause: false, cancel: false, progress: 0 })
        this.preUploadVideoQuery.push({ filename: e.target.files[i].name, size: e.target.files[i].size })
      }
      // 检测是否有文件列表
      if (!this.fileList.length) {
        alert('请先选择需要上传的文件!')
        return
      }
      // 预上传请求
      this.preUploadVideo()
    },
    pause(index, type) {
      if (this.uploaders[index]) {
        if (type === 0) {
          this.uploaders[index].stopUpload()
          this.fileProgressList[index].pause = true
        } else {
          this.uploaders[index].startUpload()
          this.fileProgressList[index].pause = false
        }
      }
    },
    // 取消文件上传
    remove(index) {
      const dom = `<div class="message-content" style="word-wrap: break-word">
                  <i class="el-icon-warning"></i> 确定取消上传 ${this.fileProgressList[index].name} ?</div>`
      this.$confirm(dom, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        if (this.uploaders[index]) {
          const params = this.preUploadList[index]
          cancelPreupload(params)
          this.uploaders[index].cancelFile(0)
          this.fileProgressList.splice(index, 1)
          this.fileList.splice(index, 1)
          if (this.successList.length === this.fileProgressList.length) {
            this.successList.forEach((v, index) => {
              this.uploaders[index].cleanList()
            })
            this.fileList = []
            this.fileProgressList = []
            this.preUploadVideoQuery = []
            this.preUploadList = []
            this.uploadPanelShow = false
            this.successList = []
            this.resetUploadResult()
          }
        }
      })
    },
    // 文件上传失败
    fail(name) {
      const index = this.fileList.filter(v => { return v.name === name })
      const params = this.preUploadList[index]
      cancelPreupload(params)
    },
    // 清除上传结果
    resetUploadResult() {
      this.uploadResult = {
        success: 0,
        fail: 0,
        cancel: 0
      }
    },
    // 预上传请求
    preUploadVideo() {
      preUploadVideoApi(this.preUploadVideoQuery).then(res => {
        if (res && res.length) {
          res.forEach(v => {
            this.uploadVideoQuery.push({ convertId: 0, data: v.data })
          })
          this.preUpload(res)
          this.getVideoUploadAuth()
        }
      })
    },
    // 预上传请求 -- 本地
    preUpload(res) {
      // 用于区分是否视频素材库上传
      res.forEach(res => {
        res.type = 'PROMOTE_VID'
      })
      preUpload(res).then(res => {
        if (res.length) {
          this.preUploadList = res
        }
      })
    },
    // 获取上传凭证
    getVideoUploadAuth() {
      getUploadAuth(this.uploadVideoQuery).then(res => {
        if (res.userId) {
          this.vodUploadOption.region = res.region
          this.vodUploadOption.userId = res.userId
          this.vodUploadOption.parallel = res.parallel
          this.vodUploadOption.retryCount = res.retryCount
          this.vodUploadOption.retryDuration = res.retryDuration
          this.vodUploadOption.timeout = res.timeout
          this.uploadVideoInfo = res.uploadInfo
          this.initUpload()
        } else {
          this.$message.error('视频上传发生了未知错误！请稍后尝试！')
        }
      })
    },
    // 实例化上传组件
    createUploader(i) {
      const self = this
      const uploader = new window.AliyunUpload.Vod({
        timeout: self.vodUploadOption.timeout || 60000,
        partSize: self.vodUploadOption.partSize || 1048576,
        parallel: self.vodUploadOption.parallel || 5,
        retryCount: self.vodUploadOption.retryCount || 3,
        retryDuration: self.vodUploadOption.retryDuration || 2,
        region: self.vodUploadOption.region,
        userId: self.vodUploadOption.userId,
        // 添加文件成功
        addFileSuccess: function(uploadInfo) {
          // console.log('addFileSuccess: ' + JSON.stringify(uploadInfo))
        },
        // 开始上传
        onUploadstarted: function(uploadInfo) {
          const fileIndex = self.fileList.findIndex(v => { return v.name === uploadInfo.file.name })
          const { uploadAuth, uploadAddress, vodVideoId } = self.uploadVideoInfo[fileIndex]
          if (!uploadInfo.videoId) {
            uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, vodVideoId)
          } else {
            refreshUploadAuthAli({ aliVid: uploadInfo.videoId }).then(res => {
              console.log(uploadInfo)
              const { uploadAuth, uploadAddress, vodVideoId } = res
              uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, vodVideoId)
            }).catch(() => {
              uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, vodVideoId)
            })
          }
        },
        // 文件上传成功
        onUploadSucceed: function(uploadInfo) {
          self.uploadResult.success++
          // console.log('onUploadSucceed: ' + uploadInfo.file.name + ', endpoint:' + uploadInfo.endpoint + ', bucket:' + uploadInfo.bucket + ', object:' + uploadInfo.object)
        },
        // 文件上传失败
        onUploadFailed: function(uploadInfo, code, message) {
          this.fail(uploadInfo.file.name)
          self.uploadResult.fail++
          // console.log('onUploadFailed: file:' + uploadInfo.file.name + ',code:' + code + ', message:' + message)
        },
        // 取消文件上传
        onUploadCanceled: function(uploadInfo, code, message) {
          // self.$message.success('"' + uploadInfo.file.name + '"文件已取消上传')
          self.uploadResult.cancel++
          // console.log('Canceled file: ' + uploadInfo.file.name + ', code: ' + code + ', message:' + message)
        },
        // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress: function(uploadInfo, totalSize, progress) {
          const index = self.fileProgressList.findIndex(item => { return item.name === uploadInfo.file.name })
          const progressPercent = Math.ceil(progress * 100)
          self.fileProgressList[index].progress = progressPercent
          self.fileProgressList[index].totalSize = totalSize
        },
        // 上传凭证超时
        onUploadTokenExpired: function(uploadInfo) {
          refreshUploadAuthAli({ aliVid: uploadInfo.videoId }).then(res => {
            const uploadAuth = res.UploadAuth
            uploader.resumeUploadWithAuth(uploadAuth)
            // console.log('upload expired and resume upload with uploadauth ' + uploadAuth)
          })
        },
        // 全部文件上传结束
        onUploadEnd: function() {
          self.successList.push(i)
          console.log(self.successList.length, self.fileList.length)
          if (self.successList.length === self.fileList.length) {
            self.$message.success('全部文件已处理完成！')
            self.$emit('get-list')
            // 清空已选择的文件内容
            self.successList.forEach((v, index) => {
              self.uploaders[index].cleanList()
            })
            self.fileList = []
            self.fileProgressList = []
            self.preUploadVideoQuery = []
            self.preUploadList = []
            self.uploadPanelShow = false
            self.successList = []
            console.log('upload result: success--->' + self.uploadResult.success + ' fail--->' + self.uploadResult.fail + ' cancel--->' + self.uploadResult.cancel)
            self.resetUploadResult()
          }
        }
      })
      return uploader
    },
    // 主动上传
    authUpload(i) {
      // 然后调用 startUpload 方法, 开始上传
      if (this.uploaders[i]) {
        this.uploaders[i].startUpload()
      }
    },
    // 调用上传
    initUpload() {
      this.uploadPanelShow = true
      // if (this.uploader) {
      //   this.uploader.stopUpload()
      // }

      this.fileList.forEach((v, i) => {
        this.uploaders[i] = this.createUploader(i)
        const userData = '{"Vod":{"name": "' + v.name + '"}}'
        this.uploaders[i].addFile(v, null, null, null, userData)
        this.authUpload(i)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
%pr {
  position: relative;
}
%pa {
  position: absolute;
}
%bold {
  font-weight: bold;
}
%box {
  box-sizing: border-box;
}
.el-mask{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000000;
  background: rgba($color: #000000, $alpha: 0.4);
  z-index: 9999;
}
.vod-upload{
  overflow: visible;
  @extend %pr;
  vertical-align: middle;
}
.select-btn{
  position: relative;
  label{
    cursor:pointer;
    @extend %pa;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    input{
      @extend %pa;
      top: -9999px
    }
  }
}
.upload-panel {
  content: "";
  display: block;
  @extend %pa, %box;
  width: 430px;
  height: 354px;
  padding-top: 42px;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: -1px 2px 10px 0 rgba(16, 16, 16, 0.4);
  border-radius: 2px;
  right: calc(100% + 19px);
  top: 0;
  text-align: center;
  font-size: 14px;
  z-index: 10000;

  .panel-title {
    @extend %bold, %box, %pa;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    color: #409eff;
    background: #f8f8f8;
    line-height: 42px;
    left: 0;
    top: 0;
    i {
      cursor: pointer;
      @extend %bold;
    }
  }

  ::v-deep .panel-list {
    max-height: calc(354px - 26px);
    padding: {
      top: 4px;
      bottom: 19px;
    }
    @extend %box;
  }
  .panel-item {
    @extend %box;
    padding: 8px 18px;
    display: flex;
    align-items: center;
    &-left {
      width: 90%;
      .file-title {
        margin-bottom: 5px;
        width: 100%;
        overflow: hidden;
        text-align: left;
        word-wrap: break-word;
      }
      .file-progress {
        margin-bottom: 5px;
        color: #999;
        font-size: 12px;
        width: 100%;
        overflow: hidden;
        text-align: left;
      }

      .file-tool {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @mixin bar($bg) {
          height: 6px;
          border-radius: 3px;
          flex: 1;
          margin-right: 21px;
          background: $bg;
        }

        .file-progress-bg {
          @extend %pr;
          @include bar(#e9e9e9);

          .file-progress {
            transition: width 0.2s ease;
            @extend %pa;
            @include bar(#409eff);

            &.is-error {
              background: #f40f0c;
            }
          }
        }
      }
    }
    &-right {
      display: flex;
      align-items: center;
      i {
          margin-right: 5px;
          font-size: 18px;
          cursor: pointer;
          color: #409eff;
          @extend %bold;

          &:hover {
            color: #f40f0c;
          }
        }
    }
  }
}
</style>
