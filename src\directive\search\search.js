/**
 * 'v-search' 搜索框指令
 * @params {Object} value 输入框失去焦点的时的设置
 * @params {Function} value.fn 输入框失去焦点时的操作
 * @params {Number} value.delay 设置输入框失去焦点时多久执行 fn
 *
 */
export default {
  bind(el, binding, vnode) {
    const int = el.children[0]
    if (int.tagName === 'INPUT') {
      int.addEventListener('keyup', e => {
        if (e.code === 'Enter') {
          int.blur()
        }
      })
      int.addEventListener('blur', e => {
        const { value } = binding
        if (typeof value.fn === 'function') {
          value.delay = value.delay || 500
          const timer = setTimeout(function() {
            value.fn()
            clearTimeout(timer)
          }, value.delay)
        }
      })
    }
  }
}
