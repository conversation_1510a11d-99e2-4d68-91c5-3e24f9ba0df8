<template>
  <div class="promote ad-info">
    <h3>曝光点击明细
      <el-button type="primary" @click="exportExcel">导出</el-button>
    </h3>
    <el-table :data="promoteDetail" border stripe>
      <el-table-column prop="advertiserName" label="广告主" align="center" />
      <el-table-column prop="name" label="广告" align="center" />
      <el-table-column prop="realName" label="姓名" align="center" />
      <el-table-column prop="phone" label="手机" align="center" />
      <el-table-column prop="identityName" label="身份" align="center" />
      <el-table-column prop="majorName" label="专科" align="center" />
      <el-table-column prop="academicName" label="职称" align="center" />
      <el-table-column prop="orgName" label="单位" align="center" />
      <el-table-column prop="areaName" label="区域" align="center" />
      <el-table-column prop="ip" label="IP" align="center" />
      <el-table-column prop="exposureTime" label="曝光时间" align="center" />
      <el-table-column prop="hitTime" label="点击时间" align="center" />
      <el-table-column prop="link" label="点击链接" align="center" />
    </el-table>
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <a v-show="false" ref="downId" :download="download" :href="excelUrl" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { adExposureClickList, adExposureClickExport } from '@/api/ad'
export default {
  components: { Pagination },
  props: {
    adId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      promoteDetail: [],
      tableQuery: {
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // pagination
      layout: 'total, prev, pager, next, jumper',
      total: 0,
      // download
      excelUrl: '',
      download: ''
    }
  },
  watch: {
    adId: {
      deep: true,
      handler() {
        this.getadExposureClickList()
      }
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getadExposureClickList()
    }
  },
  methods: {
    getadExposureClickList() {
      const param = {
        ...this.tableQuery,
        adId: this.adId
      }
      adExposureClickList(param).then(res => {
        res.records.forEach(item => {
          // 如果ip有值优先用ip，没有的话用rip字段的值
          item.ip = item.ip === '' ? item.rip : item.ip
        })
        this.promoteDetail = res.records
        this.total = res.total
      })
    },
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getadExposureClickList()
    },
    exportExcel() {
      adExposureClickExport(this.adId).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.promote {
  h3 {
    position: relative;
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
    button {
      position: absolute;
      top: 5px;
      right: 20px;
    }
  }
}
</style>
