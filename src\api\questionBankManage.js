import request from '@/utils/request'

// 试题分类-获取单位所有试题分类信息树形结构集合，权限
export function getAllTree() {
  return request({
    url: '/question/category/getAllTreeList',
    method: 'get'
  })
}

// 添加试题分类，权限
export function addCategory(params) {
  return request({
    url: '/question/category/add',
    method: 'post',
    data: params
  })
}

// 编辑试题分类，权限
export function updateCategory(params) {
  return request({
    url: '/question/category/update',
    method: 'post',
    data: params
  })
}

// 删除试题分类，权限
export function deleteCategory(params) {
  return request({
    url: `/question/category/delete`,
    method: 'get',
    params: { questionCategoryId: params }
  })
}

// 拖拽更新分类：父级，层级，排序，权限
export function dragUpdateCategory(params) {
  return request({
    url: '/question/category/dragUpdate',
    method: 'post',
    data: params
  })
}

// 搜索查询-获取下级分类集合，权限标识
// export function childListByParentId(params) {
//   return request({
//     url: '/question/category/childListByParentId',
//     method: 'get',
//     data: { parentId: params }
//   })
// }

// 试题列表-平台
export function paperList(params) {
  return request({
    url: '/paper/list',
    method: 'post',
    data: params
  })
}

// 识别试题
export function recognize(params) {
  return request({
    url: '/paper/recognize',
    method: 'post',
    data: params
  })
}

// 录入试题
export function paperCreate(params) {
  return request({
    url: '/paper/create',
    method: 'post',
    data: params
  })
}

// 删除试题
export function paperDel(params) {
  return request({
    url: `/paper/delete/${params}`,
    method: 'post'
  })
}

// 试题详情
export function paperDetail(params) {
  return request({
    url: `/paper/detail/${params}`,
    method: 'get'
  })
}

// 修改分类
export function updateCates(params) {
  return request({
    url: '/paper/update/cates',
    method: 'post',
    data: params
  })
}

export function paperupdate(params) {
  return request({
    url: '/paper/update',
    method: 'post',
    data: params
  })
}
