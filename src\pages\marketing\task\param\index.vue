<template>
  <section class="params">
    <el-form ref="form" :model="form" label-width="140px">
      <el-form-item label="拜访范围限制 (米) :">
        <el-input v-model.number="form.visitScope" />
      </el-form-item>
      <el-form-item label="首页推广位数量 :">
        <el-input v-model.number="form.recommendNum" />
      </el-form-item>
      <el-form-item label="文章-视频比例 :">
        <div class="ratio">
          <el-input v-model.number="form.recommendArticle" />
          <span>:</span>
          <el-input v-model.number="form.recommendVideo" />
        </div>
      </el-form-item>
      <el-form-item label="抖喇推广位数量 :">
        <el-input v-model.number="form.doulaRecommendNum" />
      </el-form-item>
      <el-form-item label="图文-短视频比例 :">
        <div class="ratio">
          <el-input v-model.number="form.doulaRecommendArticle" />
          <span>:</span>
          <el-input v-model.number="form.doulaRecommendVideo" />
        </div>
      </el-form-item>
      <el-form-item label="年度预警线 :">
        <el-input v-model.number="form.annualWarningLine" :maxlength="7" />
      </el-form-item>
      <el-form-item label="年度限制线 :">
        <el-input v-model.number="form.annualLimitLine" :maxlength="7" />
      </el-form-item>
      <el-form-item>
        <el-button @click="getConfig()">取消</el-button>
        <el-button type="primary" @click="saveConfig()">保存</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import { getPromoteConfig, setPromoteConfig } from '@/api/marketing/taskPromote'
export default {
  data() {
    return {
      form: {
        visitScope: 0,
        recommendNum: 0,
        recommendArticle: 0,
        recommendVideo: 0,
        doulaRecommendNum: 0,
        doulaRecommendArticle: 0,
        doulaRecommendVideo: 0,
        annualWarningLine: 0,
        annualLimitLine: 0
      }
    }
  },
  created() {
    this.getConfig()
  },
  methods: {
    getConfig() {
      getPromoteConfig().then(res => { this.form = res })
    },
    saveConfig() {
      if (this.form.annualLimitLine > this.form.annualWarningLine) {
        setPromoteConfig(this.form).then(() => { this.$message.success('设置成功') })
      } else {
        this.$message.error('年度限制线必须大于预警线')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.params {
  margin: 80px;
  width: 500px;
  .ratio {
    display: flex;
    .el-input {
      width: 60px
    }
    span {
      margin: 0 10px;
    }
  }
}
</style>
