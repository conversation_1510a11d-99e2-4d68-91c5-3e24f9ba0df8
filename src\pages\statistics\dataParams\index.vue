<template>
  <section class="data-params">
    <div>
      <h2>数据设置</h2>
      <ul>
        <li>
          <div>
            <span>培训人次:</span>
            <el-input-number v-model="params.peopleNum" :disabled="edit" :controls="false" />
          </div>
          <div>
            <span>培训覆盖率: </span>
            <el-input-number v-model="params.coverRate" :disabled="edit" :controls="false" /> %
          </div>
          <div>
            <span>刷新间隔: </span>
            <el-input-number v-model="params.refreshTime" :disabled="edit" :controls="false" /> 分钟
          </div>
        </li>
        <li>
          <div>
            <span>参与人次: </span>
            <el-input-number v-model="params.joinNum" :disabled="edit" :controls="false" />
          </div>
          <div>
            <span>培训参与率: </span>
            <el-input-number v-model="params.joinRate" :disabled="edit" :controls="false" /> %
          </div>
        </li>
        <li>
          <div>
            <span>通过人次: </span>
            <el-input-number v-model="params.passNum" :disabled="edit" :controls="false" />
          </div>
          <div>
            <span>培训通过率: </span>
            <el-input-number v-model="params.passRate" :disabled="edit" :controls="false" /> %
          </div>
        </li>
      </ul>
      <el-button type="primary" @click="editParams()">{{ edit ? '编辑' : '保存' }}</el-button>
    </div>
    <div>
      <h2>APP运营大屏访问账号
        <div><el-button type="primary" @click="dialogVisible = true">添加</el-button></div>
      </h2>
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="delAccount(row.uid)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <select-user :dialog-visible.sync="dialogVisible" @enter="addAccount" />
  </section>
</template>

<script>
import { getTrainData, editTrainData, getVisitAccount, delVisitAccount, addVisitAccount } from '@/api/statistics'
import selectUser from './components/searchUser.vue'
export default {
  name: 'DataParams',
  components: { selectUser },
  data() {
    return {
      params: {
        coverRate: 0,
        joinNum: 0,
        joinRate: 0,
        passNum: 0,
        passRate: 0,
        peopleNum: 0,
        refreshTime: 0
      },
      edit: true,
      tableColumn: [
        { prop: 'uid', label: 'UID' },
        { prop: 'realName', label: '姓名', width: '30' },
        { prop: 'username', label: '账号', width: '80' },
        { prop: 'phone', label: '绑定手机', width: '40' }
      ],
      tableData: [],
      dialogVisible: false
    }
  },
  created() {
    this.getParamsData()
    this.getAccount()
  },
  methods: {
    getParamsData() {
      getTrainData().then(res => {
        this.params = res
      })
    },
    editParams() {
      if (this.edit) {
        this.edit = false
      } else {
        editTrainData(this.params).then(() => {
          this.getParamsData()
          this.edit = true
        })
      }
    },
    getAccount() {
      getVisitAccount().then(res => {
        this.tableData = res
      })
    },
    delAccount(uid) {
      this.$confirm('确认后,将删除该账号的APP运营大屏访问权限', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delVisitAccount(uid).then(() => {
          this.getAccount()
        })
      })
    },
    addAccount(uid) {
      addVisitAccount(uid).then(() => {
        this.getAccount()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.data-params {
  padding: 20px 40px;
  h2 {
    display: flex;
    justify-content: space-between;
  }
  ul {
    display: flex;
    width: 1200px;
    padding: 0;
    li {
      margin: 10px 20px;
      div {
        span {
          display: inline-block;
          width: 90px;
          margin-bottom: 40px;
          text-align: right;
        }
      }
    }
  }
}
</style>
