import request from '@/utils/request'

// 预创建培训计划
export function createPackagePre(params) {
  return request({
    url: '/examine/examinepackageTemplate/pre/add',
    method: 'post',
    data: params
  })
}
// 启用/禁用
export function enablePackage(params) {
  return request({
    url: '/examine/examinepackageTemplate/enable',
    method: 'post',
    params
  })
}
// 培训计划列表
export function packageList(params) {
  return request({
    url: '/examine/examinepackageTemplate/packagePage',
    method: 'post',
    data: params
  })
}
// 编辑培训计划
export function editPackage(params) {
  return request({
    url: '/examine/examinepackageTemplate/update',
    method: 'post',
    data: params
  })
}

// 培训计划详情
export function getPackageDetail(id) {
  return request({
    url: `/examine/examinepackageTemplate/detail/${id}`,
    method: 'get'
  })
}

// 培训计划可选单位列表
export function jobTrainOrgList(params) {
  return request({
    url: '/organization/examinepackageTemplate/orgList',
    method: 'post',
    data: params
  })
}

// 培训计划已选单位列表
export function jobTrainSelectedOrgList(params) {
  return request({
    url: '/organization/examinepackageTemplate/selectedOrgList',
    method: 'post',
    data: params
  })
}

// 添加培训计划单位
export function jobTrainAddOrg(params) {
  return request({
    url: '/organization/examinepackageTemplate/batchAddOrg',
    method: 'post',
    data: params
  })
}

// 删除培训计划单位
export function jobTrainDelOrg(params) {
  return request({
    url: '/organization/examinepackageTemplate/batchDelOrg',
    method: 'post',
    data: params
  })
}
