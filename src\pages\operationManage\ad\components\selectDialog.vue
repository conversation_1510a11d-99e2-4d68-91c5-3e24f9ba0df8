<template>
  <el-dialog title="选择广告" :visible.sync="dialogShow" width="60%" top="8vh">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.advertisementName" placeholder="请输入广告名称/广告主" clearable @keyup.enter.native="search">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="search" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.adTypeId" filterable clearable placeholder="请选择广告落地页类型">
          <el-option v-for="item in adTypeList" :key="item.adTypeId" :label="item.name" :value="item.adTypeId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.playPlaceId" filterable clearable placeholder="请选择广告位置">
          <el-option v-for="item in adPositionList" :key="item.adPlaceId" :label="item.place" :value="item.adPlaceId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.status" filterable clearable placeholder="请选择广告状态">
          <el-option v-for="item in adStateList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
    </div>

    <!-- ad list -->
    <el-table
      ref="table"
      size="mini"
      :data="adList"
      border
      stripe
      highlight-current-row
      @row-click="handleCurrentChange"
    >
      <el-table-column prop="name" label="广告名称" align="center" />
      <el-table-column prop="advertiserName" label="广告商" align="center" />
      <el-table-column label="广告落地页类型" align="center">
        <template slot-scope="{row}">
          <span>{{ row.adType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="广告位置" align="center">
        <template slot-scope="{row}">
          <span>{{ row.playPlace }}</span>
        </template>
      </el-table-column>
      <el-table-column label="广告状态" align="center">
        <template slot-scope="{row}">
          <span>{{ row.status | statusFmt }}</span>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogShow = false">取 消</el-button>
      <el-button type="primary" @click="dialogShow = false">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { adType, adPlace, adList } from '@/api/ad'
import Pagination from '@/components/Pagination/index.vue'
import table from '@/mixins/table'

export default {
  name: 'SelectDialog',
  components: { Pagination },
  filters: {
    statusFmt(v) {
      const arr = ['下架', '上架', '待上架']
      return arr[v - 1]
    }
  },
  mixins: [table],
  props: {
    timeRange: {
      type: [Array, Object],
      default: () => []
    }
  },
  data() {
    return {
      dialogShow: false,
      // search params
      tableQuery: {
        condition: {
          adTypeId: '',
          playPlaceId: '',
          status: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // select list
      adTypeList: [],
      adPositionList: [],
      // table
      adList: [],
      adStateList: [
        { value: 1, name: '下架' },
        { value: 2, name: '上架' },
        { value: 3, name: '待上架' }
      ],
      // pagination
      layout: 'total, prev, pager, next, jumper',
      total: 0
    }
  },
  watch: {
    dialogShow(v) {
      if (v) {
        this.getAdList()
        Promise.all([adType(), adPlace()]).then(res => {
          [this.adTypeList, this.adPositionList] = res
        })
      }
    },
    'tableQuery.condition': {
      handler(v) {
        this.tableQuery.pager.page = 1
        this.getAdList()
      },
      deep: true
    },
    timeRange(val) {
      this.tableQuery.condition.startTime = val ? val[0] : ''
      this.tableQuery.condition.endTime = val ? val[1] : ''
    }
  },
  methods: {
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getAdList()
    },
    search() {
      this.tableQuery.pager.page = 1
      this.getAdList()
    },
    // get ad list
    getAdList() {
      adList(this.tableQuery).then(res => {
        this.adList = res.records
        this.total = res.total
      })
    },
    // select row
    handleCurrentChange(val) {
      this.$emit('select', val)
      this.dialogShow = false
    }
  }
}
</script>
