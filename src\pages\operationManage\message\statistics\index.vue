<template>
  <section class="statistics">
    <div class="screen">
      <el-input
        v-model="messageTitle"
        placeholder="请先选择消息"
        style="width: 300px"
        disabled
      />
      <el-button type="primary" @click="selectMessage">选择消息</el-button>
      <el-select v-model="listQuery.condition.channel" placeholder="请选择">
        <el-option label="App push" value="APP_PUSH" />
        <el-option label="站内信" value="STATION_MSG" />
      </el-select>

      <select-message
        :dialog-visible.sync="dialogVisible"
        :channel.sync="listQuery.condition.channel"
        :msg-id.sync="listQuery.condition.msgId"
        :msg-title.sync="messageTitle"
      />
    </div>
    <ul>
      <li>
        <p>发送人数</p>
        {{ sendCount }}
      </li>
      <li>
        <p>{{ listQuery.condition.channel === 'APP_PUSH' ? '已送达人数' : '已读人数' }}</p>
        {{ deliveredCount }}
      </li>
      <li>
        <p>{{ listQuery.condition.channel === 'APP_PUSH' ? '未送达人数' : '未读人数' }}</p>
        {{ unDeliveredCount }}
      </li>
    </ul>
    <div class="data">
      <h3>
        <el-radio-group v-model="listQuery.condition.sendStatus">
          <el-radio-button :label="1">{{ listQuery.condition.channel === 'APP_PUSH' ? '已送达' : '已读' }}</el-radio-button>
          <el-radio-button :label="0">{{ listQuery.condition.channel === 'APP_PUSH' ? '未送达' : '未读' }}</el-radio-button>
        </el-radio-group>
      </h3>
      <div class="data-screen">
        <el-input v-model="keyword" class="input-with-select" placeholder="输入查询方式对应关键字" clearable @keyup.enter.native="search">
          <el-select slot="prepend" v-model="keywordType" filterable clearable placeholder="个人账号" style="width: 110px">
            <el-option v-for="item in accountList" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="search" />
        </el-input>
        <el-cascader
          v-model="listQuery.condition.areaIds"
          placeholder="请选择区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'areaId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="search"
        />
        <el-select
          v-model="listQuery.condition.identityIds"
          multiple
          filterable
          clearable
          placeholder="请选择身份"
          collapse-tags
          @change="search"
        >
          <el-option v-for="item in identityList" :key="item.identityId" :label="item.name" :value="item.identityId" />
        </el-select>
        <el-cascader
          v-model="listQuery.condition.majorIds"
          placeholder="请选择专科"
          :options="majorList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'majorId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="search"
        />
        <el-cascader
          v-model="listQuery.condition.academicIds"
          placeholder="请选择职称"
          :options="academicTreeList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="search"
        />
      </div>
      <div class="data-table">
        <el-table
          :data="tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        >
          <el-table-column
            v-for="item in tableColumns"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
            align="center"
          />
          <el-table-column prop="handle" label="操作" width="80" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="viewPersonal(scope.row)"
              >员工信息</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :page-size="listQuery.pager.pageSize" :total="total" :page="listQuery.pager.page" @pagination="pagination" />
        <DialogUserInfo title="查看账号信息" :show.sync="dialogInfoVisible" :data="userInfoData" />
      </div>
    </div>
  </section>
</template>

<script>
import SelectMessage from '../components/selectMessage.vue'
import Pagination from '@/components/Pagination/index.vue'
import DialogUserInfo from '@/pages/user/compontents/dialogUserInfo.vue'
import { getPersonalDetail } from '@/api/userManage'
import { getAreaTree } from '@/api/area'
import { identityList } from '@/api/category'
import { treeList } from '@/api/major'
import { academicTreeList } from '@/api/academic'
import { personList, overview } from '@/api/message'
export default {
  components: {
    SelectMessage,
    Pagination,
    DialogUserInfo
  },
  data() {
    return {
      messageTitle: '',
      listQuery: {
        condition: {
          areaIds: [],
          identityIds: [],
          majorIds: [],
          academicIds: [],
          msgId: '',
          channel: 'APP_PUSH',
          sendStatus: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      dialogVisible: false,
      sendCount: 0,
      deliveredCount: 0,
      unDeliveredCount: 0,
      people: [],
      keyword: '',
      keywordType: 'userName',
      accountList: [
        { value: 'userName', name: '个人账号' },
        { value: 'phone', name: '手机' },
        { value: 'realName', name: '用户姓名' },
        { value: 'staffAccount', name: '员工账号' },
        { value: 'staffName', name: '员工姓名' }
      ],
      areaList: [],
      identityList: [],
      majorList: [],
      academicTreeList: [],
      tableData: [],
      tableColumns: [
        { label: 'UID', align: 'center', prop: 'uid' },
        { label: '姓名', align: 'center', prop: 'realName' },
        { label: '个人账号', align: 'center', prop: 'userName' },
        { label: '手机', align: 'center', prop: 'phone' },
        { label: '身份', align: 'center', prop: 'identityName' },
        { label: '专科', align: 'center', prop: 'academicName' },
        { label: '职称', align: 'center', prop: 'majorName' },
        { label: '区域', align: 'center', prop: 'areaName' },
        { label: '注册时间', align: 'center', prop: 'regTime' }
      ],
      dialogInfoVisible: false,
      userInfoData: []
    }
  },
  watch: {
    'listQuery.condition.msgId'(v) {
      if (v) {
        this.getPeople()
        this.getList()
      }
    },
    'listQuery.condition.channel'() {
      if (this.listQuery.condition.msgId) {
        this.getPeople()
        this.getList()
      }
    },
    'listQuery.condition.sendStatus'(v) {
      this.getList()
    },
    keyword(v) {
      this.listQuery.condition[this.keywordType] = v
    },
    keywordType(v) {
      this.listQuery.condition.phone = ''
      this.listQuery.condition.realName = ''
      this.listQuery.condition.staffAccount = ''
      this.listQuery.condition.staffName = ''
      this.listQuery.condition.userName = ''
    }
  },
  created() {
    if (this.$route.params.id) {
      this.listQuery.condition.msgId = this.$route.params.id
      this.messageTitle = this.$route.params.title
      this.getPeople()
      this.getList()
    }
    Promise.all([getAreaTree(), identityList(), treeList(), academicTreeList()]).then(res => {
      [this.areaList, this.identityList, this.majorList, this.academicTreeList] = res
    })
  },
  methods: {
    selectMessage() {
      this.dialogVisible = true
    },
    getPeople() {
      const query = {
        channel: this.listQuery.condition.channel,
        id: this.listQuery.condition.msgId
      }
      overview(query).then(res => {
        this.sendCount = res.sendCount
        this.deliveredCount = res.deliveredCount
        this.unDeliveredCount = res.unDeliveredCount
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    getList() {
      personList(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    pagination(val) {
      this.listQuery.pager = val
      this.getList()
    },
    viewPersonal(row) {
      getPersonalDetail({ userId: row.uid }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    font-size: 0;
    .el-button {
      border-radius: 0;
    }
    .el-input__inner {
      border-radius: 0;
    }
    .el-select {
      margin-left: 20px;
    }
  }
  ul {
    display: flex;
    justify-content: space-between;
    padding: 50px;
    background-color: #fff;
    border: 1px solid #eee;
    font-size: 30px;
    p{
      font-size: 16px;
    }
  }
  .data {
    margin-top: 20px;
    border: 1px solid #eee;
    overflow: hidden;
    h3 {
      margin: 0;
      background: #eee;
      line-height: 50px;
      padding-left: 20px;
      .el-button {
        position: absolute;
        top: 5px;
        right: 20px;
      }
    }
    &-screen {
      padding: 20px 20px 0;
      .el-input{
        width:320px
      }
    }
    &-table {
      padding: 20px;
    }
  }
}
</style>
