/**
 * 'v-permission' 用户权限指令
 * @params {Array} value 当前元素的操作权限。eg: v-permission="['account:staff:add']"
 */
/**
 * 权限表查看 user_permission.json
 */

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    if (Object.prototype.toString.call(value) !== '[object Array]') {
      throw Error(`权限检查输入有误：${value}`)
    }
    let flag = false
    const result = []
    const operations = JSON.parse(localStorage.getItem('SET_OPERATION'))
    if (operations && operations.length) {
      if (value && value.length) {
        operations.forEach(v => {
          const _flag = value.includes(Object.keys(v)[0])
          result.push(_flag)
        })
        result.filter(v => { if (v) flag = true })
        if (!flag) {
          el.parentNode && el.parentNode.removeChild(el)
        }
      } else {
        throw Error(`请设置相关权限!`)
      }
    } else {
      throw new Error(`权限分配有误！请重新登录获取`)
    }
  }
}
