import request from '@/utils/request'

// 单位会员分页查询
export function memberOrgPage(params) {
  return request({
    url: '/member/org/memberOrgPage',
    method: 'post',
    data: params
  })
}

// 开通单位会员
export function addMemberOrg(params) {
  return request({
    url: '/member/org/addMemberOrg',
    method: 'post',
    data: params
  })
}

// 续期/升级单位会员
export function renewalMemberOrg(params) {
  return request({
    url: '/member/org/renewalMemberOrg',
    method: 'post',
    data: params
  })
}

// 删除单位会员
export function deleteMemberOrg(orgId) {
  return request({
    url: `/member/org/delete/${orgId}`,
    method: 'post'
  })
}

// 单位开通会员的历史记录
export function getActiveLog(orgId) {
  return request({
    url: `/member/org/activeLog/${orgId}`,
    method: 'get'
  })
}
