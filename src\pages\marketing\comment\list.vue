<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="searchKeyword"
        class="group"
        placeholder="请输入搜索关键字"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
        <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="clearKeyWord">
          <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-input>

      <el-select
        v-model="listQuery.condition.blockStatus"
        placeholder="全部状态"
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.articleType"
        placeholder="全部类型"
        @change="search"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      评论列表
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.isBlock === '正常'"
              type="text"
              size="mini"
              @click="updateBlock(scope.row.id,1)"
            >屏蔽</el-button>
            <el-button
              v-if="scope.row.isBlock === '屏蔽'"
              type="text"
              size="mini"
              @click="updateBlock(scope.row.id,0)"
            >取消屏蔽</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { commentList, commentBlock } from '@/api/marketing/commentPromote'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      searchType: 'userName',
      searchTypeList: [
        { label: '评论人', value: 'userName' },
        { label: '评论人账号', value: 'userAccount' },
        { label: '评论内容', value: 'commentContent' },
        { label: '评论对象', value: 'articleTitle' }
      ],
      searchKeyword: '',
      stateOptions: [
        { label: '全部状态', value: null },
        { label: '正常', value: 0 },
        { label: '屏蔽', value: 1 }
      ],
      typeOptions: [
        { label: '全部类型', value: null },
        { label: '文章', value: 'ARTICLE' },
        { label: '视频', value: 'VIDEO' },
        { label: '抖喇图文', value: 'DOULA_ARTICLE' },
        { label: '抖喇视频', value: 'DOULA_VIDEO' }
      ],
      listQuery: {
        condition: {
          blockStatus: null,
          articleType: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'id', label: '评论ID', width: '60' },
        { prop: 'userName', label: '评论人', width: '60' },
        { prop: 'userAccount', label: '评论人账号', width: '60' },
        { prop: 'content', label: '评论内容', width: '200' },
        { prop: 'createdTime', label: '评论时间', width: '120' },
        { prop: 'articleTitle', label: '评论对象', width: '120' },
        { prop: 'articleType', label: '评论对象类型', width: '60' },
        { prop: 'likeNum', label: '点赞数', width: '60' },
        { prop: 'isBlock', label: '状态', width: '60' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getcommentList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getcommentList()
    },
    getcommentList() {
      commentList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.isBlock = this.stateOptions.find(i => i.value === v.isBlock).label
          v.articleType = this.typeOptions.find(i => i.value === v.articleType).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    clearKeyWord() {
      this.searchKeyword = ''
      this.listQuery.condition = {
        blockStatus: this.listQuery.condition.blockStatus,
        articleType: this.listQuery.condition.articleType
      }
      this.getcommentList()
    },
    search() {
      this.listQuery.pager.page = 1
      if (this.searchKeyword === '') {
        this.listQuery.condition = {
          blockStatus: this.listQuery.condition.blockStatus,
          articleType: this.listQuery.condition.articleType
        }
      } else {
        this.listQuery.condition[this.searchType] = this.searchKeyword
      }
      this.getcommentList()
    },
    updateBlock(commentId, isBlock) {
      commentBlock({ commentId, isBlock }).then(() => {
        this.getcommentList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
