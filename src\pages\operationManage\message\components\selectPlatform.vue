<template>
  <el-dialog
    :title="typeTitle"
    :visible.sync="dialogVisible"
    width="50%"
    center
    :before-close="handleClose"
  >
    <el-input
      v-model="listQuery.condition.name"
      :placeholder="placeholder"
      class="input-with-select"
      style="width: 300px"
    >
      <el-button slot="append" icon="el-icon-search" @click="getList" />
    </el-input>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        v-for="item in tableColumn"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        align="center"
      />
      <el-table-column prop="handle" label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="select(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </el-dialog>

</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getPlatformContents } from '@/api/message'
import { certExamineList } from '@/api/certificate'
export default {
  components: {
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      typeTitle: '选择视频资源',
      listQuery: {
        condition: {
          contentType: this.type,
          name: null,
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 6
        }
      },
      tableData: [],
      tableColumn: [
        { prop: 'name', label: '名称/标题' },
        { prop: 'author', label: '作者', width: '120' }
      ],
      total: 0
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        this.getList()
      }
      this.listQuery.condition.name = ''
    },
    type: {
      handler(v) {
        if (v === 'PLATFORM_VIDEO') {
          this.typeTitle = '选择视频资源'
          this.placeholder = '请输入视频名称'
          this.tableColumn = [
            { prop: 'name', label: '名称/标题' },
            { prop: 'durationTime', label: '视频时长', width: '120' }
          ]
        } else if (v === 'EXAMINE_CERT') {
          this.typeTitle = '选择证书培训'
          this.placeholder = '请输入证书名称'
          this.tableColumn = [
            { prop: 'name', label: '名称/标题' }
          ]
        } else {
          this.tableColumn = [
            { prop: 'name', label: '名称/标题' },
            { prop: 'author', label: '作者', width: '120' }
          ]
          if (v === 'PLATFORM_COURSE') {
            this.typeTitle = '选择课程资源'
            this.placeholder = '请输入课程名称'
          } else if (v === 'PROMOTE_VIDEO') {
            this.typeTitle = '选择营销视频'
            this.placeholder = '请输入视频名称'
          } else if (v === 'PROMOTE_ARTICLE') {
            this.typeTitle = '选择营销文章'
            this.placeholder = '请输入文章标题'
          } else if (v === 'PROMOTE_DOULA_ARTICLE') {
            this.typeTitle = '选择抖喇图文'
            this.placeholder = '请输入文字内容'
          } else if (v === 'PROMOTE_DOULA_VIDEO') {
            this.typeTitle = '选择抖喇短视频'
            this.placeholder = '请输入文字内容'
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    getList() {
      const API = this.type === 'EXAMINE_CERT' ? certExamineList : getPlatformContents
      API(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
    },
    select(row) {
      this.$emit('update:contentId', this.type === 'EXAMINE_CERT' ? row.certId : row.contentId)
      this.$emit('update:contentTitle', row.name)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.input-with-select {
  margin-bottom: 20px;
}
</style>
