<template>
  <el-dialog
    title="选择广告主"
    :visible.sync="dialogVisible"
    width="600px"
    center
    :before-close="handleClose"
  >
    <el-input
      v-model="name"
      placeholder="单位名称"
      class="input-with-select"
      @change="getList"
    >
      <el-button slot="append" icon="el-icon-search" @click="getList" />
    </el-input>
    <el-table
      :data="tableData"
      style="width: 100%"
      height="300"
    >
      <el-table-column
        prop="id"
        label="ID"
        align="center"
      />
      <el-table-column
        prop="orgName"
        label="单位名称"
        align="center"
      />
      <el-table-column
        prop="type"
        label="单位类型"
        align="center"
      />
      <el-table-column
        prop="area"
        label="所在地区"
        align="center"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
      />
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="checkedItem.id !== scope.row.id"
            size="mini"
            type="primary"
            @click="checked(scope.row)"
          >选择</el-button>
          <el-button
            v-if="checkedItem.id === scope.row.id"
            size="mini"
            type="danger"
            @click="checkedItem = {}"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="handleClose('confirm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getOrgList } from '@/api/userManage'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    advertiserId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      name: '',
      tableData: [],
      checkedItem: {}
    }
  },
  methods: {
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      if (e === 'confirm') {
        this.$emit('checked', this.checkedItem)
      }
    },
    getList() {
      getOrgList({ orgName: this.name }).then(res => {
        this.tableData = res
      })
    },
    checked(row) {
      this.checkedItem = row
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .input-with-select {
    width: 300px;
  }
  .el-dialog__body {
    height: 450px;
    padding-bottom: 0;
  }
  .pagination-container {
    margin: 0;
    padding: 25px ;
  }
}
</style>
