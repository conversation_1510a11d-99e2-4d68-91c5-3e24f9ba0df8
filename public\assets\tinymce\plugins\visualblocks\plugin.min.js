!function(){"use strict";var o=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return o(n())}}},e=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=function(e,t){e.fire("VisualBlocks",{state:t})},s=function(e){return e.getParam("visualblocks_default_state",!1)},c=function(e){return e.settings.visualblocks_content_css},l=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),u=tinymce.util.Tools.resolve("tinymce.util.Tools"),a=l.DOM.uniqueId(),r=function(e,t){var n=u.toArray(e.getElementsByTagName("link"));if(0===u.grep(n,function(e){return e.id===a}).length){var o=l.DOM.create("link",{id:a,rel:"stylesheet",href:t});e.getElementsByTagName("head")[0].appendChild(o)}},m=function(e,t,n){var o=e.dom,s=c(e);r(e.getDoc(),s||t+"/css/visualblocks.css"),o.toggleClass(e.getBody(),"mce-visualblocks"),n.set(!n.get()),i(e,n.get())},f=function(e,t,n){e.addCommand("mceVisualBlocks",function(){m(e,t,n)})},d=function(t,e,n){t.on("PreviewFormats AfterPreviewFormats",function(e){n.get()&&t.dom.toggleClass(t.getBody(),"mce-visualblocks","afterpreviewformats"===e.type)}),t.on("init",function(){s(t)&&m(t,e,n)}),t.on("remove",function(){t.dom.removeClass(t.getBody(),"mce-visualblocks")})},n=function(n,o){return function(e){var t=e.control;t.active(o.get()),n.on("VisualBlocks",function(e){t.active(e.state)})}},v=function(e,t){e.addButton("visualblocks",{active:!1,title:"Show blocks",cmd:"mceVisualBlocks",onPostRender:n(e,t)}),e.addMenuItem("visualblocks",{text:"Show blocks",cmd:"mceVisualBlocks",onPostRender:n(e,t),selectable:!0,context:"view",prependToContext:!0})};e.add("visualblocks",function(e,t){var n=o(!1);f(e,t,n),v(e,n),d(e,t,n)})}();