<template>
  <section class="statistics">
    <div class="overview">
      <h3>活跃概览</h3>
      <ul>
        <li v-for="item in totalList" :key="item.title">
          <h1>{{ item.title }}</h1>
          <p v-for="i in item.list" :key="i.value">
            <span>{{ i.label }}</span>
            {{ i.value }}
          </p>
        </li>
      </ul>
    </div>
    <div class="date">
      <!-- 日期 -->
      <el-date-picker
        v-if="listQuery.condition.timeType === 3"
        v-model="timeRange"
        value-format="yyyyMMdd"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
      />
      <!-- 月份 -->
      <el-date-picker
        v-if="listQuery.condition.timeType === 2"
        v-model="timeRange"
        value-format="yyyyMMdd"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        :picker-options="pickerOptions"
      />
      <!-- 年份 -->
      <year-picker
        v-if="listQuery.condition.timeType === 1"
        v-model="timeRange"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
      />
      <el-radio-group v-model="listQuery.condition.timeType" @change="handlerTimeType">
        <el-radio-button v-for="item in dateTypeList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
      <el-cascader
        v-model="listQuery.condition.areaId"
        placeholder="请选择区域"
        :options="areaList"
        collapse-tags
        :show-all-levels="false"
        :props="{
          value:'areaId',
          label:'name',
          children:'childList',
          checkStrictly: true,
          emitPath:false
        }"
        clearable
      />
      <el-cascader
        v-model="listQuery.condition.majorId"
        placeholder="请选择专科"
        :options="majorList"
        collapse-tags
        :props="{
          value:'majorId',
          label:'name',
          children:'childList',
          emitPath: false,
          checkStrictly: true
        }"
        clearable
      />
      <div class="inquire">
        <el-button type="primary" @click="inquire">查询</el-button>
      </div>
    </div>
    <trend :explain="explain" :data="trendData" />
    <div class="data">
      <h3>用户活跃数据 <el-button type="primary" @click="activeExport()">导出</el-button></h3>
      <div class="data-table">
        <el-table
          :data="tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        >
          <el-table-column
            prop="ds"
            label="日期"
            align="center"
          />
          <el-table-column label="活跃用户数">
            <template slot-scope="{row}">
              <el-button size="mini" type="text" @click="detail(row.ds)">{{ row.num }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="growRate"
            label="活跃率"
          />
        </el-table>
        <Pagination :page-size="listQuery.pager.pageSize" :total="total" :page="listQuery.pager.page" @pagination="pagination" />
      </div>
    </div>
  </section>
</template>

<script>
import moment from 'moment'
import YearPicker from './components/yearPicker.vue'
import Trend from './components/trend.vue'
import Pagination from '@/components/Pagination'
import { getAreaTree } from '@/api/area' // 区域树
import { treeList } from '@/api/major' // 专科树
import { uvOverview, uvTrend, uvTrendExport } from '@/api/statistics'
export default {
  components: {
    YearPicker,
    Trend,
    Pagination
  },
  data() {
    return {
      totalList: [],
      listQuery: {
        condition: {
          areaId: '0',
          majorId: '',
          startTime: moment().startOf('month').format('YYYYMMDD'),
          endTime: moment().format('YYYYMMDD'),
          timeType: 3
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      timeRange: [
        moment().startOf('month').format('YYYYMMDD'),
        moment().format('YYYYMMDD')
      ],
      dateTypeList: [
        { value: 3, label: '天' },
        { value: 2, label: '月' },
        { value: 1, label: '年' }
      ],
      areaList: [],
      majorList: [],
      pickerMinDate: '',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime()
          if (maxDate) { this.pickerMinDate = '' }
          console.log(maxDate, minDate)
        },
        disabledDate: time => {
          if (this.listQuery.condition.timeType === 3) {
            if (this.pickerMinDate !== '') {
              let maxTime = new Date(moment(this.pickerMinDate).add(3, 'months')).getTime()
              const minTime = new Date(moment(this.pickerMinDate).subtract(3, 'months')).getTime()
              if (maxTime > new Date()) { maxTime = new Date() }
              return time.getTime() > maxTime || time.getTime() < new Date('2020-01-01 00:00:00').getTime() || time.getTime() < minTime
            }
            return time.getTime() > Date.now() || time.getTime() < new Date('2020-01-01 00:00:00').getTime()
          } else {
            if (this.pickerMinDate !== '') {
              let maxTime = new Date(moment(this.pickerMinDate).add(11, 'months')).getTime()
              const minTime = new Date(moment(this.pickerMinDate).subtract(11, 'months')).getTime()
              if (maxTime > new Date()) { maxTime = new Date() }
              return time.getTime() > maxTime || time.getTime() < new Date('2020-01-01 00:00:00').getTime() || time.getTime() < minTime
            }
            return time.getTime() > Date.now() || time.getTime() < new Date('2020-01-01 00:00:00').getTime()
          }
        }
      },
      trendData: {},
      explain: {
        title: '用户活跃趋势',
        count: '活跃数',
        ratio: '活跃率'
      },
      tableData: [],
      tableOptions: [
        { label: '日期', align: 'center', prop: 'ds', width: '' },
        { label: '活跃用户数', align: 'left', prop: 'num', width: '' },
        { label: '活跃率', align: 'left', prop: 'growRate', width: '' }
      ]
    }
  },
  watch: {
    timeRange(v) {
      if (this.listQuery.condition.timeType === 2) {
        v[1] = moment(v[1]).endOf('month').format('YYYYMMDD')
      }
      this.listQuery.condition.startTime = v[0]
      this.listQuery.condition.endTime = v[1]
    }
  },
  created() {
    if (this.$route.query.timeType) {
      if (this.$route.query.timeType === 'day') {
        this.timeRange = [
          moment().format('YYYYMMDD'),
          moment().format('YYYYMMDD')
        ]
      } else if (this.$route.query.timeType === 'oldDay') {
        this.timeRange = [
          moment().subtract(1, 'day').format('YYYYMMDD'),
          moment().subtract(1, 'day').format('YYYYMMDD')
        ]
      } else {
        this.listQuery.condition.timeType = 2
        this.timeRange = [
          moment().subtract(1, 'months').startOf('month').format('YYYYMMDD'),
          moment().subtract(1, 'months').endOf('month').format('YYYYMMDD')
        ]
      }
    }
    uvOverview().then(res => {
      this.totalList = [
        {
          title: '上月活跃',
          list: [
            { label: '活跃数', value: res.monthActiveNum },
            { label: '活跃率', value: res.monthActiveRate }
          ]
        },
        {
          title: '上年活跃',
          list: [
            { label: '活跃数', value: res.yearActiveNum },
            { label: '活跃率', value: res.yearActiveRate }
          ]
        },
        {
          title: '用户总量',
          list: [
            { label: '用户数', value: res.staffNum }
          ]
        }
      ]
    })
    // 获取专科树
    treeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', majorId: '0' })
      this.majorList = newArr
    })
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      res.unshift({ name: '全国', areaId: '0', level: 4 })
      this.areaList = this.clearNullChildList(res, 'childList')
    })
    this.inquire()
  },
  methods: {
    getTrendData() {
      const trendData = {
        date: [],
        count: [],
        ratio: []
      }
      uvTrend({
        condition: this.listQuery.condition,
        pager: {
          page: 1,
          pageSize: 100
        }
      }).then(res => {
        res.records = res.records.reverse()
        res.records.forEach(v => {
          trendData.date.push(v.ds)
          trendData.count.push(v.num)
          trendData.ratio.push(parseFloat(v.growRate))
        })
      })
      this.trendData = trendData
    },
    getTableData() {
      uvTrend(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    inquire() {
      this.getTrendData()
      this.getTableData()
    },
    handlerTimeType() {
      if (this.listQuery.condition.timeType === 3) {
        this.timeRange = [
          moment().startOf('month').format('YYYYMMDD'),
          moment().format('YYYYMMDD')
        ]
      } else if (this.listQuery.condition.timeType === 2) {
        this.timeRange = [
          moment().startOf('year').format('YYYYMMDD'),
          moment().endOf('month').format('YYYYMMDD')
        ]
      } else {
        this.timeRange = [
          moment().startOf('year').format('YYYYMMDD'),
          moment().endOf('year').format('YYYYMMDD')
        ]
      }
      this.inquire()
    },
    activeExport() {
      uvTrendExport(this.listQuery.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    pagination(val) {
      this.listQuery.pager = val
      this.getTableData()
    },
    detail(date) {
      this.$router.push({
        name: 'StatisticsActiveDetail',
        query: {
          date,
          timeType: this.listQuery.condition.timeType,
          areaId: this.listQuery.condition.areaId,
          majorId: this.listQuery.condition.majorId
        }
      })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  padding: 20px 40px;
  .overview {
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    ul {
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      li {
        padding: 10px 50px 30px;
        width: 30%;
        border-radius: 10px;
        background-color: #eee;
        p {
          font-size: 18px;
          font-weight: bold;
          span {
            font-weight: 400;
            margin-right: 150px;
            font-size: 14px;
            color: #999;
          }
        }
      }
    }
  }
  h3 {
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
  }
  .date {
    display: flex;
    margin: 30px 0 20px;
    .el-date-editor {
      width: 300px;
    }
    .el-radio-group {
      margin-left: 20px;
    }
    .el-cascader {
      margin-left: 20px;
    }
    .inquire {
      flex: 1;
      position: relative;
      .el-button {
        position: absolute;
        right: 20px;
      }
    }
  }
  .data {
    margin-top: 20px;
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    h3 {
      position: relative;
      .el-button {
        position: absolute;
        top: 5px;
        right: 20px;
      }
    }
    &-table {
      padding: 20px;
    }
  }
}
</style>
