<template>
  <el-dialog title="选择培训计划" :visible.sync="dialogVisible" width="1300px" top="60px" class="list" center @close="$emit('update:dialogVisible', false)">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.name"
        class="group"
        placeholder="名称"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>

      <el-select
        v-model="listQuery.condition.status"
        placeholder="状态"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.source"
        placeholder="资源来源"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in sourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="handleSelect(row.id)"
            >选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import { packageList, getPackageDetail } from '@/api/jobTrain'
export default {
  name: 'JobTrainList',
  components: {
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      stateOptions: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 }
      ],
      sourceOptions: [
        { label: '平台', value: 1 },
        { label: '单位', value: 2 }
      ],
      listQuery: {
        condition: {
          name: '',
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'name', label: '名称', width: '180' },
        { prop: 'courseNum', label: '教程数', width: '50' },
        { prop: 'cycleStr', label: '培训周期', width: '50' },
        { prop: 'totalDuration', label: '总时长', width: '40' },
        { prop: 'orgNum', label: '培训单位数', width: '60' },
        { prop: 'userNum', label: '培训人次', width: '50' },
        { prop: 'statusStr', label: '状态', width: '30' },
        { prop: 'sourceStr', label: '资源来源', width: '50' },
        { prop: 'orgName', label: '单位', width: '70' },
        { prop: 'updateName', label: '更新人', width: '40' },
        { prop: 'updateTime', label: '更新时间', width: '60' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getPackageList()
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getPackageList()
    },
    getPackageList() {
      packageList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.statusStr = this.stateOptions.find(i => i.value === v.status).label
          v.sourceStr = this.sourceOptions.find(i => i.value === v.source).label
          v.cycleStr = v.cycle + '个月'
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getPackageList()
    },
    handleSelect(id) {
      getPackageDetail(id).then(res => {
        res.examineContentList.forEach(item => {
          item.directoryName = item.name
          item.directoryNo = item.month
          item.coursesList.forEach(v => {
            v.courseSeq = v.courseOrder
            v.courseAccuracy = v.accuracy
            v.courseName = v.name
          })
          item.children = item.coursesList
        })
        this.$emit('handleSelect', res.examineContentList)
        this.$emit('update:dialogVisible', false)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  border-radius: 4px;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  .table {
    padding-top: 25px;
    background-color: #fff;
  }
}
</style>
