<template>
  <div class="app-container">
    <el-dialog
      title="再次确认"
      :visible.sync="visible"
      width="600px"
      @close="close()"
    >
      <!-- <div class="search-column">
        <div class="search-column__item fr">
          <el-input v-model="seachQuery.keyword" placeholder="请输入关键字搜索" suffix-icon="el-icon-search" />
        </div>
      </div>
      <el-table :data="tableList" border stripe>
        <el-table-column
          v-for="col in tableColumnList"
          :key="col.id"
          :prop="col.id"
          :label="col.label"
          :align="col.align"
        />
        <el-table-column
          label="操作"
        >
          <template slot-scope="{row}">
            <el-button type="primary" @click="handleChoseUnit(row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table> -->
      <div class="dialog-content">
        <h3>您当前的操作将会改变"{{ selectOrg }}"的单位关系，请确认无误之后保存！</h3>
        <div class="mg-b">已选择的下级对象</div>
        <div class="choose-box">
          <el-tooltip v-for="item in data" :key="item.orgId" class="tag-mg" :content="item.orgName" placement="top">
            <el-tag>{{ item.orgName | filtersFont }}</el-tag>
          </el-tooltip>
        </div>
      </div>

      <span slot="footer">
        <el-button @click="handleCancelForm()">取 消</el-button>
        <el-button type="primary" @click="handleSaveForm()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DialogBindAccount',
  components: {

  },
  filters: {
    filtersFont(val) {
      if (val.length > 8) { return val.slice(0, 8) + '...' }
      return val
    }
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '绑定医院账号'
    },
    type: {
      type: Number,
      default: 1
    },
    selectOrg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: this.show
    }
  },
  computed: {

  },
  watch: {
    show() {
      this.visible = this.show
    }
  },
  created() {

  },
  methods: {
    close() {
      this.$emit('update:show', false)
    },
    handleCancelForm() {
      this.close()
    },
    handleSaveForm() {
      this.$emit('handleSave')
    },
    handlePagination(val) {
      this.tableQuery.page = val.page
    }
  }
}
</script>

<style scoped>
  .tag-mg{
    margin: 0 5px 5px 0;
  }
</style>
