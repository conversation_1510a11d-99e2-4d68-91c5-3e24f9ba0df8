<template>
  <div class="app-container">
    <div class="container">
      <div class="search-column">
        <div class="search-column__item" style="line-height: 40px;"><span class="record-text" />{{ query.type|typeList }}变化趋势对比</div>
        <div class="search-column__item fr">
          <el-cascader
            v-model="majorIds"
            placeholder="请选择专科"
            :options="majorList"
            collapse-tags
            :props="{
              multiple: multiple,
              value:'majorId',
              label:'name',
              children:'childList',
              emitPath: false,
              checkStrictly: false
            }"
            clearable
            :disabled="disabled"
            @change="getList"
          />
          <el-cascader
            v-model="academicIds"
            placeholder="请选择职称"
            :options="academicList"
            collapse-tags
            :props="{
              multiple: true,
              value:'academicId',
              label:'name',
              children:'childList',
              emitPath: false
            }"
            clearable
            :disabled="disabled"
            @change="getList"
          />

          <el-button v-show="isShow === 2" type="text" @click="handlerCutChart(1)"><i class="el-icon-s-fold" style="margin-right:10px" />列表</el-button>
          <el-button v-show="isShow === 1" type="text" @click="handlerCutChart(2)"><i class="el-icon-s-data" style="margin-right:10px" />图表</el-button>
          <el-button type="text" @click="handleDownload"> <i class="el-icon-download" style="margin-right:10px" />下载</el-button>
        </div>
      </div>

      <div class="table">
        <div v-if="isShow===3" class="empty">暂无数据</div>
        <div v-show="isShow===2" id="TrendContrast" style="width: 100%;height: 400px" />
        <div v-if="isShow===1" style="min-height: 400px;padding: 10px 20px;">
          <el-table class="eltable" :data="tableData" :render-header="labelHead" border style="font-size:16px">
            <el-table-column prop="name" label="区域" align="center" />
            <el-table-column v-for="(item, index) in tableHeader" :key="index" :prop="item.item" :label="item.label" align="center" />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const echarts = require('echarts/lib/echarts')
require('echarts/lib/component/title')
require('echarts/lib/component/toolbox')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/grid')
require('echarts/lib/component/legend')
require('echarts/lib/chart/line')

import { majorTreeList } from '@/api/category' // 选择身份后的 专科树
import { academicTreeListById } from '@/api/academic' // 选择单个身份后的 职称树
import request from '@/api/dataStatistics/statisticsCase'

export default {
  name: 'StatisticsTrendContrast',
  filters: {
    typeList(v) {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[v]
    }
  },
  props: {
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      request,
      myChart: null,
      majorList: [],
      academicList: [],
      academicList1: [
        { academicId: 0, name: '无' }
      ],
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      isShow: 2,
      tableData: [],
      academicIds: [],
      majorIds: [],
      data: [], // 接口返回数据
      seriesAry: [],
      tableHeader: [],
      disabled: true,
      multiple: false
    }
  },
  watch: {
    query: {
      handler() {
        this.getList()
      },
      immediate: true,
      deep: true
    },
    'query.identityIds': {
      handler(arr) {
        if (arr.length === 0) { // 未选身份，禁用
          this.Reset()
          this.disabled = true
        } else if (arr.length === 1) {
          if (arr[0] === '0') { // 身份选择'无'
            this.ident1() // 把专科和职称置为'无'
            this.disabled = true
          } else {
            this.ident2(arr) // 选择一个身份
            this.disabled = false
          }
        } else { // 多个身份
          this.ident3(arr)
          this.disabled = false
        }
      },
      deep: true
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.myChart = echarts.init(document.getElementById('TrendContrast'))
        window.addEventListener('resize', () => {
          this.myChart.resize()
        })
      })
    },
    labelHead(h, { column, index }) { // 动态表头渲染
      return h('span', { class: 'table-head', style: { width: '100%' }}, [column.label])
    },
    // 获取用户数变化趋势数据
    async getList() {
      this.tableData = []
      const params = this.getParams()
      await this.request.trendContrast(params).then(res => {
        if (JSON.stringify(res) === '{}' || JSON.stringify(res) === '[]') {
          this.isShow = 3
          return
        } else {
          this.seriesAry = []
          this.data = JSON.parse(JSON.stringify(res))
          this.handleTableData()
          // 加载柱状图
          this.handleData()
          this.isShow = 2
          this.initCharts()
        }
      })
      this.setOptions()
    },
    getParams() {
      const params = this.copy(this.query)
      // params.academicIds = this.academicIds
      params.majorIds = this.majorIds
      // 应后端要求，用户未选择身份、职称、专科时，单值字段给-1
      if (params.identityIds.length === 0) {
        params.identityId = -1
      }
      if (this.academicIds.length === 0) {
        params.academicId = -1
      }
      if (this.majorIds.length === 0) {
        params.majorId = -1
      }
      // 应后端要求，选择多个身份时，职称字段用academicClassIds，单个身份时用academicIds
      if (params.identityIds.length > 1) {
        params.academicClassIds = this.academicIds
      } else {
        params.academicIds = this.academicIds
      }
      return params
    },
    handlerCutChart(type) {
      this.isShow = type
      if (type === 1) {
        // 加载表格
        this.$nextTick(() => {
          this.handleTableData()
        })
      }
    },
    setOptions() {
      this.myChart.clear()
      this.myChart.setOption({
        // 表格标题
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          top: 'bottom',
          bottom: 10,
          left: 'center',
          data: (() => {
            let res = []
            const dataOld = JSON.parse(JSON.stringify(this.data))
            const dataAry = []
            dataOld.forEach(item => {
              dataAry.push(item.name)
            })
            res = [...new Set(dataAry)]
            return res
          })()
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '12%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          left: 10,
          boundaryGap: false,
          minInterval: 1,
          data: (() => {
            let res = []
            const dataOld = JSON.parse(JSON.stringify(this.data))
            let dataNew = []
            const dataAry = []
            // 数组排序
            var compare = function(prop) {
              return function(obj1, obj2) {
                var val1 = obj1[prop]
                var val2 = obj2[prop]
                if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
                  val1 = Number(val1)
                  val2 = Number(val2)
                }
                if (val1 < val2) {
                  return -1
                } else if (val1 > val2) {
                  return 1
                } else {
                  return 0
                }
              }
            }
            dataNew = dataOld.sort(compare('dm'))
            dataNew.forEach(item => {
              dataAry.push(item.dm)
            })
            res = [...new Set(dataAry)]
            return res
          })()
        },
        yAxis: {
          type: 'value'
        },
        series: this.seriesAry
      })
    },
    // 数据处理 取出每月每个省对应的用户数
    handleData() {
      this.seriesAry = []
      const data = JSON.parse(JSON.stringify(this.data))
      let dataNew = [] // 按日期升序后的数据
      const dataAry = [] // 保存筛选后的数据
      let dataName = [] // 保存去重后的name
      const dataSeries = [] // 保存显示的数据

      // 数组排序
      var compare = function(prop) {
        return function(obj1, obj2) {
          var val1 = obj1[prop]
          var val2 = obj2[prop]
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1)
            val2 = Number(val2)
          }
          if (val1 < val2) {
            return -1
          } else if (val1 > val2) {
            return 1
          } else {
            return 0
          }
        }
      }
      dataNew = data.sort(compare('dm'))

      // 筛选去重后的name
      data.forEach(item => {
        dataAry.push(item.name)
      })
      dataName = [...new Set(dataAry)]

      // 按去重后的name 与 升序后的数据 取出对应的num
      for (let i = 0; i < dataName.length; i++) {
        const obj = {
          name: dataName[i],
          type: 'line',
          data: []
        }
        for (let j = 0; j < dataNew.length; j++) {
          if (dataName[i] === dataNew[j].name) {
            obj.data.push(parseInt(dataNew[j].num))
          }
        }
        dataSeries.push(obj)
      }
      this.$forceUpdate()
      this.seriesAry = dataSeries
    },
    // 表格数据处理
    handleTableData() {
      const data = JSON.parse(JSON.stringify(this.data))
      let tableRow = []
      let tableCol = []
      const tableHeader = []
      const arrOrder = this.handleDateRiseOrder(data) // 排序后的数组
      // 筛选去重后的name
      arrOrder.forEach(item => {
        tableRow.push(item.dm)
        tableCol.push(item.name)
      })
      tableRow = [...new Set(tableRow)]
      tableCol = [...new Set(tableCol)]

      if ([8, 9, 10, 11, 13].includes(this.query.type)) {
        tableRow.forEach((item, i) => {
          const obj = {}
          obj.item = 'dm' + i
          obj.label = item
          tableHeader.push(obj)
        })
      } else {
        tableRow.forEach((item, i) => {
          const obj = {}
          obj.item = 'dm' + i
          obj.label = item
          tableHeader.push(obj)
        })
      }
      this.tableHeader = tableHeader

      // 存放
      const arrAll = []
      for (let i = 0; i < tableCol.length; i++) {
        const objs = {}
        objs.name = tableCol[i]
        const dataAry = this.handleDataName(arrOrder, tableCol[i])
        dataAry.forEach((item, index) => {
          objs[`dm${index}`] = parseInt(item.num)
        })
        arrAll.push(objs)
      }
      this.tableData = JSON.parse(JSON.stringify(arrAll))
    },
    // name对应的数据
    handleDataName(arr, name) {
      let result = []
      result = arr.filter(item => {
        if (item.name === name) return item
      })
      return result
    },
    // 按照日期升序
    handleDateRiseOrder(arr) {
      let arrNew = []
      // 数组排序
      var compare = function(prop) {
        return function(obj1, obj2) {
          var val1 = obj1[prop]
          var val2 = obj2[prop]
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1)
            val2 = Number(val2)
          }
          if (val1 < val2) {
            return -1
          } else if (val1 > val2) {
            return 1
          } else {
            return 0
          }
        }
      }
      arrNew = arr.sort(compare('dm'))
      return arrNew
    },
    // 身份 选择'无'
    ident1() {
      this.majorList = [{
        name: '无',
        majorId: 0
      }]
      this.academicList = this.academicList1
      this.academicIds = [0]
      this.majorIds = [0]
    },
    // 选择一个身份
    ident2(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.majorList = newArr
      })
      academicTreeListById(arr[0]).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.academicList = newArr
      })
    },
    // 选择多个身份
    ident3(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.majorList = newArr
      })
      this.academicList = this.academicList2
    },
    Reset() { // 用户清除身份时，进行reset
      this.academicIds = []
      this.majorIds = []
    },
    // 当展示图表时，下载图表图片，当展示列表时，下载excel数据表
    handleDownload() {
      // 将tabel表格转换为execl数据表，并下载为execl文件
      if (this.isShow === 1) {
        if (this.tableData.length < 1) {
          this.$message.info('当前数据为空')
          return
        }
        const params = this.getParams()
        this.request.exportTrendContrast(params).then(() => {
          this.$message.success('导出成功，请在导出管理中查看')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      }
      // 将echarts图表转换为canvas,并将canvas下载为图片
      if (this.isShow === 2) {
        const aLink = document.createElement('a')
        const blob = this.base64ToBlob()
        const evt = document.createEvent('HTMLEvents')
        evt.initEvent('click', true, true)
        aLink.download = '用户数变化趋势对比'
        aLink.href = URL.createObjectURL(blob)
        aLink.click()
      }
    },
    exportImg() { // echarts返回一个 base64的URL
      const myChart = echarts.init(
        document.getElementById('TrendContrast')
      )
      return myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
    },
    base64ToBlob() { // 将base64转换blob
      const img = this.exportImg()
      const parts = img.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding-left: 0;

}
.container {
  border-radius: 5px;
  background: #fff;
  box-shadow: 0 0 8px rgb(167, 165, 165);
  padding-top: 14px;
  padding-bottom: 20px;

  .search-column {
    border-radius: 5px;
    padding: 5px 20px;
  }

  .empty{
    text-align: center;
    width: 100%;
    height: 200px;
    line-height: 40px;
    color: #999;
    font-size: 24px;
  }
}
.record-text {
  margin-bottom: 10px;
  padding-left: 15px;
  border-left: 2px solid #4F8EF8;
}
</style>
