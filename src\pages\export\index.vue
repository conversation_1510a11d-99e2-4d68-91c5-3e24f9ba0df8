<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" type="monthrange" value-format="yyyyMMdd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false" @change="handleChange" />
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" :header-cell-style="{background:'#ECF0F1'}" stripe @selection-change="onSelectChange">
      <template slot="actions" slot-scope="{row}">
        <el-button v-if="row.status != 0 " size="mini" type="text" @click="download(row)">下载</el-button>
        <span v-else>处理中，请稍后</span>
        <template slot="header">123</template>
      </template>
      <template slot="actionsHeader">
        <div class="table-header">操作</div>
        <div style="font-size: 12px;" class="table-header">（导出文件有效期为一周，请尽快下载）</div>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request, { getFileUrl } from '@/api/export'
import { setDefaultDate, getEndDate } from './utils'

const columns = [
  {
    props: {
      label: '发起时间',
      align: 'center',
      prop: 'createTime',
      width: 160
    }
  },
  { props: { label: '导出发起人', align: 'center', prop: 'createName' }},
  { props: { label: '导出人账号', align: 'center', prop: 'staffNumber' }},
  { props: { label: '导出类型', align: 'center', prop: 'type' }},
  { props: { label: '数据量（条数）', align: 'center', prop: 'total' }},
  {
    props: { align: 'center', label: '操作', width: '240' },
    slot: 'actions',
    header: 'actionsHeader'
  }
]

export default {
  name: 'ExportIndex',
  mixins: [table],
  data() {
    return {
      columns,
      request,
      timeRange: [],
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 8.64e7
        }
      },
      conditionWatch: {
        startTime: '',
        endTime: ''
      },
      initList: true
    }
  },
  created() {
    this.setDefaultDateFn()
  },
  methods: {
    download(row) {
      const date1 = new Date(row.finishTime.replace(/-/g, '/')).getTime()
      const date2 = new Date(
        new Date().getTime() + this.$store.getters.timeDifference
      ).getTime()
      if (date1 + 7 * 24 * 60 * 60 * 1000 < date2) {
        this.$message.error('数据文件已失效')
        return
      }
      getFileUrl(row.fileId).then(res => {
        res.fileUrls.forEach(link => {
          window.open(link)
        })
      })
    },
    datasplit(t, split = '-') {
      let time = t
      if (time) {
        time = time.toString()
        return `${time.substr(0, 4)}${split}${time.substr(
          4,
          2
        )}${split}${time.substr(6, 2)}`
      }
      return ''
    },
    setDefaultDateFn() {
      this.timeRange = setDefaultDate().map(v => v.replace(/-/g, ''))
      this.conditionWatch.startTime =
        this.datasplit(this.timeRange[0]) + 'T00:00:00.000Z'
      this.conditionWatch.endTime =
        this.datasplit(this.timeRange[1]) + 'T23:59:59.999Z'
    },
    handleChange(v) {
      this.pager.page = 1
      this.conditionWatch.startTime = this.datasplit(v[0]) + 'T00:00:00.000Z'
      this.conditionWatch.endTime =
        this.datasplit(getEndDate(v[1])) + 'T23:59:59.999Z'
    }
  }
}
</script>

<style scoped lang="scss">
.table-header {
  line-height: 18px;
}
</style>
