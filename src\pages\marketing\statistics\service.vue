<template>
  <section class="service">
    <div v-if="!$route.path.includes('service/detail')">
      <h2>
        服务商列表
        <el-button type="primary" @click="exportList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column prop="serviceProvider" label="服务商" width="300">
            <template slot-scope="scope">
              <span @click="toDetail(scope.row)">{{ scope.row.serviceProvider }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
      </div>
      <Pagination
        :page="queryParams.pager.page"
        :page-size="queryParams.pager.pageSize"
        :total="total"
        @pagination="handlePagination"
      />
    </div>
    <!-- 详情 -->
    <router-view :search-param="searchParam" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { serviceList, serviceListExport } from '@/api/marketing/promoteArticle'
import { deleteEmptyProperty } from '@/utils/index'
export default {
  components: {
    Pagination
  },
  props: {
    searchParam: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableColumn: [
        { prop: 'articleFeeWithUnit', label: '文章征集业务量', width: '300' },
        { prop: 'videoFeeWithUnit', label: '视频征集业务量', width: '300' },
        { prop: 'doulaFeeWithUnit', label: '抖喇征集业务量', width: '300' },
        { prop: 'visitFeeWithUnit', label: '拜访业务量', width: '300' },
        { prop: 'totalFeeWithUnit', label: '总业务量' }
      ],
      tableData: [],
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          orgId: null,
          serviceProvider: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.queryParams.condition.serviceProvider = newVal.condition.keyword
        this.queryParams.pager.page = newVal.pager.page
        if (this.$route.path.includes('service/detail')) return
        this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toDetail(row) {
      this.$router.push({
        name: 'MarketingStatisticsServiceDetail',
        params: row
      })
    },
    getList() {
      const param = JSON.parse(JSON.stringify(this.queryParams))
      deleteEmptyProperty(param)
      serviceList(param).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportList() {
      if (this.total <= 0) {
        this.$message.error('暂无可导出数据')
        return
      }
      const param = JSON.parse(JSON.stringify(this.queryParams.condition))
      deleteEmptyProperty(param)
      serviceListExport(param).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.service {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  h2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
    .el-button {
      width: 80px;
      height: 35px;
    }
  }
  .table {
    padding: 25px 20px 0;
    .cell {
      span {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}
</style>
