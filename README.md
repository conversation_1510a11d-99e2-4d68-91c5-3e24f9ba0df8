# 名医传世-CMS重构版
> 版本： 1.0.0
> 作者： Ming
> 联系： <EMAIL>
> 时间： 2020.04.03

## 一、项目介绍
> 本次项目为名医传世重构版本的说明文档，内容包括项目使用的框架，代码开发规范，页面默认内容，打包配置等内容。<br/>
> 本次项目的主要技术栈为 `vue + vue-router + vuex + axios + element-ui + webpack`, 兼容ie10+及其他现代浏览器

## 二、项目使用的目录结构，框架以及第三方插件
### 2.1 项目使用的目录结构

    此为前端的目录结构，后端请根据实际项目调整。

```css
###项目目录###

├── build                      # 构建相关
├── mock                       # 项目mock 模拟数据
├── public                     # 静态资源
│   │── favicon.ico            # favicon图标
│   └── index.html             # html模板
├── src                        # 源代码
│   ├── api                    # 所有请求
│   ├── assets                 # 主题 字体等静态资源
│   ├── components             # 全局公用组件
│   ├── directive              # 全局指令
│   ├── filters                # 全局 filter
│   ├── icons                  # 项目所有 svg icons
│   ├── lang                   # 国际化 language
│   ├── layout                 # 全局 layout
│   ├── router                 # 路由
│   ├── store                  # 全局 store管理
│   ├── styles                 # 全局样式
│   ├── utils                  # 全局公用方法
│   ├── vendor                 # 公用vendor
│   ├── views                  # views 所有页面
│   ├── App.vue                # 入口页面
│   ├── main.js                # 入口文件 加载组件 初始化等
│   └── permission.js          # 权限管理
├── tests                      # 测试
├── .env.xxx                   # 环境变量配置
├── .eslintrc.js               # eslint 配置项
├── .babelrc                   # babel-loader 配置
├── vue.config.js              # vue-cli 配置
├── postcss.config.js          # postcss 配置
└── package.json               # package.json
```


|目录|说明|
|-|-|
|src|开发环境目录|

|三级目录|说明|
|-|-|
|build|项目构建配置目录|
|dist|正式环境目录，包含项目打包部署到正式环境的所有文件|
|public|index.html文件及静态资源目录|
|src|开发环境目录|
|.editorconfig|代码结构规格配置|
|.env.development|开发环境联调配置。该系列文件中，均定义了ENV的环境属性，区分打包到不同环境是使用不同的base_spi。其中根据后端不同模块，区分使用同一环境的不同模块的base_api。|
|.env.test|测试环境联调配置。说明同.env.development|
|.env.uat|uat环境联调配置。说明同.env.development|
|.env.production|正式环境联调配置。说明同.env.development|
|.env.eslintignore|使用eslint检查代码规范可以忽略文件或路径|
|.env.eslintrc.js|使用eslint检查代码规范的配置|
|.gitignore|忽略提交的文件或路径配置|
|.travis.yml|持续集成的项目配置文件|
|.babel.config.js|JavaScript 语法编程的配置文件|
|.jest.config.js|jest测试配置文件|
|.jsconfig.json|开发使用，便于[vscode](https://code.visualstudio.com/)快速识别'@'路径|
|LICENSE|开源协议。项目基于[vue-element-admin](https://panjiachen.github.io/vue-element-admin)|
|package.json|打包依赖|
|package-lock.json|打包依赖打包文件锁版本，非必要|
|postcss.config.js|转换css的配置文件|
|README.md|项目文档说明|
|README.vea.md|基于[vue-element-admin](https://panjiachen.github.io/vue-element-admin)进行二次开发的项目文档说明，[点我阅读](./README.vea.md)|
|vue.config.js|vue4中使用的项目运行、打包或端口等配置|
|webstorm.webpack.js|开发使用，便于[webstorm](https://www.jetbrains.com/webstorm/)快速识别'@'路径|

|目录build的四级目录|说明|
|-|-|
|index.js|项目运行或打包的配置文件|

|目录public的四级目录|说明|
|-|-|
|index.html|项目入口|
|IETip.html|正式环境在IE10以下版本的IE浏览器中运行项目时转到此页面进行提示升级|
|favicon.icon|项目的专属icon，用于标签中显示|
|static|项目使用到的静态资源，包含ckplayer|
|assets|项目使用到的资源，用于IETip.html提示页面，包括imgs图片资源和js逻辑代码|

|目录src的四级目录|说明（`项目主体，带 * 的目录，在该目录下有专有的README.md的说明`）|`*`文档链接|
|-|-|-|
|App.vue|项目主体入口||
|main.js|项目主体全局配置和渲染vue生成页面的文件||
|permission.js|控制路由跳转||
|setting.js|项目部分标题或元素显示的设置文件||
|api|集中管理所有调用的接口，接口默认的base_api为.env系列文件中的VUE_APP_BASE_API，部分模块需要特别引入其他base_url。eg：登录模块需要引用process.env.VUE_APP_LOGIN_URL使用||
|assets|项目主体使用的资源文件，其中custom-theme目录的资源为[element-ui主题配置](https://element.eleme.cn/#/zh-CN/theme)下载的资源包，项目初期根据UI进行配置||
|`*` components|公共组件集合，大多没有使用，项目初期基于[vue-element-admin](https://panjiachen.github.io/vue-element-admin)而修改留下项目未来可能会使用的组件，没有使用的组件可删除|[Components](./src/components)|
|directive|指令合集，eg：在窗口组件中使用v-drag，可以拖拽窗口||
|filters|公共过滤器合集，在main.js中引入，可以直接调用||
|icons|图标合集，使用svg的形式，可以在css中修改样式||
|`*` layout|项目的外框，包括头部、菜单栏等|[Layout](./src/layout)|
|`*` pages|业务页面，按模块拆分||
|`*` router|业务页面路由，按模块拆分|[Router](./src/router)|
|store|[vuex](https://vuex.vuejs.org/zh/)的配置，为 Vue.js 应用程序开发的状态管理模式，集中式存储管理应用的所有组件的状态||
|styles|公共样式||
|`*` utils|使用工具|[Utils](./src/utils)|
|vendor|导出文件方法||
|`*` views|公共页面|[Views](./src/views)|

### 2.2 项目使用的框架
|框架|版本|说明|官网|
|-|-|-|-|
|vue|2.6.10|渐进式 JavaScript 框架|https://cn.vuejs.org/|
|vue-router|3.0.2| Vue.js 官方的路由管理器|https://router.vuejs.org/|
|vuex|3.1.0|Vuex 是一个专为 Vue.js 应用程序开发的状态管理模式|https://vuex.vuejs.org/|
|axios|0.18.1|易用、简洁且高效的http库|https://github.com/axios/axios|
|element-ui|2.12.0|Element，一套为开发者、设计师和产品经理准备的基于 Vue 2.0 的桌面端组件库|https://element.eleme.cn/|
|jquery|3.4.1|一个高效、精简并且功能丰富的 JavaScript 工具库|http://jquery.com/|
|sass| |世界上最成熟、最稳定、最强大的专业级CSS扩展语言|https://www.sass.hk/|

### 2.3 项目使用的第三方插件
|插件|版本|说明|官网|
|-|-|-|-|
|echarts|4.2.1|百度系图表插件|http://echarts.baidu.com/|
|webuploader|0.1.5|百度系上传组件|http://fex.baidu.com/webuploader/|
|Aliplayer|2.8.2|阿里云player|https://player.alicdn.com/aliplayer/index.html|

## 三、项目代码规范

#### 3.1.1 css 规范
遵从css规范 [查看规范](https://codeguide.bootcss.com/#css)

> 注：
> css命名一般为单词或者以"-"连接的词汇 eg: `.wrap{}` `.picture-box{}`

#### 3.1.2 js 规范
遵从eslint规范 [查看规范](https://cn.eslint.org/docs/rules/)

> 注：
> javasrcipt `function`函数名使用`小驼峰式`,,注意`名词`在前，`动词`在后, eg: 
```javascript
function ajaxPost () {}
```
`Class`类函数名使用`大驼峰式`
```javascript 
Class Person(){}
```


## 四、项目代码开发示例

### 4.1 默认vue模板
```vue
<template>
  <div>
    vue
  </div>
</template>

<script>
export default {
  name: 'Demo',
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>

</style>
```

### 4.2 组件引入示例
```html
<template>
  <div>
    <Pagination :total="1000" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
export default {
  name: 'Demo',
  components: {
    Pagination
  },
  data() {
    return {}
  },
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>

</style>
```

### 4.3 上传插件使用例子

> 先引入百度的上传组件webuploader，文件位于plugins文件夹中

```javascript
/**
 * API 说明
 * upBtn  // 触发选择文件框弹出的按钮
 * url    // 后台接收图片的地址
 * fn     // 上传之后的回调
 * 具体配置可参考	http://fex.baidu.com/webuploader/
 */
function uploadImage (upBtn, url, fn){
  var uploader = WebUploader.create({
    auto: true,
    swf: "../../../plugins/webuploader/0.1.5/Uploader.swf",
    server: url,
    pick: $(upBtn),
    accept: {
      title: "Images",
      extensions: "gif, jpg, jpeg, bmp, png",
      miniTypes: "image/*"
    }
  })
  uploader.on("fileQueued", function(file){
    if(typeof fn == "function"){
      fn(file)
    }
  });
}
```

## 五、项目打包配置
### 5.1 安装node
[点击下载](http://nodejs.cn/download/)

### 5.2 配置文件说明
|名称|说明|是否必要|
|-|-|-|
|package.json|打包依赖|是|
|package-lock.json|打包依赖打包文件锁版本|否|

1. 安装完node之后，打开终端，输入 `node -v` 正确回显证明nodejs环境安装成功。
2. 运行 `npm install` 安装打包依赖。

> 注：建议使用公司内网的nmp私库安装 `npm config set registry http://npm.nexus-server:17172/repository/npm-group/` , 然后再运行 `npm install`; 或者使用 `npm install --registry=http://npm.nexus-server:17172/repository/npm-group/` 安装。
> 注：如果无法通过npm安装请先运行 `npm install -g cnpm --registry=https://registry.npm.taobao.org` ,然后再运行 `cnpm install` 进行安装打包依赖。

### 5.3 webpack 命令说明
1. `npm run build:prod` 运行单次打包过程，打包任务包括 **清空dist文件夹，压缩混淆css、js并添加版本，生成文件引索，替换html内资源链接**。':'后的参数为各个环境的打包参数，eg：测试环境的打包命令为`npm run build:test`。
2. `npm run dev` 运行带 **实时监听** 的打包程序。
