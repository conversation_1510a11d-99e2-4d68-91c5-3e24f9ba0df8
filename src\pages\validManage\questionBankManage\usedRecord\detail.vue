<template>
  <div class="contain">
    <div class="search-column">
      <div class="search-column__item">
        <el-select
          v-model="condition.type"
          filterable
          clearable
          placeholder="请选择搜索类型"
        >
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="getDetail" @clear="getDetail">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="getDetail" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select
          v-model="condition.questionType"
          filterable
          clearable
          placeholder="请选择题型"
          @clear="getDetail"
          @change="getDetail"
        >
          <el-option v-for="item in questionTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select
          v-model="condition.difficulty"
          filterable
          clearable
          placeholder="请选择难易程度"
          @clear="getDetail"
          @change="getDetail"
        >
          <el-option v-for="item in difficultyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr" style="margin-right:30px">
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </div>
    </div>

    <div v-if="records&&records.length">
      <QuestionDiv v-for="(item,index) in records" :key="index" :data="item" />
    </div>

    <div v-else class="empty">
      暂无数据
    </div>

    <Pagination
      :total="total"
      :page="pager.page"
      @pagination="pagination"
    /></div>
</template>

<script>
import Pagination from '@/components/Pagination'
import QuestionDiv from '@/components/questionDiv'
import { getDetail } from '@/api/questions'
export default {
  components: { Pagination, QuestionDiv },
  data() {
    return {
      condition: {
        difficulty: null,
        keyword: null,
        questionType: null,
        type: 1
      },
      typeList: [
        { label: '试题题干', value: 1 },
        { label: '考点关键字', value: 2 }
      ],
      questionTypeList: [
        { label: 'A1题型', value: 1 },
        { label: 'A2题型', value: 2 },
        { label: 'A3题型', value: 3 },
        { label: 'A4题型', value: 4 },
        { label: 'B1题型', value: 5 },
        { label: 'X题型', value: 6 },
        { label: '判断题型', value: 7 },
        { label: '选择填空题型', value: 8 }
      ],
      difficultyList: [
        { label: '简单', value: 1 },
        { label: '普通', value: 2 },
        { label: '困难', value: 3 }
      ],
      pager: { page: 1, pageSize: 10 },
      total: 0,
      records: []
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      this.condition.taskId = this.$route.query.taskId
      this.condition.originId = this.$route.query.originId
      getDetail({ pager: this.pager, condition: this.condition }).then(res => {
        this.total = res.total
        this.records = res.records
      })
    },
    pagination(pager) {
      this.pager = pager
      this.getDetail()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-column{
  margin: 10px 0 -10px 30px;
}
.empty{
  height: 600px;
  font-size: 22px;
  text-align: center;
  color: #888;
  padding-top: 200px;
}
</style>
