<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" value-format="yyyyMMdd" :picker-options="pickerOptions" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onTimeChange" />
      </div>
      <div class="search-column__item">
        <el-input v-model="ad" placeholder="请选择广告" clearable @focus="$refs.selectDialog.dialogShow=true" />
      </div>
      <div class="search-column__item">
        <el-button type="primary" @click="$refs.selectDialog.dialogShow=true">选择广告</el-button>
      </div>
    </div>
    <select-dialog ref="selectDialog" :time-range="timeRange" @select="onSelect" />
    <overview :data="overview" class="ad-info" :ad-id="adId" />
    <trend class="ad-info" :data="trendData" />
    <promote :ad-id="adId" />
  </div>
</template>

<script>
import overview from './components/overview.vue'
import selectDialog from './components/selectDialog.vue'
import trend from './components/trend.vue'
import promote from './components/promote.vue'
import request from '@/api/ad'

export default {
  name: 'Statistics',
  components: { overview, trend, promote, selectDialog },
  data() {
    return {
      adId: this.$route.query.id,
      ad: this.$route.query.name,
      // time picker
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      timeRange: [],
      // component data
      overview: {
        click: {},
        exposure: {}
      },
      trendData: {
        xAxisData: [],
        exposure: [],
        hit: []
      }
    }
  },
  async created() {
    if (!this.adId) {
      const param = {
        condition: {
          adTypeId: '',
          playPlaceId: '',
          status: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
      const res = await request.list(param)
      this.adId = res.records[0].advertisementId
      this.ad = res.records[0].name
    }
    this.getOverview()
    this.getTrend()
  },
  methods: {
    getOverview() {
      const param = {
        adId: this.adId,
        startTime: this.timeRange[0] || '',
        endTime: this.timeRange[1] || ''
      }
      request.overview(param).then(res => {
        this.overview = {
          click: {
            hitNum: res.hitNum,
            hitPersonNum: res.hitPersonNum,
            avgHitNum: res.avgHitNum,
            hitRate: (res.hitRate * 100).toFixed(2) + '%'
          },
          exposure: {
            exposureNum: res.exposureNum,
            exposurePersonNum: res.exposurePersonNum,
            avgExposureNum: res.avgExposureNum,
            exposureRate: (res.exposureRate * 100).toFixed(2) + '%'
          }
        }
      })
    },
    getTrend() {
      const param = {
        adId: this.adId,
        startTime: this.timeRange[0] || '',
        endTime: this.timeRange[1] || ''
      }
      request.adTrend(param).then(res => {
        const trendData = {
          xAxisData: [],
          exposure: [],
          hit: []
        }
        res.forEach(v => {
          trendData.xAxisData.push(v.ds)
          trendData.exposure.push(v.exposureNum)
          trendData.hit.push(v.hitNum)
        })
        this.trendData = trendData
      })
    },
    // dialog select ad
    onSelect(v) {
      this.ad = v.name
      this.adId = v.advertisementId
      this.getOverview()
      this.getTrend()
    },
    onTimeChange() {
      this.getOverview()
      this.getTrend()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .ad-info {
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
  }
}
</style>
