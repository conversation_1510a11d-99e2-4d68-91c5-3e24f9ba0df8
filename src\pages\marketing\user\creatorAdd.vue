<template>
  <section class="creator-add">
    <select-user :id.sync="form.userId" :dialog-visible.sync="dialogVisible" />
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="服务商:" prop="serviceProviderOrgId">
        <el-select v-model="form.serviceProviderOrgId">
          <el-option
            v-for="item in serverOptions"
            :key="item.serviceOrgId"
            :label="item.serviceName"
            :value="item.serviceOrgId"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="!$route.query.id" prop="userId">
        <el-button type="primary" class="select-user" @click="dialogVisible = true">选择用户</el-button>
      </el-form-item>
      <el-form-item label="真实姓名:" prop="identityInfo.realName">
        <el-input v-model="form.identityInfo.realName" />
      </el-form-item>
      <!-- 身份选择器 -->
      <el-form-item label="身份" prop="majorInfo.identityId">
        <el-cascader
          v-model="form.majorInfo.identityId"
          placeholder="请选择身份"
          :options="identityTree"
          :show-all-levels="false"
          :props="{
            value:'identityId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          @change="identityChange"
        />
      </el-form-item>
      <!-- 专科选择器 -->
      <el-form-item label="执业专科" prop="majorInfo.majorId">
        <el-cascader
          v-model="form.majorInfo.majorId"
          placeholder="请选择专科"
          :options="majorList"
          :props="{
            value:'majorId',
            label:'name',
            children:'childList',
            emitPath: false,
            checkStrictly: true
          }"
          :disabled="disabled"
        />
      </el-form-item>
      <!-- 职称选择器 -->
      <el-form-item label="职称" prop="majorInfo.academicId">
        <el-cascader
          v-model="form.majorInfo.academicId"
          placeholder="请选择职称"
          :options="academicList"
          :props="{
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          :disabled="disabled"
        />
      </el-form-item>
      <el-form-item label="工作单位:" prop="majorInfo.company">
        <el-input v-model="form.majorInfo.company" />
      </el-form-item>
      <el-form-item label="科室/部门:" prop="majorInfo.department">
        <el-input v-model="form.majorInfo.department" />
      </el-form-item>
      <el-form-item label="所在地区:" prop="region">
        <el-cascader
          v-model="form.region"
          placeholder="请选择区域"
          :options="areaList"
          :props="{
            value:'areaId',
            label:'name',
            children:'childList'
          }"
          @change="handleRegion"
        />
      </el-form-item>
      <el-form-item label="身份证号码:" prop="idcard">
        <el-input v-model="form.identityInfo.idcard" />
      </el-form-item>
      <el-form-item label="银行卡号:" prop="bankcard">
        <el-input v-model="form.identityInfo.bankcard" @blur="getbankinfo" />
      </el-form-item>
      <el-form-item label="卡类型:" prop="bankName">
        <el-input v-model="form.identityInfo.bankName" disabled />
      </el-form-item>
      <el-form-item label="开户支行:" prop="bankSubName">
        <el-input v-model="form.identityInfo.bankSubName" />
      </el-form-item>
      <el-form-item label="手机:" prop="bankBindPhone">
        <el-input v-model="form.identityInfo.bankBindPhone" />
      </el-form-item>
      <el-form-item label="身份证正/反面:">
        <UploadPic
          :key="form.identityInfo.idcardFrontImgUrl"
          tips="身份证正面"
          :pic-id.sync="form.identityInfo.idcardFrontImg"
          :url="form.identityInfo.idcardFrontImgUrl"
        />
        <UploadPic
          :key="form.identityInfo.idcardBehindImgUrl"
          tips="身份证反面"
          :pic-id.sync="form.identityInfo.idcardBehindImg"
          :url="form.identityInfo.idcardBehindImgUrl"
        />
      </el-form-item>
      <el-form-item label="资质证书:">
        <UploadPic
          :key="form.majorInfo.certImgUrl"
          tips="资格证书"
          :pic-id.sync="form.majorInfo.certImg"
          :url="form.majorInfo.certImgUrl"
        />
        <UploadPic
          :key="form.majorInfo.practiceCertImgUrl"
          tips="工作证/执业证书"
          :pic-id.sync="form.majorInfo.practiceCertImg"
          :url="form.majorInfo.practiceCertImgUrl"
        />
      </el-form-item>
      <el-form-item label="本人照片:">
        <UploadPic
          :key="form.majorInfo.avatarUrl"
          tips="本人照片"
          :pic-id.sync="form.majorInfo.avatar"
          :url="form.majorInfo.avatarUrl"
        />
      </el-form-item>
      <el-form-item class="save">
        <el-button @click="$router.go(-1)">取消</el-button>
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </el-form-item>
    </el-form>
  </section>
</template>

<script>
import SelectUser from './components/selectUser.vue'
import { getCreatorDetail, saveAuthApply } from '@/api/marketing/userPromote'
import { identityTreeList, majorTreeListId } from '@/api/category' // 身份树，随身份联动的专科树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树
import { getAreaTree } from '@/api/area' // 区域树
import UploadPic from '@/components/Upload/SingleImage4.vue'
import { serviceProviderList } from '@/api/marketing/taskPromote'
import { getBankBin } from 'bankcardinfo'
export default {
  components: {
    SelectUser,
    UploadPic
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        serviceProviderOrgId: null,
        createType: 2,
        userId: this.$route.query.id || '',
        promoteType: 'CREATOR',
        identityInfo: {
          bankBindPhone: '',
          bankName: '',
          bankSubName: '',
          bankcard: '',
          idcard: '',
          idcardBehindImg: '',
          idcardFrontImg: '',
          realName: ''
        },
        majorInfo: {
          academicId: '',
          areaId: '',
          avatar: '',
          certImg: '',
          company: '',
          department: '',
          identityId: '',
          majorId: '',
          practiceCertImg: ''
        },
        region: []
      },
      serverOptions: [],
      identityTree: [],
      majorList: [],
      academicList: [],
      areaList: [],
      disabled: false,
      rules: {
        userId: [
          { required: true, message: '请选择用户', trigger: 'blur' }
        ],
        'identityInfo.realName': [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        'majorInfo.identityId': [
          { required: true, message: '请选择身份', trigger: 'change' }
        ],
        'majorInfo.majorId': [
          { required: true, message: '请选择执业专科', trigger: 'change' }
        ],
        'majorInfo.academicId': [
          { required: true, message: '请选择职称', trigger: 'change' }
        ],
        'majorInfo.company': [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        'majorInfo.department': [
          { required: true, message: '请输入科室/部门', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请选择所在地区', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    dialogVisible(v) {
      if (!v && this.form.userId !== '') {
        this.getDateil()
      }
    }
  },
  created() {
    serviceProviderList().then(res => {
      this.serverOptions = res
    })
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    // 获取专科树
    treeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', majorId: '0' })
      this.majorList = newArr
    })
    // 获取区域树
    getAreaTree().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      this.areaList = newArr
    })
    if (this.$route.query.id) {
      this.getDateil()
    }
  },
  methods: {
    getDateil() {
      getCreatorDetail(this.form.userId).then(res => {
        this.form.serviceProviderOrgId = this.$route.query.serviceOrgId || null
        this.form.identityInfo = res.identityInfo
        this.form.majorInfo = res.majorInfo
        this.form.region = [
          res.majorInfo.provinceId,
          res.majorInfo.cityId,
          res.majorInfo.areaId
        ]
        this.identityChange(this.form.majorInfo.identityId)
        this.$refs.form.validate('rules')
      })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      if (e && e === '0') {
        // 身份选'无'，专科职称也为无
        this.form.majorInfo.majorId = '0'
        this.form.majorInfo.academicId = '0'
        this.disabled = true
        return
      } else {
        this.disabled = false
        majorTreeListId(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: '0' })
          this.majorList = newArr
        })
        academicTreeListById(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: '0' })
          this.academicList = newArr
        })
      }
    },
    handleRegion() {
      this.form.majorInfo.provinceId = this.form.region[0]
      this.form.majorInfo.cityId = this.form.region[1]
      this.form.majorInfo.areaId = this.form.region[2]
    },
    getbankinfo() {
      getBankBin(this.form.identityInfo.bankcard).then(res => {
        this.form.identityInfo.bankName = res.bankName + res.cardTypeName
      }).catch(() => { this.$message.error('银行卡号错误,请重新输入') })
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          saveAuthApply(this.form).then(res => {
            this.$message.success('成功保存创作者信息')
            this.$router.go(-1)
          })
        } else {
          this.$message.error('请检查完善必填项')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.creator-add {
  height: calc(100% - 50px);
  padding: 50px 100px;
  background-color: #fff;
  .select-user {
    width: 420px;
  }
  .el-form {
    margin-top: 20px;
    ::v-deep .el-form-item__content {
      display: flex;
      div {
        margin-right: 10px;
      }
    }
    .el-cascader,
    .el-date-editor,
    .el-textarea,
    .el-input {
      width: 420px;
    }
    .el-form-item.save {
      margin-top: 50px;
      .el-button {
        width: 120px;
      }
    }
  }
}
</style>
