<template>
  <div class="part">
    <div ref="chart" class="chart" />
    <span>{{ name }}</span>
    <p>{{ basics === 100 ? `${value}%`: value }}</p>
  </div>
</template>

<script>
var echarts = require('echarts/lib/echarts')
require('echarts/lib/chart/line')
require('echarts/lib/chart/bar')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/legend')
import { GridComponent, ToolboxComponent } from 'echarts/components'
echarts.use([GridComponent, ToolboxComponent])

export default {
  props: {
    name: {
      type: String,
      default: ''
    },
    index: {
      type: Number,
      default: 1
    },
    value: {
      type: Number,
      default: 0
    },
    basics: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(v) {
        this.myChart.dispose()
        this.myChart = echarts.init(this.$refs.chart)
        this.setOptions(v)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(this.$refs.chart)
      this.setOptions(this.data)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    })
  },
  methods: {
    setOptions(data) {
      this.myChart.setOption({
        graphic: {
          elements: [{
            type: 'image',
            style: {
              image: require(`@/assets/images/bs/part${this.index}.png`),
              width: 36,
              height: 36
            },
            left: 'center',
            top: 'center'
          }]
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['100%', '80%'],
            avoidLabelOverlap: false,
            hoverAnimation: false,
            color: ['#3fecff', '#11273a'],
            itemStyle: {
              borderRadius: 10
            },
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: [
              { value: this.value },
              { value: this.basics - this.value }
            ]
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.myChart) {
        return
      }
      this.myChart.dispose()
      this.myChart = null
    }
  }
}
</script>

<style scoped lang="scss">
.part {
  text-align: center;
  .chart {
    margin: 0 auto;
    width: 80px;
    height: 80px;
  }
  span {
    font-size: 12px;
    line-height: 26px;
    opacity: 0.8;
  }
  p {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
