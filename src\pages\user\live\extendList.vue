<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable @clear="getList()" @keydown.enter.native="getList()">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="getList()" />
          <el-select slot="prepend" v-model="keywordType" style="width:130px">
            <el-option label="名称" value="extenderName" />
            <el-option label="编码" value="code" />
            <el-option label="手机" value="phone" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.type" clearable placeholder="请选择类型" @change="getList()">
          <el-option label="个人" :value="1" />
          <el-option label="企业" :value="2" />
        </el-select>
      </div>
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='type'">
            <span>{{ scope.row[col.prop] === 1 ? '个人' : '企业' }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right">
        <template slot-scope="{row}">
          <el-button type="text" @click="openDetail(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <el-dialog title="内容扩展方" :visible.sync="visible" width="50vw" top="20px">
      <div style="padding:20px">
        <el-row class="detail">
          <!-- 个人 -->
          <el-form
            v-if="type===1"
            ref="form"
            :model="detail"
            label-width="200px"
            label-position="right"
          >
            <h2>个人信息</h2>
            <el-form-item label="编码:">
              <span>{{ detail.code }}</span>
            </el-form-item>
            <el-form-item label="真实姓名:">
              <span>{{ detail.realName }}</span>
            </el-form-item>
            <el-form-item label="身份证号码:">
              <span>{{ detail.idcard }}</span>
            </el-form-item>
            <el-form-item label="身份证正/反面:">
              <div class="imgBox">
                <el-image
                  style="width: 300px"
                  fit="cover"
                  :src="detail.idcardFrontImgUrl"
                  :preview-src-list="[detail.idcardFrontImgUrl]"
                />
                <el-image
                  style="width: 300px"
                  fit="cover"
                  :src="detail.idcardBehindImgUrl"
                  :preview-src-list="[detail.idcardBehindImgUrl]"
                />
              </div>
            </el-form-item>
            <el-form-item label="手机:">
              <span>{{ detail.phone }}</span>
            </el-form-item>

            <h2>结算信息</h2>
            <el-form-item label="开户行:">
              <span>{{ detail.bankName }}</span>
            </el-form-item>
            <el-form-item label="户名:">
              <span>{{ detail.realName }}</span>
            </el-form-item>
            <el-form-item label="银行卡号:">
              <span>{{ detail.bankcard }}</span>
            </el-form-item>
          </el-form>
          <!-- 企业 -->
          <el-form
            v-else
            ref="form"
            :model="detail"
            label-width="200px"
            label-position="right"
          >
            <h2>企业信息</h2>
            <el-form-item label="编码:">
              <span>{{ detail.code }}</span>
            </el-form-item>
            <el-form-item label="企业全称:">
              <span>{{ detail.orgName }}</span>
            </el-form-item>
            <el-form-item label="统一社会信用代码:">
              <span>{{ detail.socialCreditCode }}</span>
            </el-form-item>
            <el-form-item label="营业执照:">
              <el-image
                style="width: 300px"
                fit="cover"
                :src="detail.businessLicenseImgUrl"
                :preview-src-list="[detail.businessLicenseImgUrl]"
              />
            </el-form-item>
            <el-form-item label="企业法人:">
              <span>{{ detail.legalPerson }}</span>
            </el-form-item>
            <el-form-item label="企业法人身份证号码:">
              <span>{{ detail.legalPersonIdcard }}</span>
            </el-form-item>
            <el-form-item label="企业法人身份证正/反面:">
              <div class="imgBox">
                <el-image
                  style="width: 300px"
                  fit="cover"
                  :src="detail.legalPersonIdcardFrontImgUrl"
                  :preview-src-list="[detail.legalPersonIdcardFrontImgUrl]"
                />
                <el-image
                  style="width: 300px"
                  fit="cover"
                  :src="detail.legalPersonIdcardBehindImgUrl"
                  :preview-src-list="[detail.legalPersonIdcardBehindImgUrl]"
                />
              </div>
            </el-form-item>

            <h2>联系信息</h2>
            <el-form-item label="联系人:">
              <span>{{ detail.contacts }}</span>
            </el-form-item>
            <el-form-item label="手机:">
              <span>{{ detail.phone }}</span>
            </el-form-item>
            <el-form-item label="联系地址:">
              <span>{{ detail.areaName }}</span>
            </el-form-item>
            <el-form-item label="">
              <span>{{ detail.address }}</span>
            </el-form-item>

            <h2>结算信息</h2>
            <el-form-item label="开户行:">
              <span>{{ detail.bankName }}</span>
            </el-form-item>
            <el-form-item label="户名:">
              <span>{{ detail.orgName }}</span>
            </el-form-item>
            <el-form-item label="银行卡号:">
              <span>{{ detail.bankcard }}</span>
            </el-form-item>
          </el-form>
        </el-row>
        <el-row style="text-align:center">
          <el-button @click="visible = false">返  回</el-button>
        </el-row>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getExtenderList, getExtenderDetailed } from '@/api/liveApply'

export default {
  name: 'ExtendList',
  components: { Pagination },
  data() {
    return {
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 1, label: '编码', align: 'center', prop: 'code' },
        { id: 2, label: '名称', align: 'center', prop: 'extenderName' },
        { id: 3, label: '手机', align: 'center', prop: 'phone' },
        { id: 4, label: '类型', align: 'center', prop: 'type' },
        { id: 5, label: '讲师数', align: 'center', prop: 'lecturerNumber' },
        { id: 6, label: '直播场次', align: 'center', prop: 'liveNumber' },
        { id: 7, label: '总费用(元)', align: 'center', prop: 'totalCostNumber' },
        { id: 8, label: '入驻时间', align: 'center', prop: 'auditTime', width: '160px' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      type: '',
      keywordType: 'extenderName',
      keyword: '',
      visible: false,
      detail: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList(reset = true) {
      this.tableQuery.condition.extenderName = ''
      this.tableQuery.condition.code = ''
      this.tableQuery.condition.phone = ''
      this.tableQuery.condition[this.keywordType] = this.keyword
      reset && (this.tableQuery.pager.page = 1)
      getExtenderList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList(false)
    },
    openDetail(row) {
      this.type = row.type
      getExtenderDetailed(row.id).then(res => {
        if (res.type === 1) {
          this.detail = res.userDto
        } else if (res.type === 2) {
          this.detail = res.orgDto
        } else {
          this.detail = {}
        }
        this.visible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
