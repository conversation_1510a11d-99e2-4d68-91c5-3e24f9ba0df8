<template>
  <!-- ---------------身份详情-------------- -->
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <!-- 时间选择器 -->
      <div class="search-column__item">
        <el-date-picker
          v-model="timeRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyyMM"
          :picker-options="pickerOptions"
          :clearable="false"
          @change="changePicker"
        />
      </div>
      <!-- 职称选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="condition.academicIds"
          placeholder="请选择职称"
          :options="academicList"
          collapse-tags
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false,
            checkStrictly: true
          }"
          clearable
        />
      </div>
      <!-- 区域选择器 -->
      <div class="search-column__item">
        <el-cascader
          ref="cascader1"
          v-model="areaId"
          placeholder="请选择区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            value:'areaId',
            label:'name',
            children:'childList',
            checkStrictly:true
          }"
          @change="handlerChange"
        />
      </div>
      <!-- 查询/重置/导出 按钮 -->
      <div class="search-column__item fr">
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="exportRecord">导出</el-button>
      </div>
    </div>

    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe :default-sort="{prop: 'staffNum', order: 'descending'}" />
  </div>
</template>

<script>
import table from '@/mixins/ctable' // qyc 公共table.js
import request from '@/api/dataStatistics/identityDetail' // 身份详情api
import { getAreaTree } from '@/api/area' // 区域树

// 自定义表格 表头
const columns = [
  {
    props: { label: '序号', align: 'center', type: 'index', width: '60px' }
  },
  {
    props: { label: '用户身份', align: 'center', prop: 'name', width: '130px' }
  },
  {
    props: { label: '用户数', align: 'center', prop: 'staffNum' }
  },
  {
    props: { label: '培训人数', align: 'center', prop: 'trainPeopleNum' }
  },
  {
    props: { label: '培训人次', align: 'center', prop: 'trainPersonNum' }
  },
  {
    props: { label: '培训任务数', align: 'center', prop: 'trainTaskNum' }
  },
  {
    props: { label: '培训时长(分钟)', align: 'center', prop: 'trainTime', width: '80px' }
  },
  {
    props: { label: '参与人次', align: 'center', prop: 'joinPeopleNum' }
  },
  {
    props: { label: '通过人次', align: 'center', prop: 'passPeopleNum' }
  },
  {
    props: { label: '培训覆盖率', align: 'center', prop: 'coverageRate' } //
  },
  {
    props: { label: '培训参与率', align: 'center', prop: 'partRate' } //
  },
  {
    props: { label: '培训通过率', align: 'center', prop: 'passRate' } //
  },
  {
    props: { label: '参与达标率', align: 'center', prop: 'controlRate' } //
  },
  {
    props: { label: '活跃数', align: 'center', prop: 'activePeopleNum' }
  },
  {
    props: { label: '活跃率', align: 'center', prop: 'activeRate' } //
  },
  {
    props: { label: '自学时长(分钟)', align: 'center', prop: 'learnTime', width: '80px' }
  },
  {
    props: { label: '集体效果指数', align: 'center', prop: 'collectiveIndex', width: '110px' }
  }
]

export default {
  name: 'IdentityDetail',
  mixins: [table],
  data() {
    return {
      hasPage: 0,
      academicList: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      areaTypeList: [
        { value: 1, label: '全国' },
        { value: 2, label: '省' },
        { value: 3, label: '市' },
        { value: 4, label: '县/区' }
      ],
      columns,
      request,
      timeRange: [],
      // 自动查找的参数
      condition: {
        academicIds: [], // 职称集合
        areaType: 1,
        areaId: '0',
        startTime: '',
        endTime: ''
      },
      areaList: [], // 区域集合
      areaId: ['0'],
      dataJson: {}
    }
  },
  watch: {
    // 关闭级联下拉框
    areaId() {
      if (this.$refs.cascader1) {
        this.$refs.cascader1.dropDownVisible = false
      }
    }
  },
  created() {
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.unshift({ name: '全国', areaId: '0' })
      this.areaList = newArr
    })
    // 获取当前月份及上月
    this.getDate()
  },
  methods: {
    getListData() {
      const params = this.copy(this.condition)
      // 应后端要求，用户未选择时，单值字段给-1
      if (params.academicIds.length === 0) {
        params.academicId = -1
      }
      return params
    },
    // 重置预设数据
    reset() {
      const { startTime, endTime } = this.copy(this.dataJson)
      this.timeRange = []
      this.timeRange.push(startTime)
      this.timeRange.push(endTime)
      this.condition.startTime = startTime
      this.condition.endTime = endTime
      this.condition.academicIds = []
      this.areaId = ['0']
      this.condition.areaId = '0'
      this.condition.areaType = 1
    },
    // 导出
    exportRecord() {
      if (this.list.length < 1) {
        this.$message.info('当前数据为空')
        return
      }
      const parmas = this.getListData()
      this.request.export(parmas).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>
