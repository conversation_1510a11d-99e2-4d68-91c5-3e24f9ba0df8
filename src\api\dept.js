import request from '@/utils/request'

/**
 * 部门库相关
 */

// 部门库列表
export function deptList(params) {
  return request({
    url: '/cms/departmentpack/list',
    method: 'post',
    data: params
  })
}

// 部门库名称列表 (用于添加广告页)
export function deptNameList(params) {
  return request({
    url: '/cms/departmentpack/findDepaPackNameList',
    method: 'post',
    data: params
  })
}

// 添加部门库
export function addDept(params) {
  return request({
    url: '/cms/departmentpack/add',
    method: 'post',
    data: params
  })
}

// 编辑部门库
export function editDept(params) {
  return request({
    url: '/cms/departmentpack/edit',
    method: 'post',
    data: params
  })
}

// 删除部门库
export function delDept(params) {
  return request({
    url: '/cms/departmentpack/delete',
    method: 'get',
    params
  })
}

// 获取指定单位部门树列表
export function getDeptTree(params) {
  return request({
    url: '/department/treeList',
    method: 'get',
    params
  })
}

