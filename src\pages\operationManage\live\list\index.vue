<template>
  <div class="app-container">
    <div class="search-column">
      <Search :has-live-status="true" :time="true" @search="search" />
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='liveStatus'">
            <span>{{ scope.row[col.prop] | liveStatusFilter }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="110">
        <template slot-scope="{row}">
          <template v-if="row.liveStatus!==3">
            <el-button type="text" @click="share(row)">分享</el-button>
          </template>
          <template v-if="row.liveStatus===3">
            <el-button v-if="row.billStatus===0" type="text" @click="confirmCost(row.liveId)">确认费用</el-button>
            <el-button v-if="row.billStatus===1" type="text" @click="costData(row.liveId)">直播费用</el-button>
            <el-button type="text" @click="liveData(row.liveId)">直播数据</el-button>
            <el-button type="text" @click="handlePlayVideo(row)">回看</el-button>
          </template>
          <el-button type="text" @click="toManage(row)">管理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <ComfirmCost v-if="comfirmCostVisible" :visible.sync="comfirmCostVisible" :cost-info="costInfo" @confirmCostSuccess="confirmCostSuccess" />

    <CostData :visible.sync="costDataVisible" :cost-data1="costData1" :cost-data2="costData2" />

    <LiveData :visible.sync="liveDataVisible" :live-id="liveId" />

    <QRPoster v-if="QRPosterVisible" :visible.sync="QRPosterVisible" :url="shareBehindImgUrl" :qrurl="shareUrl" :pic-title="picTitle" />

    <el-dialog class="playVideoDialog" :title="playerTitle" :visible.sync="videoDialog" width="1024px" @close="handlePauseVideo">
      <div class="pos-re">
        <AliPlayer
          ref="aliplayer"
          :play-style="aliPlayerConfig.playStyle"
          :source="aliPlayerConfig.source"
          :height="aliPlayerConfig.height"
          :skin-layout="aliPlayerConfig.skinLayout"
          @ready="handleReadyVideo"
          @pause="handlePauseVideo"
          @error="handleError"
        />
      </div>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { liveList, referCost, liveLookBack, getShareUrl, getCostData, getLiveManageUrl } from '@/api/liveManage'
import Search from '../components/search.vue'
import ComfirmCost from './confirmCost.vue'
import AliPlayer from '@/components/Aliplayer/index.vue'
import QRPoster from '@/components/QRPoster/index.vue'
import CostData from './costData.vue'
import LiveData from './liveData.vue'

export default {
  name: 'LiveList',
  filters: {
    liveStatusFilter(val) {
      const arr = ['', '进行中', '未开始', '已结束']
      return arr[val]
    }
  },
  components: { Pagination, Search, ComfirmCost, AliPlayer, QRPoster, CostData, LiveData },
  data() {
    return {
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: '直播ID', align: 'center', prop: 'liveId', width: '100px' },
        { id: 2, label: '直播主题', align: 'center', prop: 'title' },
        { id: 3, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 4, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 5, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 6, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 7, label: '推广类型', align: 'center', prop: 'promotionType' },
        { id: 8, label: '推广人数', align: 'center', prop: 'promotionNum' },
        { id: 9, label: '直播时间', align: 'center', prop: 'startTime' },
        { id: 10, label: '直播状态', align: 'center', prop: 'liveStatus' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      liveId: '',
      comfirmCostVisible: false,
      costInfo: {},
      // 回看----
      playerTitle: '',
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        source: null,
        skinLayout: [
          {
            name: 'bigPlayButton',
            align: 'cc'
          },
          {
            name: 'H5Loading',
            align: 'cc'
          },
          {
            name: 'errorDisplay',
            align: 'tlabs',
            x: 0,
            y: 0
          },
          {
            name: 'infoDisplay'
          },
          {
            name: 'tooltip',
            align: 'blabs',
            x: 0,
            y: 56
          },
          {
            name: 'controlBar',
            align: 'blabs',
            x: 0,
            y: 0,
            children: [
              {
                name: 'progress',
                align: 'blabs',
                x: 0,
                y: 44
              },
              {
                name: 'playButton',
                align: 'tl',
                x: 15,
                y: 12
              },
              {
                name: 'timeDisplay',
                align: 'tl',
                x: 10,
                y: 7
              },
              {
                name: 'fullScreenButton',
                align: 'tr',
                x: 10,
                y: 12
              },
              {
                name: 'volume',
                align: 'tr',
                x: 5,
                y: 10
              }
            ]
          }
        ]
      },
      videoDialog: false,
      // 回看----
      // 分享---
      picTitle: '',
      shareUrl: '',
      shareBehindImgUrl: '',
      QRPosterVisible: false,
      // 分享---
      costDataVisible: false,
      costData1: [],
      costData2: [],
      liveDataVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // ------------回看-------------
    // 点击回看
    handlePlayVideo(row) {
      this.playerTitle = row.title
      liveLookBack(row.liveId).then(res => {
        if (res.videoPlayInfoList && res.videoPlayInfoList.length) {
          const sourceUrl = {}
          res.videoPlayInfoList.map(v => {
            if (v.definition === 'SD') {
              sourceUrl[v.definition] = v.playUrl
            }
          })
          this.aliPlayerConfig.source = JSON.stringify(sourceUrl)
          this.videoDialog = true
          this.$nextTick(() => {
            this.$refs.aliplayer.init()
          })
        } else {
          this.$message.error('该视频暂时无法播放，请稍后重试！')
        }
      })
    },
    // 视频播放
    handleReadyVideo(val) {
      this.player = val
    },
    // 视频暂停
    handlePauseVideo() {
      this.player.pause()
    },
    // 视频错误
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    },
    // ------------回看-------------
    share(row) {
      this.picTitle = row.title
      getShareUrl(row.liveId).then(res => {
        this.shareUrl = res.shareUrl
        this.shareBehindImgUrl = res.shareBehindImgUrl
        this.QRPosterVisible = true
      })
    },
    confirmCost(liveId) {
      this.costInfo.liveId = liveId
      referCost(liveId).then(res => {
        res.feeRuleResponseDtos.forEach(item => {
          switch (item.feeItemCode) {
            case '1':
              this.costInfo.feeRuleOneDto = item
              break
            case '2':
              this.costInfo.feeRuleTwoDto = item
              break
            case '3':
              this.costInfo.feeRuleThreeDto = item
              break
            case '4':
              this.costInfo.feeRuleFourDto = item
              break
            case '5':
              this.costInfo.feeRuleFiveDto = item
              break
          }
        })
        this.costInfo.extenderName = res.extenderName
        this.costInfo.extenderPhone = res.extenderPhone
        this.costInfo.lecturerName = res.lecturerName
        this.costInfo.lecturerPhone = res.lecturerPhone
        this.comfirmCostVisible = true
      })
    },
    confirmCostSuccess(liveId) {
      // 确认费用成功后，关闭弹窗自动打开直播费用弹窗
      this.comfirmCostVisible = false
      this.getList()
      this.costData(liveId)
    },
    costData(liveId) {
      getCostData(liveId).then(res => {
        this.costData1.length = 0
        this.costData1.push(res)
        this.costData2 = res.liveCostResponseDtos
        this.costDataVisible = true
      })
    },
    liveData(liveId) {
      this.liveId = liveId
      this.liveDataVisible = true
    },
    toManage(row) {
      if (row.assistantCode) {
        this.copyToClipboard(row.assistantCode)
      }
      getLiveManageUrl(row.liveId).then(res => {
        window.open(res, '_blank')
      })
    },
    search(condition) {
      this.tableQuery.condition = condition
      this.getList()
    },
    getList(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      liveList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList(false)
    },
    // 点击复制到剪贴板函数
    copyToClipboard(content) {
      if (window.clipboardData) {
        window.clipboardData.setData('text', content)
      } else {
        (function(content) {
          document.oncopy = function(e) {
            e.clipboardData.setData('text', content)
            e.preventDefault()
            document.oncopy = null
          }
        })(content)
        document.execCommand('Copy')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button+.el-button ::v-deep {
  margin-left: 0;
}
</style>
