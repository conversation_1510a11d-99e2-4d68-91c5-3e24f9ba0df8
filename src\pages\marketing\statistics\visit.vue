<template>
  <section class="visit">
    <div v-if="!$route.path.includes('visit/detail')">
      <h2 v-if="!isTotal">
        拜访列表
        <el-button type="primary" @click="exportList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="!isTotal ? tableData : tableData.slice(0,4) "
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column prop="id" label="拜访客户" width="260">
            <template slot-scope="scope">
              <span @click="toDetail(scope.row.id)">{{ scope.row.customer }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
        </el-table>
      </div>
      <Pagination
        v-if="!isTotal"
        :page="queryParams.pager.page"
        :page-size="queryParams.pager.pageSize"
        :total="total"
        @pagination="handlePagination"
      />
    </div>
    <!-- 详情 -->
    <router-view />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { visitList, visitListExport } from '@/api/marketing/promoteArticle'
import { deleteEmptyProperty } from '@/utils/index'
export default {
  components: {
    Pagination
  },
  props: {
    isTotal: {
      type: Boolean,
      default: false
    },
    searchParam: {
      type: Object,
      default: () => {}
    },
    userType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableColumn: [
        { prop: 'companyName', label: '客户单位', width: '280' },
        { prop: 'type', label: '拜访类型', width: '140' },
        { prop: 'serviceProvider', label: '服务商', width: '130' },
        { prop: 'orgName', label: '企业', width: '130' },
        { prop: 'productDesc', label: '产品', width: '300' },
        { prop: 'userName', label: '推广员', width: '140' },
        { prop: 'visitTime', label: '拜访时间', width: '160' },
        { prop: 'feeStr', label: '费用 (元)', width: '' }
      ],
      tableData: [],
      queryParams: {
        condition: {
          startTime: null,
          endTime: null,
          productId: null,
          keyword: null,
          serviceProviderOrgId: null,
          orgzId: null,
          orgzIds: null,
          orgId: null,
          userId: null,
          visitType: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0
    }
  },
  watch: {
    searchParam: {
      handler(newVal, oldVal) {
        for (const key in this.queryParams.condition) {
          if (newVal.condition[key] !== undefined) {
            this.queryParams.condition[key] = newVal.condition[key]
          }
        }
        this.queryParams.pager.page = newVal.pager.page
        this.queryParams.condition['orgzId'] = this.queryParams.condition['orgId']
        if (this.$route.path.includes('visit/detail')) return
        this.getArticleAnalysisList()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    toDetail(id) {
      this.$router.push({
        name: 'MarketingStatisticsVisitDetail',
        query: {
          id
        }
      })
    },
    getArticleAnalysisList() {
      if (this.userType !== '') {
        this.queryParams.condition.userType = this.userType
        this.tableColumn.forEach(v => {
          v.width = ''
        })
        if (this.tableColumn.length === 8) {
          this.userType === 'CREATOR' ? this.tableColumn.splice(1, 3) : this.tableColumn.splice(1, 2)
        }
      }
      const param = JSON.parse(JSON.stringify(this.queryParams))
      deleteEmptyProperty(param)
      visitList(param).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportList() {
      if (this.total <= 0) {
        this.$message.error('暂无可导出数据')
        return
      }
      const param = JSON.parse(JSON.stringify(this.queryParams.condition))
      deleteEmptyProperty(param)
      visitListExport(param).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getArticleAnalysisList()
    }
  }
}
</script>

<style lang="scss" scoped>
.visit {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  h2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
    .el-button {
      width: 80px;
      height: 35px;
    }
  }
  .table {
    padding: 25px 20px 0;
    .cell {
      span {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
}
</style>
