<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-if="type === '1'" v-model="condition.keyword" placeholder="请输入关键字搜索" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
        <el-input v-else v-model="condition.videoName" placeholder="请输入关键字搜索" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="index" slot-scope="{$index, row}">
        <span v-if="type == data.type && (data.id == row.videoId || data.id == row.coursePackId)" class="f9"> {{ pager.page * pager.pageSize - 9 + $index }}</span>
        <span v-else>{{ pager.page * pager.pageSize - 9 + $index }}</span>
        <!-- <el-tooltip class="id" effect="dark" :content="row.name" placement="top-start">
          <span v-if="type == data.type && (data.id == row.videoId || data.id == row.coursePackId)" class="f9"> {{ row.videoId || row.coursePackId }}</span>
          <span v-else>{{ row.videoId || row.coursePackId }}</span>
        </el-tooltip> -->
      </template>
      <template slot="id" slot-scope="{row}">
        <el-tooltip class="id" effect="dark" :content="row.name" placement="top-start">
          <span v-if="type == data.type && (data.id == row.videoId || data.id == row.coursePackId)" class="f9"> {{ row.videoId || row.coursePackId }}</span>
          <span v-else>{{ row.videoId || row.coursePackId }}</span>
        </el-tooltip>
      </template>
      <template slot="name" slot-scope="{row}">
        <el-tooltip class="item" effect="dark" :content="row.name" placement="top-start">
          <span v-if="type == data.type && (data.id == row.videoId || data.id == row.coursePackId)" class="f9"> {{ row.name }}</span>
          <span v-else>{{ row.name }}</span>
        </el-tooltip>
      </template>
      <template slot="introduction" slot-scope="{row}">
        <el-tooltip class="item" effect="dark" :content="row.introduction" placement="top-start">
          <span v-if="type == data.type && data.id == row.videoId" class="f9"> {{ row.introduction }}</span>
          <span v-else>{{ row.introduction }}</span>
        </el-tooltip>
      </template>
      <template slot="appDescribe" slot-scope="{row}">
        <el-tooltip class="item" effect="dark" :content="row.appDescribe" placement="top-start">
          <span v-if="type == data.type && data.id == row.coursePackId" class="f9"> {{ row.appDescribe }}</span>
          <span v-else> {{ row.appDescribe }}</span>
        </el-tooltip>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button v-if="type == data.type && (data.id == row.videoId || data.id == row.coursePackId)" size="mini" disabled type="text">已选择</el-button>
        <el-button v-else size="mini" type="text" @click="emit(row)">选择</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import { coursePack, videoValidList } from '@/api/validManage'

const columns1 = [
  {
    props: { label: '序号', type: 'index', align: 'center', width: '85' },
    slot: 'index'
  },
  {
    props: { label: '课程Id', align: 'center', prop: 'coursePackId' },
    slot: 'id'
  },
  {
    props: { label: '课程名称', align: 'center' },
    slot: 'name'
  },
  {
    props: { label: '课程简介', align: 'center', prop: 'appDescribe' },
    slot: 'appDescribe'
  },
  {
    props: { align: 'center', label: '操作', width: '55' },
    slot: 'actions'
  }
]
const columns2 = [
  {
    props: { label: '序号', type: 'index', align: 'center', width: '85' },
    slot: 'index'
  },
  {
    props: { label: '视频Id', align: 'center', prop: 'videoId' },
    slot: 'id'
  },
  {
    props: { label: '视频名称', align: 'center' },
    slot: 'name'
  },
  {
    props: { label: '视频简介', align: 'center', prop: 'introduction' },
    slot: 'introduction'
  },
  {
    props: { align: 'center', label: '操作', width: '55' },
    slot: 'actions'
  }
]

export default {
  name: 'ResourceRecommendDialogList',
  filters: {
    typeFlt(v) {
      const arr = ['', '课程', '视频']
      return arr[v]
    }
  },
  mixins: [table],
  props: {
    type: {
      type: String,
      default: '1'
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      columns: this.type === '1' ? columns1 : columns2,
      request: {
        list: this.type === '1' ? coursePack : videoValidList
      },
      condition: {
        keyword: '',
        audit: 2,
        videoName: ''
      },
      mainName: 'name'
    }
  },
  watch: {
    type(v) {
      if (v === '1') {
        this.request.list = coursePack
        this.columns = columns1
      } else {
        this.request.list = videoValidList
        this.columns = columns2
      }
      this.pager.page = 1
      this.condition = {
        courseName: '',
        audit: 2,
        videoName: ''
      }
      this.handleFilter()
    }
  },
  methods: {
    emit(row) {
      if (this.type === '1') {
        this.$emit('change', row.coursePackId)
      } else {
        this.$emit('change', row.videoId)
      }
    },
    recharge() {
      this.condition = {
        keyword: '',
        audit: 2,
        videoName: ''
      }
      this.pager.page = 1
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button.title{
  white-space: normal;
}
.app-container ::v-deep .cell {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.f9 {
  color: #999999;
}
</style>
