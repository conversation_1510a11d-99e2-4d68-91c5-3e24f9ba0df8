<template>
  <section class="userDetail">
    <search :query="listQuery.condition" @search="search" @clear="clear" />
    <el-table
      :data="tableData"
      border
      :header-cell-style="{background:'#f9f9f9',color:'#333'}"
    >
      <el-table-column
        v-for="item in tableOptions"
        :key="item.prop"
        :prop="item.prop"
        :width="item.width"
        :label="item.label"
        :align="item.align"
      />
    </el-table>
    <Pagination :page-size="listQuery.pager.pageSize" :total="total" :page="listQuery.pager.page" @pagination="pagination" />
  </section>
</template>

<script>
import Search from '../components/search.vue'
import Pagination from '@/components/Pagination'
import { userDetail } from '@/api/statistics'
export default {
  components: {
    Search,
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          academicId: 0,
          areaId: '0',
          areaLevel: 4,
          identityId: 0,
          majorId: '',
          majorLevel: null,
          parentMajorId: 0,
          orgName: '',
          phone: '',
          realName: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableData: [],
      tableOptions: [
        { label: 'UID', align: 'center', prop: 'userId', width: '200px' },
        { label: '姓名', align: 'left', prop: 'realName', width: '150px' },
        { label: '手机', align: 'left', prop: 'phone', width: '200px' },
        { label: '身份', align: 'left', prop: 'identity', width: '100px' },
        { label: '专科', align: 'left', prop: 'major', width: '200px' },
        { label: '职称', align: 'left', prop: 'academic', width: '200px' },
        { label: '所在单位', align: 'left', prop: 'orgName', width: '' },
        { label: '所在地区', align: 'left', prop: 'area', width: '' }
      ]
    }
  },
  created() {
    this.listQuery.condition.areaId = this.$route.query.areaId
    this.listQuery.condition.areaLevel = this.$route.query.areaLevel
    this.listQuery.condition.majorId = this.$route.query.majorId
    this.listQuery.condition.majorLevel = this.$route.query.majorLevel
    this.listQuery.condition.parentMajorId = this.$route.query.parentMajorId
    this.getList()
  },
  methods: {
    getList() {
      userDetail(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    pagination(val) {
      this.listQuery.pager = val
      this.getList()
    },
    search(condition) {
      this.listQuery.pager.page = 1
      this.listQuery.condition = condition
      this.getList()
    },
    clear(condition) {
      this.listQuery = {
        condition: {
          academicId: 0,
          areaId: '0',
          areaLevel: 4,
          identityId: 0,
          majorId: '',
          majorLevel: null,
          orgName: '',
          phone: '',
          realName: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.userDetail {
  padding: 20px 40px;
  .el-table {
    margin-top: 40px;
  }
}
</style>
