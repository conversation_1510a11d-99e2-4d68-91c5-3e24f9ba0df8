<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.name"
        class="group"
        placeholder="名称"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>

      <el-select
        v-model="listQuery.condition.status"
        placeholder="状态"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.source"
        placeholder="资源来源"
        clearable
        @change="search"
      >
        <el-option
          v-for="item in sourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      培训计划
      <div><el-button type="primary" @click="toDetail('create')">+ 创建</el-button></div>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              v-if="row.source === 1"
              type="text"
              size="mini"
              @click="updateStatus(row.id, row.status === 0 ? 1 :0)"
            >{{ row.status === 0 ? '启用' :'停用' }}</el-button>
            <el-button
              v-if="row.source === 1"
              type="text"
              size="mini"
              @click="toDetail('edit',row.id)"
            >编辑</el-button>
            <el-button
              type="text"
              size="mini"
              @click="toDetail('copy',row.id)"
            >复制</el-button>
            <el-button
              type="text"
              size="mini"
              @click="toDetail('view',row.id)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { packageList, enablePackage } from '@/api/jobTrain'
export default {
  name: 'JobTrainList',
  components: {
    Pagination
  },
  data() {
    return {
      stateOptions: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 }
      ],
      sourceOptions: [
        { label: '平台', value: 1 },
        { label: '单位', value: 2 }
      ],
      listQuery: {
        condition: {
          name: '',
          status: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'name', label: '名称', width: '180' },
        { prop: 'courseNum', label: '教程数', width: '30' },
        { prop: 'cycleStr', label: '培训周期', width: '40' },
        { prop: 'totalDuration', label: '总时长', width: '40' },
        { prop: 'orgNum', label: '培训单位数', width: '40' },
        { prop: 'userNum', label: '培训人次', width: '40' },
        { prop: 'statusStr', label: '状态', width: '30' },
        { prop: 'sourceStr', label: '资源来源', width: '40' },
        { prop: 'orgName', label: '单位', width: '70' },
        { prop: 'updateName', label: '更新人', width: '30' },
        { prop: 'updateTime', label: '更新时间', width: '60' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getPackageList()
  },
  methods: {
    toDetail(type, id) {
      this.$router.push({
        name: 'JobTrainDetail',
        query: {
          type,
          id
        }
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getPackageList()
    },
    getPackageList() {
      packageList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.statusStr = this.stateOptions.find(i => i.value === v.status).label
          v.sourceStr = this.sourceOptions.find(i => i.value === v.source).label
          v.cycleStr = v.cycle + '个月'
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getPackageList()
    },
    updateStatus(id, status) {
      this.$confirm(`此操作将${status === 1 ? '启用' : '停用'}该培训计划, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enablePackage({ id, status }).then(() => {
          this.getPackageList()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding-top: 25px;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
