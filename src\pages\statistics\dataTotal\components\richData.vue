<template>
  <div class="richData">
    <rich-data-part
      v-for="(item,index) in data"
      :key="item.count"
      :name="item.name"
      :value="item.count"
      :basics="item.basics"
      :index="index + 1"
    />
  </div>
</template>

<script>
import RichDataPart from './richDataPart.vue'
export default {
  components: {
    RichDataPart
  },
  props: {
    data: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="scss">
.richData {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 160px;
}
</style>
