import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import userRouter from './modules/user' // 用户管理
import exportRouter from './modules/exportResult'
import validManageRouter from './modules/validManage' // 视频管理
import trainRouter from './modules/train' // 考核管理
import operationManageRouter from './modules/operationManage' // 运营管理
import systemManageRouter from './modules/systemManage' // 部门管理
import marketingRouter from './modules/marketing' // 营销中心
import statisticsRouter from './modules/statistics' // 统计分析

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    permission_roles: [{'user:add': true}]  currrent page operation permission
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/statistics/dataTotal',
    component: () => import('@/pages/statistics/dataTotal/index'),
    name: 'StatisticsDataTotal',
    meta: {
      title: '数据总览',
      target: '_blank'
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  },
  userRouter,
  systemManageRouter,
  exportRouter,
  validManageRouter,
  operationManageRouter,
  marketingRouter,
  statisticsRouter,
  // 404 page must be placed at the end !!!
  {
    path: '*',
    redirect: '/404',
    component: Layout,
    hidden: true
  }
]

export const cmsRoutes = [trainRouter]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
