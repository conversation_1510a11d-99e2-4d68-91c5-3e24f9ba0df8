<template>
  <div class="container">
    <el-form ref="formQuestion" :model="formQuestion" label-width="100px" :rules="rules" label-position="right">
      <el-form-item label="题干提示:">
        <div><i class="el-icon-warning-outline" />选项，正确答案，选项组，是根据题干内容定义的填空为准，一个填空对应一个选项</div>
      </el-form-item>
      <el-form-item label="题干:" prop="title">
        <el-input v-model="formQuestion.title" type="textarea" :resize="none" :autosize="autosize" placeholder="请输入题干，题干形式：XXX（1）XXX（2)" @change="contentBlur" />
      </el-form-item>

      <el-form-item label="选项:" prop="optionsList" style="margin-bottom: 0">
        <div v-for="(item, idx) in formQuestion.optionList" :key="idx" class="flex">
          <span>{{ item.index }}.</span>
          <el-input v-model="item.content" type="text" placeholder="请输入选项内容" />
        </div>
      </el-form-item>
      <!-- <el-button plain class="xl-btn" :disabled="addBtn" @click="addOption">
        <i class="el-icon-plus">添加选项</i>
      </el-button> -->

      <el-form-item label="正确答案:">
        <div v-for="(item,o) in formQuestion.gapGroup.answer" :key="o" style="display: flex;justify-content: flex-start;align-items:center">
          <span style="margin-right: 15px">{{ item.num }}.</span>
          <el-radio-group v-model="item.opt">
            <el-radio v-for="(ite, j) in formQuestion.optionList" :key="j" :label="ite.index" @change="radioChang">{{ ite.index }}</el-radio>
          </el-radio-group>
        </div>
      </el-form-item>
      <el-form-item label="选项组:">
        <div><i class="el-icon-warning-outline" />设为选项组的填空，其答案可互换；一个填空只能允许在一个选项组里；同一选项组最少2个填空请勾选填空后，点击添加为选项组进行操作</div>
        <div style="display:flex;justify-content:flex-start;align-items:center">
          <el-checkbox-group v-model="checkList" style="padding-right: 30px">
            <el-checkbox v-for="(item,o) in formQuestion.gapGroup.answer" :key="o" :label="item.num" :disabled="groupDisable[o]" @change="checkChange(item.num,o)">{{ item.num }}</el-checkbox>
          </el-checkbox-group>
          <el-button @click="addCheck"><i class="el-icon-plus" />添加选项组</el-button>
        </div>
        <el-tag v-for="(item,index) in formQuestion.gapGroup.group" :key="index" closable type="" style="margin-right: 20px" @close="handleClose(item,index)">{{ item.join('、') }} </el-tag>
      </el-form-item>

      <el-form-item label="考点关键字:">
        <el-input
          v-model="formQuestion.points"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          :maxlength="300"
          placeholder="请输入考点关键字,多个关键字以逗号分隔"
        />
      </el-form-item>

      <el-form-item label="解析:">
        <el-input
          v-model="formQuestion.desc"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          :maxlength="3000"
          placeholder="请输入试题解析"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Aone',
  props: {
    aData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      title: 'A1',
      none: 'none', // 控制快速录入输入框不能被缩放
      addBtn: false, // 控制添加选项是否禁用
      deleteBtn: false, // 删除选项是否禁用
      groupDisable: [], //
      checkArr: [], // 保存选项组 所选中的选项
      checkList: [],
      check: [],
      autosize: {
        minRows: 6,
        maxRows: 8
      },
      radio: '',
      formQuestion: {
        title: '',
        gapGroup: {
          answer: [
            { num: '', opt: '' }
          ],
          group: []
        },
        optionList: [{ index: '', content: '' }],
        points: '',
        desc: ''
      },
      rules: {
        title: [{ required: true, message: '请输入题干', trigger: 'blur' }],
        optionsList: [
          { required: true, message: '选项内容不能为空', trigger: 'input' }
        ]
      }
    }
  },
  watch: {
    aData: {
      handler(newval, oldvar) {
        if (JSON.stringify(newval) !== '{}') {
          this.$nextTick(() => {
            this.formQuestion = newval
            this.formQuestion.gapGroup.group.forEach(item => {
              item.forEach(v => {
                this.checkList.push(v)
                const idx = Number(v)
                this.groupDisable[idx - 1] = true
              })
            })
          })
        }
      },
      immediate: true
    }
  },
  created() {
    const arr = [
      { index: 'A', content: '' },
      { index: 'B', content: '' },
      { index: 'C', content: '' },
      { index: 'D', content: '' },
      { index: 'E', content: '' }
    ]
    this.formQuestion.optionList = arr
    const array = [
      { num: '1', opt: '' },
      { num: '2', opt: '' },
      { num: '3', opt: '' },
      { num: '4', opt: '' }
    ]
    this.formQuestion.gapGroup.answer = array
  },
  methods: {
    radioChang() {
      this.$forceUpdate()
    },
    checkChange(e, idx) {
      const flag = this.checkArr.includes(e)
      if (!flag) {
        this.checkArr.push(e)
      }
      this.groupDisable[idx] = true
      this.$forceUpdate()
    },
    contentBlur() {
      const regex = /(\（[1-9]\）)|(\uff08\uff09)|(\（[1][0]\）)|(\([1-9]\))|(\([1][0]\))/g
      const arr = this.formQuestion.title.match(regex)
      if (arr === null) return
      if (arr.length > 10) return this.$message('最多10个填空')
      if (arr.length < 2) return this.$message('最少2个填空')
      // 当题干中填空数量大于选项数量，则添加选项
      if (arr.length > this.formQuestion.optionList.length) {
        const arrLength = arr.length - this.formQuestion.optionList.length
        for (let index = 0; index < arrLength; index++) {
          const obj = {
            index: String.fromCharCode(this.formQuestion.optionList.length + 65),
            content: ''
          }
          this.formQuestion.optionList.push(obj)
        }
      }
      // 当题干中填空数量小于选项数量，则删除选项
      if (arr.length < this.formQuestion.optionList.length) {
        this.formQuestion.optionList.splice(arr.length, this.formQuestion.optionList.length - arr.length)
      }
      // 当题干中填空数量大于答案数量，则添加答案
      if (arr.length > this.formQuestion.gapGroup.answer.length) {
        const answerLength = arr.length - this.formQuestion.gapGroup.answer.length
        for (let i = 0; i < answerLength; i++) {
          const obj = {
            num: this.formQuestion.gapGroup.answer.length + i + 1,
            opt: ''
          }
          this.$nextTick(() => {
            this.formQuestion.gapGroup.answer.push(obj)
          })
        }
      }
      // 当题干中填空数量小于答案数量，则删除多余的答案
      if (arr.length < this.formQuestion.gapGroup.answer.length) {
        this.$forceUpdate()
        this.$nextTick(() => {
          this.formQuestion.gapGroup.answer.splice(arr.length, this.formQuestion.gapGroup.answer.length - arr.length)
        })
      }
    },
    // 添加选项
    addOption() {
      if (this.formQuestion.optionList.length > 9) {
        this.$message('最多10个选项')
      } else {
        const obj = {
          index: String.fromCharCode(this.formQuestion.optionList.length + 65),
          content: ''
        }
        this.formQuestion.optionList.push(obj)
      }
    },
    // 删除选项
    btnClick(idx) {
      if (this.formQuestion.optionList.length < 3) {
        this.$message('最少2个选项')
      } else {
        this.formQuestion.optionList.splice(idx, 1)
        for (const key in this.formQuestion.optionList) {
          this.formQuestion.optionList[key].index = this.charCode(Number(key))
        }
      }
    },
    addCheck() {
      if (this.checkArr.length < 1) return this.$message('最少两个选项')
      this.checkArr = this.checkArr.sort((a, b) => {
        return a - b
      })
      this.formQuestion.gapGroup.group.push(this.checkArr)
      this.checkArr = []
    },
    handleClose(item, index) {
      item.forEach(v => {
        this.groupDisable[Number(v) - 1] = false
      })
      this.checkList = this.checkList.filter(val => {
        return item.indexOf(val) === -1
      })
      this.formQuestion.gapGroup.group.splice(index, 1)
    },
    charCode(val) {
      return String.fromCharCode(val + 65)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 60%;
  margin: 20px auto;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    * {
      margin: 0 10px 20px 0;
    }
    .deleteBtn {
      margin-bottom: 0px;
    }
  }
  ol {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: upper-latin;
    li {
      list-style-position: outside;
    }
  }
  .xl-btn {
    width: 90%;
    margin: 0px 0 10px 100px;
    border: 1px dashed #dcdfe6;
  }
  .xl-btn:hover {
    border: 1px dashed #409eff;
  }
}
::v-deep .el-radio-group {
  display: block;
  font-size: 16px;
  line-height: 16px;
}
.container .li:last-child {
  margin-bottom: -20px;
}
.problem {
  border: 1px dashed #dcdfe6;
}
</style>
