<template>
  <section class="promoters">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        placeholder="姓名/手机"
        @change="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.signStatus" placeholder="签约状态" @change="handleFilter()">
        <el-option
          v-for="item in signStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-cascader
        v-model="listQuery.condition.chargeAreaIds"
        :options="areaOptions"
        :props="{
          multiple: true,
          emitPath: false,
          value:'areaId',
          label:'name',
          children:'childList'
        }"
        collapse-tags
        clearable
        placeholder="负责地区"
        @change="handleFilter()"
      />
      <el-select v-model="listQuery.condition.serviceProviderId" clearable placeholder="全部服务商" @change="handleFilter()">
        <el-option
          v-for="item in serverOptions"
          :key="item.serviceOrgId"
          :label="item.serviceName"
          :value="item.serviceOrgId"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.orgId"
        clearable
        placeholder="全部企业"
        @change="getProductList()"
        @clear="productList = []"
      >
        <el-option
          v-for="item in companyOptions"
          :key="item.orgId"
          :label="item.orgName"
          :value="item.orgId"
        />
      </el-select>
      <el-select
        :key="listQuery.condition.orgId"
        v-model="listQuery.condition.productId"
        placeholder="产品"
        @visible-change="hint"
        @change="handleFilter()"
      >
        <el-option
          v-for="item in productList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.platformId"
        placeholder="全部代征平台"
        clearable
        @change="handleFilter()"
      >
        <el-option
          v-for="item in platformTypeList"
          :key="item.desc"
          :label="item.desc"
          :value="item.id"
        />
      </el-select>
    </div>
    <h2>
      推广员列表
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="toPreview(scope.row.userId,1)">平台入驻协议</el-button>
            <el-button type="text" size="mini" @click="toPreview(scope.row.userId,2)">承揽协议</el-button>
            <el-button type="text" size="mini" @click="reSign(scope.row.applyId)">重签协议</el-button>
            <el-button type="text" size="mini" @click="set(scope.row.userId,scope.row.phone,scope.row.serviceProviderOrgId)">设置</el-button>
            <el-button type="text" size="mini" @click="toStatistics(scope.row.userId)">统计分析</el-button>
            <el-button type="text" size="mini" @click="look(scope.row.applyId)">查看</el-button>
            <el-button type="text" size="mini" @click="synchronize(scope.row)">同步至代征</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <select-product :dialog-visible.sync="dialogVisible" :user-id="userId" :area-set="true" :phone="phone" :service-provider-id="serviceProviderId" />
    <info-detail
      :dialog-visible.sync="infoDialogVisible"
      :look="true"
      :info="info"
    />
    <re-sign :id="reSignId" :dialog-visible.sync="reSignDialogVisible" />
    <get-agreement :id="userId" :dialog-visible.sync="getAgreementDialogVisible" />
    <synchronize :user-id="userId" :platform-id-list="platformIdList" :name="serviceOrgName" :dialog-visible.sync="synchronizeDialogVisible" />
  </section>
</template>

<script>
import InfoDetail from './components/infoDetail.vue'
import Pagination from '@/components/Pagination'
import { findOrganListPage } from '@/api/organization'
import { productList, serviceProviderList, platformList } from '@/api/marketing/taskPromote'
import { getAreaTree } from '@/api/area'
import { userList, getLink, auditDeail } from '@/api/marketing/userPromote'
import SelectProduct from './components/selectProduct.vue'
import ReSign from './components/reSign.vue'
import getAgreement from './components/getAgreement.vue'
import synchronize from './components/synchronize.vue'
export default {
  components: {
    Pagination,
    SelectProduct,
    InfoDetail,
    ReSign,
    getAgreement,
    synchronize
  },
  data() {
    return {
      listQuery: {
        condition: {
          chargeAreaIds: [],
          keyword: '',
          promoteType: 'PROMOTER',
          orgId: null,
          productId: null,
          signStatus: null,
          platformId: null,
          serviceProviderId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      signStatusOptions: [
        { label: '全部状态', value: null },
        { label: '未签约', value: 0 },
        { label: '已签约', value: 1 }
      ],
      areaOptions: [],
      serverOptions: [],
      companyOptions: [],
      productList: [],
      platformTypeList: [],
      dialogVisible: false,
      searchProduct: '',
      tableColumn: [
        { prop: 'realName', label: '推广员', width: '170' },
        { prop: 'phone', label: '手机', width: '110' },
        { prop: 'chargeProCityAreas', label: '负责地区', width: '170' },
        { prop: 'serviceProvider', label: '服务商', width: '100' },
        { prop: 'productNames', label: '关联产品', width: '170' },
        { prop: 'signStatus', label: '签约状态', width: '80' },
        { prop: 'auditTime', label: '认证时间', width: '170' },
        { prop: 'signTime', label: '签约时间', width: '170' },
        { prop: 'platform', label: '代征平台', width: '150' }
      ],
      tableData: [],
      userId: null,
      phone: null,
      serviceProviderId: null,
      info: {},
      infoDialogVisible: false,
      reSignDialogVisible: false,
      reSignId: null,
      getAgreementDialogVisible: false,
      platformIdList: [],
      serviceOrgName: '',
      synchronizeDialogVisible: false
    }
  },
  mounted() {
    this.getUserList()
    getAreaTree().then(res => {
      this.areaOptions = res
    })
    this.getOrganListPage()
    serviceProviderList().then(res => {
      this.serverOptions = res
    })
    platformList({ excludeIds: 1 }).then(res => {
      this.platformTypeList = res
    })
  },
  methods: {
    look(id) {
      auditDeail({ id }).then(res => {
        this.info = res
        this.info.id = this.id
        this.infoDialogVisible = true
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getUserList()
    },
    getOrganListPage() {
      const orgListParam = {
        condition: {
          status: 1,
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      findOrganListPage(orgListParam).then(res => {
        this.companyOptions = res.records
      })
    },
    getProductList() {
      if (this.listQuery.condition.orgId) {
        productList({ orgzId: this.listQuery.condition.orgId, pageSize: 1000 }).then(res => {
          res.unshift({ name: '全部产品', id: null })
          this.productList = res
        })
        this.handleFilter()
      }
    },
    hint(v) {
      if (v && !this.listQuery.condition.orgId) {
        this.$message.warning('请先选择企业')
      }
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getUserList()
    },
    getUserList() {
      userList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.signStatus = this.signStatusOptions.find(i => i.value === v.signStatus).label
          v.productNames = v.productNames.join(', ')
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    toPreview(id, type) {
      if (type === 2) {
        this.userId = id
        this.getAgreementDialogVisible = true
        return
      }
      getLink({
        bizId: id,
        bizType: type
      }).then(res => {
        window.open(res)
      })
    },
    set(id, phone, serviceProviderId) {
      this.dialogVisible = true
      this.userId = id
      this.phone = phone
      if (serviceProviderId !== 0) {
        this.serviceProviderId = serviceProviderId
      }
    },
    toStatistics(userId) {
      this.$router.push({
        name: 'MarketingStatisticsPromoterDetail',
        query: {
          userId
        }
      })
    },
    reSign(id) {
      this.reSignDialogVisible = true
      this.reSignId = id
    },
    synchronize(row) {
      this.userId = row.userId
      this.platformIdList = row.platformIdList
      this.serviceOrgName = row.serviceProvider
      this.synchronizeDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.promoters {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input,
    .el-cascader,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
}
</style>
