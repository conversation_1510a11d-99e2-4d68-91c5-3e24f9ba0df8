<template>
  <section class="detail">
    <div class="module">
      <h2>
        抖喇概况
      </h2>
      <ul>
        <li v-for="item in roughly" :key="item.label" class="roughlyLi">
          <p>{{ item.label }}</p>
          {{ item.value }}
        </li>
      </ul>
    </div>
    <div class="module">
      <h2>
        抖喇预览
      </h2>
      <div class="preview">
        <a :href="viewLink" target="_blank">{{ viewLink }}</a>
      </div>
    </div>
    <div class="module">
      <h2>
        点击详情
        <el-button type="primary" @click="exportArticleClickDetailList">导出</el-button>
      </h2>
      <div class="table">
        <el-table
          :data="tableData"
          border
          :header-cell-style="{background:'#f9f9f9',color:'#333'}"
          style="width: 100%"
        >
          <el-table-column
            v-for="item in tableColumn"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
            :width="item.width"
          />
          <el-table-column prop="link" label="点击链接">
            <template slot-scope="scope">
              <a>{{ scope.row.link }}</a>
            </template>
          </el-table-column>
        </el-table>
        <Pagination
          :page="queryParams.pager.page"
          :page-size="queryParams.pager.pageSize"
          :total="total"
          @pagination="handlePagination"
        />
      </div>
    </div>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { articleDetailSurvey, articleClickDetailList, articleClickDetailListExport } from '@/api/marketing/promoteArticle'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      roughly: [],
      viewLink: '',
      tableColumn: [
        { prop: 'name', label: '姓名', width: '140' },
        { prop: 'phone', label: '手机', width: '160' },
        { prop: 'identity', label: '身份', width: '160' },
        { prop: 'major', label: '专科', width: '160' },
        { prop: 'academic', label: '职称', width: '160' },
        { prop: 'area', label: '区域', width: '160' },
        { prop: 'company', label: '单位', width: '230' },
        { prop: 'clickTime', label: '点击时间', width: '160' },
        { prop: 'ip', label: 'IP', width: '160' }
      ],
      tableData: [],
      queryParams: {
        condition: {
          articleId: null,
          articleName: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0
    }
  },
  created() {
    this.queryParams.condition.articleId = this.$route.query.id
    this.getArticleDetailSurvey()
    this.getArticleClickDetailList()
  },
  methods: {
    getArticleDetailSurvey() {
      articleDetailSurvey(this.queryParams.condition).then(res => {
        this.queryParams.condition.articleName = res.title
        this.roughly = [
          { label: '文字内容', value: res.title.length > 25 ? res.title.slice(0, 22) + '...' : res.title },
          { label: '类型', value: res.articleTypeStr },
          { label: '产品', value: res.productName },
          { label: '发布人', value: res.createdName },
          { label: '作者', value: `${res.authorInfoResp.authorName} ${res.authorInfoResp.academic} ${res.authorInfoResp.company} ${res.authorInfoResp.major}` },
          { label: '发布时间', value: res.releaseTime },
          { label: '浏览量', value: res.viewNum },
          { label: '浏览人数', value: res.peopleViewNum },
          { label: '费用', value: res.totalFee }
        ]
        this.viewLink = res.link
      })
    },
    getArticleClickDetailList() {
      articleClickDetailList(this.queryParams).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    exportArticleClickDetailList() {
      articleClickDetailListExport(this.queryParams.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(res => {
        if (res !== null && (res.message === null || res.message === '')) {
          this.$message.error('导出失败')
        }
      })
    },
    handlePagination(v) {
      this.queryParams.pager = v
      this.getArticleClickDetailList()
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  .module {
    margin-bottom: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      font-size: 20px;
      line-height: 55px;
      background-color: #f9f9f9;
      .el-button {
        height: 35px;
      }
    }
    ul {
      display: flex;
      justify-content: space-between;
      padding: 24px;
      font-size: 20px;
      font-weight: bold;
      li {
        max-width: 200px;
        &:nth-last-child(1) {
          margin-right: 0;
        }
        p {
          font-size: 18px;
          color: #666;
          font-weight: 400;
          margin-bottom: 20px;
        }
      }
    }
    .preview {
      height: 125px;
      padding-left: 24px;
      line-height: 125px;
      a {
        color: #409eff;
        font-size: 20px;
        font-weight: bold;
      }
    }
    .table {
      padding: 18px 24px 0;
      .pagination-container {
        padding: 30px 20px;
      }
      a {
        color: #409eff;
        font-size: 14px;
      }
    }
  }
}
.roughlyLi {
  width: 10%;;
}
</style>
