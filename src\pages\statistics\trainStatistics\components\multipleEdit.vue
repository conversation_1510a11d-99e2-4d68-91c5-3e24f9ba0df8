<template>
  <div class="app-container">
    <el-dialog
      title="设置倍数"
      :visible.sync="multipleVisible"
      width="40%"
      center
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
      @open="open"
    >
      <el-form ref="multipleForm" label-width="100px" :model="multipleForm">
        <el-form-item label="区域：">
          <div>{{ multipleForm.areaName }}</div>
        </el-form-item>
        <el-form-item label="用户数：">
          <el-input v-model="multipleForm.staffAmount" oninput="value=value.replace(/[^0-9.]/g,'')" />
        </el-form-item>
        <el-form-item label="活跃数：">
          <el-input v-model="multipleForm.activeAmount" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="培训人数：">
          <el-input v-model="multipleForm.trainingAmount" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="培训任务数：">
          <el-input v-model="multipleForm.trainingTaskAmount" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="培训人次：">
          <el-input v-model="multipleForm.receivedPersonNum" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="参与人次：">
          <el-input v-model="multipleForm.joinedPersonNum" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="通过人次：">
          <el-input v-model="multipleForm.passedPersonNum" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="培训时长：">
          <el-input v-model="multipleForm.examineTime" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item label="自学时长：">
          <el-input v-model="multipleForm.studyTime" oninput="value=value.replace(/[^0-9.]/g,'')" type="number" />
        </el-form-item>
        <el-form-item style="display: flex;justify-content: center;">
          <el-button size="medium" @click="closeForm">取消</el-button>
          <el-button type="primary" size="medium" @click="submitForm">确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  editMultiple,
  multipleDetail
} from '@/api/dataStatistics/statisticsParams'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    multipleId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      multipleVisible: false,
      multipleForm: {
        multipleId: '',
        areaId: '',
        areaName: '',
        staffAmount: '',
        activeAmount: '',
        trainingAmount: '',
        trainingTaskAmount: '',
        receivedPersonNum: '',
        joinedPersonNum: '',
        passedPersonNum: '',
        examineTime: '',
        studyTime: '',
        listOrder: ''
      }
    }
  },
  watch: {
    visible() {
      this.multipleVisible = this.visible
    }
  },
  methods: {
    open() {
      multipleDetail(this.multipleId).then(res => {
        this.multipleForm = JSON.parse(JSON.stringify(res))
      })
    },
    submitForm() {
      editMultiple(this.multipleForm).then(res => {
        const data = JSON.parse(JSON.stringify(res))
        this.multipleVisible = false
        this.$emit('close', data)
      })
    },
    closeForm() {
      this.multipleVisible = false
    },
    close() {
      this.$emit('close', '')
    }
  }
}
</script>
