<template>
  <el-dialog width="800px" top="8vh" title="用户列表" :visible.sync="show" :before-close="beforeClose">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.keyword" style="width:350px;" :value="condition.keyword" placeholder="根据左侧查询方式输入对应关键字" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
          <el-select slot="prepend" v-model="condition.type" style="width:90px">
            <el-option :value="0" label="全部" />
            <el-option :value="1" label="姓名" />
            <el-option :value="2" label="账号" />
          </el-select>
        </el-input>
      </div>
    </div>
    <!-- body -->
    <a-table size="mini" :columns="columns" fit :data="list" border stripe>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="deleteRow(row)">移除</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
  </el-dialog>
</template>

<script>
import request from '@/api/roleCms'
import table from '@/mixins/table'

const columns = [
  { props: { label: 'ID', align: 'center', prop: 'id' }},
  { props: { label: '账号', align: 'center', prop: 'username' }},
  { props: { label: '姓名', align: 'center', prop: 'realname' }},
  { props: { label: '拥有角色', align: 'center', prop: 'rolename' }},
  { props: { label: '修改人', align: 'center', prop: 'uname' }},
  { props: { label: '修改时间', align: 'center', prop: 'utime' }},
  { props: { label: '操作', align: 'center' }, slot: 'actions' }
]

export default {
  name: 'DialogUserList',
  mixins: [table],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    roleId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      request,
      columns,
      condition: {
        keyword: '',
        roleId: '',
        type: 0
      },
      searchType: 'realName',
      initList: false
    }
  },
  watch: {
    show(v) {
      v && (this.getList())
    },
    roleId: {
      handler(v) {
        this.condition.roleId = v
      },
      immediate: true
    }
  },
  methods: {
    getList() {
      const param = {
        condition: this.condition,
        pager: this.pager
      }
      request.roleUser(param).then(res => {
        this.list = res.records
        this.total = res.total || 0
      })
    },
    beforeClose(done) {
      this.$emit('update:show', false)
      this.$nextTick(() => {
        done()
      })
    },
    searchSelectChange(val) {
      this.condition.username = ''
      this.condition.realName = ''
      this.condition.staffUserName = ''
      this.condition.phone = null
    },
    deleteRow(row) {
      this.$confirm('移除用户后，该用户将不再具备该角色权限，请慎重操作', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          request.roleRemove(row.id).then(res => {
            this.$message.success('移除成功')
            this.getList()
          })
        })
        .catch(console.log)
    }
  }
}
</script>
