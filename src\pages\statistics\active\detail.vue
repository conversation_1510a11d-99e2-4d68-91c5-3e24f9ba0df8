<template>
  <section class="active-detail">
    <search :time-select="true" :query="listQuery.condition" @search="search" @clear="clear" />
    <el-table
      :data="tableData"
      border
      :header-cell-style="{background:'#f9f9f9',color:'#333'}"
    >
      <el-table-column
        v-for="item in tableOptions"
        :key="item.prop"
        :prop="item.prop"
        :width="item.width"
        :label="item.label"
        :align="item.align"
      />
    </el-table>
    <Pagination :page-size="listQuery.pager.pageSize" :total="total" :page="listQuery.pager.page" @pagination="pagination" />
  </section>
</template>

<script>
import moment from 'moment'
import Search from '../components/search.vue'
import Pagination from '@/components/Pagination'
import { uvDetail } from '@/api/statistics'
export default {
  components: {
    Search,
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          academicId: 0,
          areaId: '0',
          areaLevel: 4,
          identityId: 0,
          majorId: '',
          majorLevel: null,
          orgName: '',
          phone: '',
          realName: '',
          startTime: '',
          endTime: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableData: [],
      tableOptions: [
        { label: 'UID', align: 'center', prop: 'userId', width: '200px' },
        { label: '姓名', align: 'left', prop: 'username', width: '100px' },
        { label: '手机', align: 'left', prop: 'mobile', width: '110px' },
        { label: '身份', align: 'left', prop: 'identity', width: '100px' },
        { label: '专科', align: 'left', prop: 'major', width: '200px' },
        { label: '职称', align: 'left', prop: 'academic', width: '200px' },
        { label: '所在单位', align: 'left', prop: 'company', width: '' },
        { label: '所在地区', align: 'left', prop: 'area', width: '' },
        { label: '活跃时间', align: 'left', prop: 'eventTime', width: '' },
        { label: 'IP', align: 'left', prop: 'userIp', width: '' },
        { label: '客户端', align: 'left', prop: 'clientType', width: '70' }
      ]
    }
  },
  created() {
    this.listQuery.condition.areaId = this.$route.query.areaId
    this.listQuery.condition.majorId = this.$route.query.majorId
    if (this.$route.query.timeType === 1) {
      this.listQuery.condition.startTime = moment(this.$route.query.date).startOf('year').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      this.listQuery.condition.endTime = moment(this.$route.query.date).endOf('year').endOf('day').format('YYYY-MM-DD HH:mm:ss')
    } else if (this.$route.query.timeType === 2) {
      this.listQuery.condition.startTime = moment(this.$route.query.date).startOf('month').startOf('day').format('YYYY-MM-DD HH:mm:ss')
      this.listQuery.condition.endTime = moment(this.$route.query.date).endOf('month').endOf('day').format('YYYY-MM-DD HH:mm:ss')
    } else {
      this.listQuery.condition.startTime = moment(this.$route.query.date).startOf('day').format('YYYY-MM-DD HH:mm:ss')
      this.listQuery.condition.endTime = moment(this.$route.query.date).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }
    this.getList()
  },
  methods: {
    getList() {
      uvDetail(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    pagination(val) {
      this.listQuery.pager = val
      this.getList()
    },
    search(condition) {
      this.listQuery.pager.page = 1
      this.listQuery.condition = condition
      this.getList()
    },
    clear(condition) {
      this.listQuery = {
        condition: {
          academicId: 0,
          areaId: '0',
          identityId: 0,
          majorId: '',
          orgName: '',
          phone: '',
          realName: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.active-detail {
  padding: 20px 40px;
  .el-table {
    margin-top: 40px;
  }
}
</style>
