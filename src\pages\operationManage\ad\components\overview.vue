<template>
  <div class="overview">
    <h3>广告概况<i class="el-icon-warning" @click="dialogShow = true" />
      <el-button class="fr detail" type="text" @click="viewDetail">明细</el-button>
    </h3>
    <div class="exposure">
      <h4>曝光概况</h4>
      <div class="data-row">
        <div>
          <span>曝光次数</span>
          <span>曝光人数</span>
          <span>人均曝光次数</span>
          <span>曝光率</span>
        </div>
        <div><span v-for="(v,i) in data.exposure" :key="i" class="data-num">{{ v }}</span></div>
      </div>
    </div>
    <div class="click-exposure">
      <h4>点击概况</h4>
      <div class="data-row">
        <div>
          <span>点击次数</span>
          <span>点击人数</span>
          <span>人均点击次数</span>
          <span>点击率</span>
        </div>
        <div><span v-for="(v,i) in data.click" :key="i" class="data-num">{{ v }}</span></div>
      </div>
    </div>

    <el-dialog title="规则说明" :visible.sync="dialogShow" width="40%">
      <div>
        <p>曝光次数：所有用户查看到广告总次数，广告被用户查看</p>
        <p>曝光人数：所有查看到广告的总人数，相同用户只计1次</p>
        <p>人均曝光次数：单个用户平均看到广告的次数，人均曝光次数=曝光次数/曝光人数（保留整数）</p>
        <p>曝光率：曝光人数在总的广告对象人数的占比，曝光率=曝光人数/活动对象总人数*100%（保留两位小数）</p>
        <p>点击次数：所有用户点击广告进入广告详情的总次数</p>
        <p>点击人数：所有点击进入广告详情的总人数，相同用户只计1次</p>
        <p>人均点击次数：单个用户平均点击进入广告详情的次数，人均点击次数=点击次数/点击人数（保留整数）</p>
        <p>点击率：点击人数在总的曝光人数的占比，点击率=点击人数/曝光人数*100%（保留两位小数）</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogShow = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Overview',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    adId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogShow: false
    }
  },
  methods: {
    viewDetail() {
      this.$router.push({
        path: 'statisticsDetail',
        query: {
          id: this.adId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.overview {
  h3 {
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
    i {
      cursor: pointer;
      margin-left: 10px;
      color: #3bb19c;
    }
    .detail{
      margin-top: 6px;
      margin-right: 20px;
    }
  }
  .exposure,
  .click-exposure {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h4 {
      width: 20%;
    }
  }
  .data-row {
    flex: 1;
    margin: 14px 0;
    div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        flex: 1;
        text-align: left;
      }
      .data-num {
        line-height: 1.6;
        font-size: 30px;
      }
    }
  }
}
</style>
