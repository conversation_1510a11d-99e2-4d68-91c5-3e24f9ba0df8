<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">SOP审核列表</header>
    </div>
    <div class="search-column">
      <div class="">
        <div class="search-column__item">
          <div class="search-column__label">更新时间：</div>
          <div class="search-column__inner">
            <el-date-picker
              v-model="dateArray"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleChangeDate"
            />
          </div>
        </div>
        <div class="search-column__item">
          <div class="search-column__label">审核状态：</div>
          <el-select v-model="tableQuery.condition.audit" placeholder="请选择审核状态" clearable @change="handleChangeAudit">
            <el-option
              v-for="item in videoAduitList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-input v-model="tableQuery.condition.sopName" placeholder="请输入搜索sop名称" />
        </div>
        <div class="search-column__item">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchKey()">搜索</el-button>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table :data="sopValidList" border stripe>
      <el-table-column
        v-for="col in tableColumnList"
        :key="col.courseId"
        :prop="col.prop"
        :label="col.label"
        :align="col.align"
      >
        <template slot-scope="scope">
          <template v-if="col.filter === 'introduction'">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ scope.row[col.prop] }}
              </div>
              <span>{{ scope.row[col.prop] | filterFont }}</span>
            </el-tooltip>
          </template>
          <template v-else-if="col.filter === 'audit'">
            <span>{{ scope.row[col.prop] | filterAduitStatus }}</span>
          </template>
          <template v-else-if="col.filter === 'permission'">
            <span>{{ scope.row.extPermission | filterExtPermission }}</span>
          </template>
          <template v-else-if="col.filter === 'cateNameArr'">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ scope.row[col.prop] }}
              </div>
              <span>{{ scope.row[col.prop] | filterFont }}</span>
            </el-tooltip>
          </template>
          <template v-else>
            <span>
              {{ scope.row[col.prop] }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button v-if="[1].includes(row.audit)" type="text" @click="handleValidateCourse(row)">审核</el-button>
          <el-button type="text" @click="handleViewHistory(row)">审核历史</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <!-- dialog history -->
    <el-dialog
      title="审核历史"
      :visible.sync="dialogHistoryVisible"
      width="600px"
      @close="handleCloseDialog()"
    >
      <el-table :data="sopHistoryList" border stripe>
        <el-table-column
          v-for="col in tableHistoryColumnList"
          :key="col.id"
          :prop="col.prop"
          :label="col.label"
          :align="col.align"
        >
          <template slot-scope="scope">
            <template v-if="col.filter === 'audit'">
              {{ scope.row[col.prop] | filterHistoryAduitStatus }}
            </template>
            <template v-else>
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <Pagination class="text-center" :layout="layout" :total="sopHistoryTotal" :page="auditHistoryQuery.pager.page" @pagination="handleHistoryPagination" />
    </el-dialog>

  </div>
</template>

<script>
import { sopList, sopValidateHistroy } from '@/api/validManage'
import Pagination from '@/components/Pagination'
import { filterAduitStatus, filterHistoryAduitStatus, filterExtPermission } from '@/utils'
export default {
  name: 'VideoValid',
  components: {
    Pagination
  },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    },
    filterAduitStatus,
    filterHistoryAduitStatus,
    filterExtPermission
  },
  mixins: [],
  props: {

  },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // 审核历史弹层
      dialogHistoryVisible: false,
      // 时间集合
      dateArray: [],
      // 教程搜索类型
      videoAduitList: [
        // { label: '待提交审核', value: 0 },
        { label: '待审核', value: 1 },
        { label: '审核通过', value: 2 },
        { label: '审核不通过', value: 3 }
      ],
      // 表格表头
      tableColumnList: [
        { id: 0, prop: 'sopId', label: 'ID', align: 'center' },
        { id: 1, prop: 'name', label: 'sop名称', align: 'left' },
        { id: 2, prop: 'cateNameArr', label: '分类', align: 'center', filter: 'cateNameArr' },
        { id: 3, prop: 'introduction', label: 'sop简介', align: 'center', filter: 'introduction' },
        { id: 7, prop: 'author', label: '创建者名称', align: 'center' },
        { id: 7, prop: 'updateTime', label: '提交审核时间', align: 'center' },
        { id: 8, prop: 'audit', label: '审核状态', align: 'center', filter: 'audit' }
      ],
      // 审核列表表头
      tableHistoryColumnList: [
        { id: 0, prop: 'id', label: 'ID', align: 'center' },
        { id: 1, prop: 'name', label: '视频名称', align: 'left' },
        { id: 2, prop: 'author', label: '创建者名称', align: 'center' },
        { id: 3, prop: 'auditName', label: '审核人', align: 'center' },
        { id: 4, prop: 'updateTime', label: '更新时间', align: 'center' },
        { id: 5, prop: 'audit', label: '审核状态', align: 'center', filter: 'audit' }
      ],
      // 请求参数
      tableQuery: {
        condition: {
          audit: null,
          sopName: '',
          endTime: '',
          startTime: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 审核历史
      auditHistoryQuery: {
        condition: {
          courseId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 视频审核列表
      sopValidList: [],
      total: 0,
      // 审核历史列表
      sopHistoryList: [],
      sopHistoryTotal: 0
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.getSopValidList()
  },
  methods: {
    // 时间选择
    handleChangeDate(val) {
      this.tableQuery.condition.startTime = val[0]
      this.tableQuery.condition.endTime = val[1]
      this.tableQuery.pager.page = 1
      this.getSopValidList()
    },
    // 审核状态选择
    handleChangeAudit(val) {
      this.tableQuery.pager.page = 1
      this.getSopValidList()
    },
    // 搜索
    handleSearchKey() {
      this.tableQuery.pager.page = 1
      this.getSopValidList()
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager.page = val.page
      this.getSopValidList()
    },
    // 审核列表分页操作
    handleHistoryPagination(val) {
      this.auditHistoryQuery.pager.page = val.page
      this.getSopValidateHistoryList()
    },
    // sop列表
    getSopValidList() {
      sopList(this.tableQuery).then(res => {
        res.records.forEach(item => {
          item.cateNameArr = item.cateNameArr.join(';')
        })
        this.sopValidList = res.records
        this.total = res.total
      })
    },
    // 审核历史列表
    getSopValidateHistoryList() {
      this.dialogHistoryVisible = true
      sopValidateHistroy(this.auditHistoryQuery).then(res => {
        this.sopHistoryList = res.records
        this.sopHistoryTotal = res.total
      })
    },
    // close
    handleCloseDialog() {
      this.dialogHistoryVisible = false
    },
    // 审核
    handleValidateCourse(row) {
      this.$router.push({
        name: 'SopDetail',
        params: {
          sopInfoId: row.sopInfoId
        }
      })
    },
    // 审核历史
    handleViewHistory(row) {
      this.auditHistoryQuery.condition.sopId = row.sopId
      this.getSopValidateHistoryList()
    }
  }
}
</script>

<style scoped>

</style>
