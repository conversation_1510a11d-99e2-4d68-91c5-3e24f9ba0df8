<template>
  <section class="statistics">
    <div v-show="!isDetail" class="screen">
      <el-date-picker
        v-model="selectData"
        type="monthrange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        value-format="yyyy-MM-dd HH:mm:ss"
        @change="changeDate()"
      />
      <template v-if="activeName !== 'MarketingStatisticsService'">
        <el-select
          v-if="!['MarketingStatisticsServiceDetail','MarketingStatisticsPromotion'].includes(activeName)"
          v-model="serverInfo"
          value-key="serviceOrgId"
          clearable
          placeholder="全部服务商"
          @change="changeServer"
        >
          <el-option
            v-for="item in serverOptions"
            :key="item.serviceOrgId"
            :label="item.serviceName"
            :value="item"
          />
        </el-select>
        <el-select v-model="searchParam.condition.orgId" clearable placeholder="全部企业" @change="changeOrg()">
          <el-option
            v-for="item in companyOptions"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="searchParam.condition.productId" clearable placeholder="全部产品">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select
          v-if="activeName === 'MarketingStatisticsVisit'"
          v-model="searchParam.condition.visitType"
          clearable
          placeholder="拜访类型"
        >
          <el-option
            v-for="item in visitTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-if="['MarketingStatisticsDoula','MarketingStatisticsPromotion'].includes(activeName)"
          v-model="searchParam.condition.type"
          :clearable="['MarketingStatisticsPromotion'].includes(activeName)"
          placeholder="全部类型"
        >
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-cascader
          v-if="['MarketingStatisticsArticle','MarketingStatisticsVideo','MarketingStatisticsTotal'].includes(activeName)"
          v-model="selectCategoryId"
          :props="categoryProps"
          clearable
          placeholder="全部分类"
          @change="changeCategory"
        />
      </template>
      <el-input
        v-if="['MarketingStatisticsArticle','MarketingStatisticsVideo', 'MarketingStatisticsDoula', 'MarketingStatisticsPromotion'].includes(activeName)"
        v-model="keyword"
        :placeholder="searchPlaceholder"
        clearable
        style="width: 350px;"
        @keyup.enter.native="keywordChange()"
        @clear="clearSelect()"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer" @click="keywordChange()" />
        <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段">
          <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-input>
      <el-input
        v-if="['MarketingStatisticsCreator','MarketingStatisticsPromoter','MarketingStatisticsVisit', 'MarketingStatisticsService'].includes(activeName)"
        v-model="keyword"
        style="width: 350px;"
        :placeholder="activeName === 'MarketingStatisticsVisit' ? '推广员/客户' : (activeName === 'MarketingStatisticsService' ? '名称' : '姓名/手机号')"
        clearable
        @keyup.enter.native="keywordChange()"
        @clear="clearSelect()"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer" @click="keywordChange()" />
      </el-input>
    </div>
    <router-view :search-param.sync="searchParam" />
  </section>
</template>

<script>
import { findOrganListPage } from '@/api/organization'
import { productList, serviceProviderList } from '@/api/marketing/taskPromote'
import { listByParentId } from '@/api/category'
import moment from 'moment'
import { isEmptyStr } from '@/utils'
export default {
  data() {
    return {
      activeName: this.$route.name,
      isDetail: !!this.$route.query.id,
      keyWordPlaceholder: '',
      keyword: null,
      selectData: [
        moment(moment(moment().endOf('month')).subtract(1, 'years')).format('YYYY-MM-DD HH:mm:ss'),
        moment(moment().endOf('month')).format('YYYY-MM-DD HH:mm:ss')
      ],
      serverOptions: [],
      serverInfo: {},
      companyOptions: [],
      productList: [],
      searchPlaceholder: '发布人姓名',
      searchType: 'taskUserName',
      searchTypeList: [
        { label: '发布人', value: 'taskUserName' },
        { label: '作者', value: 'authorName' },
        { label: '文章标题', value: 'keyword' }
      ],
      typeList: [
        { label: '全部类型', value: 'DOULA' },
        { label: '图文', value: 'DOULA_ARTICLE' },
        { label: '短视频', value: 'DOULA_VIDEO' }
      ],
      visitTypeList: [
        { label: '医院拜访', value: 'HOSPITAL' },
        { label: '药店拜访', value: 'DRUGSTORE' },
        { label: '商业拜访', value: 'BIZ' }
      ],
      orgListParam: {
        condition: {
          status: 1,
          type: 2002,
          serviceProviderOrgId: null
        },
        pager: {
          page: 1,
          pageSize: 100
        }
      },
      searchParam: {
        condition: {
          startTime: null,
          endTime: null,
          orgzIds: [],
          serviceProviderOrgId: null,
          orgId: null,
          productId: null,
          categoryId: null,
          authorName: null,
          taskUserName: null,
          keyword: null,
          visitType: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      selectCategoryId: null,
      categoryProps: {
        lazy: true,
        checkStrictly: true,
        lazyLoad: (node, resolve) => {
          // node 节点数据 node.value => 当前节点的值
          // level: 层级 => 1,2,3,4
          const { level } = node
          // 动态节点
          const nodes = []
          // 为0代表第一次请求
          const parentId = level === 0 ? 479 : node.value
          this.getCategoryList(parentId).then((res) => {
            // 节点数组
            res.map((item) => {
              // obj里的键值是官方要求的
              const obj = {
                value: item.categoryId,
                label: item.name,
                leaf: node.level >= 2
              }
              nodes.push(obj)
            })
            // resolve 节点返回
            resolve(nodes)
          })
            .catch((error) => {
              console.log(error)
            })
        }
      },
      candidateList: []
    }
  },
  watch: {
    searchType(newVal) {
      if (newVal === 'taskUserName') {
        this.searchPlaceholder = '发布人姓名'
      } else if (newVal === 'authorName') {
        this.searchPlaceholder = '作者姓名'
      } else {
        this.searchPlaceholder = this.$route.name === 'MarketingStatisticsArticle' ? '文章标题' : (this.$route.name === 'MarketingStatisticsPromotion' ? '标题/文字' : '视频标题')
      }
      this.keyword = ''
    }
  },
  created() {
    // // 年月选择器默认选中近一年
    // this.selectData = [
    //   moment(moment(moment().endOf('month')).subtract(1, 'years')).format('YYYY-MM-DD HH:mm:ss'),
    //   moment(moment().endOf('month')).format('YYYY-MM-DD HH:mm:ss')
    // ]
    this.changeDate()
    this.activeName = this.$route.name
    this.isDetail = Object.keys(this.$route.query).length >= 1
    this.getOrganListPage(this.orgListParam)
    this.changeOrg()
    this.getServiceList()
    if (this.activeName === 'MarketingStatisticsArticle') {
      this.searchTypeList[2].label = '文章标题'
    } else if (this.activeName === 'MarketingStatisticsVideo') {
      this.searchTypeList[2].label = '视频标题'
    } else if (this.activeName === 'MarketingStatisticsPromotion') {
      this.searchTypeList[2].label = '标题/文字'
      this.typeList = [
        { label: '文章', value: 'ARTICLE' },
        { label: '视频', value: 'VIDEO' },
        { label: '抖喇图文', value: 'DOULA_ARTICLE' },
        { label: '抖喇短视频', value: 'DOULA_VIDEO' }
      ]
    } else {
      this.searchTypeList[2].label = '文字内容'
    }
  },
  methods: {
    getServiceList() {
      serviceProviderList().then(res => {
        this.serverOptions = res
      })
    },
    getOrganListPage(orgListParam) {
      findOrganListPage(orgListParam).then(res => {
        this.companyOptions = res.records
      })
    },
    changeServer(v) {
      this.searchParam.condition.orgId = ''
      this.searchParam.condition.serviceProviderOrgId = v.serviceOrgId || ''
      this.orgListParam.condition.serviceProviderOrgId = v.serviceOrgId || ''
      this.getOrganListPage(this.orgListParam)
    },
    changeOrg() {
      const param = {}
      if (this.searchParam.condition.orgId !== '' && this.searchParam.condition.orgId !== null) {
        param.orgzId = this.searchParam.condition.orgId
      }
      param.page = 1
      param.pageSize = 100
      this.searchParam.condition.productId = null
      productList(param).then(res => {
        this.productList = res
      })
    },
    getCategoryList(parentId) {
      return listByParentId(parentId)
    },
    changeDate() {
      this.searchParam.condition.startTime = ''
      this.searchParam.condition.endTime = ''
      if (this.selectData !== null && this.selectData !== undefined && this.selectData !== '') {
        this.searchParam.condition.startTime = moment(this.selectData[0]).startOf('month').format('YYYY-MM-DD HH:mm:ss')
        this.searchParam.condition.endTime = moment(this.selectData[1]).endOf('month').format('YYYY-MM-DD HH:mm:ss')
      }
    },
    clearSelect() {
      this.keyword = null
      this.searchParam.condition.taskUserName = ''
      this.searchParam.condition.authorName = ''
      this.searchParam.condition.keyword = ''
      document.activeElement.blur()
    },
    changeCategory() {
      this.searchParam.condition.categoryId = this.selectCategoryId[0] || ''
    },
    keywordChange() {
      this.searchParam.condition.taskUserName = ''
      this.searchParam.condition.authorName = ''
      this.searchParam.condition.keyword = ''
      this.searchParam.pager.page = 1
      if (['MarketingStatisticsCreator', 'MarketingStatisticsPromoter', 'MarketingStatisticsVisit', 'MarketingStatisticsService'].includes(this.activeName)) {
        this.searchParam.condition.keyword = this.keyword
      } else {
        if (!isEmptyStr(this.keyword)) {
          this.searchParam.condition[this.searchType] = this.keyword
        } else {
          this.$message({
            message: '请先输入搜索关键词',
            type: 'warning'
          })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.statistics {
  padding: 1px 0;
  background: #eaeaee;
  .screen {
    margin: 14px auto;
    .el-input,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  ::v-deep .pagination-container {
    margin: 0 auto;
  }
}
// 修改下拉列表的宽度，使得其可以自动适应内容。
::v-deep.el-autocomplete .el-popper{
    width: auto !important;
}
</style>
