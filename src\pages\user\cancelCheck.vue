<template>
  <div class="app-container">
    <div class="content-container">
      <el-row class="search-column">
        <div class="search-column__item">

          <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable @clear="getList()" @keydown.enter.native="getList()">
            <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="getList()" />
            <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="searchSelectChange()">
              <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-input>
        </div>
        <div class="search-column__item">
          <el-select v-model="condition.isHire" placeholder="是否单位用户" clearable @change="getList">
            <el-option
              v-for="item in isHireOpt"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-select v-model="condition.isMember" placeholder="是否个人VIP会员" clearable @change="getList">
            <el-option
              v-for="item in isMemberOpt"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-select v-model="condition.auditStatus" placeholder="审核状态" clearable @change="getList">
            <el-option
              v-for="item in auditStatusOpt"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item fr">
          <el-button type="primary" @click="reset">重置</el-button>
        </div>
      </el-row>
      <el-row class="mt-15">
        <el-table v-loading="loading" :data="tableData" tooltip-effect="dark" :header-cell-style="{background:'#ECF0F1'}">
          <el-table-column label="用户姓名" align="center" prop="realName" />
          <el-table-column label="用户账号" align="center" prop="username" />
          <el-table-column label="安全手机" align="center" prop="phone" />
          <el-table-column label="是否单位用户" align="center" prop="isHire">
            <template slot-scope="{ row }">
              <span>{{ row.isHire | isHireFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否个人VIP会员" align="center" prop="isMember">
            <template slot-scope="{ row }">
              <span>{{ row.isMember | isMemberFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" align="center" prop="auditStatus">
            <template slot-scope="{ row }">
              <span>{{ row.auditStatus | auditStatusFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核人" align="center" prop="auditUid">
            <template slot-scope="{ row }">
              <span>{{ row.auditUid==0? '':row.auditUid }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核时间" align="center" prop="auditTime" />
          <el-table-column label="申请时间" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" width="180">
            <template slot-scope="{ row }">
              <!-- 只有待审核状态(auditStatus=0)才显示操作按钮 -->
              <template v-if="row.auditStatus === 0">
                <el-button type="primary" size="mini" @click="handlePass(row)">通过</el-button>
                <el-button type="danger" size="mini" @click="handleReject(row)">驳回</el-button>
              </template>
              <template v-else>
                <span class="text-muted">已处理</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="pagination" />

    <!-- 驳回说明弹窗 -->
    <el-dialog title="驳回说明" :visible.sync="rejectDialogVisible" width="500px" :close-on-click-modal="false">
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="80px">
        <el-form-item label="驳回说明" prop="remark">
          <el-input
            v-model="rejectForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入驳回说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmReject">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCancleCheckList, cancleCheck } from '@/api/adminRule'
import Pagination from '@/components/Pagination'

export default {
  name: 'AdminRule',
  components: { Pagination },
  filters: {
    isHireFilter(val) {
      const arr = ['否', '是']
      return arr[val]
    },
    isMemberFilter(val) {
      const arr = ['否', '是']
      return arr[val]
    },
    auditStatusFilter(val) {
      const arr = ['待审核', '审核通过', '审核不通过']
      return arr[val]
    }
  },
  data() {
    return {
      keyword: '',
      searchType: 'realName',
      searchTypeList: [
        { label: '用户姓名', value: 'realName' },
        { label: '用户账号', value: 'username' },
        { label: '安全手机', value: 'phone' }
      ],
      condition: {
        realName: '',
        username: '',
        phone: '',
        isHire: '',
        isMember: '',
        auditStatus: ''
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      total: 0,
      tableData: [],
      isHireOpt: [
        { value: 1, name: '是' },
        { value: 0, name: '否' }
      ],
      isMemberOpt: [
        { value: 1, name: '是' },
        { value: 0, name: '否' }
      ],
      auditStatusOpt: [
        { value: 0, name: '待审核' },
        { value: 1, name: '审核通过' },
        { value: 2, name: '审核不通过' }
      ],
      loading: false,
      layout: 'total, prev, pager, next, jumper', // 默认分页样式
      // 驳回弹窗相关数据
      rejectDialogVisible: false,
      rejectForm: {
        remark: ''
      },
      rejectRules: {
        remark: [
          { required: true, message: '请输入驳回说明', trigger: 'blur' },
          { min: 1, max: 200, message: '驳回说明长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      },
      currentRejectRow: null // 当前要驳回的行数据
    }
  },
  created() {
    this.getList()
  },
  methods: {
    searchSelectChange(val) {
      this.condition.realName = ''
      this.condition.username = ''
      this.condition.phone = ''
      this.condition[this.searchType] = this.keyword.replace(/\s*/g, '')
    },
    getList() {
      this.searchSelectChange()
      this.pager.page = 1
      this.loading = true
      const query = {}
      query.condition = this.condition
      query.pager = this.pager
      getCancleCheckList(query).then(res => {
        this.tableData = res.records
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    reset() {
      this.keyword = ''
      this.condition.isHire = ''
      this.condition.isMember = ''
      this.condition.auditStatus = ''
      this.getList()
    },
    pagination(pager) {
      this.pager = pager

      this.loading = true
      const query = {}
      query.condition = this.condition
      query.pager = this.pager
      getCancleCheckList(query).then(res => {
        this.tableData = res.records
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    // 处理通过审核
    handlePass(row) {
      this.$confirm(`确认通过用户"${row.realName}"的注销申请吗？`, '确认通过', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用审核接口，auditStatus=1表示审核通过
        const params = {
          auditStatus: 1,
          recordId: row.recordId
        }
        cancleCheck(params).then(res => {
          this.$message.success('审核通过成功')
          this.getList() // 刷新列表
        }).catch(err => {
          this.$message.error(err.message || '审核失败')
        })
      })
    },
    // 处理驳回审核
    handleReject(row) {
      this.currentRejectRow = row
      this.rejectForm.remark = ''
      this.rejectDialogVisible = true
      // 清除表单验证状态
      this.$nextTick(() => {
        this.$refs.rejectForm && this.$refs.rejectForm.clearValidate()
      })
    },
    // 确认驳回
    confirmReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          const params = {
            auditStatus: 2, // 2表示审核不通过
            recordId: this.currentRejectRow.recordId,
            remark: this.rejectForm.remark
          }
          cancleCheck(params).then(res => {
            this.$message.success('驳回成功')
            this.rejectDialogVisible = false
            this.getList() // 刷新列表
          }).catch(err => {
            this.$message.error(err.message || '驳回失败')
          })
        }
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.text-muted {
  color: #999;
  font-size: 12px;
}

.el-button + .el-button {
  margin-left: 8px;
}
</style>

