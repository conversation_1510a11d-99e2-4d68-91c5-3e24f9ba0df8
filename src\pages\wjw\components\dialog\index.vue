<template>
  <div class="app-container">

    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :show="show"
      width="800px"
      @close="close()"
    >

      <!-- chose box -->
      <div class="dialog-content">
        <div class="content-l">
          <el-input v-model="defaultQuery.condition.keyword" v-search="searckOption" placeholder="输入相关单位名称搜索" clearable>
            <i slot="suffix" class="el-input__icon el-icon-search" />
          </el-input>
          <el-tabs
            v-model="unitType"
            @tab-click="handleTabsClick"
          >
            <el-tab-pane
              v-for="item in unitTypeList"
              :key="item.key"
              :label="item.label"
              :name="item.id"
            />
          </el-tabs>

          <el-table :data="unitList" border stripe height="400px">
            <el-table-column
              v-for="col in unitHeadList"
              :key="col.id"
              :prop="col.prop"
              :label="col.label"
              :align="col.align"
            />
            <el-table-column width="80px" label="操作" align="center">
              <template slot-scope="{row}">
                <el-button :type="row.selected?'info':'primary'" size="mini" @click="handleChose(row)">{{ row.selected?'已选':'选择' }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <Pagination class="pagination-extra" :total="total" :page="defaultQuery.pager.page" :pager-count="3" :page-sizes="defaultQuery.pager.pagesize" :layout="paginationLayout" @pagination="handlePagination" />
        </div>
        <div class="content-r">
          <div class="mg-b">已选择的单位：</div>
          <div class="choose-box">
            <el-tooltip v-for="item in selectedItemList" :key="item.orgId" class="tag-mg" :content="item.orgName" placement="top">
              <el-tag closable @close="closeSelectedItem(item)">{{ item.orgName | filtersFont }}</el-tag>
            </el-tooltip>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" size="medium" @click="handleSaveForm">确 定</el-button>
      </span>
    </el-dialog>
    <DialogBindAccount :data="selectedItemList" :type="accountType" :select-org="selectOrgName" :title="bindAccountTitle" :show.sync="dialogBindAccountVisible" @handleSave="handleSave" />
  </div>
</template>

<script>
import DialogBindAccount from './bindingAccount'
import Pagination from '@/components/Pagination'
import { getWjwSubList, getHospitalSubList, addWjwSubOrg } from '@/api/wjw'
export default {
  name: 'WjwDialog',
  components: {
    DialogBindAccount,
    Pagination
  },
  filters: {
    // 字长控制
    filtersFont(val) {
      if (val.length > 8) { return val.slice(0, 8) + '...' }
      return val
    }
  },
  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '添加子机构'
    },
    selectOrg: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 提交内容
      selectOrgName: '',
      selectOrgItem: {},
      // 查询下级列表参数
      defaultQuery: {
        condition: {
          keyword: '',
          level: 0
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 10,
      paginationLayout: 'total, prev, pager, next, jumper',
      searckKeyword: '',
      searckOption: {
        fn: this.getSubListByType
      },
      unitList: [],
      unitHeadList: [
        { label: '单位名称', align: 'left', prop: 'orgName', id: 'orgId' }
      ],
      unitType: '1',
      unitTypeList: [
        { key: 1, label: '医院', id: '1' },
        { key: 2, label: '卫健委', id: '2' }
      ],
      levelList: [],
      selectedItemList: [],
      visible: this.show,
      dialogBindAccountVisible: false,
      bindAccountTitle: '',
      accountType: 1 // type: 1(医院) 2(卫计委)
    }
  },
  computed: {

  },
  watch: {
    show() {
      this.visible = this.show
      this.getSubListByType()
    },
    selectOrg: {
      handler: function(val) {
        if (val.level !== undefined) {
          this.defaultQuery.condition.level = val.level
          this.getSubListByType()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 过滤已选中的内容
    filtersSelectItem(arr, remoteArr) {
      arr.forEach(v => {
        remoteArr.forEach(rv => {
          if (v.orgId === rv.orgId) {
            v.selected = true
          }
        })
      })
    },

    // 剔除未选中
    removeSelectedItem(row) {
      this.selectedItemList = this.selectedItemList.filter(v => {
        return v.orgId !== row.orgId
      })
    },

    // 复原请求中选中状态
    resetSelectedItem(row) {
      this.unitList.forEach(v => {
        if (v.orgId === row.orgId) {
          v.selected = false
        }
      })
    },

    // 根据type类型获取下级列表
    getSubListByType() {
      const accountType = this.accountType
      if (accountType === 1) {
        this.getHospitalSubList()
      } else if (accountType === 2) {
        this.getWjwSubList()
      }
    },

    // 卫计委下级列表
    getWjwSubList() {
      getWjwSubList(this.defaultQuery).then(res => {
        // 伪选中状态
        res.records.forEach(v => {
          v.selected = false
        })
        if (this.selectedItemList.length) {
          this.filtersSelectItem(res.records, this.selectedItemList)
        }
        this.unitList = res.records
        this.total = res.total
      })
    },

    // 医院下级列表
    getHospitalSubList() {
      getHospitalSubList(this.defaultQuery).then(res => {
        // 伪选中状态
        res.records.forEach(v => {
          v.selected = false
        })
        if (this.selectedItemList.length) {
          this.filtersSelectItem(res.records, this.selectedItemList)
        }
        this.unitList = res.records
        this.total = res.total
      })
    },

    handleTabsClick(val) {
      this.accountType = Number(val.name)
      this.getSubListByType()
    },

    // 选中下级
    handleChose(row) {
      row.selected = !row.selected
      if (row.selected) {
        this.selectedItemList.push({ orgId: row.orgId, orgName: row.orgName, level: row.level, parentOrgId: row.parentOrgId, parentOrgIds: row.parentOrgIds })
      } else {
        this.removeSelectedItem(row)
      }
    },

    closeSelectedItem(row) {
      // 剔除未选中
      this.removeSelectedItem(row)
      this.resetSelectedItem(row)
    },

    handleCloseDialog() {
      this.$emit('handleCancel')
    },

    // 清空查询参数
    resetQuery() {
      this.defaultQuery = {
        condition: {
          keyword: '',
          level: 0
        },
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    },

    close() {
      this.resetQuery()
      this.selectedItemList.forEach(v => {
        this.resetSelectedItem(v)
      })
      this.selectedItemList = []
      this.$emit('update:show', false)
    },

    handleSaveForm() {
      this.selectOrgName = this.selectOrg.orgName
      this.selectOrgItem = this.selectOrg
      if (this.selectedItemList.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择至少一个单位！'
        })
        return
      }
      this.dialogBindAccountVisible = true
    },

    // 分页
    handlePagination(val) {
      this.defaultQuery.pager.page = val.page
      this.getSubListByType()
    },

    // 提交
    handleSave() {
      const subOrgData = {
        orgId: this.selectOrgItem.orgId,
        parentOrgIds: this.selectOrgItem.parentOrgIds,
        subList: this.selectedItemList
      }
      // 添加下级单位
      addWjwSubOrg(subOrgData).then(res => {
        this.$message.success('添加下级单位成功！')
        this.getSubListByType()
        this.dialogBindAccountVisible = false
        this.$emit('handleAddSuccess')
        this.$emit('update:show', false)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .dialog-content{
    display: flex;
    height: 500px;
    .content-l{
      max-width: 500px;
      padding: 10px;
      border-right: 1px solid #ccc;
      flex: 2;
    }
    .content-r{
      padding: 10px;
      flex: 1;
    }
  }
  .choose-box{
    height: 455px;
    overflow: auto;
  }
  .pagination-extra{
    margin: 0;
    padding: 20px 16px;
    text-align: center;
  }
  .tag-mg{
    margin: 0 5px 5px 0;
  }
</style>
