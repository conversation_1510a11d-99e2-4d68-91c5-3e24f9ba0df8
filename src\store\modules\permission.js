import { constantRoutes } from '@/router'

export function generateUserRoutes(remoteRoutes, localRoutes) {
  const newArray = []
  if (localRoutes === undefined) {
    return newArray
  }
  remoteRoutes.forEach((rv) => {
    if (rv.url !== '') {
      newArray.push(rv)
      return
    }
    localRoutes.some((lv) => {
      // filter routes
      if (rv.name === lv.name && !lv.hidden) {
        newArray.push(lv)
        if (rv.children && rv.children.length) {
          const rvc = rv.children
          const lvc = lv.children
          newArray[newArray.length - 1].children = generateUserRoutes(rvc, lvc)
        } else {
          delete newArray[newArray.length - 1].children
        }
        return true
      }
    })
  })
  localRoutes.forEach((lv) => {
    // hidden on slidder
    if (lv.local) {
      newArray.push(lv)
    }
  })
  return newArray
  // return localRoutes
}

/**
 * 替换path
 */
function replacePath(routes, url) {
  routes.path = url
  delete routes.component
  return routes
}

const state = {
  routes: [],
  roles: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = routes
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  REMOVE_ROLES: (state) => {
    state.roles = []
  }
}

const actions = {
  generateRoutes({ commit }, menu) {
    return new Promise(resolve => {
      // 跟远程做匹配之后生成的路由
      const accessedRoutes = generateUserRoutes(menu, constantRoutes)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },
  setPageRoles({ commit }, roles) {
    return new Promise(resolve => {
      commit('SET_ROLES', roles)
      resolve(roles)
    })
  },
  removePageRoles({ commit }) {
    commit('REMOVE_ROLES')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
