import request from '@/utils/request'

/**
 * 活动相关
 */

export default {
  // 视频播放
  getVideo(param) {
    return request({
      url: `/video/play/${param}`,
      method: 'get'
    })
  },
  // 预创建活动
  preAdd(param) {
    return request({
      url: `/activity/pre/add`,
      method: 'post',
      data: param
    })
  },
  // 创建活动
  create(param) {
    return request({
      url: `/activity/create`,
      method: 'post',
      data: param
    })
  },
  // 编辑活动
  edit(param) {
    return request({
      url: `/activity/edit`,
      method: 'post',
      data: param
    })
  },
  // 删除活动
  del(param) {
    return request({
      url: `/activity/del/${param}`,
      method: 'post'
    })
  },
  // 终止活动
  stop(param) {
    return request({
      url: `/activity/terminate/${param}`,
      method: 'post'
    })
  },
  // 活动列表
  list(param) {
    return request({
      url: `/activity/list`,
      method: 'post',
      data: param
    })
  },
  // 活动详情
  detail(param) {
    return request({
      url: `/activity/detail/${param}`,
      method: 'post'
    })
  },
  // 活动概况
  actOverview(param) {
    return request({
      url: `/statistics/act/actOverview/${param}`,
      method: 'get'
    })
  },
  // 活动排名
  rankList(params) {
    return request({
      url: `/statistics/act/actRank`,
      method: 'post',
      data: params
    })
  },
  // 活动趋势
  actTrend(param) {
    return request({
      url: `/statistics/act/actTrend/${param}`,
      method: 'get'
    })
  },
  // 活动统计明细
  userRank(param) {
    return request({
      url: `/statistics/act/actUserRank`,
      method: 'post',
      data: param
    })
  },
  // 活动参与明细
  actUserJoin(params) {
    return request({
      url: `/statistics/act/actUserJoin/${params.actId}/${params.userId}`,
      method: 'get',
      params
    })
  },
  // 活动答题闲情
  actExamePaper(param) {
    return request({
      url: `/statistics/act/actExamePaper/${param}`,
      method: 'get'
    })
  },
  // 活动排名导出
  actExport() {
    return process.env.VUE_APP_BASE_API + `/statistics/act/actRankExport`
  },
  // 活动统计明细导出
  actUserRankExport() {
    return process.env.VUE_APP_BASE_API + `/statistics/act/actUserRankExport`
  }
}
