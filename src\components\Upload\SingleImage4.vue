<template>
  <el-upload
    ref="uploadFile"
    :class="{'hide':hide}"
    :style="cssVars"
    list-type="picture-card"
    :limit="1"
    :multiple="false"
    :action="uploadFileApi"
    :data="uploadData"
    :file-list="fileList"
    :before-upload="beforeUpload"
    :on-success="uploadSuccess"
    :on-remove="removePic"
    :disabled="disabled"
  >
    <span slot="default" class="uploadStyle">
      <i class="el-icon-plus" />
      <span>{{ tips }}</span>
    </span>
  </el-upload>
</template>

<script>
// readme
// 图片上传组件
// 封装特点：上传一张图后自动隐藏上传框
// tips为图标下方文字
// 监听函数picIdChange可获取上传图片的id
import { preUploadApi, uploadFileApi } from '@/api/biz'
import { getToken } from '@/utils/auth'

export default {
  props: {
    tips: {
      type: String,
      default: '点击上传'
    },
    url: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '148px'
    },
    height: {
      type: String,
      default: '148px'
    }
  },
  data() {
    return {
      uploadFileApi,
      uploadData: {
        data: ''
      },
      uploadHeaders: {
        token: getToken()
      },
      hide: false,
      fileList: []
    }
  },
  computed: {
    cssVars() {
      return {
        '--width': this.width,
        '--height': this.height
      }
    }
  },
  watch: {
    url: {
      handler(value) {
        if (value) {
          this.fileList = [{ url: value }]
          this.hide = true
        }
      },
      immediate: true
    }
  },
  methods: {
    uploadSuccess(val) {
      this.hide = true
      if (val.data && val.data.id) {
        this.$emit('update:picId', Number(val.data.id))
      }
      this.$emit('imgValidate')
    },
    removePic() {
      this.hide = false
      this.$emit('update:picId', '')
    },
    async beforeUpload(val) {
      this.hide = true
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      if (!JPG && !PNG && !JPEG) {
        this.hide = false
        const uid = val.uid // 关键作用代码，去除文件列表失败文件
        const index = this.$refs.uploadFile.uploadFiles.findIndex(item => item.uid === uid) // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）
        this.$refs.uploadFile.uploadFiles.splice(index, 1) // 关键作用代码，去除文件列表失败文件
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return false
      } else {
        const param = {
          filename: val.name,
          size: val.size,
          type: val.type
        }
        const res = await preUploadApi(param)
        this.uploadData.data = res
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hide ::v-deep .el-upload--picture-card {
    display: none;
}
::v-deep .el-upload, ::v-deep .el-upload-list__item{
    width: var(--width);
    height: var(--height)
  }
.uploadStyle{
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  span{
    color: #8c939d;
    line-height: 2;
  }
}
</style>
