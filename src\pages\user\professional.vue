<template>
  <div class="app-container">
    <div class="search-column" style="display:flex">
      <div class="search-column__item" style="margin-right:10px;">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable @clear="init()" @keydown.enter.native="init()">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="init()" />
          <el-select slot="prepend" v-model="searchType" style="width: 140px" placeholder="请选择搜索的字段" @change="searchSelectChange()">
            <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <Search @search="search" @clear="clear" />
      <div class="search-column__item fr" style="margin-left:15px;">
        <el-button type="primary" @click="handleAdd">添加</el-button>
      </div>
    </div>

    <el-table :data="professionalList" border stripe>
      <!-- <el-table-column type="selection" fixed="left" /> -->
      <el-table-column v-for="col in tableColumnList" :key="col.userId" v-bind="col">
        <template slot-scope="scope">
          <template>
            <span>
              {{ scope.row[col.prop] }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="100">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="look(row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <info-detail
      :dialog-visible.sync="infoDialogVisible"
      :look="true"
      :info="info"
    />

    <Pagination class="text-center" :limit="tableQuery.limit" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { majorUserList } from '@/api/professional'
import { auditDeail } from '@/api/marketing/userPromote'
import Search from '@/pages/operationManage/live/components/search_1.vue'
import InfoDetail from './components/infoDetail.vue'

export default {
  name: 'Specialist',
  components: { Pagination, Search, InfoDetail },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  data() {
    return {
      keyword: '',
      searchType: 'keyword',
      searchTypeList: [
        { label: '姓名/手机', value: 'keyword' },
        { label: '单位', value: 'company' },
        { label: '科室', value: 'department' }
      ],
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 3, label: '姓名', align: 'center', prop: 'realName' },
        { id: 3, label: '手机', align: 'center', prop: 'phone' },
        { id: 4, label: '身份', align: 'center', prop: 'identityName' },
        { id: 4, label: '专科', align: 'center', prop: 'majorName' },
        { id: 5, label: '职称', align: 'center', prop: 'academicName' },
        { id: 6, label: '工作单位', align: 'center', prop: 'company' },
        { id: 7, label: '部门/科室', align: 'center', prop: 'department' },
        { id: 7, label: '所在地区', align: 'center', prop: 'proCityArea' },
        { id: 10, label: '认证时间', align: 'center', prop: 'auditTime' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {
          keyword: '',
          department: '',
          company: '',
          identityIds: [],
          academicClassIds: []
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      professionalList: [], // 专家列表
      disabled: false,
      info: {},
      infoDialogVisible: false
    }
  },
  created() {
    this.init()
  },
  methods: {
    look(id) {
      auditDeail({ id }).then(res => {
        this.info = res
        this.info.id = this.id
        this.infoDialogVisible = true
      })
    },
    search(condition) {
      if (condition) {
        this.tableQuery.condition = condition
      }
      this.tableQuery.condition.keyword = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition.department = ''
      this.tableQuery.condition[this.type] = this.keyword
      this.tableQuery.pager.page = 1
      this.init()
    },
    clear(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.condition.keyword = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition.department = ''
      this.keyword = ''
      this.tableQuery.pager.page = 1
      this.init()
    },
    searchSelectChange(val) {
      this.tableQuery.condition.keyword = ''
      this.tableQuery.condition.department = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition[this.searchType] = this.keyword.replace(/\s*/g, '')
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.init(false)
    },
    // 获取专业人士列表
    init(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      this.searchSelectChange()
      majorUserList(this.tableQuery).then(res => {
        this.professionalList = res.records
        this.total = res.total
      })
    },
    handleAdd() {
      this.$router.push({ name: 'ProfessionaDetail' })
    },
    handleLook(row) {
      this.$router.push({
        name: 'ProfessionaDetail',
        query: {
          id: row.uid
        }
      })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>

<style scoped>
.video-links {
  color: #409eff;
  cursor: pointer;
}
</style>
