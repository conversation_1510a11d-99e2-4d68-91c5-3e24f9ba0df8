<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">资助项目</header>
    </div>
    <div class="search-column">
      <div class="search-column__item" style="width:400px;">
        <el-input v-model="tableQuery.condition.keyWord" placeholder="请输入搜索关键字" clearable @clear="handleSearchKey()" @keydown.enter.native="handleSearchKey()">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="handleSearchKey()" />
        </el-input>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="addUserDialogShow()">创建项目</el-button>
      </div>
    </div>
    <!-- body -->
    <el-table :data="foundationList" border stripe>
      <el-table-column
        v-for="col in tableColumnList"
        :key="col.id"
        :prop="col.prop"
        :label="col.label"
        align="center"
      />
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="viewRow(row)">查看</el-button>
          <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <!-- dialog add /edit -->
    <DialogFoundationEdit :data="formData" :title="title" :action="action" :show.sync="dialogVisible" @handleCancel="handleCancelForm" @handleSave="handleSaveForm" />
  </div>
</template>

<script>
import { getFoundationList, addFoundation, updateFoundation, deleteFoundation } from '@/api/userManage'
import Pagination from '@/components/Pagination'
import permission from '@/directive/permission/index'
import DialogFoundationEdit from './compontents/dialogFoundationEdit'

export default {
  name: 'Foundation',
  components: {
    Pagination,
    DialogFoundationEdit
  },
  directives: { permission },
  data() {
    return {
      // user edit type
      action: 'add',
      tableQuery: {
        condition: {
          keyWord: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      title: '新建项目',
      dialogVisible: false,
      // table field
      tableColumnList: [
        { label: '项目名称', prop: 'projectName' },
        { label: '单位名称', prop: 'orgName' },
        { label: '创建人', prop: 'createName' },
        { label: '创建时间', prop: 'createTime' },
        { label: '最后更新人', prop: 'updateName' },
        { label: '最后更新时间', prop: 'updateTime' }
      ],
      // table list
      foundationList: [],
      // pagination
      total: 0,
      layout: 'total, prev, pager, next, jumper',
      // edit
      formData: {
        createUid: null, // 创建人id
        introduce: '', // 简介
        orgId: '', // 基金会id
        projectName: '', // 资助项目
        unit: '' // 被资助单位
      }
    }
  },
  created() {
    this.getFoundationList()
  },
  methods: {
    handleSearchKey() {
      this.tableQuery.pager.page = 1
      this.getFoundationList()
    },
    // create project
    addUserDialogShow() {
      this.action = 'add'
      this.title = '创建项目'
      this.dialogVisible = true
    },
    // reset formdata
    resetForm() {
      this.formData = {
        createUid: null,
        introduce: '',
        orgId: '',
        projectName: '',
        unit: ''
      }
    },
    // cancel
    handleCancelForm() {
      this.resetForm()
    },
    // save
    handleSaveForm(val) {
      if (this.action === 'add') {
        // add
        addFoundation(val).then(res => {
          this.$message.success('新增项目成功！')
          this.getFoundationList()
          this.dialogVisible = false
        })
      } else {
        updateFoundation(val).then(res => {
          this.$message.success('编辑项目成功！')
          this.getFoundationList()
          this.dialogVisible = false
        })
      }
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getFoundationList()
    },
    getFoundationList() {
      getFoundationList(this.tableQuery).then(res => {
        this.foundationList = res.records
        this.total = res.total
      })
    },
    // view
    viewRow(row) {
      this.action = 'view'
      this.title = '查看项目'
      this.viewEdit(row)
    },
    // edit
    editRow(row) {
      this.action = 'edit'
      this.title = '编辑项目'
      this.viewEdit(row)
    },
    // common action
    viewEdit(row) {
      this.formData = row
      this.dialogVisible = true
    },
    // del
    deleteRow(row) {
      this.$confirm('您确认删除 "' + row.projectName + '" 这个项目吗', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFoundation({ foundationId: row.id }).then(res => {
          this.$message.success('删除项目成功！')
          this.getFoundationList()
        })
      })
    }
  }
}
</script>
