<template>
  <div>
    <!-- <div class="down-container"><a ref="downId" :download="download" :href="excelUrl" /></div> -->
    <!-- 导入员工/导入部门和员工 -->
    <el-dialog
      :title="dialogImport.title"
      :visible.sync="importVisible"
      :width="dialogImport.width"
      center
      @open="clearFiles()"
      @close="close()"
    >
      <el-row class="text-center">
        <div style="margin-bottom: 15px;">导入{{ !orgId ? '卫健委单位' : '医院单位' }}</div>
        <div>
          请根据导入
          <a style="color: #44b9a2" :href="templateDownload()">《{{ !orgId ? '卫健委单位' : '医院单位' }}模板文件》</a>
          的格式内容批量导入
        </div>
        <br>
        <div><el-checkbox v-if="orgId" v-model="inheritWjwArea" style="color: red;">继承当前卫健委地理信息ID（选择该项则导入模板无需填写所属区ID）</el-checkbox></div>
        <br>
        <el-upload
          ref="upload"
          :action="dialogImport.uploadUrl"
          :headers="uploadHeader"
          :data="uploadData"
          :on-success="uploadSuccess"
          :on-error="uploadError"
          :before-upload="uploadProgress"
          :http-request="uploadSectionFile"
        >
          <el-button size="small" type="primary">选择文件</el-button>
        </el-upload>
      </el-row>
    </el-dialog>
    <el-dialog
      title="校验中"
      :visible.sync="verifyDialogVisible"
      width="400px"
      center
    >
      <el-row class="text-center">
        <i class="el-icon-loading" />
        <span>导入模板格式内容校验中</span>
      </el-row>
    </el-dialog>
    <el-dialog
      title="模板内容异常，无法导入"
      :visible.sync="errDialogVisible"
      width="635px"
      center
    >
      <div style="width: 380px; margin: 0 auto;">
        <h4 style="text-align: center;">反馈文件下载中...</h4>
        <span>如果没有响应，请<a ref="downId" style="color: #2CB39B;" :download="download" :href="excelUrl">点击重新下载</a></span>
        <div>请根据下载问题的问题，重新编辑模板内容后再次导入。</div>
      </div>
    </el-dialog>
    <!-- 模板内容异常无法导入 -->
    <el-dialog
      title="数据正在导入中..."
      :visible.sync="successDialogVisible"
      width="635px"
      center
    >

      <div style="padding: 0 100px">批量导入用户可能需要一段时间进行处理，请稍后点击“查看导入数据”按钮查看导入结果。</div>
      <span slot="footer" class="dialog-footer">
        <el-button style="background: #2CB39B;color: #ffffff" @click="successDialogVisible = false">确 定</el-button>
        <!-- <el-button v-if="batchNo" type="primary" @click="exportIssueDetails()">导出问题内容</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import request from '@/api/wjw'
import axios from 'axios'
import { parseTime } from '@/utils'

export default {
  name: 'WjwImport',
  mixins: [],
  props: {
    show: {
      type: Boolean,
      default: false
    },
    orgId: {
      type: String,
      default: ''
    },
    parentOrgId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 导入弹层
      dialogImport: {
        uploadUrl: '',
        title: '批量导入单位',
        width: '635px'
      },
      uploadData: null,
      uploadHeader: {
        token: getToken()
      },
      excelUrl: '',
      download: '',
      importVisible: false,
      verifyDialogVisible: false,
      errDialogVisible: false,
      successDialogVisible: false,
      inheritWjwArea: false
    }
  },
  watch: {
    show(val) {
      this.importVisible = val
      if (val) {
        if (!this.orgId) {
          this.dialogImport.uploadUrl = request.getImportWjw()
          this.uploadData = null
        } else {
          this.inheritWjwArea = false
          this.dialogImport.uploadUrl = request.getImportYy()
          this.uploadData = {
            parentOrgId: this.orgId,
            inheritWjwArea: this.inheritWjwArea
          }
        }
      }
    },
    inheritWjwArea(val) {
      if (this.orgId) {
        this.uploadData.inheritWjwArea = this.inheritWjwArea
      }
    }
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit('update:show', false)
    },
    // 清空files
    clearFiles() {
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
    },
    // 模板
    templateDownload(code) {
      const token = getToken()
      if (!this.orgId) {
        return request.getDownloadWjwTemplate(token)
      } else {
        return request.getDownloadYyTemplate(token)
      }
    },

    // 自定义上传
    uploadSectionFile(param) {
      const fileObj = param.file
      const form = new FormData()
      form.append('file', fileObj)
      let url = this.dialogImport.uploadUrl
      if (this.orgId) {
        url += '/' + this.orgId + '/' + (this.uploadData.inheritWjwArea ? '1' : '0')
      }
      axios.post(url, form, {
        headers: { ...this.uploadHeader, 'Content-Type': 'multipart/form-data' },
        responseType: 'blob'
      }).then((res) => {
        console.log(res)
        if (!res.headers['content-type'] || res.headers['content-type'].indexOf('application/json') === -1) {
          const blob = new Blob([res.data], { type: res.headers['content-type'] })
          this.excelUrl = window.URL.createObjectURL(blob)
          if (res.headers['content-disposition']) {
            this.download = res.headers['content-disposition'].split(';')[1].split('=')[1]
          } else {
            this.download = 'errorMessage-' + parseTime(new Date().getTime(), '{y}-{m}-{d}-{h}-{i}-{s}')
          }
          this.verifyDialogVisible = false
          this.errDialogVisible = true
          this.$nextTick(() => this.$refs.downId.click())
          this.clearFiles()
        } else {
          const blob = new Blob([res.data], { type: res.headers['content-type'] })
          var reader = new FileReader()
          reader.readAsText(blob)
          reader.onload = () => {
            this.uploadSuccess(JSON.parse(reader.result))
          }
          // res.data.text().then((text) => {
          //   this.uploadSuccess(JSON.parse(text))
          // })
        }
      }, () => {
        this.clearFiles()
        this.verifyDialogVisible = false
        this.$message({
          message: '文件上传失败',
          type: 'error',
          duration: 5 * 1000
        })
      })
    },
    // 上传时效验
    uploadProgress(res, file, fileList) {
      this.verifyDialogVisible = true
    },

    // 上传成功
    uploadSuccess(res) {
      this.verifyDialogVisible = false
      if (res.code !== 1) {
        if (res.code === 2) {
          this.$emit('error', {
            title: '导入发生异常失败',
            message: res.message,
            batchNo: ''
          })
          this.clearFiles()
          return
        }
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: action => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            return
          default:
            this.$emit('error', {
              title: '导入发生异常失败',
              message: res.message,
              batchNo: ''
            })
            this.clearFiles()
        }
        return Promise.reject(new Error(res.msg || 'Error'))
      } else {
        this.$emit('update:show', false)
        this.successDialogVisible = true
        // const data = res.data
        // this.$alert('批量导入用户可能需要一段时间进行处理，请稍后点击“查看导入数据”按钮查看导入结果。', '数据正在导入中...', {
        //   confirmButtonText: '确定'
        // })
        //   .then(() => {
        //     // window.location.href = downloadReport(data.reportId)
        //   })
      }
    },
    // 上传失败
    uploadError() {
      this.verifyDialogVisible = false
      this.$message({
        message: '文件上传失败',
        type: 'error',
        duration: 5 * 1000
      })
    }
  }
}
</script>
