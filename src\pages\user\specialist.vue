<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable @clear="init()" @keydown.enter.native="init()">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="init()" />
          <el-select slot="prepend" v-model="searchType" style="width: 100px" placeholder="请选择搜索的字段" @change="searchSelectChange()">
            <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <!-- 身份选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="tableQuery.condition.identityIds"
          placeholder="请选择身份"
          :options="identityTree"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'identityId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="identityChange"
        />
      </div>
      <!-- 职称选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="tableQuery.condition.academicClassIds"
          placeholder="请选择职称"
          :options="academicList"
          collapse-tags
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false,
            checkStrictly: true
          }"
          :disabled="disabled"
          clearable
          @change="init"
        />
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="handleAdd">添加</el-button>
      </div>
    </div>

    <el-table :data="doctorList" border stripe>
      <!-- <el-table-column type="selection" fixed="left" /> -->
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.type==='avatarUrl'">
            <el-image style="width: 100px" :src="scope.row[col.prop]" @click="preview(scope.row[col.prop])" />
          </template>
          <template v-else-if="col.type==='introduction'">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ scope.row[col.prop] }}
              </div>
              <span>{{ scope.row[col.prop] | filterFont }}</span>
            </el-tooltip>
          </template>
          <template v-else-if="col.type==='skill'">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ scope.row[col.prop] }}
              </div>
              <span>{{ scope.row[col.prop] | filterFont }}</span>
            </el-tooltip>
          </template>
          <template v-else>
            <span>
              {{ scope.row[col.prop] || '无' }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="100">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :visible.sync="dialogImgShow">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

    <Pagination class="text-center" :limit="tableQuery.limit" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { identityTreeList } from '@/api/category' // 身份树，随身份联动的专科树
import { academicTreeListById } from '@/api/academic' // 职称树
import { doctorList, delDoctor } from '@/api/doctor'

export default {
  name: 'Specialist',
  components: { Pagination },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    }
  },
  data() {
    return {
      keyword: '',
      searchType: 'realName',
      searchTypeList: [
        { label: '姓名', value: 'realName' },
        { label: '单位', value: 'company' }
      ],
      // 职称
      academicList: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      identityTree: [], // 身份列表
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: 'UID', align: 'center', prop: 'uid', width: '180' },
        { id: 2, label: '头像', align: 'center', prop: 'avatarUrl', width: '110', type: 'avatarUrl' },
        { id: 3, label: '姓名', align: 'center', prop: 'realName' },
        { id: 4, label: '身份', align: 'center', prop: 'identityName' },
        { id: 5, label: '职称', align: 'center', prop: 'academicName' },
        { id: 6, label: '部门/科室', align: 'center', prop: 'department' },
        { id: 7, label: '工作单位', align: 'center', prop: 'company' },
        { id: 8, label: '个人简介', align: 'center', prop: 'introduction', width: '140', type: 'introduction' },
        { id: 9, label: '专家擅长', align: 'center', prop: 'skill', width: '140', type: 'skill' },
        { id: 10, label: '创建时间', align: 'center', prop: 'createTime' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {
          realName: '',
          company: '',
          identityIds: [],
          academicClassIds: []
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      doctorList: [], // 专家列表
      dialogImageUrl: '',
      dialogImgShow: false,
      disabled: false
    }
  },
  created() {
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    this.init()
  },
  methods: {
    searchSelectChange(val) {
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition[this.searchType] = this.keyword.replace(/\s*/g, '')
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.init(false)
    },
    // 获取专家列表
    init(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      this.searchSelectChange()
      doctorList(this.tableQuery).then(res => {
        this.doctorList = res.records
        this.total = res.total
      })
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      this.tableQuery.condition.academicClassIds = []
      if (e[0] === '0') {
        // 身份选'无'，职称也为无
        this.academicList = [{ name: '无', academicId: '0' }]
      } else if (e && e.length > 1) {
        this.academicList = this.copy(this.academicList2)
      } else if (e && e.length === 1) {
        academicTreeListById(e[0]).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: '0' })
          this.academicList = newArr
        })
      } else {
        this.academicList = this.copy(this.academicList2)
      }
      this.init()
    },
    handleAdd() {
      this.$router.push({ name: 'SpeciaDetail' })
    },
    handleEdit(row) {
      this.$router.push({
        name: 'SpeciaDetail',
        query: {
          id: row.uid
        }
      })
    },
    handleDelete(row) {
      if (row.hasResource) {
        this.$message.error('该用户有关联的文章或视频等资源，请解除关联后再删除')
      } else {
        const hint = row.creatorCreateType === 1 ? '将删除该用户的专家身份，确认删除吗?' : '将同时删除该用户的专家和创作者身份，确认删除吗?'
        this.$confirm(hint, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          delDoctor(row.uid).then(res => {
            this.$message.success('删除成功!')
            this.init()
          })
        }).catch(() => {
          this.$message.info('已取消删除')
        })
      }
    },
    preview(url) {
      this.dialogImageUrl = url
      this.dialogImgShow = true
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>

<style scoped>
.video-links {
  color: #409eff;
  cursor: pointer;
}
</style>
