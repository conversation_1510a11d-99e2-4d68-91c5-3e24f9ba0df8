<template>
  <div class="upload-container">
    <el-button id="btn1" style="margin:0 0 0 -5px" @click="btnClick1">选择文件</el-button>
    <el-button id="btn2" style="margin:0 0 0 -5px;" @click="btnClick2">开始上传</el-button>
    <!-- <div id="ossfile">你的浏览器不支持flash,Silverlight或者HTML5！</div> -->
  </div>
</template>

<script>
// 参考链接  https://www.cnblogs.com/2050/p/3913184.html
// 步骤：1.搜索 第一步，preUploadApk为后端接口，请求参数是filename，并将接口返回的数据用于第二步
// 2.搜索 第二步，get_signature中的allData为接口返回数据，逐一对应赋值
// 3.第三步
import plupload from 'plupload'
import { preUploadApk } from '@/api/biz'

var accessid = ''
var host = ''
var policyBase64 = ''
var signature = ''
var callbackbody = ''
var key = ''
var allData = null

// 第二步
function get_signature() {
  host = allData.host
  policyBase64 = allData.policy
  accessid = allData.accessid
  signature = allData.signature
  callbackbody = allData.callback
  key = allData.key
  // expire = parseInt(allData.expire)
}

// 第三步
function set_upload_param(up) {
  get_signature()
  const new_multipart_params = {
    policy: policyBase64,
    OSSAccessKeyId: accessid,
    signature: signature,
    key: key,
    callback: callbackbody,
    success_action_status: '200' // 让服务端返回200,不然，默认会返回204
  }
  const a = Object.keys(allData.callbackVar)[0] // 根据后端要求添加字段
  new_multipart_params[a] = allData.callbackVar[a]
  // 重新设置某个特定的配置参数
  up.setOption({
    url: host,
    multipart_params: new_multipart_params
  })

  up.start()
}

// 该函数是原生xml请求后端接口获取上传前的必要参数，在vue中直接调用api：preUploadApk
// function send_request() {
//   console.log(1)
//   var xmlhttp = null
//   if (window.XMLHttpRequest) {
//     xmlhttp = new XMLHttpRequest()
//   }

//   if (xmlhttp != null) {
//     // serverUrl是 用户获取 '签名和Policy' 等信息的应用服务器的URL，请将下面的IP和Port配置为您自己的真实信息。
//     // serverUrl = 'http://***********:8888/aliyun-oss-appserver-php/php/get.php'
//     // const serverUrl = 'http://xxx/oss/up/'

//     xmlhttp.open('GET', serverUrl, false)
//     xmlhttp.send(null)
//     return xmlhttp.responseText
//   } else {
//     alert('Your browser does not support XMLHTTP.')
//   }
// }

// 截取文件名后缀: .apk
// function get_suffix(filename) {
//   const pos = filename.lastIndexOf('.')
//   let suffix = ''
//   if (pos !== -1) {
//     suffix = filename.substring(pos)
//   }
//   return suffix
// }

export default {
  data() {
    return {
      fileName: '',
      flag: false,
      uploader: null,
      hasError: false
    }
  },
  mounted() {
    this.initPlUploader()
  },
  methods: {
    btnClick1() {
      if (!this.flag) {
        this.$message({
          message: '你的浏览器不支持flash,Silverlight或者HTML5！',
          type: 'error',
          duration: 5 * 1000
        })
      }
    },
    btnClick2() {
      if (!this.flag) {
        this.$message({
          message: '你的浏览器不支持flash,Silverlight或者HTML5！',
          type: 'error',
          duration: 5 * 1000
        })
      }
      if (allData == null) {
        if (this.hasError) {
          this.$message({
            message: '网络出错了，请检查网络后重新选择文件',
            type: 'warning',
            duration: 5 * 1000
          })
          return
        } else {
          this.$message({
            message: '未选择文件，请选择apk文件',
            type: 'warning',
            duration: 5 * 1000
          })
          return
        }
      }
      // 打开进度条
      this.$emit('showPercent', true)
      set_upload_param(this.uploader)
    },
    initPlUploader() {
      const _this = this
      // 实例化一个plupload上传对象
      this.uploader = new plupload.Uploader({
        runtimes: 'html5,flash,silverlight,html4',
        browse_button: 'btn1', // 触发文件选择对话框的按钮id
        multi_selection: false, // 是否可以在文件浏览对话框中选择多个文件，true为可以，false为不可以。默认true
        // container: document.getElementById('container'), //用来指定Plupload所创建的html结构的父容器，默认为前面指定的browse_button的父元素。

        filters: {
          mime_types: [
            // 只允许上传图片和zip文件以及apk文件
            { title: 'apk files', extensions: 'apk' }
            // { title: 'Image files', extensions: 'jpg,gif,png,bmp' }
            // { title: 'Zip files', extensions: 'zip,rar,ipa' }
          ],
          max_file_size: '1000mb', // 最大只能上传1000mb的文件
          prevent_duplicates: true // 不允许选取重复文件
        },

        init: {
          // 当Init事件发生后触发
          PostInit: function() {
            _this.flag = true
          },

          // 当文件添加到上传队列后触发：第一步
          FilesAdded: function(up, files) {
            // 将文件名传给父组件展示
            _this.$emit('showFileName', files[0].name)
            // 请求后端接口 获取必要参数，上传文件的地址是返回体的host
            preUploadApk(files[0].name).then(res => {
              _this.hasError = false
              allData = res
            }).catch(() => {
              _this.hasError = true
              allData = null
            })
          },

          // 当队列中的某一个文件正要开始上传前触发
          BeforeUpload: function(up, file) {
            //
          },

          // 会在文件上传过程中不断触发，可以用此事件来显示上传进度
          UploadProgress: function(up, file) {
            _this.$emit('percent', file.percent)
          },

          // 当队列中的某一个文件上传完成后触发
          FileUploaded: function(up, file, info) {
            _this.$emit('showPercent', false)
            if (info.status === 200) {
              _this.$message.success('文件上传成功')
              _this.$emit('uploadSuccess', allData)
            } else if (info.status === 203) {
              _this.$message.warning('文件上传到OSS成功，但是OSS访问用户设置的上传回调服务器失败')
            } else {
              _this.$message.warning('文件上传失败')
            }
          },

          // 当发生错误时触发
          Error: function(up, err) {
            if (err.code === -600) {
              _this.$message({
                message: '选择的文件太大了',
                type: 'error',
                duration: 5 * 1000
              })
            } else if (err.code === -601) {
              _this.$message({
                message: '选择的文件后缀不对',
                type: 'error',
                duration: 5 * 1000
              })
            } else if (err.code === -602) {
              _this.$message({
                message: '这个文件已经上传过一遍了',
                type: 'error',
                duration: 5 * 1000
              })
            } else {
              _this.$message({
                message: `Error xml:${err.response}`,
                type: 'error',
                duration: 5 * 1000
              })
            }
          }
        }
      })
      // 在实例对象上调用init()方法进行初始化
      this.uploader.init()
      this.flag = true
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-container{
  display: inline-block;
  width: 202px;
  height: 38px;
}
</style>
