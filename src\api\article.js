import request from '@/utils/request'

// 文件上传
export const uploadFileApi = process.env.VUE_APP_BIZ_URL + '/upload/ueditor'

/**
 * 内容管理相关
 */

export default {
  // 新闻列表
  list(param) {
    return request({
      url: `/news/media/list`,
      method: 'post',
      data: param
    })
  },
  // 新闻详情
  mediaDetail(id) {
    return request({
      url: `/news/media/detail/${id}`,
      method: 'get'
    })
  },
  // 新闻创建
  createMedia(param) {
    return request({
      url: `/news/media/create`,
      method: 'post',
      data: param
    })
  },
  // 新闻分类
  cateList(param) {
    return request({
      url: `/news/media/cate/list`,
      method: 'post',
      data: param
    })
  },
  // 新闻编辑
  updateMedia(param) {
    return request({
      url: `/news/media/update`,
      method: 'post',
      data: param
    })
  },
  // 新闻删除
  deleteMedia(param) {
    return request({
      url: `/news/media/delete`,
      method: 'post',
      data: param
    })
  },
  // 新闻置顶/取消置顶
  mediaStick(param) {
    return request({
      url: `/news/media/stick`,
      method: 'post',
      data: param
    })
  },
  // 设置虚拟值
  virtualSet(param) {
    return request({
      url: `/news/media/virtual`,
      method: 'post',
      data: param
    })
  }
}
