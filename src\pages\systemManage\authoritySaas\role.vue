<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="角色名称/code" clearable @keyup.enter.native="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>
    <!-- body -->
    <a-table :columns="columns" fit :data="list" border stripe>
      <template slot="type" slot-scope="{row}">
        <span>{{ typeFmt(row) }}</span>
      </template>
      <template slot="super" slot-scope="{row}">
        <span>{{ row.isSuper|superFmt }}</span>
      </template>
      <template slot="default" slot-scope="{row}">
        <span>{{ row.isDefault|defaultFmt }}</span>
      </template>
      <template slot="push" slot-scope="{row}">
        <span>{{ row.isPush|pushFmt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button :disabled="row.isPush!=0" type="text" @click="pushRow(row)">推送</el-button>
        <el-button :disabled="row.isPush==2" type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="row.isDefault!==1&&!row.isSuper" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button type="text" @click="toDetail(row)">查看</el-button>
        <el-button type="text" @click="openPermis(row,1)">名医传世权限</el-button>
        <!-- <el-button type="text" @click="openPermis(row,2)">SDK权限</el-button> -->
        <el-button type="text" @click="openPermis(row,3)">钉钉权限</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />

    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawer"
      direction="rtl"
      size="400px"
      @close="permisCancle"
    >
      <el-form v-if="drawer" ref="per-form" :disabled="perDisabled" :rules="perRules" label-width="60px" class="form">
        <!-- <h3>权限信息</h3> -->
        <el-form-item label="" prop="permissionList">
          <div class="scroll-tree">
            <el-scrollbar>
              <el-tree
                ref="tree"
                node-key="id"
                show-checkbox
                :data="unitList"
                :filter-node-method="filterNode"
                :props="{
                  children: 'childList',
                  label: 'name'
                }"
                :default-checked-keys="defaultCheckedKeys"
                @check-change="handleCheckChange"
              />
            </el-scrollbar>
          </div>
        </el-form-item>
      </el-form>
      <div v-show="perDisabled" style="text-align:center">
        <el-button @click="perDisabled=false">编辑</el-button>
      </div>
      <div v-show="!perDisabled" style="text-align:center">
        <el-button @click="permisCancle">取消</el-button>
        <el-button type="primary" @click="permisConfirm">确定</el-button>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import { getOrganTypeList } from '@/api/userManage'
import request from '@/api/roleSaas'
import table from '@/mixins/table'
import { toObject } from 'element-ui/src/utils/util'

const columns = [
  { props: { label: 'code', align: 'center', prop: 'code' }},
  { props: { label: '角色名称', align: 'center', prop: 'name' }},
  { props: { label: '角色类型', align: 'center' }, slot: 'type' },
  { props: { label: '是否超级管理员', align: 'center' }, slot: 'super' },
  { props: { label: '是否默认角色', align: 'center' }, slot: 'default' },
  { props: { label: '描述', align: 'center', prop: 'description' }},
  { props: { label: '修改人', align: 'center', prop: 'updateName' }},
  { props: { label: '修改时间', align: 'center', prop: 'updateTime' }},
  { props: { label: '推送状态', align: 'center' }, slot: 'push' },
  { props: { label: '操作', align: 'center' }, slot: 'actions' }
]

export default {
  name: 'Role',
  filters: {
    superFmt(v) {
      return v ? '是' : '否'
    },
    defaultFmt(v) {
      return v == 1 ? '是' : '否'
    },
    pushFmt(v) {
      const arr = ['未推送', '已推送', '推送中']
      return arr[v]
    }
  },
  mixins: [table],
  data() {
    const permissionValid = (rule, value, cb) => {
      if (!this.permissionList.length) {
        cb(new Error('请选择权限'))
      } else {
        cb()
      }
    }
    return {
      perDisabled: true,
      permissionList: [],
      perRules: {
        permissionList: [
          { validator: permissionValid, trigger: 'change' }
        ]
      },
      unitList: [],
      defaultCheckedKeys: [],
      columns,
      request,
      condition: {
        keyword: ''
      },
      mainKey: 'id',
      editPath: 'saasRoleAdd',
      userShow: false,
      orgList: [],
      initList: false,
      drawer: false,
      id: '',
      type: '',
      drawerTitle: '名医传世权限信息'
    }
  },
  created() {
    getOrganTypeList().then(res => {
      this.orgList = res
      request.list(this.getListData()).then(res => {
        this.list = res.records
        this.total = res.total
      })
    })
  },
  methods: {
    openPermis(row, type) {
      this.id = row.id
      this.type = type
      request.roleMenuList(type).then(res => {
        this.unitList = res
        this.pickList(row.id, type)
        this.perDisabled = true
      })
    },
    pickList(id, type) {
      request.rolePickList(id).then(res => {
        let arr = []
        if (type === 1) {
          this.drawerTitle = '名医传世权限信息'
          arr = res.NORMAL
        } else if (type === 2) {
          this.drawerTitle = 'SDK权限信息'
          arr = res.DEVELOPER
        } else if (type === 3) {
          this.drawerTitle = '钉钉权限信息'
          arr = res.DINGDING
        }
        const gotChildNodes = this.flat(this.unitList)
        const sv = {}
        arr.forEach(si => {
          sv[si] = true
        })
        this.permissionList = Object.keys(sv)
        gotChildNodes.forEach(v => {
          if (sv[v.id] && v.childList.some(j => sv[j.id])) {
            delete sv[v.id]
          }
        })
        this.defaultCheckedKeys = Object.keys(sv)
        // this.permissionList = Object.keys(sv)
        this.drawer = true
      })
    },
    flat(arr) {
      let arrList = []
      arr.forEach(v => {
        if (v.childList.length) {
          arrList.push(v)
          arrList = arrList.concat(this.flat(v.childList))
        }
      })
      return arrList
    },
    // tree 复选框选中
    handleCheckChange(data, checked, indeterminate) {
      const selectedArr = this.$refs.tree.getCheckedKeys()
      const half = this.$refs.tree.getHalfCheckedNodes().map(v => v.id)
      this.permissionList = half.concat(selectedArr)
      this.defaultCheckedKeys = selectedArr
    },
    getCheckedKeys(node) {
      let arr = []
      const arrNode = []
      node.forEach(i => {
        if (i.checked) {
          arr.push(i.data.id)
          if (i.childNodes.length) {
            arrNode.push({
              id: i.data.id,
              childNodes: i.childNodes
            })
          }
        }
      })
      arrNode.forEach(i => {
        i.childNodes.forEach(j => {
          arr = arr.filter(item => item !== j.data.id)
        })
      })
      return arr
    },
    permisConfirm() {
      const query = {
        id: this.id,
        groupName: '',
        permissionList: this.permissionList
      }
      if (this.type === 1) {
        query.groupName = 'NORMAL'
      } else if (this.type === 2) {
        query.groupName = 'DEVELOPER'
      } else if (this.type === 3) {
        query.groupName = 'DINGDING'
      }
      request.editRoleMenu(query).then(res => {
        this.$message.success('编辑成功')
        this.drawer = false
        this.getList()
      })
    },
    permisCancle() {
      this.permissionList = []
      this.unitList = []
      this.defaultCheckedKeys = []
      this.drawer = false
    },
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    },
    typeFmt(v) {
      const arr = ['', '个人', '机构', '医务工作者', '人物', '讲师', '内容扩展方']
      if (v.roleType === 2) {
        const target = this.orgList.find(sv => sv.orgTypeId === v.orgType)
        if (target) {
          return arr[v.roleType] + '-' + target.name
        } else {
          return arr[v.roleType]
        }
      } else {
        return arr[v.roleType]
      }
    },
    // edit row
    editRow(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          id: row[this.mainKey],
          code: row.code,
          name: row.name,
          description: row.description,
          roleType: row.roleType,
          orgType: row.orgType,
          isSuper: row.isSuper,
          isDefault: row.isDefault
        }
      })
    },
    deleteRow(row) {
      request.roleDel(row.id).then(res => {
        this.getList()
      })
    },
    toDetail(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          readOnly: 1,
          id: row[this.mainKey],
          code: row.code,
          name: row.name,
          description: row.description,
          roleType: row.roleType,
          orgType: row.orgType,
          isSuper: row.isSuper,
          isDefault: row.isDefault
        }
      })
    },
    pushRow(row) {
      request.rolePush(row.id).then(res => {
        this.$message.success(res)
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .scroll-tree {
    height: 500px;
    overflow: hidden;
    .el-scrollbar {
      height: 100%;
      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
    ::v-deep {
      .el-checkbox__input.is-disabled {
        &.is-checked .el-checkbox__inner,
        &.is-indeterminate .el-checkbox__inner {
          background-color: #409eff;
          border-color: #409eff;
        }
      }
    }
  }
</style>
