<template>
  <div class="app-container">
    <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="150px" class="form">
      <h3>广告信息</h3>
      <el-form-item label="广告主" prop="advertiserName">
        <el-button v-if="form.advertiserName === ''" type="primary" @click="selectOrgVisible = true">选择广告主</el-button>
        <select-org
          :dialog-visible.sync="selectOrgVisible"
          @checked="checkedOrg"
        />
        <el-tag v-if="form.advertiserName !== ''" :closable="noActivity" @close="clearOrg">{{ form.advertiserName }}</el-tag>
      </el-form-item>
      <el-form-item label="广告位置" prop="playPlaceId">
        <el-select v-model="form.playPlaceId" filterable placeholder="请选择" :disabled="isOffshelf" @change="form.adMaterialType = 1">
          <el-option v-for="item in adPlaceList" :key="item.adPlaceId" :label="item.place" :value="item.adPlaceId" />
        </el-select>
      </el-form-item>
      <el-form-item label="广告素材类型" prop="adMaterialType">
        <el-select v-model="form.adMaterialType" filterable placeholder="请选择" :disabled="isOffshelf">
          <el-option v-for="item in adMaterialTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="广告素材" prop="materialId">
        <!-- 上传 -->
        <!--暂时不需要token :headers="uploadHeaders" -->
        <div style="display: flex">
          <el-upload
            v-if="form.adMaterialType !== 3"
            :class="{ hideupdate: form.fileList.length >= 1 }"
            :action="uploadFileApi"
            :data="uploadData"
            :file-list="form.fileList"
            list-type="picture-card"
            :multiple="false"
            :limit="1"
            :before-upload="(file) => { return handleBeforeUpload(file, 'adMaterial')}"
            :on-success="handleSuccess"
          >
            <i slot="default" class="el-icon-plus" />
            <div slot="file" slot-scope="{file}">
              <img class="el-upload-list__item-thumbnail" :src="file.url">
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in" />
                </span>
                <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete" />
                </span>
              </span>
            </div>
          </el-upload>
          <video-upload
            v-if="form.adMaterialType === 3 && form.materialId === ''"
            :key="form.materialId"
            :video-file-id.sync="form.materialId"
          />
          <video-play
            v-show="form.adMaterialType === 3 && form.materialId !== ''"
            :video-file-id="form.materialId"
            @del="form.materialId = ''"
          />
          <div class="fileListHint">
            <el-alert
              :title="fileListHint.title"
              type="info"
              show-icon
              :description="fileListHint.description"
              :closable="false"
            />
          </div>
        </div>
        <el-dialog :visible.sync="dialogVisible" top="8vh">
          <img width="100%" :src="dialogImageUrl">
        </el-dialog>
      </el-form-item>
      <el-form-item label="广告落地页" prop="adTypeId">
        <el-radio-group v-model="form.adTypeId">
          <el-radio
            v-for="item in adTypeList"
            :key="item.adTypeId"
            :label="item.adTypeId"
          >{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="content">
        <div v-if="form.adTypeId === '0'" />
        <div v-else-if="form.adTypeId === '1'" style="width: 700px; line-height: 1.2em;padding-bottom: 10px">
          <el-alert
            type="info"
            show-icon
            :closable="false"
          >
            <pre slot="title">
可支持苹果手机访问苹果链接，安卓手机访问安卓链接
--若安卓和苹果链接一样，可只录入一个链接
--若安卓和苹果链接不一样，可录入两个链接，
安卓链接：“Android-”+“http://或https://链接”，
苹果链接：“ios-”+“http://或https://链接”，两个链接以“|”分隔
            </pre>
          </el-alert>
          <el-input
            v-model="form.content"
            autosize
            type="textarea"
            placeholder="请输入http://或https://开头的链接"
            :disabled="isOffshelf"
          />
        </div>
        <div v-else-if="form.adTypeId === '2'">
          <el-upload
            v-if="form.content === ''"
            accept="application/pdf"
            :show-file-list="false"
            :class="{ hideContentUpdate: form.contentList.length >= 1 }"
            :action="uploadFileApi"
            :data="uploadData"
            :limit="1"
            :file-list="form.contentList"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
          >
            <div class="uploadPdf">
              <h2>
                <i class="el-icon-plus avatar-uploader-icon" /> 上传PDF
              </h2>
              大小不超过50M
            </div>
          </el-upload>
          <el-tag
            v-if="form.content !== ''"
            closable
            @close="handleContentRemove"
          >{{ form.content }}</el-tag>
        </div>
        <div v-else-if="form.adTypeId === '3'">
          <video-upload
            v-if="form.content === ''"
            :key="form.content"
            :video-file-id.sync="videoId"
          />
          <el-tag
            v-if="form.content !== ''"
            closable
            @close="handleContentRemove"
          >{{ form.content }}</el-tag>
        </div>
        <select-content-activity
          v-else-if="form.adTypeId === '4'"
          ref="contentActivity"
          :activity-id.sync="form.content"
          :disabled="isOffshelf"
          @checkedActivity="checkedActivity"
        />
        <editor
          v-else-if="form.adTypeId === '5'"
          v-model="form.content"
          height="300"
          :disabled="readOnly"
        />
        <div v-else class="platform">
          <el-button type="primary" @click="dialogVisiblePlatform = true">选择内容</el-button>
          <el-input
            v-model="form.contentName"
            placeholder="请选择平台内容"
            style="width: 400px"
            disabled
          />
          <select-platform
            :key="form.adTypeId"
            :dialog-visible.sync="dialogVisiblePlatform"
            :type="platformType"
            :content-id.sync="form.content"
            :content-title.sync="form.contentName"
          />
        </div>
      </el-form-item>
      <el-form-item label="第三方平台监测-曝光" prop="exposureMonitorUrl">
        <el-input v-model="form.exposureMonitorUrl" />
      </el-form-item>
      <el-form-item label="第三方平台监测-点击" prop="clickMonitorUrl">
        <el-input v-model="form.clickMonitorUrl" />
      </el-form-item>
      <el-form-item label="广告名称" prop="advertisementName">
        <el-input v-model="form.advertisementName" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="排序" prop="listOrder">
        <el-input-number v-model="form.listOrder" :min="1" :max="10" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="300" show-word-limit />
      </el-form-item>

      <h3 v-if="!isEdit && form.restrict=== 1 ">投放设置</h3>
      <el-form-item label="投放时间">
        <el-date-picker
          v-model="putTime"
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="上架时间"
          end-placeholder="下架时间"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <el-form-item label="投放对象" prop="restrict">
        <el-radio-group v-model="form.restrict" :disabled="form.adTypeId === '4'">
          <el-radio :label="0">全部投放</el-radio>
          <el-radio :label="1">部分投放</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template>
      <user-list
        v-if="form.advertisementId && form.restrict=== 1 && form.adTypeId !== '4'"
        key="ad"
        :read-only="readOnly"
        api-type="ad"
        :advertisement-id="form.advertisementId"
      />
      <user-list
        v-if="form.restrict=== 1 && form.adTypeId === '4' && form.content !== ''"
        :read-only="true"
        :activity-id="form.content"
        :is-add-ad="true"
      />
    </template>
    <div>
      <el-button @click="cancel">{{ readOnly?'返回':'取消' }}</el-button>
      <el-button v-if="!readOnly" type="primary" @click="submitForm('form')">保存</el-button>
    </div>

    <!-- select ad dialog -->
    <select-ad ref="selectDialog" @selected="handleAdSelect" />
  </div>
</template>

<script>
import selectAd from './components/selectAd.vue'
import { adOrgList, adDetail, adType, adPlace, adAdd, adEdit, preAdd } from '@/api/ad'
import { getAreaTree } from '@/api/area'
import { treeList } from '@/api/major'
import { identityList } from '@/api/category'
import { uploadFileApi, preUploadApi, getVideoPreview } from '@/api/biz'
import { deptNameList } from '@/api/dept'
import selectOrg from './components/selectOrg.vue'
import selectContentActivity from '@/components/SelectContent/Activity.vue'
import userList from '@/components/PublishUserList/index.vue'
import videoPlay from '@/components/Upload/videoPlay.vue'
import videoUpload from '@/components/Upload/videoUpload.vue'
import editor from '@/components/wangEditor/index.vue'
import selectPlatform from '@/pages/operationManage/message/components/selectPlatform.vue'

export default {
  name: 'AddAd',
  components: { selectOrg, selectAd, selectContentActivity, userList, videoUpload, videoPlay, editor, selectPlatform },
  data() {
    return {
      form: {
        advertiserName: '',
        advertiserId: '',
        adMaterialType: 1,
        materialId: '',
        playPlaceId: '1',
        fileList: [],
        adTypeId: '1',
        contentName: '',
        content: '',
        contentList: [],
        exposureMonitorUrl: '',
        clickMonitorUrl: '',
        advertisementId: '',
        advertisementName: '',
        listOrder: 1,
        startTime: '',
        endTime: '',
        remark: '',
        restrict: 0,
        target: [],
        identity: [],
        department: [],
        area: [],
        major: [],
        scopeConditions: []
      },
      rules: {
        playPlaceId: [
          { required: true, message: '请选择广告位置', trigger: 'change' }
        ],
        adMaterialType: [
          { required: true, message: '请选择广告素材类型', trigger: 'change' }
        ],
        adTypeId: [
          { required: true, message: '请选择落地页类型', trigger: 'change' }
        ],
        materialId: [
          { required: true, message: '请上传广告素材', trigger: 'change' }
        ],
        content: [
          { required: true, message: '落地页不能为空', trigger: 'change' }
        ],
        advertisementName: [
          { required: true, message: '请填写广告名称', trigger: 'blur' }
        ],
        listOrder: [{ required: true, message: '请填写排序', trigger: 'blur' }],
        restrict: [
          { required: true, message: '请选择投放限制', trigger: 'change' }
        ],
        target: [{ required: true, message: '请选择对象', trigger: 'change' }],
        identity: [
          { required: true, message: '请选择身份', trigger: 'change' }
        ],
        department: [
          { required: true, message: '请选择部门库', trigger: 'change' }
        ]
      },
      // select list
      adOrgList: [],
      adTypeList: [],
      adTypeIdList: [
        { adTypeId: 1, typeName: '链接' },
        { adTypeId: 2, typeName: 'PDF' },
        { adTypeId: 3, typeName: '视频' }
      ],
      adPlaceList: [],
      adMaterialTypeList: [
        { value: 1, label: '静态图片' },
        { value: 2, label: '动态图片' },
        { value: 3, label: '小视频' }
      ],
      fileListHint: {
        title: '可支持jpg、jpeg、png格式图片',
        description: '推荐分辨率 1080 * 1920'
      },
      adStateList: [
        { value: 1, label: '下架' },
        { value: 2, label: '上架' }
      ],
      idList: [],
      // tree list
      areaList: [],
      majorList: [],
      props: {
        children: 'childList',
        label: 'name'
      },
      // dept
      deptList: [],
      // upload
      uploadHeaders: { token: this.$store.getters.token },
      uploadData: { data: '' },
      uploadFileApi,
      dialogImageUrl: '',
      dialogVisible: false,
      // action
      isEdit: this.$route.query.isEdit || false, // 是否编辑活动
      isOffshelf: !!this.$route.query.isOffshelf, // 当前操作数据是否下架
      readOnly: !!this.$route.query.readOnly, // 是否查看
      // 键:this.form当中的属性  键值:表单项"对象属性"的值
      contrast: {
        identity: '身份',
        department: '部门',
        major: '专科',
        area: '区域'
      },
      selectOrgVisible: false,
      dialogVisiblePlatform: false,
      noActivity: true,
      videoId: '',
      platformType: '',
      putTime: []
    }
  },
  watch: {
    'form.playPlaceId': {
      handler(v) {
        if (['1', '4', '7'].includes(v)) {
          this.adMaterialTypeList = [
            { value: 1, label: '静态图片' },
            { value: 2, label: '动态图片' },
            { value: 3, label: '小视频' }
          ]
        } else if (['2', '3', '6'].includes(v)) {
          this.adMaterialTypeList = [
            { value: 1, label: '静态图片' },
            { value: 2, label: '动态图片' }
          ]
        } else {
          this.form.adMaterialType = 3
          this.adMaterialTypeList = [{ value: 3, label: '小视频' }]
        }
        if (v === '1') {
          this.fileListHint.description = '推荐分辨率 1080 * 1920'
        } else if (v === '2') {
          this.fileListHint.description = '推荐分辨率 1010 * 446'
        } else if (['3', '6'].includes(v)) {
          this.fileListHint.description = '推荐分辨率 1010 * 274'
        } else { this.fileListHint.description = '推荐分辨率 1010 * 564' }
      }
    },
    'form.adMaterialType': {
      handler(v) {
        this.fileListHint.title = v === 1 ? '可支持jpg、jpeg、png格式图片' : (v === 2 ? '可支持gif格式图片' : '可支持MP4格式视频')
      }
    },
    'form.adTypeId': {
      handler(v) {
        this.noActivity = true
        this.handleContentRemove()
        if (v === '0') {
          this.rules.content = undefined
        }
        if (v === '6') {
          this.platformType = 'PLATFORM_VIDEO'
        } else if (v === '7') {
          this.platformType = 'PLATFORM_COURSE'
        } else if (v === '8') {
          this.platformType = 'PROMOTE_VIDEO'
        } else if (v === '9') {
          this.platformType = 'PROMOTE_ARTICLE'
        } else if (v === '10') {
          this.platformType = 'PROMOTE_DOULA_ARTICLE'
        } else if (v === '11') {
          this.platformType = 'PROMOTE_DOULA_VIDEO'
        } else if (v === '12') {
          this.platformType = 'EXAMINE_CERT'
        }
        if (v && this.form.advertisementId === '') {
          preAdd().then(res => {
            this.form.advertisementId = res
          })
        }
      },
      immediate: true
    },
    putTime(v) {
      this.form.startTime = v[0]
      this.form.endTime = v[1]
    },
    videoId(v) {
      getVideoPreview({ videoFileId: v }).then(res => {
        this.$nextTick(() => {
          this.form.content = res
        })
      })
    }
  },
  created() {
    const { isEdit = false, readOnly = false, id } = this.$route.query
    this.$route.meta.title = isEdit ? '编辑广告' : (readOnly ? '广告详情' : '添加广告')
    if (isEdit || readOnly) {
      adDetail(id).then((res) => {
        res.adTypeId += ''
        Object.assign(this.form, res)
        this.putTime = [this.form.startTime, this.form.endTime]
        this.form.fileList = [
          {
            id: res.materialId,
            url: res.imgUrl,
            name: res.advertisementName
          }
        ]
        if (res.adTypeId !== 1) {
          this.form.contentList = [
            {
              url: res.content,
              name: res.content
            }
          ]
        }
        this.assign()
        this.$nextTick(() => {
          this.form.content = res.content
          this.form.contentName = res.contentName
        })
      })
    }

    this.getSelectList()
  },
  methods: {
    // assign detail data
    assign() {
      for (const key in this.contrast) {
        const tempIndex = this.form.conditions.findIndex(
          (v) => v.property === key
        )
        if (tempIndex !== -1) {
          // 将包含的值追加到 this.form.target 中, 用于"对象属性"回显
          this.form.target.push(this.contrast[key])

          const { val } = this.form.conditions[tempIndex]
          this.form[key] = val.split(',')
        }
      }
    },
    // get select list
    getSelectList() {
      Promise.all([
        adOrgList(),
        adPlace(),
        adType(),
        identityList(),
        getAreaTree(),
        treeList(),
        deptNameList({})
      ]).then((res) => {
        [
          this.adOrgList,
          this.adPlaceList,
          this.adTypeList,
          this.idList,
          this.areaList,
          this.majorList,
          this.deptList
        ] = res
      })
    },
    // dialog select
    handleAdSelect(e) {
      this.form.content = e.content
      if (!this.form.advertisementName) {
        this.form.advertisementName = e.advertisementName
      }
    },
    checkedOrg(v) {
      this.form.advertiserName = v.orgName
      this.form.advertiserId = v.id
    },
    checkedActivity(v) {
      if (v.advertiserId) {
        this.form.advertiserId = v.advertiserId
        this.form.advertiserName = v.advertiserName
        this.noActivity = false
      } else { this.noActivity = true }
      this.form.content = v.activityId
      this.putTime = [v.startTime, v.endTime]
      this.form.restrict = v.isRestrict
    },
    clearOrg() {
      this.form.advertiserName = ''
      this.form.advertiserId = ''
    },
    // form
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.spell()
        } else {
          return false
        }
      })
    },
    // spell form data
    spell() {
      const arr = []
      for (const key in this.contrast) {
        const item = this.contrast[key]
        if (this.form.target.includes(item)) {
          const val = this.form[key].join(',')
          const obj = {
            necessary: 1,
            property: `${key}`,
            val
          }
          arr.push(obj)
        }
      }
      this.form.scopeConditions = arr
      this.$forceUpdate()
      if (this.form.adTypeId === '1') {
        this.form.content = this.form.content.replace(/[\n\r]/g, '')
      }
      // submit
      if (this.isEdit) {
        adEdit(this.form).then(() => {
          this.$message.success('编辑成功')
          this.$router.go(-1)
        })
      } else {
        adAdd(this.form).then(() => {
          this.$message.success('添加成功')
          this.$router.go(-1)
        })
      }
    },
    cancel() {
      this.$router.go(-1)
    },
    // upload
    async handleBeforeUpload(val, type) {
      if (type === 'adMaterial') {
        if (this.form.adMaterialType === 1) {
          if (!['image/jpeg', 'image/png'].includes(val.type)) return this.$message.error('上传的图片只能是 JPG或者PNG格式!')
        } else {
          if (!['image/gif'].includes(val.type)) return this.$message.error('上传的图片只能是 GIF 格式!')
        }
      } else {
        const flag = ['application/pdf'].includes(val.type)
        if (!flag) return this.$message.error('请上传PDF文件')
      }

      const param = {
        filename: val.name,
        size: val.size,
        type: 'adv'
      }
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        const flag = ['image/jpeg', 'image/png', 'image/gif'].includes(file.raw.type)
        if (!flag) {
          // 上传文件为pdf时返回的链接需要拼接pdf文件预览页
          this.form.content = this.form.adTypeId === 2 ? `${process.env.VUE_APP_H5_URL}/ad.html#/pdf?url=${res.data.url}` : res.data.url
          this.form.contentName = file.name
          this.form.contentList = [
            {
              url: this.form.content,
              name: this.form.contentName
            }
          ]
        } else {
          this.form.materialId = res.data.id
          this.form.fileList = [
            {
              id: res.data.id,
              url: res.data.url,
              name: res.data.url
            }
          ]
        }
      }
    },
    handleRemove() {
      this.form.fileList = []
    },
    handleContentRemove() {
      this.form.contentList = []
      this.form.content = ''
      this.form.contentName = ''
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // tree
    onAreaChange(data, checked, indeterminate) {
      this.form.area = this.$refs.areaTree.getCheckedKeys()
    },
    onMajorChange(data, checked, indeterminate) {
      this.form.major = this.$refs.majorTree.getCheckedKeys()
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  h3 {
    width: 800px;
    margin-bottom: 30px;
  }
  .area,
  .major {
    border: 1px solid #eee;
    height: 400px;
    overflow: hidden;

    .el-scrollbar {
      height: 100%;

      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
  }
  .adTypeId {
    display: flex;
    ::v-deep .el-select {
      width: 100px;
    }
    ::v-deep .el-textarea__inner {
      height: 40px !important;
    }
    span {
      display: block;
      width: 80px;
      height: 40px;
      border: 1px solid #eee;
      color: #999;
    }
  }
}
.hideupdate {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}
.hideContentUpdate {
  ::v-deep .el-upload {
    display: none;
  }
}
.pdf-success,.uploadPdf {
  width: 520px;
  height: 184px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 14px;
  color: #999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  h2 {
    margin: 0 auto;
    font-size: 22px;
    color: #000;
  }
}
.pdf-success {
  position: relative;
  img{
    width: 60px;
    &.pdf-success-del {
      position: absolute;
      top: 14px;
      right: 14px;
      width: 24px;
      cursor: pointer;
    }
  }
}
.platform {
  font-size: 0;
  .el-button {
    border-radius: 0;
  }
  .el-input__inner {
    border-radius: 0;
  }
}
</style>
