import request from '@/utils/request'

/**
 * 管理员守则签署
 */

// 获取单位Id
export function getOrgID(params) {
  return request({
    url: '/wjw/system/country/orgId',
    method: 'get',
    params: params
  })
}

// 获取管理员守则签署列表
export function getAdminRuleList(data) {
  return request({
    url: '/user/listUserSignRecord',
    method: 'post',
    data: data
  })
}

// 获取注销审核列表
export function getCancleCheckList(data) {
  return request({
    url: '/user/audit/list',
    method: 'post',
    data: data
  })
}

// 注销审核
export function cancleCheck(data) {
  return request({
    url: '/user/cancelAudit',
    method: 'post',
    data: data
  })
}
