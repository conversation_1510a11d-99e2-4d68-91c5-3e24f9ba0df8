<template>
  <div class="app-container">
    <div class="search-column">
      <!-- 时间选择器 -->
      <div class="search-column__item">
        <el-date-picker
          v-model="timeRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyyMM"
          :picker-options="pickerOptions"
          :clearable="false"
          @change="changePicker"
        />
      </div>
      <!-- 身份选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="condition.identityIds"
          placeholder="请选择身份"
          :options="identityTree"
          collapse-tags
          :show-all-levels="false"
          :props="{
            multiple: true,
            value:'identityId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="identityChange"
        />
      </div>
      <!-- 专科选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="condition.majorIds"
          placeholder="请选择专科"
          :options="majorList"
          collapse-tags
          :props="{
            multiple: true,
            value:'majorId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          :disabled="disabled"
          clearable
        />
      </div>
      <!-- 职称选择器 -->
      <div class="search-column__item">
        <el-cascader
          v-model="condition.academicIds"
          placeholder="请选择职称"
          :options="academicList"
          collapse-tags
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath: false,
            checkStrictly: true
          }"
          :disabled="disabled"
          clearable
        />
      </div>
      <!-- 区域选择器 -->
      <div class="search-column__item">
        <el-cascader
          ref="cascader1"
          v-model="areaId"
          placeholder="请选择区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            value:'areaId',
            label:'name',
            children:'childList',
            checkStrictly:true
          }"
          @change="handlerChange"
        />
      </div>
      <!-- 查询/重置/导出 按钮 -->
      <div class="search-column__item fr">
        <el-button type="primary" @click="handleFilter">查询</el-button>
        <el-button type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="exportRecord">导出</el-button>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe :default-sort="{prop: 'effectIndex', order: 'descending'}" />
    <!-- pagination -->
    <Pagination class="text-center" layout="total, prev, pager, next, jumper" :total="total" :page.sync="pager.page" @pagination="handlePagination" />

  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue' // Secondary package based on el-pagination
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件

import request from '@/api/dataStatistics/personDetail' // 人员详情api
import { identityTreeList, majorTreeList } from '@/api/category' // 身份树，随身份联动的专科树
import { getAreaTree } from '@/api/area' // 区域树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树

const columns = [
  {
    props: { label: '排名', align: 'center', type: 'index', width: '60px' }
  },
  {
    props: { label: '账号', align: 'center', prop: 'name', width: '120px' }
  },
  {
    props: { label: '姓名', align: 'center', prop: 'uname', width: '120px' }
  },
  {
    props: { label: '身份', align: 'center', prop: 'identity' }
  },
  {
    props: { label: '专科', align: 'center', prop: 'major' }
  },
  {
    props: { label: '职称', align: 'center', prop: 'academic' }
  },
  {
    props: { label: '所在单位', align: 'center', prop: 'orgName', width: '150px' }
  },
  {
    props: { label: '所在地区', align: 'center', prop: 'area', width: '80px' }
  },
  {
    props: { label: '效果指数', align: 'center', prop: 'effectIndex' }
  },
  {
    props: { label: '接收任务', align: 'center', prop: 'receivedNum' }
  },
  {
    props: { label: '参与任务', align: 'center', prop: 'joinedNum' }
  },
  {
    props: { label: '通过任务', align: 'center', prop: 'passedNum' }
  },
  {
    props: { label: '参与率', align: 'center', prop: 'partRate' } //
  },
  {
    props: { label: '通过率', align: 'center', prop: 'passRate' } //
  },
  {
    props: { label: '参与达标率', align: 'center', prop: 'controlRate', width: '110px' } //
  },
  {
    props: { label: '考核次数', align: 'center', prop: 'examineNum' }
  },
  {
    props: { label: '考核时长(分钟)', align: 'center', prop: 'examineTime', width: '80px' }
  },
  {
    props: { label: '自学时长(分钟)', align: 'center', prop: 'studyTime', width: '80px' }
  }
]

export default {
  name: 'PersonDetail',
  components: { Pagination, ATable },
  data() {
    const _this = this
    return {
      listLoading: false, // 列表请求状态
      list: [],
      total: 0,
      pager: {
        page: 1,
        pageSize: 10
      },
      hasPage: 1,
      identityTree: [],
      majorList: [],
      academicList: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      areaTypeList: [
        { value: 1, label: '全国' },
        { value: 2, label: '省' },
        { value: 3, label: '市' },
        { value: 4, label: '县/区' }
      ],
      columns,
      request,
      timeRange: [],
      // 自动查找的参数
      condition: {
        identityIds: [],
        majorIds: [],
        academicIds: [],
        areaType: 1,
        areaId: '0',
        startTime: '',
        endTime: ''
      },
      areaList: [], // 区域
      areaId: ['0'],
      dataJson: {},
      disabled: false,
      status: 0, // 1开0关
      multiple: 1,
      truePage: 1,
      duration: [],
      pickerOptions: {
        disabledDate(time) {
          if (typeof (_this.duration[0]) === 'undefined') {
            return time.getTime() > Date.now()// 禁止选择以后的时间
          } else {
            const currentTime = _this.duration[0]
            const threeMonths = 60 * 60 * 1000 * 24 * 31 * 11
            if (currentTime) {
              return ((time.getTime() > currentTime.getTime() + threeMonths || time.getTime() < currentTime.getTime() - threeMonths) || time.getTime() > Date.now())
    	      }
          }
        },
        onPick({ minDate, maxDate }) {
          // 当第一时间选中才设置禁用
          if (minDate && !maxDate) {
            _this.duration[0] = minDate
          }
          if (maxDate) {
            _this.duration[1] = maxDate
          }
        }
      }
    }
  },
  watch: {
    areaId() { // 关闭级联下拉框
      if (this.$refs.cascader1) {
        this.$refs.cascader1.dropDownVisible = false
      }
    },
    condition: {
      handler() {
        this.pager.page = 1
        this.getList()
      },
      deep: true
    }
  },
  created() {
    this.request.getMultipleStatus().then(res => {
      if (res.status === 1) {
        this.status = 1
        this.getMultiple(0)
      } else {
        this.getDate()
      }
    })
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    // 获取专科树
    treeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', majorId: '0' })
      this.majorList = newArr
    })
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.unshift({ name: '全国', areaId: '0' })
      this.areaList = newArr
    })
  },
  methods: {
    getMultiple(id) {
      this.request.getMultiple(Number(id)).then(res => {
        this.multiple = res.staffAmount
        this.getDate()
      })
    },
    getListData() {
      const condition = this.addParams()
      const pager = { pageSize: this.pager.pageSize }
      if (this.status === 1) {
        if (this.pager.page > this.truePage) {
          pager.page = parseInt(this.pager.page / this.multiple)
        } else {
          pager.page = this.pager.page
        }
      } else {
        pager.page = this.pager.page
      }
      const data = {
        condition: condition,
        pager: pager
      }
      return data
    },
    // 对请求参数进行处理
    addParams() {
      const params = this.copy(this.condition)
      // 应后端要求，用户未选择身份、职称、专科时，单值字段给-1
      if (params.identityIds.length === 0) {
        params.identityId = -1
      }
      if (params.academicIds.length === 0) {
        params.academicId = -1
      }
      if (params.majorIds.length === 0) {
        params.majorId = -1
      }
      // 应后端要求，选择多个身份时，职称字段用academicClassIds，单个身份时用academicIds
      if (params.identityIds.length > 1) {
        params.academicClassIds = params.academicIds
        params.academicIds = []
      }
      return params
    },
    getList() {
      if (this.listLoading) {
        return
      }
      this.listLoading = true
      const data = this.getListData()
      return this.request.list(data).then(res => {
        this.listLoading = false

        if (res && res.records && res.records.length === 0) {
          this.list = []
        } else {
          this.handleData(res.records) // 对数据进行处理，添加各种百分率数据
        }
        this.truePage = parseInt((res.total / this.multiple) / 10)
        this.total = res.total
      }, () => {
        this.listLoading = false
      })
    },
    handleData(records) {
      const rec = records
      rec.forEach((item, index) => {
        item.receivedNum = parseInt(item.receivedNum) // 接受任务
        item.joinedNum = parseInt(item.joinedNum) // 参与任务
        item.passedNum = parseInt(item.passedNum) // 通过任务
        item.examineNum = parseInt(item.examineNum) // 考核次数
        item.partRate = item.partRate + '%'// 参与率
        item.passRate = item.passRate + '%'// 通过率
        item.controlRate = item.controlRate + '%'// 参与达标率
      })
      this.list = rec
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      if (e[0] === '0') {
        // 身份选'无'，专科职称也为无
        this.condition.majorIds = []
        this.condition.academicIds = []
        this.disabled = true
        return
      } else {
        this.disabled = false
      }
      if (e && e.length > 0) {
        majorTreeList({ identityIds: e }).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: '0' })
          this.majorList = newArr
        })
      }
      if (e && e.length > 1) {
        this.academicList = [
          { academicId: 1004, name: '正高级' },
          { academicId: 1003, name: '副高级' },
          { academicId: 1002, name: '中级' },
          { academicId: 1001, name: '初级' },
          { academicId: 0, name: '无' }
        ]
      }
      if (e && e.length === 1) {
        academicTreeListById(e[0]).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: '0' })
          this.academicList = newArr
        })
      }
    },
    // 日期选择器
    changePicker(data) {
      if (data && data.length > 0) {
        this.condition.startTime = data[0]
        this.condition.endTime = data[1]
      }
    },
    // 默认显示本月及上月
    getDate() {
      const myDate = new Date()
      const year = myDate.getFullYear()
      const month = myDate.getMonth()
      const monthNew = month + 1 < 11 ? '0' + month : '' + month
      this.condition.startTime = year + monthNew
      this.condition.endTime = year + monthNew
      this.timeRange.push(this.condition.startTime)
      this.timeRange.push(this.condition.endTime)
      this.dataJson.startTime = this.copy(this.condition.startTime)
      this.dataJson.endTime = this.copy(this.condition.endTime)
    },
    // 区域级联change事件
    handlerChange(e) {
      if (e) {
        this.request.getMultiple(Number(e[0])).then(res => {
          this.multiple = res.staffAmount
          // 等该省倍数回来后再做处理
          this.condition.areaId = e[e.length - 1]
          if (e[0] == '0') {
            this.condition.areaType = 1
          } else {
            const arr = [1, 2, 3, 4]
            this.condition.areaType = arr[e.length]
          }
        })
      }
    },
    handleFilter() {
      this.pager.page = 1
      this.getList()
    },
    handlePagination(val) {
      this.pager = val
      this.getList()
    },
    // 重置预设数据
    reset() {
      const { startTime, endTime } = this.copy(this.dataJson)
      this.timeRange = []
      this.timeRange.push(startTime)
      this.timeRange.push(endTime)
      this.condition.startTime = startTime
      this.condition.endTime = endTime
      this.condition.identityIds = []
      this.condition.academicIds = []
      this.condition.majorIds = []
      this.areaId = ['0']
      this.condition.areaId = '0'
      this.condition.areaType = 1
      this.pager.page = 1
      this.pager.pageSize = 10
    },
    // 导出
    exportRecord() {
      if (this.list.length === 0) {
        this.$message.info('当前数据为空')
        return
      }
      const parmas = this.getListData()
      parmas.condition.index = 0
      this.request.export(parmas).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>
