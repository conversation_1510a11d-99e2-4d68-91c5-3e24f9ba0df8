<template>
  <el-dialog class="playVideoDialog" :title="playerTitle" :visible.sync="dialogVisible" width="1024px" @close="handlePauseVideo">
    <div class="pos-re">
      <ali-player
        ref="aliplayer"
        :play-style="aliPlayerConfig.playStyle"
        :source="aliPlayerConfig.source"
        :cover="aliPlayerConfig.cover"
        :height="aliPlayerConfig.height"
        :skin-layout="aliPlayerConfig.skinLayout"
        @ready="handleReadyVideo"
        @pause="handlePauseVideo"
        @error="handleError"
      />
    </div>
  </el-dialog>
</template>

<script>
import AliPlayer from '@/components/Aliplayer'
import { getVideoDetail, playVideoInfo } from '@/api/validManage'
export default {
  components: {
    AliPlayer
  },
  props: {
    // dialogVisible: {
    //   type: Boolean,
    //   default: false
    // },
    videoId: {
      type: String,
      default: ''
    },
    videoFileId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      // 阿里播放器设置
      playerTitle: '',
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            name: 'bigPlayButton',
            align: 'cc'
          },
          {
            name: 'H5Loading',
            align: 'cc'
          },
          {
            name: 'errorDisplay',
            align: 'tlabs',
            x: 0,
            y: 0
          },
          {
            name: 'infoDisplay'
          },
          {
            name: 'tooltip',
            align: 'blabs',
            x: 0,
            y: 56
          },
          {
            name: 'controlBar',
            align: 'blabs',
            x: 0,
            y: 0,
            children: [
              {
                name: 'progress',
                align: 'blabs',
                x: 0,
                y: 44
              },
              {
                name: 'playButton',
                align: 'tl',
                x: 15,
                y: 12
              },
              {
                name: 'timeDisplay',
                align: 'tl',
                x: 10,
                y: 7
              },
              {
                name: 'fullScreenButton',
                align: 'tr',
                x: 10,
                y: 12
              },
              {
                name: 'volume',
                align: 'tr',
                x: 5,
                y: 10
              }
            ]
          }
        ]
      }
    }
  },
  watch: {
    videoId(v) {
      if (v !== '') {
        this.getVideo(v)
      }
    },
    videoFileId(v) {
      if (v !== '') {
        const response = {
          videoId: 0,
          videoInfoId: 0,
          videoFileId: v
        }
        this.handlePlayVideo(response)
      }
    }
  },
  methods: {
    getVideo(v) {
      getVideoDetail({ videoId: v, videoSource: 1 }).then(response => {
        this.handlePlayVideo(response)
      })
    },
    // 播放视频
    handlePlayVideo(response) {
      playVideoInfo({
        videoId: response.videoId,
        videoInfoId: response.videoInfoId,
        videoFileId: response.videoFileId
      }).then(res => {
        if (res.playInfoList && res.playInfoList.length) {
          const sourceUrl = {}
          res.playInfoList.map(v => {
            sourceUrl[v.definition] = v.playURL
          })
          this.aliPlayerConfig.cover = res.coverUrl
          this.aliPlayerConfig.source = JSON.stringify(sourceUrl)
          this.playerTitle = response.name || res.videoName
          this.dialogVisible = true
          this.$nextTick(() => {
            this.$refs.aliplayer.init()
          })
        } else {
          this.$message.error('该视频暂时无法播放，请稍后重试！')
        }
      })
    },
    // 阿里云播放器事件
    handleReadyVideo(val) {
      this.player = val
    },
    handlePauseVideo() {
      // this.init()
      this.player.pause()
      this.$emit('update:videoId', '')
      this.$emit('update:videoFileId', '')
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    }
  }
}
</script>

<style>

</style>
