import request from '@/utils/request'

// 用户区域分布
export function mapDistribution(params) {
  return request({
    url: '/statistics/user/userAreaDistribution',
    method: 'get'
  })
}

// 用户专科分布
export function majorDistribution(params) {
  return request({
    url: '/statistics/user/userMajorDistribution',
    method: 'get'
  })
}

// 离线培训时长
export function pastTime() {
  const orgId = process.env.VUE_APP_SAAS_API_URL === 'https://webapi.mycs.cn' ? '1306501892793851906' : '1306078929069813762'
  return request({
    url: `/statistisc/screen/pastTime/${orgId}`,
    method: 'get',
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// 课程学习排名
export function courseRank(params) {
  return request({
    url: '/statistics/video/getTopFive',
    method: 'get'
  })
}

// 课程学习动态
export function getStudyTrends() {
  return request({
    url: '/statistics/video/getStudyTrends',
    method: 'get'
  })
}

// 用户动态
export function uvList(query) {
  return request({
    url: '/statistics/uv/uvList',
    method: 'get',
    data: query
  })
}

// 统计分析-用户分析（专科用户统计列表）
export function userAnalysis(query) {
  return request({
    url: '/statistics/user/userMajorAnalysis',
    method: 'post',
    data: query
  })
}

// 统计分析-用户分析（人员详情统计列表）
export function userDetail(query) {
  return request({
    url: '/statistics/user/userDetailList',
    method: 'post',
    data: query
  })
}

// 活跃概览
export function uvOverview() {
  return request({
    url: '/statistics/uv/uvOverview',
    method: 'get'
  })
}

// 用户活跃趋势
export function uvTrend(query) {
  return request({
    url: '/statistics/uv/uvTrend',
    method: 'post',
    data: query
  })
}

// 用户活跃明细
export function uvDetail(query) {
  return request({
    url: '/statistics/uv/uvDetail',
    method: 'post',
    data: query
  })
}

// 用户活跃数据导出
export function uvTrendExport(query) {
  return request({
    url: '/statistics/uv/uvTrendExport',
    method: 'post',
    data: query
  })
}

// 课程列表
export function courseList(query) {
  return request({
    url: '/statistics/video/getList',
    method: 'post',
    data: query
  })
}
// 课程列表导出
export function courseListExport(query) {
  return request({
    url: '/statistics/video/videoStudyRecordExport',
    method: 'post',
    data: query
  })
}

// 课程学习详情
export function courseDetailList(query) {
  return request({
    url: '/statistics/video/getVideoStudyDetailList',
    method: 'post',
    data: query
  })
}
// 课程学习详情导出
export function courseDetailListExport(videoInfoId) {
  return request({
    url: `/statistics/video/videoStudyDetailExport/${videoInfoId}`,
    method: 'get'
  })
}

// 用户活跃趋势
export function getRichData() {
  return request({
    url: '/cms/screen/trainData',
    method: 'get'
  })
}

// 获取大屏培训数据
export function getTrainData() {
  return request({
    url: '/cms/screen/trainData',
    method: 'get'
  })
}

// 更新大屏培训数据
export function editTrainData(query) {
  return request({
    url: '/cms/screen/updateData',
    method: 'post',
    data: query
  })
}

// 获取大屏访问账号
export function getVisitAccount() {
  return request({
    url: '/cms/screen/visitAccount',
    method: 'get'
  })
}

// 添加大屏访问账号
export function addVisitAccount(uid) {
  return request({
    url: `/cms/screen/addVisitAccount/${uid}`,
    method: 'post'
  })
}

// 删除大屏访问账号
export function delVisitAccount(uid) {
  return request({
    url: `/cms/screen/delVisitAccount/${uid}`,
    method: 'post'
  })
}

