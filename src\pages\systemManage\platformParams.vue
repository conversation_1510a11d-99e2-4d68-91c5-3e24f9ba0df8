<template>
  <div class="contain">
    <el-form ref="form" :rules="rules" label-width="190px" :model="form" style="width:1200px;margin-left:60px;margin-bottom:100px;">
      <el-form-item label="App开屏广告时长(s)：" prop="adTime">
        <el-input-number v-show="edit" v-model="form.adTime" :min="0" :precision="1" :step="1" step-strictly />
        <el-card v-show="!edit">
          <span style="font-size:18px">{{ form.adTime? form.adTime+' s':'' }}</span>
        </el-card>
      </el-form-item>
      <el-form-item label="用户协议：" prop="userAgreement">
        <tinymce v-if="edit" v-model="form.userAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div class="showDiv test-1" v-html="form.userAgreement" />
        </el-card>
      </el-form-item><br>
      <el-form-item label="隐私政策：" prop="privacyPolicy">
        <tinymce v-if="edit" v-model="form.privacyPolicy" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.privacyPolicy" />
        </el-card>
      </el-form-item><br>
      <el-form-item label="人脸识别服务协议：" prop="faceAgreement">
        <tinymce v-if="edit" v-model="form.faceAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.faceAgreement" />
        </el-card>
      </el-form-item><br>
      <el-form-item label="实名认证服务协议：" prop="realAuthAgreement">
        <tinymce v-if="edit" v-model="form.realAuthAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.realAuthAgreement" />
        </el-card>
      </el-form-item><br>
      <el-form-item label="个人VIP会员服务协议：" prop="memberAgreement">
        <tinymce v-if="edit" v-model="form.memberAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.memberAgreement" />
        </el-card>
      </el-form-item>
      <el-form-item label="管理员守则：" prop="superUserRule">
        <tinymce v-if="edit" v-model="form.superUserRule" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.superUserRule" />
        </el-card>
      </el-form-item>
      <el-form-item label="注销账号重要提醒：" prop="cancelAccountWarn">
        <tinymce v-if="edit" v-model="form.cancelAccountWarn" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.cancelAccountWarn" />
        </el-card>
      </el-form-item>
      <el-form-item label="内容扩展方合作协议：" prop="contentExtenderCooperation">
        <tinymce v-if="edit" v-model="form.contentExtenderCooperation" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.contentExtenderCooperation" />
        </el-card>
      </el-form-item>
      <el-form-item label="讲师协议：" prop="lecturerCooperation">
        <tinymce v-if="edit" v-model="form.lecturerCooperation" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.lecturerCooperation" />
        </el-card>
      </el-form-item>
      <el-form-item label="直播须知：" prop="liveNotice">
        <tinymce v-if="edit" v-model="form.liveNotice" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.liveNotice" />
        </el-card>
      </el-form-item>
      <el-form-item label="证书培训服务协议：" prop="certExamineAgreement">
        <tinymce v-if="edit" v-model="form.certExamineAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.certExamineAgreement" />
        </el-card>
      </el-form-item>
      <el-form-item label="继教学分学习服务协议：" prop="creditAgreement">
        <tinymce v-if="edit" v-model="form.creditAgreement" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.creditAgreement" />
        </el-card>
      </el-form-item>
      <el-form-item label="继教学分报名须知：" prop="creditNotice">
        <tinymce v-if="edit" v-model="form.creditNotice" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.creditNotice" />
        </el-card>
      </el-form-item>
      <el-form-item label="抖喇社区公约：" prop="doulaCommunityConvention">
        <tinymce v-if="edit" v-model="form.doulaCommunityConvention" :height="400" upload-type="course" />
        <el-card v-show="!edit">
          <div v-show="!edit" class="showDiv test-1" v-html="form.doulaCommunityConvention" />
        </el-card>
      </el-form-item>
    </el-form>
    <div class="btnGrounp">
      <el-button v-show="edit" @click="cancle">取消</el-button>
      <el-button v-show="edit" type="primary" @click="confirm">保存</el-button>
      <el-button v-show="!edit" type="primary" @click="edit=true">编辑</el-button>
    </div>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import { getPlatData, updatePlatData } from '@/api/systemManage'
export default {
  name: 'PlatformParams',
  components: { Tinymce },
  data() {
    return {
      edit: false,
      dataJson: {},
      form: {
        adTime: null,
        userAgreement: '',
        privacyPolicy: '',
        faceAgreement: '',
        realAuthAgreement: '',
        memberAgreement: '',
        superUserRule: '',
        cancelAccountWarn: '',
        contentExtenderCooperation: '',
        lecturerCooperation: '',
        liveNotice: '',
        certExamineAgreement: '',
        creditAgreement: '',
        creditNotice: '',
        doulaCommunityConvention: ''
      },
      rules: {
        adTime: [{ required: true, message: '请输入广告时长', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getPlatData().then(res => {
        this.dataJson = JSON.parse(JSON.stringify(res))
        this.form = JSON.parse(JSON.stringify(res))
      })
    },
    confirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$confirm('确定保存将同步至web端, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            updatePlatData(this.form).then(res => {
              this.$message.success('保存成功')
              this.init()
              this.edit = false
            })
          })
        }
      })
    },
    cancle() {
      this.$confirm('确认取消将还原当前所有改动, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.form = JSON.parse(JSON.stringify(this.dataJson))
        this.edit = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  width: 100%;
  padding: 40px 0;
}
.showDiv{
  width: 100%;
  height: 500px;
  overflow-y: auto;
  padding-right: 20px;

  ::v-deep p{
    text-indent: 2em;
    line-height: 20px;
    letter-spacing: 1px;
  }
}

.btnGrounp{
  width: 260px;
  height: 100px;
  position: fixed;
  top: 48%;
  right: 0;
}

::v-deep .el-form-item__content{
  line-height: 1;
}

.test-1::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.test-1::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 10px;
  box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.1);
  background   : #e8f3fe;
}
.test-1::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  background   : #fff;
}
</style>
