<template>
  <div>
    <ali-player
      ref="aliPlayer"
      :play-style="aliPlayerConfig.playStyle"
      :source="aliPlayerConfig.source"
      :cover="aliPlayerConfig.cover"
      :width="aliPlayerConfig.width"
      :height="aliPlayerConfig.height"
      :skin-layout="aliPlayerConfig.skinLayout"
      @ready="handleReadyVideo"
    />
    <i v-if="del" class="mycs-icon mycs-icon-del" @click="$emit('del')" />
  </div>
</template>
<script>
import AliPlayer from '@/components/Aliplayer/index.vue'
import { getVideoPreview, checkVideoStatus } from '@/api/biz'
import request from '@/api/activity'
import { initPlayerConfig } from '@/components/Aliplayer/aliplayerConfig'
// 个性化配置，可不设置，不设置则使用默认配置
export default {
  components: { AliPlayer },
  props: {
    videoFileId: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: '520px'
    },
    height: {
      type: String,
      default: '294px'
    },
    del: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      aliPlayerConfig: {},
      player: {},
      converted: false
    }
  },
  watch: {
    videoFileId: {
      handler(value) {
        if (value && value !== '') {
          this.converted = false
          checkVideoStatus({ videoFileIdList: [value] }).then(result => {
            if (result.length) {
              if (result[0].status === 2) {
                this.converted = true
              }
            }
            this.$nextTick(() => {
              this.play()
            })
          })
        } else {
          if (Object.keys(this.player).length !== 0) {
            this.player.pause()
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    play() {
      if (this.converted) {
        request.getVideo(this.videoFileId).then(res => {
          if (res.playInfoList && res.playInfoList.length) {
            const sourceUrl = {}
            res.playInfoList.reverse().map(v => {
              sourceUrl[v.definition] = v.playURL
            })
            this.aliPlayerConfig = {
              source: JSON.stringify(sourceUrl),
              width: this.width,
              height: this.height
            }
            initPlayerConfig(this.aliPlayerConfig).then(res1 => {
              this.aliPlayerConfig = res1
              this.$refs.aliPlayer.init(res.isAllowPlay)
            })
          } else {
            this.$message.error('该视频暂时无法播放，请稍后重试！')
          }
        })
      } else {
        getVideoPreview({ videoFileId: this.videoFileId }).then(res => {
          this.aliPlayerConfig = {
            source: res,
            width: this.width,
            height: this.height
          }
          initPlayerConfig(this.aliPlayerConfig).then(res1 => {
            this.aliPlayerConfig = res1
            this.$refs.aliPlayer.init(res.isAllowPlay)
          })
        })
      }
    },
    handleReadyVideo(val) {
      this.player = val
    }
  }
}
</script>

<style lang="scss" scoped>
.mycs-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 24px;
  color: #fe0137;
  cursor: pointer;
}
</style>
