export const initPlayerConfig = (config) => {
  return new Promise((resolve) => {
    const aliPlayerConfig = {
      playStyle: 'background: "#191919"',
      source: config.source || null,
      cover: config.cover || null,
      height: config.height || '600px',
      width: config.width || '400px',
      format: 'm3u8',
      skinLayout: [
        { name: 'bigPlayButton', align: 'cc' },
        {
          name: 'controlBar',
          align: 'blabs',
          x: 0,
          y: 0,
          children: [
            // h5
            { name: 'playButton', align: 'tl', x: 15, y: 12 },
            { name: 'timeDisplay', align: 'tl', x: 10, y: 7 },
            { name: 'fullScreenButton', align: 'tr', x: 10, y: 12 },
            { name: 'volume', align: 'tr', x: 5, y: 10 },
            { name: 'progress', align: 'tlabs', x: 0, y: 0 }
          ]
        },
        {
          name: 'fullControlBar',
          align: 'tlabs',
          x: 0,
          y: 0,
          children: [
            { name: 'fullTitle', align: 'tl', x: 25, y: 6 },
            { name: 'fullNormalScreenButton', align: 'tr', x: 24, y: 13 },
            { name: 'fullTimeDisplay', align: 'tr', x: 10, y: 12 },
            { name: 'fullZoom', align: 'cc' }
          ]
        },
        { name: 'errorDisplay', align: 'cc' }
      ]
    }
    resolve(aliPlayerConfig)
  })
}
