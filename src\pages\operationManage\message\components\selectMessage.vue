<template>
  <el-dialog
    title="选择消息"
    :visible.sync="dialogVisible"
    width="60%"
    center
    :before-close="handleClose"
  >
    <el-input
      v-model="listQuery.condition.title"
      placeholder="请输入标题关键字"
      class="input-with-select"
      style="width: 300px"
    >
      <el-button slot="append" icon="el-icon-search" @click="search" />
    </el-input>
    <el-select v-model="listQuery.condition.channel" placeholder="请选择" @change="search">
      <el-option label="App push" value="APP_PUSH" />
      <el-option label="站内信" value="STATION_MSG" />
    </el-select>
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        v-for="item in tableColumn"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.width"
        align="center"
      />
      <el-table-column prop="handle" label="操作" width="80" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="select(scope.row)"
          >选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </el-dialog>

</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getMessageTaskList } from '@/api/message'
export default {
  components: {
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    channel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      listQuery: {
        condition: {
          channel: this.channel,
          title: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      tableData: [],
      tableColumn: [
        { prop: 'title', label: '消息标题', width: '' },
        { prop: 'msgType', label: '消息类型', width: '40' },
        { prop: 'channel', label: '消息通道', width: '60' },
        { prop: 'statusStr', label: '消息状态', width: '40' },
        { prop: 'sendTime', label: '发送时间', width: '80' },
        { prop: 'userName', label: '创建人', width: '50' },
        { prop: 'createdTime', label: '创建时间', width: '80' }
      ],
      total: 0
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        this.getList()
      }
      this.listQuery.condition.title = ''
    },
    channel(v) {
      this.listQuery.condition.channel = v
    }
  },
  methods: {
    getList() {
      getMessageTaskList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.channel = v.appPushFlag && v.stationMsgFlag ? '站内信, App push' : (!v.appPushFlag && v.stationMsgFlag ? '站内信' : 'App push')
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
    },
    select(row) {
      this.$emit('update:msgId', row.id)
      this.$emit('update:msgTitle', row.title)
      this.$emit('update:channel', this.listQuery.condition.channel)
      this.handleClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.input-with-select {
  margin-bottom: 20px;
}
</style>
