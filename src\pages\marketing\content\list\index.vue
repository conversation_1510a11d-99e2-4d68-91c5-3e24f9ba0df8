<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        class="group"
        placeholder="内容名称/发布人/作者"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>

      <el-select
        v-model="listQuery.condition.type"
        placeholder="全部类型"
        @change="search"
      >
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-cascader
        v-model="listQuery.condition.cateId"
        :options="categoryIdsOptions"
        :props="categoryIdsProps"
        placeholder="内容分类"
        clearable
        @change="search"
      />

      <el-select
        v-model="listQuery.condition.upDownStat"
        placeholder="全部状态"
        @change="search"
      >
        <el-option
          v-for="item in stateOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.condition.source"
        placeholder="全部来源"
        @change="search"
      >
        <el-option
          v-for="item in sourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <h2>
      内容列表
      <div>
        <el-button type="primary" @click="addContent('Article')">新增文章</el-button>
        <el-button type="primary" @click="addContent('Video')">新增视频</el-button>
      </div>
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="upDown(scope.row.id,scope.row.upDown === 1 ? 0 : 1)"
            >{{ scope.row.upDown === 1 ? '下架' : '上架' }}</el-button>
            <el-button
              v-if="scope.row.upDown === 0"
              type="text"
              size="mini"
              @click="edit(scope.row)"
            >编辑</el-button>
            <el-button
              v-if="scope.row.upDown === 0 && scope.row.source === 0"
              type="text"
              size="mini"
              @click="del(scope.row.id)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getCategoryTreeList } from '@/api/category'
import { contentList, contentUpOrDown, contentDel } from '@/api/marketing/contentManage'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      stateOptions: [
        { label: '全部状态', value: null },
        { label: '上架', value: 1 },
        { label: '下架', value: 0 }
      ],
      sourceOptions: [
        { label: '全部来源', value: null },
        { label: '平台自建', value: 0 },
        { label: '任务征集', value: 1 },
        { label: '平台推广', value: 3 }
      ],
      typeOptions: [
        { label: '全部类型', value: null },
        { label: '文章', value: 'ARTICLE' },
        { label: '视频', value: 'VIDEO' }
      ],
      categoryIdsOptions: [],
      categoryIdsProps: {
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      listQuery: {
        condition: {
          cateId: null,
          keyword: '',
          source: null,
          type: null,
          upDownStat: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'title', label: '内容名称', width: '120' },
        { prop: 'articleTypeString', label: '内容类型', width: '60' },
        { prop: 'cateNames', label: '内容分类', width: '200' },
        { prop: 'author', label: '作者', width: '80' },
        { prop: 'viewNum', label: '浏览数', width: '50' },
        { prop: 'likeNum', label: '点赞数', width: '50' },
        { prop: 'cmtNum', label: '评论数', width: '50' },
        { prop: 'creatorName', label: '发布人', width: '60' },
        { prop: 'releaseTime', label: '发布时间', width: '60' },
        { prop: 'upDownString', label: '状态', width: '60' },
        { prop: 'sourceString', label: '来源', width: '60' },
        { prop: 'updateName', label: '最后修改人', width: '60' },
        { prop: 'updateTime', label: '最后修改时间', width: '60' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getcontentList()
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getcontentList()
    },
    getcontentList() {
      this.listQuery.condition.cateId = parseInt(this.listQuery.condition.cateId)
      contentList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.articleTypeString = this.typeOptions.find(i => i.value === v.articleType).label
          v.upDownString = this.stateOptions.find(i => i.value === v.upDown).label
          v.sourceString = this.sourceOptions.find(i => i.value === v.source).label
          v.author = `${v.authorName} ${v.academic} ${v.company} ${v.department}`
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getcontentList()
    },
    addContent(type) {
      this.$router.push(
        {
          name: `MarketingContent${type}Add`
        }
      )
    },
    upDown(id, upDown) {
      contentUpOrDown({ id, upDown }).then(() => {
        this.getcontentList()
      })
    },
    del(id) {
      contentDel(id).then(() => {
        this.getcontentList()
      })
    },
    edit(row) {
      const type = row.articleType === 'ARTICLE' ? 'Article' : 'Video'
      this.$router.push(
        {
          name: `MarketingContent${type}Add`,
          query: { id: row.id }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
