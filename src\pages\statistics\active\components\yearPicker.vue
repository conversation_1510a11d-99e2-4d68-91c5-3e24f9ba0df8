<template>
  <div class="picker-year">
    <el-form ref="year-range-picker" :model="formData" :rules="rules">
      <el-form-item prop="yearStart">
        <el-date-picker
          v-model="formData.yearStart"
          value-format="yyyyMMdd"
          :picker-options="pickerOptions"
          :placeholder="startPlaceholder"
          type="year"
          @change="val=>onDateChange(0,val)"
        />
      </el-form-item>
      <p>至</p>
      <el-form-item prop="yearEnd">
        <el-date-picker
          v-model="formData.yearEnd"
          value-format="yyyyMMdd"
          :picker-options="pickerOptions"
          :placeholder="endPlaceholder"
          type="year"
          @change="val=>onDateChange(1,val)"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'YearRangePicker',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    startPlaceholder: {
      type: String,
      default: ''
    },
    endPlaceholder: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formData: {
        yearStart: '',
        yearEnd: ''
      },
      rules: {
        yearStart: [
          { required: true, message: '请选择开始年份', trigger: 'change' }
        ],
        yearEnd: [
          { required: true, message: '请选择结束年份', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime() || time.getTime() < new Date('2020-1').getTime()
        }
      }
    }
  },
  watch: {
    value: {
      handler(newVal, oldValue) {
        if (newVal) {
          this.formData.yearStart = newVal[0]
          this.formData.yearEnd = newVal[1]
        }
      },
      immediate: true
    },
    formData: {
      handler(newVal, oldVal) {
        this.formData.yearStart = moment(newVal.yearStart).startOf('year').format('YYYYMMDD')
        this.formData.yearEnd = moment(newVal.yearEnd).endOf('year').format('YYYYMMDD')
      },
      deep: true
    }
  },
  methods: {
    onDateChange(index, value) {
      if (this.formData.yearStart !== '') {
        this.pickerOptions = {
          disabledDate: (time) => {
            return time.getTime() > new Date().getTime() || time.getTime() < new Date(this.formData.yearStart).getTime()
          }
        }
      } else if (this.formData.yearEnd !== '') {
        this.pickerOptions = {
          disabledDate: (time) => {
            return time.getTime() > new Date(this.formData.yearEnd).getTime() || time.getTime() < new Date('2020-1').getTime()
          }
        }
      }
      if (!value) {
        this.rules = {
          yearStart: [
            { required: false, message: '请选择开始年份', trigger: 'change' }
          ],
          yearEnd: [
            { required: false, message: '请选择结束年份', trigger: 'change' }
          ]
        }
        this.formData = {
          yearStart: '',
          yearEnd: ''
        }
        this.pickerOptions = {
          disabledDate: (time) => {
            return time.getTime() > new Date().getTime() || time.getTime() < new Date('2020-1').getTime()
          }
        }
      } else {
        this.rules = {
          yearStart: [
            { required: true, message: '请选择开始年份', trigger: 'change' }
          ],
          yearEnd: [
            { required: true, message: '请选择结束年份', trigger: 'change' }
          ]
        }
      }
      this.$refs['year-range-picker'].validate((valid) => {
        if (valid) {
          this.$emit('change', [this.formData.yearStart, this.formData.yearEnd])
          this.$emit('input', [this.formData.yearStart, this.formData.yearEnd])
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.picker-year {
  .el-form {
    display: flex;
    align-items: center;
    .el-form-item {
      margin: 0
    }
    p {
      margin: 0 10px;
    }
    .el-date-editor {
      width: 132px;
    }
  }
}
</style>
