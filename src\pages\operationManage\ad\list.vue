<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title">广告列表</header>
    </div>
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.advertisementName" placeholder="请输入广告名称/广告主" clearable @keyup.enter.native="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.playPlaceId" filterable clearable placeholder="请选择广告位置" @change="handleFilter">
          <el-option v-for="item in adPlaceList" :key="item.adPlaceId" :label="item.place" :value="item.adPlaceId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.adMaterialType" filterable clearable placeholder="请选择广告素材类型" @change="handleFilter">
          <el-option v-for="item in adMaterialTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.adTypeId" filterable clearable placeholder="请选择广告落地页类型" @change="handleFilter">
          <el-option v-for="item in adTypeList" :key="item.adTypeId" :label="item.name" :value="item.adTypeId" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.status" filterable clearable placeholder="请选择广告状态" @change="handleFilter">
          <el-option v-for="item in adStateList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="add">添加广告</el-button>
      </div>
    </div>
    <!-- body -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="img" slot-scope="{row}">
        <el-image
          v-if="row.adMaterialTypeStr !== '小视频'"
          class="cover-img"
          :src="row.imgUrl"
          :preview-src-list="[row.imgUrl]"
        />
        <div v-else class="video-cover-img" @click="contentDetail(row,'adMaterial')">
          <img
            class="cover-img"
            :src="`${row.imgUrl}?x-oss-process=video/snapshot,t_0000,f_png,w_200,h_200,m_fast`"
          >
          <i class="el-icon-video-play" />
        </div>
      </template>
      <template slot="detail" slot-scope="{row}">
        <el-button class="title" size="mini" type="text" @click="toDetail(row)">{{ row.name }}</el-button>
      </template>
      <template slot="content" slot-scope="{row}">
        <div v-if=" row.adTypeId === 1 && row.content.includes('Android-') && row.content.includes('|ios-')">
          <el-button
            class="title"
            size="mini"
            type="text"
          >
            <span>Android: </span>
            <a :href="row.content | Android" target="_blank">{{ row.content | Android }}</a>
          </el-button>
          <el-button
            class="title"
            size="mini"
            type="text"
          >
            <span>ios: </span>
            <a :href="row.content | ios" target="_blank">{{ row.content | ios }}</a>
          </el-button>
        </div>
        <el-button
          v-else
          class="title"
          size="mini"
          type="text"
          @click="contentDetail(row)"
        >{{ [6,7,8,9,10,11].includes(row.adTypeId) ? row.contentName :row.content }}</el-button>
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.status | statusFmt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <!-- 3-待上架 1-已下架 2-已上架 -->
        <el-button v-if="[3].includes(row.status)" size="mini" type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="[3,1].includes(row.status)" size="mini" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button v-if="[3,1].includes(row.status)" size="mini" type="text" @click="onShelf(row)">上架</el-button>
        <el-button v-if="row.status===2" size="mini" type="text" @click="offShelf(row)">下架</el-button>
        <el-button v-if="[1,2].includes(row.status)" size="mini" type="text" @click="viewStatistics(row)">广告统计</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="pager.page" @pagination="handlePagination" />

    <video-preview :video-id.sync="videoId" :video-file-id.sync="videoFileId" />
    <lesson-video :id="lessonId" :show.sync="showLessonVideo" @close="(v)=>{showLessonVideo = v}" />
  </div>
</template>

<script>
import request, { adType, adPlace } from '@/api/ad'
import table from '@/mixins/table'
import VideoPreview from '@/components/Aliplayer/clickPreview.vue'
import lessonVideo from './components/lessonVideo.vue'

const columns = [
  { props: { type: 'selection', width: '55', align: 'center' }},
  { props: { type: 'index', width: '55', label: '排序', align: 'center' }
  },
  { props: { label: '广告素材', align: 'center', width: '130' }, slot: 'img' },
  { props: { label: '广告名称', align: 'center' }, slot: 'detail' },
  { props: { label: '广告主', align: 'center', prop: 'advertiserName' }},
  { props: { label: '广告落地页', align: 'center' }, slot: 'content' },
  { props: { label: '广告素材类型', align: 'center', prop: 'adMaterialTypeStr' }},
  { props: { label: '广告落地页类型', align: 'center', prop: 'adType' }},
  { props: { label: '广告位置', align: 'center', prop: 'playPlace' }},
  { props: { label: '状态', align: 'center' }, slot: 'status' },
  { props: { label: '修改人', prop: 'updateUsername', align: 'center' }},
  { props: { label: '修改时间', prop: 'updateTime', align: 'center' }},
  { props: { align: 'center', label: '操作', width: '240px' }, slot: 'actions'
  }
]

export default {
  name: 'Ad',
  filters: {
    statusFmt(v) {
      const arr = ['下架', '上架', '待上架']
      return arr[v - 1]
    },
    Android(v) {
      return v.match(/Android-(\S*)\|ios-/)[1]
    },
    ios(v) {
      return v.match(/\|ios-(\S*)/)[1]
    }
  },
  components: { VideoPreview, lessonVideo },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      // select list
      adTypeList: [],
      adMaterialTypeList: [
        { value: 1, label: '静态图片' },
        { value: 2, label: '动态图片' },
        { value: 3, label: '小视频' }
      ],
      adPlaceList: [],
      adStateList: [
        { value: 1, name: '下架' },
        { value: 2, name: '上架' },
        { value: 3, name: '待上架' }
      ],
      condition: {
        advertisementName: ''
      },
      conditionWatch: {
        adTypeId: '',
        adMaterialType: '',
        playPlaceId: '',
        status: ''
      },
      mainKey: 'advertisementId',
      editPath: 'addAd',
      videoId: '',
      lessonId: '',
      videoFileId: '',
      showLessonVideo: false
    }
  },
  created() {
    // get select list
    Promise.all([adType(), adPlace()]).then(res => {
      [this.adTypeList, this.adPlaceList] = res
    })
  },
  methods: {
    // edit row
    editRow(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          isOffshelf: row.status === 1 ? 1 : 0, // 下架仅可编辑部分
          isEdit: 1,
          id: row[this.mainKey]
        }
      })
    },
    viewStatistics(row) {
      this.$router.push({
        path: 'statistics',
        query: {
          id: row.advertisementId,
          name: row.name
        }
      })
    },
    contentDetail(row, source) {
      if (source === 'adMaterial') {
        this.videoFileId = row.materialId
      } else {
        if (row.adTypeId === 6) {
          this.videoId = row.content
        } else if (row.adTypeId === 7) {
          this.showLessonVideo = true
          this.lessonId = row.content
        } else {
          window.open(row.content, '_blank')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.video-cover-img {
  cursor: pointer;
  position: relative;
  i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
  }
}
.cover-img{
  width: 100px;
  height: auto;
  object-fit: cover;
}
.el-button.title{
  white-space: normal;
  margin-left: 0;
  span {
    color: #606266;
  }
}
</style>
