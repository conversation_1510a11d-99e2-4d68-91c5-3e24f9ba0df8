<template>
  <section class="popularize">
    <div class="handle">
      <el-radio-group v-model="columnType" @input="getList()">
        <el-radio-button :label="1">推荐栏目</el-radio-button>
        <el-radio-button :label="2">视频栏目</el-radio-button>
      </el-radio-group>
      <div>
        <el-button type="primary" @click="oneKeyPut">一键投放</el-button>
        <el-button v-show="columnType === 2" type="primary" @click="copy()">复制推荐栏目推广视频</el-button>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{background:'#f9f9f9',color:'#333'}"
      style="width: 100%"
    >
      <el-table-column
        v-for="item in tableColumn"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.width"
      />
      <el-table-column prop="handle" label="操作">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.bizId === 0 && scope.row.recommendIsadvert === 0"
            type="text"
            size="mini"
            @click="add(scope.row.id,scope.row.recommendType)"
          >添加</el-button>
          <el-button
            v-if="scope.row.bizId !== 0 || scope.row.recommendIsadvert === 1"
            type="text"
            size="mini"
            @click="clear(scope.row.id)"
          >清除</el-button>
          <el-button
            v-if="scope.row.recommendType === 2 && scope.row.recommendIsadvert === 0 && scope.row.bizId === 0"
            type="text"
            size="mini"
            @click="set(scope.row.id)"
          >设为广告位</el-button>
        </template>
      </el-table-column>
    </el-table>

    <addContent
      :id.sync="recommenId"
      :dialog-visible.sync="dialogVisible"
      :type.sync="recommenType"
      :excluded-ids="excludedIds"
    />
  </section>
</template>

<script>
import { popularizeList, clearColumn, setAdvertColumn, copy2Video, oneKeyAdvertising } from '@/api/marketing/popularize'
import addContent from '@/pages/marketing/content/list/components/addContent'
export default {
  components: { addContent },
  data() {
    return {
      columnType: 1,
      tableColumn: [
        { prop: 'recommendNo', label: '推广位编号', width: '40' },
        { prop: 'recommendTypeString', label: '推广类型', width: '40' },
        { prop: 'recommendTitle', label: '推广内容标题', width: '200' },
        { prop: 'recommendTime', label: '推广时间', width: '110' },
        { prop: 'targetClickNum', label: '点击量要求', width: '50' },
        { prop: 'currentClickNum', label: '当前点击量', width: '50' }
      ],
      tableData: [],
      recommenId: '',
      recommenType: 1,
      dialogVisible: false,
      excludedIds: []
    }
  },
  watch: {
    dialogVisible(v) {
      if (!v) {
        this.getList()
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      popularizeList({ columnType: this.columnType }).then(res => {
        this.excludedIds = []
        res.forEach(i => {
          i.recommendTypeString = i.recommendType === 1 ? '文章' : '视频/广告'
          i.recommendTime = i.bizId === 0 ? '' : (i.recommendStartTime !== '' ? `${i.recommendStartTime} ~ ${i.recommendEndTime}` : '不限')
          i.targetClickNum = i.bizId === 0 ? '' : (i.targetClickNum === 0 ? '不限' : i.targetClickNum)
          i.currentClickNum = i.bizId === 0 ? '' : i.currentClickNum
          if (i.bizId !== 0) {
            this.excludedIds.push(i.bizId)
          }
        })
        this.tableData = res
      })
    },
    copy() {
      copy2Video({ force: 1 }).then(() => {
        this.$message.success('复制成功')
        this.getList()
      })
    },
    add(id, type) {
      this.recommenId = id
      this.recommenType = type
      this.dialogVisible = true
    },
    clear(id) {
      this.$confirm('推广目标未达成, 确定清除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        clearColumn({ id }).then(() => {
          this.getList()
        })
      })
    },
    set(id) {
      setAdvertColumn({ id }).then(() => {
        this.getList()
      })
    },
    oneKeyPut() {
      oneKeyAdvertising({ columnType: this.columnType }).then(res => {
        this.$message.success(`成功投放${res}条文章/视频`)
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.popularize {
  padding: 50px;
  .handle {
    margin-bottom: 40px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
