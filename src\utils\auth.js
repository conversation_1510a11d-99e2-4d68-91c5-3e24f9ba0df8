import Cookies from 'js-cookie'

const TokenKey = 'cmsToken'
const Path = '/'
const Domain = window.Domain

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token, { path: Path, domian: Domain })
}

export function removeToken() {
  return Cookies.remove(TokenKey, { path: Path, domian: Domain })
}

export function setCookie(key, value) {
  return Cookies.set(key, value, { path: Path, domian: Domain })
}

export function getCookie(key) {
  return Cookies.get(key, { path: Path, domian: Domain })
}

export function removeCookie(key) {
  return Cookies.remove(key, { path: Path, domian: Domain })
}

export function setBatchCookies(obj = {}) {
  Object.keys(obj).forEach((v, i) => {
    Cookies.set(v, obj[v], { path: Path, domian: Domain })
  })
}
