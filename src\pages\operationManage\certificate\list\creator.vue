<template>
  <div class="app-container">
    <el-form ref="form" :model="form" label-width="120px" :rules="rules" :disabled="type === 'view'">
      <h2>基本信息</h2>
      <el-form-item label="证书名称:" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入证书名称"
          maxlength="50"
        />
      </el-form-item>
      <el-form-item label="截止日期:" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          value-format="yyyy-MM-dd [23:59:59]"
          placeholder="选择截止日期"
        />
      </el-form-item>
      <el-form-item label="培训期限:" prop="limitDays">
        <el-input-number v-model="form.limitDays" controls-position="right" :controls="false" :min="0" step-strictly />&nbsp;&nbsp;&nbsp;天
        <span class="hint">
          <i class="el-icon-warning" />
          默认0为不限; 其他正整数按实际限制
        </span>
      </el-form-item>
      <el-form-item label="培训名额:" prop="limitUsers">
        <el-input-number v-model="form.limitUsers" controls-position="right" :controls="false" :min="0" step-strictly />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <span class="hint">
          <i class="el-icon-warning" />
          默认0为不限; 其他正整数按实际限制
        </span>
      </el-form-item>
      <el-form-item label="封面:" prop="coverImgId">
        <UploadPic :key="form.coverImgUrl" width="300px" tips="上传图片" :pic-id.sync="form.coverImgId" :url="form.coverImgUrl" />
      </el-form-item>
      <el-form-item label="证书编号:" required>
        <span style="margin:0 10px 0 0;">前缀</span>
        <el-input
          v-model="form.numberPrefix"
          placeholder="20个任意大小写数字字母组合"
          maxlength="20"
          oninput="value=value.replace(/[^\w\.\/]/ig,'')"
          @blur="form.numberPrefix = $event.target.value"
        />
        <span style="margin:0 10px 0 20px;">流水号</span>
        <el-input-number v-model="form.numberSuffixBit" placeholder="位数" step-strictly :controls="false" :min="1" :max="5" />
      </el-form-item>
      <el-form-item label="证书文案:" prop="certRemark">
        <el-input
          v-model="form.certRemark"
          :rows="6"
          type="textarea"
          placeholder="请输入"
          maxlength="300"
          show-word-limit
        />
      </el-form-item>
      <el-form-item label="证书章:" prop="certSealList">
        <el-checkbox-group
          v-model="form.certSealList"
        >
          <el-checkbox label="名医传世" />
          <el-checkbox label="广东省医学教育协会" />
          <el-checkbox label="中国医学教育协会" />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="证书培训分类:" prop="categoryIdList">
        <el-cascader
          v-model="form.categoryIdList"
          :options="cateOptions"
          :props="{
            multiple: true,
            emitPath: false,
            label: 'name',
            value: 'categoryId'
          }"
          collapse-tags
          clearable
        />
      </el-form-item>
      <el-form-item label="发证单位:" prop="issueOrgz">
        <el-input
          v-model="form.issueOrgz"
          placeholder="请输入"
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="执行方:" prop="executeOwner">
        <el-input
          v-model="form.executeOwner"
          placeholder="请输入"
          maxlength="100"
        />
      </el-form-item>
      <el-form-item label="价格:" required prop="joinFee">
        <el-radio-group v-model="joinFeeType">
          <el-radio :label="0">免费</el-radio>
          <el-radio :label="1">收费</el-radio>
        </el-radio-group>
        <span v-if="joinFeeType === 1" class="hint">
          ¥
          <el-input-number
            v-model="form.joinFee"
            :controls="false"
            :precision="2"
            style="width: 90px"
          />
        </span>
      </el-form-item>
      <el-form-item label="纸质证书费用:" required prop="paperFee">
        ¥ <el-input-number
          v-model="form.paperFee"
          :controls="false"
          :precision="2"
          style="width: 90px"
        />
      </el-form-item>
      <el-form-item label="分享底图:" prop="shareImgId">
        <UploadPic :key="form.shareImgUrl" width="300px" tips="上传图片" :pic-id.sync="form.shareImgId" :url="form.shareImgUrl" />
      </el-form-item>
      <el-form-item label="证书模板:" prop="certTemplateId">
        <UploadPic :key="form.certTemplateUrl" width="300px" tips="上传图片" :pic-id.sync="form.certTemplateId" :url="form.certTemplateUrl" />
      </el-form-item>
      <el-form-item label="培训介绍:" prop="joinNotes">
        <editor
          v-model="form.description"
          :disabled="type === 'view'"
          height="200"
          style="width: 800px"
        />
      </el-form-item>
      <el-form-item label="报名须知:" prop="joinNotes">
        <el-input
          v-model="form.joinNotes"
          :rows="6"
          type="textarea"
          placeholder="请输入报名须知"
          maxlength="300"
          show-word-limit
          style="width: 800px"
        />
      </el-form-item>
      <h2>培训目录</h2>
      <el-form-item label="培训目录:" required>
        <el-button type="primary" @click="selectTrainPlan">选培训计划</el-button>
        <div v-for="(item,index) in form.certExamineCourseList" :key="index" class="content-container">
          <h2>
            <div>
              第{{ item.directoryNo }}章
              <el-input
                v-model="form.certExamineCourseList[index].directoryName"
                placeholder="章名称"
                maxlength="100"
              />
            </div>
            <div>
              <el-button v-if="index !== 0" type="info" @click="sortUpDirectory(index)">
                <i class="el-icon-arrow-up" />
              </el-button>
              <el-button v-if="index !== form.certExamineCourseList.length - 1" type="info" @click="sortDownDirectory(index)">
                <i class="el-icon-arrow-down" />
              </el-button>
              <el-button type="danger" @click="handleDelDirectory(index)">
                <i class="el-icon-delete" />
              </el-button>
            </div>
          </h2>
          <el-table :data="form.certExamineCourseList[index].children" fit size="mini" style="width: 100%; margin-bottom: 20px">
            <el-table-column align="center" type="index" label="序号" />
            <el-table-column align="center" prop="courseName" label="教程名称" />
            <el-table-column align="center" prop="courseDuration" label="时长" />
            <el-table-column align="center" label="正确率要求">
              <template slot-scope="{row}">
                <el-input-number v-model="row.courseAccuracy" :precision="0" controls-position="right" :min="10" :max="100" style="width: 120px" @change="inputChange(row)">
                  <template slot="append">%</template>
                </el-input-number>
                %
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
              <template slot-scope="scope">
                <el-button v-if="scope.$index !== 0" type="info" @click="sortUp(item.directoryNo, scope.row.courseSeq)">
                  <i class="el-icon-arrow-up" />
                </el-button>
                <el-button v-if="scope.$index !== form.certExamineCourseList[index].children.length - 1" type="info" @click="sortDown(item.directoryNo, scope.row.courseSeq)">
                  <i class="el-icon-arrow-down" />
                </el-button>
                <el-button type="danger" @click="handleDelCourse(item.directoryNo, scope.row)">
                  <i class="el-icon-delete" />
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-form-item label class="text-center add-course-btn">
            <el-button icon="el-icon-plus" @click="showDialogVisible(item.directoryNo, item.directoryName)">添加节</el-button>
          </el-form-item>
        </div>
        <div style="text-align: center; margin-top: 30px">
          <el-button
            style="width: 320px"
            type="primary"
            icon="el-icon-plus"
            @click="form.certExamineCourseList.push({ directoryName: '', directoryNo: form.certExamineCourseList.length + 1, children: [] })"
          >添加章</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div style="text-align: center">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button v-if="type !== 'view'" type="primary" @click="save()">保存</el-button>
    </div>

    <select-train-content
      :show.sync="dialogVisible"
      :certificate="true"
      :selected-list="selectedList"
      :selected-list-info-id="selectedListInfoId"
      @handleSelect="handleDialogSelect"
      @handleSubmit="handleDialogSubmit"
      @handleCancel="handleDialogCancel"
    />

    <selectPlan
      :dialog-visible.sync="dialogVisiblePlan"
      @handleSelect="getCourseList"
    />
  </div>
</template>

<script>
import selectTrainContent from '@/pages/operationManage/jobTrain/components/selectTrainContent' // 内容选择
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Editor from '@/components/wangEditor'
import selectPlan from '@/pages/operationManage/certificate/components/selectPlan'
import { getCategoryTreeList } from '@/api/category'
import { certExamineCreate, certExamineDetail, certExamineEdit } from '@/api/certificate'
export default {
  components: {
    selectTrainContent,
    UploadPic,
    Editor,
    selectPlan
  },
  filters: {
    filterTableIndex(val) {
      if (val === 0) {
        return '--'
      }
      return val
    }
  },
  data() {
    return {
      type: this.$route.query.type,
      form: {
        certId: '',
        name: '',
        endTime: '',
        limitDays: 0,
        limitUsers: 0,
        numberPrefix: '',
        numberSuffixBit: null,
        certRemark: '',
        certSealList: [],
        categoryIdList: [],
        issueOrgz: '',
        executeOwner: '',
        joinFee: 0,
        paperFee: 0,
        shareImgId: '',
        certTemplateId: '',
        description: '',
        joinNotes: '',
        certExamineCourseList: [
          { directoryName: '', directoryNo: 1, children: [] }
        ]
      },
      rules: {
        name: [{ required: true, message: '请输入证书名称', trigger: 'blur' }],
        endTime: [{ required: true, message: '请选择截止日期', trigger: 'change' }],
        limitDays: [{ required: true, message: '请填写培训期限', trigger: 'blur' }],
        limitUsers: [{ required: true, message: '请填写培训名额', trigger: 'blur' }],
        coverImgId: [{ required: true, message: '请上传封面', trigger: 'change' }],
        numberPrefix: [{ required: true, message: '请输入证书编号前缀', trigger: 'blur' }],
        numberSuffixBit: [{ required: true, message: '请输入证书编号流水号', trigger: 'blur' }],
        certRemark: [{ required: true, message: '请输入证书文案', trigger: 'blur' }],
        certSealList: [{ type: 'array', required: true, message: '请至少选择一个证书章', trigger: 'change' }],
        categoryIdList: [{ required: true, message: '请选择证书培训分类', trigger: 'change' }],
        issueOrgz: [{ required: true, message: '请填写发证单位', trigger: 'blur' }],
        executeOwner: [{ required: true, message: '请填写执行方', trigger: 'blur' }],
        shareImgId: [{ required: true, message: '请上传分享底图', trigger: 'change' }],
        certTemplateId: [{ required: true, message: '请上传证书模板', trigger: 'change' }],
        description: [{ required: true, message: '请输入培训介绍', trigger: 'blur' }],
        joinNotes: [{ required: true, message: '请输入报名须知', trigger: 'blur' }]
      },
      cateOptions: [],
      joinFeeType: 0,
      directoryNo: 3,
      directoryName: '',
      contentList: [
        { directoryName: '', directoryNo: 1 }
      ],
      dialogVisible: false,
      dialogVisiblePlan: false,
      selectedList: [], // 选中的任务内容列表
      selectedListTemp: [],
      selectedListInfoId: []
    }
  },
  watch: {
    joinFeeType: {
      handler(val) {
        if (val === 0) {
          this.joinFee = 0
        }
      },
      immediate: true
    }
  },
  created() {
    if (this.type === 'create') {
      this.$route.meta.title = '创建'
    } else {
      this.$route.meta.title = this.type === 'edit' ? '编辑' : '证书详情'
      certExamineDetail(this.$route.query.id).then(res => {
        let list = []
        res.certExamineCourseList.forEach((val, index) => {
          const courseIdList = val.children.map(v => {
            return v.courseId
          })
          list = list.concat(courseIdList)
          if (index === res.certExamineCourseList.length - 1) {
            this.selectedList = list
          }
        })
        this.form = res
      })
    }
    getCategoryTreeList(0).then(res => {
      this.cateOptions = res
    })
  },
  methods: {
    selectTrainPlan() {
      if (this.form.certExamineCourseList.length === 0 || (this.form.certExamineCourseList.length === 1 && this.form.certExamineCourseList[0].children.length === 0)) {
        this.dialogVisiblePlan = true
      } else {
        this.$confirm(`选择培训计划将清空当前已生成和选择的章节, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dialogVisiblePlan = true
        })
      }
    },
    showDialogVisible(directoryNo, directoryName) {
      this.directoryNo = directoryNo
      this.directoryName = directoryName
      this.dialogVisible = true
    },
    // 窗口选中教程/sop
    handleDialogSelect(val, isMessage) {
      this.selectedList.push(val)
      this.selectedListTemp.push(val)
      if (!isMessage) {
        this.$message.success('已添加该资源内容')
      }
    },
    // 教程去重
    removeRepeatCourse(remoteArr, localArr) {
      let repeat = false
      remoteArr.forEach(ra => {
        localArr.forEach((la, i) => {
          if (la.courseId === ra.courseId) {
            repeat = true
            this.$message({
              type: 'error',
              message: '您选中的教程或SOP包含了重复的教程"' + ra.courseName + '",已自动删除该重复教程!'
            })
            localArr.splice(i, 1)
            this.selectedList.splice(this.selectedList.indexOf(la.courseId), 1)
            if (!repeat) {
              localArr.push(ra)
            }
          }
        })
      })
      repeat = false
      return localArr
    },
    // 窗口点击确定
    handleDialogSubmit(val) {
      let allList = []
      if (val && val.length) {
        this.form.certExamineCourseList.map(i => {
          allList = allList.concat(i.children)
        })
        val = this.removeRepeatCourse(allList, val)
        val.forEach(v => {
          v.courseSeq = v.courseOrder
          this.form.certExamineCourseList[this.directoryNo - 1].children.push(v)
        })
        this.form.certExamineCourseList[this.directoryNo - 1].children = this.reorderListOrder(this.form.certExamineCourseList[this.directoryNo - 1].children)
        const courseIds = allList.concat(val).map(item => item.courseId)
        this.selectedList = this.selectedList.filter(v => courseIds.includes(v))
        this.selectedListTemp = []
      }
    },
    // 窗口点击取消
    handleDialogCancel() {
      let allList = []
      this.form.certExamineCourseList.forEach(i => {
        allList = allList.concat(i.children)
      })
      const courseIds = allList.map(item => item.courseId)
      this.selectedList = this.selectedList.filter(v => courseIds.includes(v))
      this.selectedListTemp = []
    },
    swapArray(arr, index1, index2) {
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    },
    reorderListOrder(arr, type) {
      let index = 0
      arr.map(v => {
        index = index + 1
        if (type === 'directory') {
          v.directoryNo = index
        } else {
          v.courseSeq = index
        }
      })
      return arr
    },
    inputChange(row) {
      if (!row.accuracy) {
        row.accuracy = 10
      }
    },
    sortUp(directoryNo, courseSeq) {
      const index = courseSeq - 1
      this.swapArray(this.form.certExamineCourseList[directoryNo - 1].children, index, index - 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.certExamineCourseList[directoryNo - 1].children)
    },
    sortUpDirectory(index) {
      this.swapArray(this.form.certExamineCourseList, index, index - 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.certExamineCourseList, 'directory')
    },
    sortDown(directoryNo, courseSeq) {
      const index = courseSeq - 1
      this.swapArray(this.form.certExamineCourseList[directoryNo - 1].children, index, index + 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.certExamineCourseList[directoryNo - 1].children)
    },
    sortDownDirectory(index) {
      this.swapArray(this.form.certExamineCourseList, index, index + 1)
      this.$forceUpdate()
      this.reorderListOrder(this.form.certExamineCourseList, 'directory')
    },
    handleDelCourse(directoryNo, row) {
      const list = this.form.certExamineCourseList[directoryNo - 1].children
      const courseId = row.courseId
      list.forEach((v, i) => {
        if (v.courseId === courseId) {
          list.splice(i, 1)
        }
      })
      this.selectedList = this.selectedList.filter(v => v !== courseId)
      this.form.certExamineCourseList[directoryNo - 1].children = this.reorderListOrder(list)
      this.$forceUpdate()
    },
    handleDelDirectory(index) {
      const list = this.form.certExamineCourseList[index].children
      const courseIdList = list.map(v => {
        return v.courseId
      })
      this.selectedList = this.selectedList.filter((v) => !courseIdList.includes(v))
      this.form.certExamineCourseList.splice(index, 1)
      this.reorderListOrder(this.form.certExamineCourseList, 'directory')
      this.$forceUpdate()
    },
    getCourseList(list) {
      this.form.certExamineCourseList = list
    },
    save() {
      let full = true
      let count = 0
      this.form.certExamineCourseList.forEach(item => {
        if (item.children.length < 1) {
          full = false
          this.$message.error(`第${item.directoryNo}章培训内容不能为空`)
        }
        count++
        if (count === this.form.certExamineCourseList.length) {
          if (full) {
            this.$refs.form.validate(valid => {
              if (valid) {
                const API = this.type === 'create' ? certExamineCreate : certExamineEdit
                API(this.form).then(() => {
                  this.$message.success(`${this.type === 'create' ? '创建' : '编辑'}成功`)
                  this.$router.push({ name: 'CertificateList' })
                })
              }
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .el-form-item {
    h2 {
      display: flex;
      justify-content: space-between;
    }
    .el-input,
    .el-textarea {
      width: 500px;
    }

    .el-table {
      border: 1px solid #dfe6ec;
      border-bottom: none;
      margin-bottom: 0!important;
      &::v-deep .el-table__header {
        th {
          background: #f6f9fb;
        }
      }
    }
    .add-course-btn {
      margin-bottom: 0;
      padding-top: 20px;
      padding-bottom: 20px;
      border: 1px solid #dfe6ec;
      border-top: none;
      .el-button {
        width: 320px;
        height: 40px;
        background-color: #ffffff;
        border-radius: 3px;
        border: 1px dashed #409EFF;
        color: #409EFF;
      }
    }
    .hint {
      margin-left: 20px;
      i {
        color: #409EFF;
      }
    }
  }
}
</style>
