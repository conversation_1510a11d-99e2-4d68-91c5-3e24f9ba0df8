<template>
  <div v-loading="loading" class="chart-wrapper">
    <div v-show="hasData" id="mapChart" style="height:640px" />
    <div v-show="!hasData" class="empty">暂无数据</div>
  </div>
</template>

<script>
import cityMap from './js/china-main-city-map'
import axios from 'axios'
import * as echarts from 'echarts/core'
import { MapChart } from 'echarts/charts'
import {
  TooltipComponent,
  VisualMapComponent,
  GeoComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
echarts.use([
  MapChart,
  CanvasRenderer,
  TooltipComponent,
  VisualMapComponent,
  GeoComponent
])

export default {
  name: 'StatisticsChinaMap',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    mapCode: {
      type: String,
      default: '100000'
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      loading: false,
      myChart: null,
      hasData: true,
      mapName: 'china2', // 上级区名(不显示南海诸岛加2)
      max: 0,
      level: 1,
      mapCode1: '100000'
    }
  },
  watch: {
    data: {
      handler(arr) {
        if (arr.length === 0) {
          this.hasData = false
          return
        } else {
          this.hasData = true
          this.findMax(arr)
          this.init()
        }
      }
    }
  },
  mounted() {
    this.myChart = echarts.init(document.getElementById('mapChart'))
    window.addEventListener('resize', () => {
      this.myChart.resize()
    })
  },
  beforeDestroy() {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    findMax(arr) {
      let max = 0
      arr.forEach(item => {
        if (item.num > max) {
          max = item.num
        }
      })
      this.max = parseInt(max)
    },
    init() {
      const i = Object.values(cityMap).findIndex(
        v => v === this.mapCode
      )
      this.mapName = Object.keys(cityMap)[i] || 'china2'
      this.initChart(this.mapName, this.mapCode)
    },
    /**
     * 初始化画布
     * @param {string} mapName 地图名称-registerMap第一参数与series中的map属性必须对应
     * @param {string} mapCode 地图areaCode请求本地对应json用, 从后台辖区排名接口获取
     */
    initChart(mapName, mapCode) {
      this.loading = true
      axios
        .get('/assets/json/map/' + mapCode + '.json', {})
        .then(({ data: mapData }) => {
          echarts.registerMap(mapName, mapData)
          const data = this.initMapData(mapData.features)
          this.setOption(mapName, data)
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * 绘制画布
     * @param {string} mapName 地图名称-registerMap第一参数与series中的map属性必须对应
     * @param {Array} data 图表数据
     */
    setOption(mapName, data) {
      this.loading = true
      this.myChart.clear() // 如果地图位置在可视区域外开启此行
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      this.myChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: `{b0}<br />${typeArr[this.type]}：{c0}${[8, 9, 10, 11, 13].includes(this.type) ? '%' : ''}`,
          backgroundColor: 'rgba(0,0,0,.7)',
          borderWidth: 0,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          transitionDuration: 0.1,
          padding: [6, 11],
          confine: true
        },
        visualMap: {
          calculable: true, // 拖拽手柄(与前两项配置不共用)
          right: '5%',
          align: 'left',
          min: 0,
          max: this.max === 0 ? 1000 : this.max,
          inRange: {
            color: ['#e6edf8', '#a4c3f5', '#8fb4f0', '#73A0E6', '#567EDF', '#274C96']
          },
          itemWidth: 10,
          itemHeight: 100
        },
        series: [
          {
            type: 'map',
            map: mapName,
            roam: true,
            scaleLimit: {
              min: 0.8,
              max: 4
            },
            itemStyle: {
              areaColor: '#f2fbf6',
              borderColor: '#fff',
              borderWidth: 1.1
            },
            emphasis: {
              itemStyle: {
                areaColor: '#a7cdfd'
              }
            },
            selectedMode: false,
            label: {
              show: true,
              textStyle: {
                color: '#666666'
              }
            },
            data: data
          }
        ]
      })
      this.loading = false
    },
    // 初始化图表数据
    initMapData(mapJson) {
      const data = JSON.parse(JSON.stringify(this.data))
      const mapData = mapJson.map(v => ({
        name: v.properties.name,
        value: 0
      }))
      for (const v of data) {
        for (const sv of mapData) {
          if (v.name.indexOf(sv.name) !== -1) {
            sv.value = v.num
            sv.areaCode = v.code
          }
        }
      }
      return mapData
    },
    // download方法用于被父组件点击下载时调用
    // 将echarts图表转换为canvas,并将canvas下载为图片
    download() {
      const aLink = document.createElement('a')
      const blob = this.base64ToBlob()
      const evt = document.createEvent('HTMLEvents')
      evt.initEvent('click', true, true)
      aLink.download = '区域分布'
      aLink.href = URL.createObjectURL(blob)
      aLink.click()
    },
    base64ToBlob() { // 将base64转换blob
      const img = this.exportImg()
      const parts = img.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    exportImg() { // echarts返回一个 base64的URL
      const myChart = echarts.init(
        document.getElementById('mapChart')
      )
      return myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  position: relative;
  z-index: 1;

  .btn {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
  }

  .empty{
    width: 100%;
    height: 640px;
    line-height: 640px;
    text-align: center;
    color: #999;
    font-size: 24px;
  }
}
</style>
