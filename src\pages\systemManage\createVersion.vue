<template>
  <div class="app-container">
    <el-form ref="form" :rules="rules" :model="form" label-width="200px" style="width:1000px;margin:50px 0 50px 100px">
      <el-form-item label="版本类型：" prop="typ">
        <el-select v-model="form.typ" placeholder="请选择版本类型">
          <el-option v-for="item in versionType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="更新类型：" prop="force">
        <el-select v-model="form.force" placeholder="请选择更新类型">
          <el-option v-for="item in updateType" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="版本号：" prop="version">
        <el-input v-model="form.version" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="版本编码：" prop="versionCode">
        <el-input v-model="form.versionCode" placeholder="请输入版本编码" @input="inputCode" />
      </el-form-item>
      <el-form-item label="版本文件地址：" prop="url" class="url">
        <el-input v-model="form.url" placeholder="请输入版本文件地址/上传文件" style="width:594px" />
        <Plupload @showFileName="showFileName" @showPercent="showPercent" @percent="percent" @uploadSuccess="uploadSuccess" />
        <div v-if="fileName" class="fileName">已选取文件名：{{ fileName }}</div>
      </el-form-item>
      <br>
      <el-form-item v-if="[2,3].includes(form.typ)" label="审核预估结束时间：">
        <el-date-picker
          v-model="date1"
          type="datetime"
          placeholder="请输入审核预估结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="changeDate"
        />
      </el-form-item>
      <el-form-item label="版本说明：" prop="msg">
        <el-input v-model="form.msg" type="textarea" placeholder="请输入版本说明" :rows="6" />
      </el-form-item>
    </el-form>
    <el-progress v-show="percentBox" style="width:400px;margin: 0 0 30px 480px" :text-inside="true" :stroke-width="24" :percentage="percentage" status="success" />
    <div style="margin-left:600px">
      <el-button type="primary" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" @click="confirm">创建</el-button>
    </div>
  </div>
</template>

<script>
import { svnAdd } from '@/api/systemManage'
import Plupload from './plupload.vue'
import { getApkAdress } from '@/api/biz'

export default {
  name: 'CreateVersion',
  components: { Plupload },
  data() {
    var validateURL = (rule, value, callback) => {
      if (this.form.url === '') {
        callback(new Error('请输入版本文件地址'))
      } else {
        callback()
      }
    }
    return {
      updateType: [
        { label: '强制更新', value: 1 },
        { label: '非强制更新', value: 0 }
      ],
      versionType: [
        { label: 'APK', value: 1 },
        { label: 'IOS(个人)', value: 2 },
        { label: 'IOS(企业)', value: 3 },
        { label: 'HarmonyOS', value: 4 }
      ],
      rules: {
        typ: [
          { required: true, message: '请选择版本类型', trigger: 'change' }
        ],
        force: [
          { required: true, message: '请选择更新类型', trigger: 'change' }
        ],
        version: [
          { required: true, message: '请输入版本号', trigger: 'blur' }
        ],
        versionCode: [
          { required: true, message: '请输入版本编码', trigger: 'blur' }
        ],
        url: [
          { validator: validateURL, trigger: 'blur' }
        ],
        msg: [
          { required: true, message: '请输入版本说明', trigger: 'blur' }
        ]
      },
      form: {
        typ: '',
        force: '',
        version: '',
        versionCode: '',
        url: '',
        msg: '',
        auditTime: ''
      },
      fileName: '',
      percentBox: false,
      percentage: 0,
      date1: ''
    }
  },
  methods: {
    inputCode(val) {
      this.form.versionCode = Number(val.replace(/[^\d]/g, ''))
    },
    changeDate(val) {
      this.form.auditTime = val
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.typ === 1) {
            this.form.auditTime = ''
          }
          this.form.status = 1
          svnAdd(this.form).then(res => {
            this.$message.success(res)
            this.$router.go(-1)
          })
        }
      })
    },
    showFileName(val) {
      this.fileName = val
    },
    showPercent(val) {
      this.percentBox = val
    },
    percent(val) {
      this.percentage = val
    },
    uploadSuccess(allData) {
      const { callbackVar } = allData
      const fileId = callbackVar['x:file_id']
      getApkAdress(fileId).then(res => {
        this.form.url = res.fileUrls[0]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.url{
  position: relative;
}
.fileName{
  position: absolute;
  top: 36px;
  right: 0;
  color: #606266;
}
</style>
