<template>
  <section>
    <div v-if="$route.name === 'MarketingTaskDoula'" class="task">
      <div class="task-header">
        <el-input
          v-model="listQuery.condition.name"
          placeholder="请输入任务名称或执行人"
          @change="handleFilter()"
        >
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
        </el-input>
        <el-select v-model="listQuery.condition.serviceProviderId" clearable placeholder="全部服务商" @change="handleFilter()">
          <el-option
            v-for="item in serverOptions"
            :key="item.serviceOrgId"
            :label="item.serviceName"
            :value="item.serviceOrgId"
          />
        </el-select>
        <el-select v-model="listQuery.condition.orgzId" clearable placeholder="全部企业" @change="handleOrg()">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="listQuery.condition.productId" clearable placeholder="全部产品" @change="handleFilter()">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select v-model="listQuery.condition.status" clearable placeholder="任务状态" @change="handleFilter()">
          <el-option
            v-for="item in taskStatus"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-select v-model="listQuery.condition.type" clearable placeholder="任务类型" @change="handleFilter()">
          <el-option
            v-for="item in taskType"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-select v-model="listQuery.condition.platformId" clearable placeholder="代征平台" @change="handleFilter()">
          <el-option
            v-for="item in platformList"
            :key="item.desc"
            :label="item.desc"
            :value="item.id"
          />
        </el-select>
      </div>
      <div class="task-main">
        <h2>
          任务列表
          <div>
            <el-button type="primary" @click="exportList()">导出</el-button>
            <el-button type="primary" @click="toDetail()">发布</el-button>
          </div>
        </h2>
        <div class="table">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
          >
            <el-table-column
              v-for="item in tableColumn"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :min-width="item.width"
            />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.status !== '未开始'"
                  type="text"
                  size="mini"
                  @click="toExecute(scope.row.id,scope.row.type,scope.row.name)"
                >执行管理</el-button>
                <el-button type="text" size="mini" @click="toDetail(scope.row.id,'read')">查看</el-button>
                <el-button v-if="scope.row.status === '未开始'" type="text" size="mini" @click="toDetail(scope.row.id,'edit')">编辑</el-button>
                <el-button v-if="scope.row.status === '未开始'" type="text" size="mini" @click="deleteTask(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
      </div>
    </div>
    <router-view :key="$route.path" />
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getUnitList } from '@/api/userManage'
import { exportDoulaTask, doulaTaskList, productList, delDoulaTask, platformList, serviceProviderList } from '@/api/marketing/taskPromote'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          name: '',
          orgzId: null,
          productId: null,
          status: null,
          type: '',
          platformId: null,
          serviceProviderId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      taskName: '',
      serverOptions: [],
      orgList: [],
      productList: [],
      taskStatus: [
        { name: '未开始', value: 0 },
        { name: '进行中', value: 1 },
        { name: '已结束', value: 2 }
      ],
      taskType: [
        { name: '抖喇图文征集', value: 'DOULA_ARTICLE' },
        { name: '抖喇短视频征集', value: 'DOULA_VIDEO' }
      ],
      platformList: [],
      tableColumn: [
        { prop: 'name', label: '任务名称', width: '100' },
        { prop: 'serviceProvider', label: '服务商', width: '100' },
        { prop: 'productName', label: '产品', width: '100' },
        { prop: 'typeName', label: '任务类型', width: '100' },
        { prop: 'totalBudget', label: '推广预算', width: '60' },
        { prop: 'totalBudgetFee', label: '制作预算', width: '60' },
        { prop: 'totalClickFee', label: '点击奖励', width: '60' },
        { prop: 'totalCheckingFee', label: '核算费用', width: '60' },
        { prop: 'totalFee', label: '结算费用', width: '60' },
        { prop: 'periodicTime', label: '任务时间', width: '100' },
        { prop: 'taskUsers', label: '执行人', width: '100' },
        { prop: 'plan', label: '完成进度', width: '60' },
        { prop: 'status', label: '状态', width: '50' },
        { prop: 'createdName', label: '发布人', width: '100' },
        { prop: 'createdTime', label: '发布时间', width: '100' }
      ],
      tableData: []
    }
  },
  created() {
    if (this.$route.name === 'MarketingTaskDoula') {
      this.getTaskList()
      this.getOrg()
      this.getplatformList()
      this.getServiceList()
    }
  },
  methods: {
    handleOrg() {
      this.getProductList()
      this.getTaskList()
    },
    handleProduct() {
      if (this.productList.length === 0) {
        this.$message.error('请先选择企业')
      }
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getTaskList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getTaskList()
    },
    getTaskList(v) {
      doulaTaskList(this.listQuery).then(res => {
        res.records.forEach(item => {
          item.periodicTime = `${item.startTime} ~ ${item.endTime}`
          item.plan = `${item.taskUserComplete}/${item.taskUserAll}`
          item.status = item.status === 0 ? '未开始' : (item.status === 1 ? '进行中' : '已结束')
          item.typeName = this.taskType.find(i => i.value === item.type).name
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    getServiceList() {
      serviceProviderList().then(res => {
        this.serverOptions = res
      })
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getplatformList() {
      platformList().then(res => {
        this.platformList = res
      })
    },
    getProductList() {
      if (this.listQuery.condition.orgzId) {
        productList({ orgzId: this.listQuery.condition.orgzId, pageSize: 1000 }).then(res => {
          this.productList = res
        })
      }
    },
    toExecute(id, type, name) {
      this.$router.push(
        {
          name: 'MarketingTaskDoulaExecute',
          query: {
            id,
            type,
            name
          }
        }
      )
    },
    toDetail(taskId, type) {
      this.$router.push(
        {
          name: 'MarketingTaskDoulaPublish',
          query: {
            taskId,
            type
          }
        }
      )
    },
    deleteTask(id) {
      delDoulaTask(id).then(() => {
        this.$message.success('已成功删除')
        this.getTaskList()
      })
    },
    exportList() {
      exportDoulaTask(this.listQuery.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.task {
  min-height: calc(100vh - 50px);
  height: calc(100% - 50px);
  padding: 20px;
  background-color: #eaeaee;
  &-header {
    .el-input {
      width: 200px;
    }
  }
  &-main {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      background: #f9f9f9;
    }
    .table {
      padding: 16px 16px 0px;
    }
    ::v-deep .pagination-container {
      margin: 0 auto;
    }
  }
}
</style>
