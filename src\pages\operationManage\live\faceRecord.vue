<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.keyword" placeholder="请输入搜索关键字" clearable @clear="search" @keydown.enter.native="search">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="search" />
          <el-select slot="prepend" v-model="tableQuery.condition.type" style="width: 140px">
            <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.liveMethod" clearable placeholder="请选择直播方式" @change="search()">
          <el-option v-for="item in live_method" :key="item.dictId" :label="item.value" :value="item.code" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.liveType" clearable placeholder="请选择直播类型" @change="search()">
          <el-option v-for="item in live_type" :key="item.dictId" :label="item.value" :value="item.code" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.contrastResult" clearable placeholder="请选择认证结果" @change="search">
          <el-option label="匹配" :value="1" />
          <el-option label="不匹配" :value="1" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-button type="primary" @click="clear()">重置</el-button>
      </div>
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='faceImg'">
            <el-image style="width: 100px" :src="scope.row[col.prop]" />
          </template>
          <template v-else-if="col.prop==='contrastFaceImg'">
            <el-image style="width: 100px" :src="scope.row[col.prop]" />
          </template>
          <template v-else-if="col.prop==='auditStatus'">
            <span>{{ scope.row[col.prop] | auditStatusFilter }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import { getDict, liveFaceList } from '@/api/liveManage'

export default {
  name: 'Livelist',
  components: { Pagination },
  filters: {
    auditStatusFilter(val) {
      if (val === 1) {
        return '匹配'
      } else if (val === 2) {
        return '不匹配'
      } else {
        return '未认证'
      }
    }
  },
  data() {
    return {
      searchTypeList: [
        { label: '直播主题', value: 1 },
        { label: '讲师姓名', value: 2 },
        { label: '内容扩展方', value: 3 }
      ],
      live_method: [],
      live_type: [],
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 1, label: '直播ID', align: 'center', prop: 'liveId' },
        { id: 2, label: '直播主题', align: 'center', prop: 'title' },
        { id: 3, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 4, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 5, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 6, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 7, label: '本人人脸', align: 'center', prop: 'faceImg' },
        { id: 8, label: '认证人脸', align: 'center', prop: 'contrastFaceImg' },
        { id: 9, label: '认证结果', align: 'center', prop: 'auditStatus' },
        { id: 10, label: '认证时间', align: 'center', prop: 'createTime', sortable: true, width: '160px' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {
          type: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: []
    }
  },
  created() {
    this.getAllType()
    this.getList()
  },
  methods: {
    // 获取字典
    getAllType() {
      // LIVE_METHOD-方式 LIVE_TYPE-类型 PROMOTION_TYPE-推广类型
      getDict({ type: 'LIVE_METHOD' }).then(res => {
        this.live_method = res
      })
      // 直播类型
      getDict({ type: 'LIVE_TYPE' }).then(res => {
        this.live_type = res
      })
    },
    search() {
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear() {
      this.tableQuery.pager.page = 1
      this.tableQuery.condition.contrastResult = ''
      this.tableQuery.condition.keyword = ''
      this.tableQuery.condition.liveMethod = ''
      this.tableQuery.condition.liveType = ''
      this.getList()
    },
    getList() {
      liveFaceList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    }
  }
}
</script>

