<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">视频审核列表</header>
    </div>
    <div class="search-column">
      <div class="">
        <div class="search-column__item">
          <div class="search-column__label">更新时间：</div>
          <div class="search-column__inner">
            <el-date-picker
              v-model="dateArray"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleChangeDate"
            />
          </div>
        </div>
        <div class="search-column__item">
          <div class="search-column__label">审核状态：</div>
          <el-select v-model="tableQuery.condition.audit" placeholder="请选择审核状态" clearable @change="handleChangeAudit">
            <el-option
              v-for="item in videoAduitList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-input v-model="tableQuery.condition.videoName" placeholder="请输入搜索视频名称" clearable />
        </div>
        <div class="search-column__item">
          <el-button type="primary" icon="el-icon-search" @click="handleSearchKey()">搜索</el-button>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table :data="videoValidList" border stripe>
      <el-table-column
        v-for="col in tableColumnList"
        :key="col.id"
        :prop="col.prop"
        :label="col.label"
        :align="col.align"
      >
        <template slot-scope="scope">
          <template v-if="col.filter === 'audit'">
            <span>{{ scope.row[col.prop] | filterAduitStatus }}</span>
          </template>
          <template v-else-if="col.filter === 'permission'">
            <span>{{ scope.row.extPermission | filterExtPermission }}、{{ scope.row.intPermission | filterIntPermission }}</span>
          </template>
          <template v-else-if="col.filter === 'cateNameArr'">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px;">
                {{ scope.row[col.prop] }}
              </div>
              <span>{{ scope.row[col.prop] | filterFont }}</span>
            </el-tooltip>
          </template>
          <template v-else>
            <span>
              {{ scope.row[col.prop] }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button v-if="[1].includes(row.audit)" type="text" @click="handleValidateVideo(row)">审核</el-button>
          <el-button type="text" @click="handleViewHistory(row)">审核历史</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <!-- dialog history -->
    <el-dialog
      title="审核历史"
      :visible.sync="dialogHistoryVisible"
      width="600px"
      @close="handleCloseDialog()"
    >
      <el-table :data="videoHistoryList" border stripe>
        <el-table-column
          v-for="col in tableHistoryColumnList"
          :key="col.id"
          :prop="col.prop"
          :label="col.label"
          :align="col.align"
        >
          <template slot-scope="scope">
            <template v-if="col.filter === 'audit'">
              {{ scope.row[col.prop] | filterHistoryAduitStatus }}
            </template>
            <template v-else>
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <Pagination class="text-center" :layout="layout" :total="videoHistoryTotal" :page="auditHistoryQuery.pager.page" @pagination="handleHistoryPagination" />
    </el-dialog>

  </div>
</template>

<script>
import { videoValidList, videoValidateHistory } from '@/api/validManage'
import Pagination from '@/components/Pagination'
import { filterAduitStatus, filterHistoryAduitStatus, filterExtPermission, filterIntPermission } from '@/utils'
export default {
  name: 'VideoValid',
  components: {
    Pagination
  },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    },
    filterAduitStatus,
    filterHistoryAduitStatus,
    filterExtPermission,
    filterIntPermission
  },
  mixins: [],
  props: {

  },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // 审核历史弹层
      dialogHistoryVisible: false,
      // 时间集合
      dateArray: [],
      // 视频搜索类型
      videoAduitList: [
        // { label: '待提交审核', value: 0 },
        { label: '待审核', value: 1 },
        { label: '审核通过', value: 2 },
        { label: '审核不通过', value: 3 }
      ],
      // 表格表头
      tableColumnList: [
        { id: 0, prop: 'videoId', label: 'ID', align: 'center' },
        { id: 1, prop: 'name', label: '视频名称', align: 'left' },
        { id: 2, prop: 'cateNameArr', label: '分类', align: 'center', filter: 'cateNameArr' },
        // { id: 3, prop: 'introduction', label: '视频说明', align: 'center', filter: 'introduction' },
        { id: 4, label: '视频权限', align: 'center', filter: 'permission' },
        { id: 5, prop: 'orgName', label: '单位名称', align: 'center' },
        { id: 6, prop: 'updateTime', label: '提交审核时间', align: 'center' },
        { id: 7, prop: 'author', label: '创建者名称', align: 'center' },
        { id: 8, prop: 'audit', label: '审核状态', align: 'center', filter: 'audit' }
      ],
      // 审核列表表头
      tableHistoryColumnList: [
        { id: 0, prop: 'id', label: 'ID', align: 'center' },
        { id: 1, prop: 'name', label: '视频名称', align: 'left' },
        { id: 2, prop: 'author', label: '创建者名称', align: 'center' },
        { id: 3, prop: 'auditName', label: '审核人', align: 'center' },
        { id: 4, prop: 'updateTime', label: '提交审核状态', align: 'center' },
        { id: 5, prop: 'audit', label: '审核状态', align: 'center', filter: 'audit' }
      ],
      // 请求参数
      tableQuery: {
        condition: {
          audit: null,
          videoName: '',
          endTime: '',
          startTime: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 审核历史
      auditHistoryQuery: {
        condition: {
          videoId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 视频审核列表
      videoValidList: [],
      total: 0,
      // 审核历史列表
      videoHistoryList: [],
      videoHistoryTotal: 0
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    this.getVideoValidList()
  },
  methods: {
    // 时间选择
    handleChangeDate(val) {
      this.tableQuery.condition.startTime = val[0]
      this.tableQuery.condition.endTime = val[1]
      this.tableQuery.pager.page = 1
      this.getVideoValidList()
    },
    // 审核状态选择
    handleChangeAudit(val) {
      this.tableQuery.pager.page = 1
      this.getVideoValidList()
    },
    // 搜索
    handleSearchKey() {
      this.tableQuery.pager.page = 1
      this.getVideoValidList()
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager.page = val.page
      this.getVideoValidList()
    },
    // 审核列表分页操作
    handleHistoryPagination(val) {
      this.auditHistoryQuery.pager.page = val.page
      this.getVideoValidateHistoryList()
    },
    // 视频列表
    getVideoValidList() {
      videoValidList(this.tableQuery).then(res => {
        res.records.forEach(item => {
          item.cateNameArr = item.cateNameArr.join(';')
        })
        this.videoValidList = res.records
        this.total = res.total
      })
    },
    // 审核历史列表
    getVideoValidateHistoryList() {
      this.dialogHistoryVisible = true
      videoValidateHistory(this.auditHistoryQuery).then(res => {
        this.videoHistoryList = res.records
        this.videoHistoryTotal = res.total
      })
    },
    // close
    handleCloseDialog() {
      this.dialogHistoryVisible = false
    },
    // 审核
    handleValidateVideo(row) {
      this.$router.push({
        name: 'VideoDetail',
        params: {
          videoInfoId: row.videoInfoId
        }
      })
    },
    // 审核历史
    handleViewHistory(row) {
      this.auditHistoryQuery.condition.videoId = row.videoId
      this.getVideoValidateHistoryList()
    }
  }
}
</script>

<style scoped>
  .video-links{
    color: #409EFF;
    cursor: pointer;
  }
</style>
