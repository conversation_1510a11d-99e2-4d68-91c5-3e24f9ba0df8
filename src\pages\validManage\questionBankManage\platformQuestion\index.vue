<template>
  <div class="app-container">
    <div class="content-container">
      <el-row type="flex">
        <!-- 左侧树结构 -->
        <el-col class="list-tree">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <header class="page-title">试题分类</header>
            <el-button v-if="isShowbtn" plain size="mini" style="margin: 0 20px 5px 0" @click="closeCheck">取消选择</el-button>
          </div>
          <el-scrollbar v-if="isTree == 1" wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container">
            <tree
              v-if="isShow == '1'"
              ref="tree"
              style="margin: -40px 0 0 -40px;"
              :allow-drag="true"
              :show-search="false"
              :tree="treeData"
              :default-expanded-keys="[staffUserInfo.deptId]"
              :highlight-current="highlightCurrent"
              @appendSameLevel="appendSameLevel"
              @appendChildLevel="appendChildLevel"
              @handleClick="handleTreeNodeClick"
              @deleteThisLevel="deleteThisLevel"
              @editThisLevel="editThisLevel"
              @nodeDragStart="handleNodeDragStart"
              @nodeDragEnd="handleNodeDragEnd"
            />
          </el-scrollbar>
          <div v-if="isTree == 2" style="text-align:center;margin-top:40px">
            <el-button plain @click="addClass"><i class="el-icon-plus" />新增分类</el-button>
          </div>
        </el-col>
        <!-- 右侧试题信息 -->
        <el-col class="list-table">
          <!-- 表头控件 -->
          <el-row type="flex" justify="space-between" :gutter="10">
            <el-col :span="4">
              <el-select v-model="condition.type" style="width: 100%;" clearable placeholder="请选择搜索方式" @change="typeChange">
                <el-option v-for="(item, index) in conditionList.type" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-input v-model="condition.keyword" style="width: 100%;" class="keyword" placeholder="输入关键字搜索" clearable @change="kwChange">
                <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="stemSearch()" />
              </el-input>
            </el-col>
            <el-col :span="3">
              <el-select v-model="condition.questionType" style="width: 100%;" clearable placeholder="请选择题型" @change="qtTypeChange">
                <el-option v-for="(item, index) in conditionList.questionType" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="condition.difficulty" style="width: 100%;" clearable placeholder="请选择难易程度" @change="difficultyChange">
                <el-option v-for="(item, index) in conditionList.difficulty" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="3">
              <el-select v-model="condition.source" clearable placeholder="请选择来源" @change="sourceChange">
                <el-option v-for="(item, index) in conditionList.source" :key="index" :label="item.label" :value="item.value" />
              </el-select>
            </el-col>
            <el-col :span="3" :offset="3" style="text-align: end;">
              <el-button type="primary" style="width:90%" @click="updateClass">修改分类</el-button>
            </el-col>
            <el-col :span="3" style="text-align: end;">
              <el-button type="primary" style="width:90%" @click="createQuestion">创建试题</el-button>
            </el-col>
          </el-row>

          <!-- 全选控件 -->
          <div v-show="listShow?false:true" class="checkBoxAll">
            <input id="boxid" type="checkbox" @click="setAllNo()">全选
          </div>

          <!-- 试题列表 -->
          <el-row>
            <el-col class="question-list">
              <div class="scroll-list">
                <el-scrollbar style="height: 100%">
                  <div v-if="listShow" class="empty">暂无数据</div>
                  <div v-else>
                    <div v-for="(item, index) in questionList" :key="index">
                      <input type="checkbox" name="love" :value="item.id" class="checkBox" @change="checkChang(index)">
                      <div>
                        <QuestionList :data="item" @edit="listEdit" @del="listDelete" />
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </el-col>
          </el-row>

          <!-- 分页器 -->
          <el-row>
            <el-col>
              <el-pagination
                class="pagination"
                :current-page="page"
                :page-sizes="[5, 10, 20]"
                :page-size="tableQuery.pager.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableQuery.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>

    <!-- 新增/编辑试题分类 -->
    <div>
      <el-dialog :title="questionForm.title" :visible.sync="dialogAddVisible" :width="dialogAddWidth" center :destroy-on-close="true" @close="close">
        <el-form ref="levelForm" :model="newDuestionItem" :rules="levelRules">
          <el-form-item label="名称" prop="name" :label-width="formLabelWidth">
            <el-input v-model="newDuestionItem.name" type="textarea" style="width: 100%;" maxlength="50" show-word-limit resize="none" :autosize="{minRows: 2, maxRows: 4 }" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelAppendLevel()">取 消</el-button>
          <el-button type="primary" @click="formAppendLevel()">确 定</el-button>
        </div>
      </el-dialog>
    </div>

    <!-- 修改分类 -->
    <div>
      <el-dialog title="修改分类" :visible.sync="updateVisible" :width="dialogAddWidth" center :destroy-on-close="true" @close="close">
        <el-form ref="levelForm" :model="newDuestionItem" :rules="levelRules">
          <el-form-item label="所属试题分类" prop="name" :label-width="formLabelWidth">
            <el-cascader
              :key="cascaderKey"
              ref="cascaderHandle"
              v-model="newDuestionItem.updateClass"
              placeholder="请选择试题分类"
              :options="treeData"
              filterable
              clearable
              :props="props"
              style="width: 80%"
              :show-all-levels="false"
              :change-on-select="false"
            />
            <!-- <el-input v-model="newDuestionItem.name" style="width: 217px;" autocomplete="off" show-word-limit /> -->
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelUpdate()">取 消</el-button>
          <el-button type="primary" @click="formUpdate()">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getAllTree,
  deleteCategory,
  dragUpdateCategory,
  updateCategory,
  addCategory,
  paperList,
  paperDel,
  updateCates
} from '@/api/questionBankManage'
import Tree from './components/tree'
import QuestionList from './components/questionList'

export default {
  name: 'QuestionBankManage',
  components: {
    Tree,
    QuestionList
  },
  data() {
    return {
      page: 1,
      isShowbtn: false, // 是否隐藏取消选择按钮
      highlightCurrent: false, // 是否开启高亮
      isTree: 2,
      cascaderKey: 0,
      updateVisible: false,
      updateClassItem: {
        updateClass: []
      },
      props: {
        label: 'name',
        value: 'questionCategoryId',
        children: 'childList',
        emitPath: false,
        checkStrictly: true,
        multiple: false
      },
      condition: {
        type: '',
        keyword: '',
        questionType: '',
        difficulty: '',
        source: ''
      },
      listShow: true,
      // 树结构数据源
      treeData: [],
      // 试题列表数据
      questionList: [],
      conditionList: {
        type: [
          { value: 1, label: '试题题干/病例' },
          { value: 2, label: '考点关键字' }
        ],
        questionType: [
          { value: 1, label: 'A1题型' },
          { value: 2, label: 'A2题型' },
          { value: 3, label: 'A3题型' },
          { value: 4, label: 'A4题型' },
          { value: 5, label: 'B1题型' },
          { value: 6, label: 'X题型' },
          { value: 7, label: '判断题' },
          { value: 8, label: '选择填空题' }
        ],
        difficulty: [
          { value: 1, label: '简单' },
          { value: 2, label: '普通' },
          { value: 3, label: '困难' }
        ],
        source: [
          { value: 1, label: '医考通' },
          { value: 2, label: '名医传世' }
        ]
      },
      // 表格参数
      tableQuery: {
        total: 0,
        condition: {
          type: null,
          source: null,
          questionType: null,
          keyword: '',
          difficulty: null,
          cateId: null,
          action: 1
        },
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 5
        }
      },
      isShow: '1', // 初始化树形控件，如存在数据 为1 不存在数据 为2
      title: '新增分类',
      dialogAddVisible: false, // 弹窗显示隐藏  true/显示 false/隐藏
      dialogAddWidth: '600px',
      formLabelWidth: '140px',
      checkAll: false,
      checkedCities: [], // 默认选中
      isIndeterminate: true,
      setAllNoData: [], // 保存全选框的选中状态
      checkbox: [],
      // 试题分类信息
      staffUserInfo: {
        action: 'add',
        roleId: '', // 默认选中员工
        roleIds: [],
        questionCategoryId: 0,
        deptIds: [],
        phone: null,
        realName: '',
        username: '',
        majorId: '',
        staffId: 0
      },
      // 试题查询条件
      tableList: {
        condition: {
          cateId: null,
          difficulty: null,
          keyword: '',
          questionType: null,
          source: null,
          type: null
        },
        pager: {
          page: 1,
          pageSize: 5
        }
      },
      questionForm: {
        title: '',
        type: '',
        orgId: 0
      },
      newDuestionItem: {
        questionCategoryId: 0,
        parentId: 0,
        name: '',
        updateClass: []
      },
      levelRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ]
      },
      idx: null
    }
  },
  watch: {},
  created() {
    this.getTreeList()
  },
  methods: {
    cancelUpdate() {
      this.updateVisible = false
      this.updateClassItem.updateClass = ''
    },
    formUpdate() {
      if (this.newDuestionItem.updateClass) {
        const loves = document.getElementsByName('love')
        const data = {
          cateId: this.newDuestionItem.updateClass,
          ids: []
        }
        loves.forEach(item => {
          if (item.checked === true) {
            data.ids.push(item.value)
          }
        })
        updateCates(data).then(res => {
          this.queryQuestionList()
        })
      } else {
        this.$message('请选择试题分类')
      }
      this.updateVisible = false
    },
    // 新增分类
    addClass() {
      this.questionForm.title = '新增试题分类'
      this.questionForm.type = 'add'
      this.showAddLevelDialog()
      this.newDuestionItem.parentId = 0
    },
    // 取消 已选中的试题分类
    closeCheck() {
      this.tableList.condition.cateId = ''
      this.highlightCurrent = false
      this.isShowbtn = false
      this.queryQuestionList()
    },
    // 获取试题分类树形数据，并做初始化
    async getTreeList() {
      this.tableList.condition.cateId = ''
      await getAllTree().then(res => {
        if (res) {
          this.isTree = 1
          this.treeData = res
        } else {
          this.isTree = 2
        }
        if (res.length === 0) {
          this.isShow = '1'
        }
      }).catch(err => {
        console.log(err)
      })
      await this.queryQuestionList()
    },
    // 点击搜索图标搜索
    stemSearch() {
      if (this.tableList.condition.type === '' || null) {
        this.$message('请选择搜索类型')
        return
      }
      this.tableList.condition.type = this.condition.type
      this.tableList.condition.keyword = this.condition.keyword
      this.queryQuestionList()
    },
    // 页面大小改变
    handleSizeChange(val) {
      this.tableList.pager.pageSize = val
      this.queryQuestionList()
    },
    // 页面发生改变
    handleCurrentChange(val) {
      // this.tableList.pager.page = val
      this.queryQuestionList('clickPage', val)
    },
    // 新增同级试题
    appendSameLevel(v) {
      this.questionForm.title = '新增试题分类'
      this.questionForm.type = 'add'
      this.showAddLevelDialog()
      this.newDuestionItem.parentId = v.parentId
    },
    // 新增下级试题
    appendChildLevel(v) {
      this.questionForm.title = '新增下级试题分类'
      this.questionForm.type = 'add'
      this.showAddLevelDialog()
      this.newDuestionItem.parentId = v.questionCategoryId
    },
    // 获取当前试题内容
    handleTreeNodeClick(data, node) {
      this.isShowbtn = true
      this.highlightCurrent = true
      const questionCategoryId = data.questionCategoryId
      if (data.level > 1) {
        this.staffUserInfo.questionCategoryId = questionCategoryId
      }
      this.tableQuery.condition.cateId = questionCategoryId
      this.tableList.condition.cateId = questionCategoryId
      this.tableQuery.pager.page = 1
      // this.tableQuery.condition.administratorAccount = null
      this.queryQuestionList(questionCategoryId)
    },
    // 删除试题
    deleteThisLevel(n, v) {
      this.$confirm('是否删除该试题分类:“' + v.name + '”?', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deleteCategory(v.questionCategoryId).then(res => {
          const parent = n.parent
          const childList = parent.data.childList || parent.data
          const index = childList.findIndex(
            d => d.questionCategoryId === v.questionCategoryId
          )
          childList.splice(index, 1)
          this.$message({
            type: 'success',
            message: '删除成功！'
          })
          this.getTreeList()
        })
      })
    },
    // 编辑试题分类
    editThisLevel(v) {
      this.questionForm.title = '编辑当前试题分类'
      this.questionForm.type = 'edit'
      // this.questionForm.questionCategoryId = v.questionCategoryId
      // this.newDuestionItem.parentId = v.parentId
      this.newDuestionItem.questionCategoryId = v.questionCategoryId
      // this.newDuestionItem.name = v.name
      this.showAddLevelDialog()
    },
    // 拖拽开始
    handleNodeDragStart(obj) {
      this.treeDataTemp = JSON.parse(JSON.stringify(this.treeData))
    },
    // 拖拽结束
    handleNodeDragEnd(obj) {
      const { enterNode, event, node } = obj
      const that = this
      // if (enterNode.level === node.level && event !== 'inner') {
      //   this.treeData = JSON.parse(JSON.stringify(this.treeDataTemp))
      //   this.$message.error('不允许将试题平移！')
      // } else {
      let str = ''
      if (event === 'inner') {
        str = '是否确认将“' + node.data.name + '”试题分类移动到“' + enterNode.data.name + '”试题分类内吗？'
      } else if (event === 'before') {
        str = '是否确认将“' + node.data.name + '”试题分类移动到“' + enterNode.data.name + '”试题分类前面吗？'
      } else if (event === 'after') {
        str = '是否确认将“' + node.data.name + '”试题分类移动到“' + enterNode.data.name + '”试题分类后面吗？'
      }
      // hack code
      if (!str) {
        return
      }
      this.$confirm(str, '警告', {
        cancelButtonText: '取消',
        confirmButtonText: '确定'
      }).then(res => {
        let upperCategoryId = ''
        let parentId = ''
        let questionCategoryId = ''
        if (event === 'inner') {
          // for (const key in enterNode.childNodes) {
          // if (enterNode.childNodes[key].data.questionCategoryId === enterNode.data.questionCategoryId) {
          const index = enterNode.childNodes.length - 1
          upperCategoryId = enterNode.childNodes.length > 1 ? enterNode.childNodes[index].data.questionCategoryId : 0
          parentId = enterNode.data.questionCategoryId
          questionCategoryId = node.data.questionCategoryId
        } else if (event === 'before') {
          parentId = enterNode.data.parentId
          questionCategoryId = node.data.questionCategoryId
          for (const key in enterNode.parent.childNodes) {
            if (enterNode.parent.childNodes[key].data.questionCategoryId === enterNode.data.questionCategoryId) {
              that.idx = key - 2
              if (that.idx > -1) {
                upperCategoryId = enterNode.parent.childNodes[that.idx].data.questionCategoryId
              } else {
                upperCategoryId = 0
              }
            }
          }
        } else {
          parentId = enterNode.data.parentId
          questionCategoryId = node.data.questionCategoryId
          upperCategoryId = enterNode.data.questionCategoryId
        }
        const obj = {
          parentId: parentId || 0,
          questionCategoryId: questionCategoryId,
          upperCategoryId: upperCategoryId
        }
        dragUpdateCategory(obj).then(res => {
          this.$message.success('更新试题分类树成功!')
        }).catch(res => {
          this.treeData = JSON.parse(JSON.stringify(this.treeDataTemp))
        })
      }).catch(res => {
        this.treeData = JSON.parse(JSON.stringify(this.treeDataTemp))
      })
      // }
    },
    // 创建试题按钮  跳转到创建试题页面
    createQuestion() {
      this.$router.push({
        name: 'CreateQuestion'
      })
    },
    // 修改分类
    updateClass() {
      let flag = true
      const loves = document.getElementsByName('love')
      loves.forEach(item => {
        if (item.checked === true) {
          flag = false
          return flag
        }
      })
      if (flag) return this.$message('请选择要修改的试题')
      this.updateVisible = true
    },
    // 全选事件
    setAllNo() {
      this.setAllNoData = []
      this.checkbox = []
      var box = document.getElementById('boxid')
      var loves = document.getElementsByName('love')
      if (box.checked === false) {
        for (let i = 0; i < loves.length; i++) {
          loves[i].checked = false
          this.setAllNoData.push(loves[i].checked)
        }
      } else {
        for (let i = 0; i < loves.length; i++) {
          loves[i].checked = true
          this.setAllNoData.push(loves[i].checked)
        }
      }
    },
    checkChang(index) {
      let flag = true
      const box = document.getElementById('boxid')
      const loves = document.getElementsByName('love')
      if (loves[index].checked === false) {
        box.checked = false
      }
      loves.forEach(item => {
        if (item.checked === false) {
          flag = false
        }
      })
      if (flag) {
        box.checked = true
      }
    },
    // 显示增加试题分类弹窗
    showAddLevelDialog() {
      this.dialogAddVisible = true
    },
    // 新增/编辑弹窗 取消事件
    cancelAppendLevel() {
      this.dialogAddVisible = false
    },
    // 关闭弹窗时回调
    close() {
      this.newDuestionItem = {
        questionCategoryId: 0,
        parentId: 0,
        name: '',
        updateClass: []
      }
    },
    // 新增/编辑弹窗 确认事件
    formAppendLevel() {
      const that = this
      const length = this.newDuestionItem.name.length
      if (!this.newDuestionItem.name) {
        this.$message('请输入试题分类名称')
        return
      }
      if (length > 50) {
        this.$message('长度不超过50个字符')
        return
      }
      if (this.questionForm.type === 'add') {
        const obj = {
          name: this.newDuestionItem.name,
          parentId: this.newDuestionItem.parentId
        }
        addCategory(obj).then(res => {
          if (res) {
            that.getTreeList()
          }
        })
      } else {
        const obj = {
          name: this.newDuestionItem.name,
          questionCategoryId: this.newDuestionItem.questionCategoryId
        }
        updateCategory(obj).then(res => {
          if (res) {
            that.getTreeList()
          }
        })
      }
      this.newDuestionItem.name = ''
      this.dialogAddVisible = false
    },
    // 试题列表 编辑
    listEdit(val) {
      this.$router.push({
        name: 'CreateQuestion',
        query: {
          value: JSON.stringify(val)
        }
      })
    },
    // 试题列表 删除
    listDelete(val) {
      this.$confirm('是否要继续删除此试题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        paperDel(val.id).then(res => {
          this.queryQuestionList()
        })
      }).catch(() => { })
    },
    // 搜索类型change事件
    typeChange(val) {
      this.tableList.condition.type = val
    },
    // 关键字change事件
    kwChange(val) {
      if (this.tableList.condition.type === '') {
        this.$message('请选择搜索类型')
        return
      }
      this.tableList.condition.keyword = val
      this.queryQuestionList()
    },
    // 题型change事件
    qtTypeChange(val) {
      this.tableList.condition.questionType = val
      this.queryQuestionList()
    },
    // 难易程度change事件
    difficultyChange(val) {
      this.tableList.condition.difficulty = val
      this.queryQuestionList()
    },
    // 来源change事件
    sourceChange(val) {
      this.tableList.condition.source = val
      this.queryQuestionList()
    },
    // 试题列表加载
    async queryQuestionList(type, page) {
      if (type && type === 'clickPage') {
        this.tableList.pager.page = page
        this.page = page
      } else {
        this.tableList.pager.page = 1
        this.page = 1
      }
      this.questionList = []
      var box = document.getElementById('boxid')
      box.checked = false
      await paperList(this.tableList).then(res => {
        if (res) {
          this.questionList = JSON.parse(JSON.stringify(res.records))
          this.tableQuery.total = res.total
          if (res.records.length > 0) {
            this.listShow = false
          } else {
            this.listShow = true
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-tree,
.list-table {
  height: calc(100vh - 100px);
  border: 1px solid #dfe6ec;
}
.list-table{
  padding: 10px 20px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
  // white-space: nowrap;
}
.keyword{
  &::v-deep .el-input__inner{
    padding-right: 55px;
  }
}
.empty{
  height: calc(100vh - 240px);
  text-align: center;
  color: #DCDFE6;
  padding-top: 200px;
}
.list-tree {
  width: 400px;
  margin-right: 16px;
  padding: 10px 0 10px 20px;
  // overflow: auto;
    overflow: hidden;

  .content-container {
    width: 100%;
    height: 100%;
    padding: 0;
    &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
      overflow-x: hidden;
    }
    &::v-deep .el-scrollbar__view.p20-scrollbar__view {
      padding: 20px;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
    }
    &::v-deep .el-scrollbar__bar.is-vertical {
      width: 10px;
    }
  }
}
.checkBoxAll {
    margin: 10px 0;
    padding-left: 18px;
}
.question-list {
  position: relative;
  .scroll-list{
    height: calc(100vh - 240px);
  }
  .checkBox {
    position: relative;
    top: 43px;
    left: 20px;
  }
}
.pagination {
  margin: 10px auto;
  text-align: center;
}
::v-deep .el-textarea .el-input__count{
  color: #909399;
  position: absolute;
  font-size: 12px;
  bottom: 15px;
  right: 10px;
  height: 15px;
}
</style>
