<template>
  <div class="app-container">
    <!-- header -->

    <div class="search-column">
      <div class="fl">
        <div class="search-column__item">
          <el-button type="primary" @click="addUnitDialogShow()">创建单位</el-button>
        </div>
      </div>
      <div class="fr">
        <div class="search-column__item">
          <div class="search-column__label">单位名称：</div>
          <div class="search-column__inner">
            <el-input v-model="tableQuery.condition.keyword" clearable placeholder="请输入搜索关键字" @clear="searchKeyword()" @keydown.enter.native="searchKeyword()">
              <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKeyword()" />
            </el-input>
          </div>
        </div>
        <div class="search-column__item">
          单位类型：
          <el-select v-model="tableQuery.condition.type" clearable placeholder="请选择单位类型" @change="handleChangeType">
            <el-option v-for="item in typeList" :key="'t'+item.orgTypeId" :label="item.name" :value="item.orgTypeId" />
          </el-select>
        </div>
        <div class="search-column__item">
          单位层级：
          <el-select v-model="tableQuery.condition.level" clearable placeholder="请选择单位层级" @change="handleChangeLevel" @blur="handleBlurLevel">
            <el-option v-for="item in levelList" :key="'l'+item.orgLevelId" :label="item.name" :value="item.orgLevelId" />
          </el-select>
        </div>
        <div class="search-column__item">
          服务状态：
          <el-select v-model="tableQuery.condition.status" clearable placeholder="请选择服务状态" @change="handleChangeStatus">
            <el-option v-for="item in statusList" :key="'s'+item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="search-column__item">
          会员等级：
          <el-select v-model="tableQuery.condition.memberLevel" clearable placeholder="请选择会员等级" @change="getUnitUserList">
            <el-option v-for="item in memberLevelList" :key="'m'+item.level" :label="item.name" :value="item.level" />
          </el-select>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table :data="userList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="'c'+col.id" :prop="col.prop" :label="col.label" :align="col.align">
        <template slot-scope="scope">
          <span v-if="col.prop === 'status'">
            {{ scope.row[col.prop] | filterStatus }}
          </span>
          <el-button v-else-if="col.prop === 'yktBankNum'" type="text" @click="lookYKT(scope.row['orgId'])">
            {{ scope.row[col.prop] }}
          </el-button>
          <span v-else>
            {{ scope.row[col.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="setManager(row)">设置管理员</el-button>
          <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="setStatusRow(row)">{{ row.status === 0?'启用':'停用' }}</el-button>
          <el-button size="mini" type="text" @click="openYKT(row)">开通医考通题库</el-button>
          <el-button size="mini" type="text" @click="openFreeCourse(row)">开通免费培训课程</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <!-- unit user add/edit -->
    <DialogUnitEdit :title="title" :action="action" :visible.sync="dialogVisible" :data="formData" @handleCancel="handleCancelDialogForm" @handleSave="handleSaveDialogForm" />

    <!-- set user manager -->
    <el-dialog v-loading="loading" :close-on-click-modal="false" title="创建管理员" :visible.sync="dialogSetManagerShow" width="500px" @close="setUserManagerDialogColse">
      <el-form ref="form" :model="formDataManager" label-width="90px" :rules="rules">
        <el-form-item label="手机号">
          <el-input v-model="formDataManager.phone" v-search="searchOption" maxlength="11" />
          <div class="warn-tips">通过手机号查找平台是否已存在用户。如用户已存在，立即成为本单位管理员。如用户不存在，将直接创建该管理员。</div>
        </el-form-item>
        <el-row v-if="hasUser">
          <el-form-item label="姓名" prop="realName">
            <el-input v-model="formDataManager.realName" placeholder="请输入姓名" />
          </el-form-item>
          <!-- 身份选择器 -->
          <el-form-item label="身份" prop="identityId">
            <el-cascader
              v-model="formDataManager.identityId"
              placeholder="请选择身份"
              :options="identityTree"
              :show-all-levels="false"
              :props="{
                value:'identityId',
                label:'name',
                children:'childList',
                leaf: 'leaf',
                emitPath: false
              }"
              @change="identityChange"
            />
          </el-form-item>
          <!-- 职称选择器 -->
          <el-form-item label="职称" prop="academicId">
            <el-cascader
              v-model="formDataManager.academicId"
              placeholder="请选择职称"
              :options="academicList"
              :props="{
                value:'academicId',
                label:'name',
                children:'childList',
                emitPath: false
              }"
              :disabled="disabled"
            />
          </el-form-item>
          <!-- 专科选择器 -->
          <el-form-item label="专科" prop="majorId">
            <el-cascader
              v-model="formDataManager.majorId"
              placeholder="请选择专科"
              :options="majorList"
              :props="{
                value:'majorId',
                label:'name',
                children:'childList',
                emitPath: false,
                checkStrictly: true
              }"
              :disabled="disabled"
            />
          </el-form-item>
          <el-form-item label="工作单位:" prop="orgName">
            <el-input v-model="formDataManager.orgName" :disabled="true" />
          </el-form-item>
          <el-form-item label="部门/科室:" prop="department">
            <el-cascader
              v-model="formDataManager.deptId"
              placeholder="请选择部门/科室"
              :options="deptList"
              :props="{
                value:'departmentId',
                label:'deptName',
                children:'childList',
                emitPath: false,
                checkStrictly: true
              }"
            />
          </el-form-item>
          <el-form-item label="所在地区:" prop="region">
            <el-cascader
              v-model="formDataManager.region"
              placeholder="请选择区域"
              :options="areaList"
              :props="{
                value:'areaId',
                label:'name',
                children:'childList'
              }"
              @change="handleRegion"
            />
          </el-form-item>
          <el-form-item label="擅长:">
            <el-input v-model="formDataManager.skill" />
          </el-form-item>
          <el-form-item label="个人简介:">
            <el-input
              v-model="formDataManager.introduction"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button @click="setUserManagerDialogColse">取 消</el-button>
        <el-button :disabled="!submitDisable" type="primary" @click="handleSetManagerSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <!-- get user manager -->
    <el-dialog title="设置单位管理员" :visible.sync="dialogGetManagerShow" width="800px">
      <div class="mg-b">
        <el-button size="mini" type="primary" @click="showCreateManager()">创建管理员</el-button>
      </div>

      <el-table :data="userManagerList" border stripe>
        <el-table-column v-for="col in userManageTableColumn" :key="'u'+col.id" :prop="col.prop" :label="col.label" :align="col.align" />
        <el-table-column label="操作" align="center">
          <template slot-scope="{row}">
            <el-button type="text" @click="quickLogin(row.safeCode)">一键登录</el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-dialog>

    <el-dialog title="开通医考通题库" :visible.sync="openYKTdialog" width="800px">
      <el-form label-width="100px">
        <el-form-item label="选择题库">
          <el-tree
            ref="quesTree"
            node-key="id"
            :data="quesTree"
            :props="quesTreeProps"
            :default-checked-keys="alreadyIds"
            show-checkbox
            @check-change="quesTreeChange"
          />
        </el-form-item>
        <el-form-item label="题库有效期">
          <el-alert title="自开通之日起一年内有效" type="info" show-icon :closable="false" />
        </el-form-item>
        <el-row style="margin-left:16px;">
          <strong>已选：</strong>
          <el-tag v-for="(item,index) in checkedTitles" :key="'tag'+index" style="margin:5px;">
            {{ item }}
          </el-tag>
        </el-row>
      </el-form>
      <span slot="footer">
        <el-button @click="openYKTdialog = false">取 消</el-button>
        <el-button type="primary" @click="openYKTconfirm">确 定</el-button>
      </span>
    </el-dialog>

    <el-dialog title="医考通题库" :visible.sync="lookYKTdialog" width="1000px">
      <div class="search-column">
        <div class="fl">
          <div class="search-column__item">
            <div class="search-column__inner">
              <el-input v-model="yktTableQuery.condition.bankName" clearable placeholder="请输入题库名称" @clear="lookYKT()" @keydown.enter.native="lookYKT(yktTableQuery.condition.orgId, 'search')">
                <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="lookYKT(yktTableQuery.condition.orgId, 'search')" />
              </el-input>
            </div>
          </div>
          <div class="search-column__item">
            <el-select v-model="yktTableQuery.condition.status" placeholder="请选择类型" @change="lookYKT(yktTableQuery.condition.orgId, 'search')">
              <el-option label="正常" :value="1" />
              <el-option label="失效" :value="0" />
            </el-select>
          </div>
        </div>
      </div>
      <!-- body -->
      <el-table :data="yktList" border stripe>
        <el-table-column prop="bankName" label="题库名称" align="center" />
        <el-table-column prop="startTime" label="生效时间" align="center" />
        <el-table-column prop="endTime" label="过期时间" align="center" />
        <el-table-column label="状态" align="center">
          <template slot-scope="{row}">
            <span>{{ row.status===1?'正常':'失效' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <Pagination class="text-center" :layout="layout" :total="yktTotal" :page="yktTableQuery.pager.page" @pagination="yktPagination" />
    </el-dialog>

    <!-- 开通免费培训课程弹窗 -->
    <DialogFreeCourse
      :show.sync="freeCourseDialogVisible"
      :org-data="currentOrgData"
      @success="handleFreeCourseSuccess"
    />

  </div>
</template>

<script>
import {
  getUnitList,
  getUnitDetail,
  addUnit,
  editUnit,
  unitStatusOption,
  setUnitManager,
  getOrganTypeList,
  getOrganAdminListByOrgId,
  invitOrgAdminStaffFind,
  addUnitManager,
  addUnitManagerWithoutStaffInfo,
  getOrganLevelList,
  getBankList,
  getQuestionByTree,
  openyktQuestion
} from '@/api/userManage'
import { easyLogin } from '@/api/user'
import Pagination from '@/components/Pagination'
import DialogUnitEdit from './compontents/dialogUnitEdit'
import DialogFreeCourse from './components/DialogFreeCourse'
import { identityTreeList, majorTreeListId } from '@/api/category' // 身份树，随身份联动的专科树
import { treeList } from '@/api/major' // 专科树
import { academicTreeListById } from '@/api/academic' // 职称树
import { getDeptTree } from '@/api/dept' // 职称树
import { getAreaTree } from '@/api/area' // 区域树
import { getVipLevelList } from '@/api/vip'

export default {
  name: 'Unit',
  components: {
    Pagination,
    DialogUnitEdit,
    DialogFreeCourse
  },
  filters: {
    filterStatus(val) {
      return val ? '启用' : '停用'
    }
  },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // loading
      loading: false,
      // 通过检验
      approved: false,
      // 提交按钮
      // submitDisable: true,
      // 手机号校验
      searchOption: {
        fn: this.invitOrgAdminStaffFind
      },
      // 事件行为
      action: 'add',
      // 获取管理员列表
      dialogGetManagerShow: false,
      // 设置管理员列表
      dialogSetManagerShow: false,
      dialogVisible: false,
      title: '创建单位',
      // 单位列表表头
      tableColumnList: [
        { id: 0, label: 'ID', prop: 'orgId', align: 'center' },
        { id: 1, label: '单位名称', prop: 'orgName', align: 'center' },
        { id: 2, label: '单位类型', prop: 'typeName', align: 'center' },
        { id: 3, label: '层级', prop: 'levelName', align: 'center' },
        { id: 4, label: '医考通题库', prop: 'yktBankNum', align: 'center' },
        { id: 5, label: '员工限额', prop: 'maxStaffCount', align: 'center' },
        { id: 6, label: '会员等级', prop: 'memberLevelName', align: 'center' },
        { id: 7, label: '服务状态', prop: 'status', align: 'center' },
        { id: 8, label: '创建时间', prop: 'createTime', align: 'center' }
      ],
      // 单位列表
      userList: [],
      // 类型
      typeList: [],
      typeFilterList: [
        { type: 183, name: '医院' },
        { type: 9, name: '卫计委' },
        { type: 8, name: '平台管理机构' }
      ],
      // 状态
      statusList: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 }
      ],
      memberLevelList: [],
      // 层级
      levelList: [],
      // 请求参数
      tableQuery: {
        condition: {
          isPromote: 0,
          keyword: null,
          level: null,
          status: null,
          type: null,
          memberLevel: null
        },
        orderBys: [],
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 是否存在用户
      hasUser: false,
      // 单位管理员列表表头
      userManageTableColumn: [
        { id: 0, label: '手机号', prop: 'userPhone', align: 'center' },
        { id: 1, label: '姓名', prop: 'realName', align: 'center' },
        { id: 2, label: '用户账号', prop: 'userAccounts', align: 'center' },
        { id: 3, label: '员工账号', prop: 'username', align: 'center' }
      ],
      userManagerList: [], // 单位管理员列表
      // 设置管理员表单
      formDataManager: {
        region: [],
        realName: '',
        phone: '',
        academicId: '',
        areaId: '',
        cityId: '',
        provinceId: '',
        orgName: '',
        deptId: '',
        identityId: '',
        majorId: '',
        orgId: 0,
        userId: 0,
        skill: '',
        introduction: ''
      },
      rules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        identityId: [
          { required: true, message: '请选择身份', trigger: 'change' }
        ],
        majorId: [
          { required: true, message: '请选择执业专科', trigger: 'change' }
        ],
        academicId: [
          { required: true, message: '请选择职称', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择所在地区', trigger: 'change' }
        ]
      },
      identityTree: [],
      majorList: [],
      academicList: [],
      deptList: [],
      areaList: [],
      disabled: false,
      curUserId: '', // 当前用户id
      curOrgId: '', // 当前单位id
      // 新增/编辑单位表单
      formData: {
        createTime: '',
        createUid: 0,
        level: null,
        maxStaffCount: 0,
        maxSizeCount: 0,
        orgId: 0,
        orgName: '',
        parentOrgId: 0,
        parentOrgIds: 0,
        status: null,
        type: '',
        shortCode: '',
        contacts: '',
        phone: '',
        provinceId: null,
        cityId: null,
        areaId: null,
        address: '',
        scale: '',
        logo: '',
        logoUrl: '',
        subjuctCode: '',
        faceFlag: null,
        examFlag: null,
        bannerId: null,
        bannerUrl: '',
        trainBannerId: null,
        trainBannerUrl: '',
        serviceProviderOrgId: null,
        socialCreditCode: null,
        keySecret: {
          key: '',
          secret: ''
        },
        memberLevel: null,
        memberStartTime: '',
        memberEndTime: ''
      },
      // 提交表单
      submitFormData: {
        enterDto: {
          address: '',
          buyVideoSize: 0,
          contacts: '',
          industryId: '',
          introdution: '',
          maxSizeCount: 0,
          maxStaffCount: 0,
          phone: '',
          scale: ''
        },
        provinceId: null,
        cityId: null,
        areaId: null,
        level: 0,
        status: 0,
        orgName: '',
        shortCode: '',
        type: 0
      },
      // 分页
      total: 0,
      // 校验手机获取的内容
      currentStaffInfo: {},
      openYKTdialog: false,
      openYKTorgId: '',
      openYKTorgName: '',
      quesTree: [],
      quesTreeProps: {
        children: 'children',
        label: 'title'
      },
      checkedTitles: [],
      lookYKTdialog: false,
      yktTableQuery: {
        condition: {
          bankName: '',
          orgId: '',
          status: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      yktTotal: 0,
      yktList: [],
      alreadyIds: [],
      // 开通免费培训课程相关
      freeCourseDialogVisible: false,
      currentOrgData: {}
    }
  },
  computed: {
    submitDisable() {
      if (this.hasUser) {
        return !!(this.formDataManager.realName)
      } else {
        return !!(this.formDataManager.phone && this.approved)
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    // ---------开通医考通--------
    openYKT(row) {
      if (row.status === 0) {
        this.$message.info('【' + row.orgName + '】已停用，禁止开通医考通题库！')
        return
      }
      this.checkedTitles = []
      this.openYKTorgId = row.orgId
      this.openYKTorgName = row.orgName
      this.yktTableQuery.condition.orgId = row.orgId
      this.yktTableQuery.condition.status = 1
      this.yktTableQuery.pager.pageSize = row.yktBankNum
      getBankList(this.yktTableQuery).then(res => {
        // 提取用户已开通且未失效的题库id用于回显默认勾选
        this.alreadyIds = res.records.filter(i => i.status === 1).map(i => i.bankId)
        this.yktList = res.records
      })
      getQuestionByTree().then(res => {
        this.quesTree = res
        this.openYKTdialog = true
        this.$nextTick(() => {
          // 回显已开通且未失效的题库名称
          this.quesTreeChange()
        })
      })
    },
    quesTreeChange() {
      this.checkedTitles = []
      this.$refs.quesTree.getCheckedNodes().forEach(item => {
        if (!item.children) {
          this.checkedTitles.push(item.title)
        }
      })
    },
    openYKTconfirm() {
      const checkedIds = []
      this.$refs.quesTree.getCheckedNodes().forEach(item => {
        if (!item.children) {
          checkedIds.push(item.id)
        }
      })
      const cancelIds = []
      const cancelBankNames = []
      if (this.yktList.length > 0) {
        this.yktList.forEach(item => {
          if (checkedIds.indexOf(item.bankId) === -1) {
            cancelIds.push(item.bankId)
            cancelBankNames.push(item.bankName)
          }
        })
      }
      const query = {
        bankIdList: checkedIds,
        orgId: this.openYKTorgId,
        cancelBankIdList: cancelIds
      }
      if (cancelIds.length > 0) {
        this.$confirm('单位【' + this.openYKTorgName + '】 已开通【' + cancelBankNames.join(',') + '】, 是否取消并继续?', '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }).then(() => {
          openyktQuestion(query).then(res => {
            this.openYKTdialog = false
            this.$message.success('开通成功')
            this.getUnitUserList()
          })
        }).catch(() => {
        })
        return
      } else {
        openyktQuestion(query).then(res => {
          this.openYKTdialog = false
          this.$message.success('开通成功')
          this.getUnitUserList()
        })
      }
    },
    // ---------开通医考通--------

    // ----------查看医考通题库-----
    lookYKT(orgId, search) {
      if (orgId && !search) {
        this.yktTableQuery.condition.orgId = orgId
        this.lookYKTdialog = true
        this.yktTableQuery.pager.pageSize = 10
      } else if (orgId && search) {
        this.yktTableQuery.pager.page = 1
      }
      getBankList(this.yktTableQuery).then(res => {
        this.yktList = res.records
        this.yktTotal = res.total
      })
    },
    yktPagination(val) {
      this.yktTableQuery.pager = val
      this.lookYKT()
    },
    // ----------查看医考通题库-----

    // hard code..
    filterType(val) {
      let typeName = ''
      this.typeFilterList.forEach(v => {
        if (v.type === val) {
          typeName = v.name
        }
      })
      return typeName
    },
    // quick login
    async quickLogin(safeCode) {
      const res = await easyLogin({
        safeCode,
        actionCode: 'UnitIndex' + 'easyLogin'
      })
      const newWindow = window.open()
      if (res) {
        const params = encodeURIComponent(JSON.stringify(res))
        newWindow.location.href =
          process.env.VUE_APP_SAAS_URL + '#/home?p=' + params
      }
    },
    // checkNumberCode
    filterCheckPhoneCode(code) {
      const codeToMessage = {
        1: '手机号用户不存在，创建成为单位管理员',
        2: '手机号用户是本单位员工，确认后添加为单位管理员',
        3: '手机号用户已存在，编辑确认后添加为单位管理员',
        4: '该手机用户正在导入中，无法新增'
      }
      return codeToMessage[code]
    },
    // init
    init() {
      this.handleGetOrganTypeList()
      this.getUnitUserList()
      // 获取身份树
      identityTreeList().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        newArr.push({ name: '无', identityId: '0' })
        this.identityTree = newArr
      })
      // 获取专科树
      treeList().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        newArr.push({ name: '无', majorId: '0' })
        this.majorList = newArr
      })
      // 获取区域树
      getAreaTree().then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.areaList = newArr
      })
      getVipLevelList().then(res => {
        this.memberLevelList = res || []
      }).catch(error => {
        console.error('获取会员等级列表失败:', error)
        this.memberLevelList = []
      })
      if (this.$route.query.id) {
        this.getDateil()
      }
    },
    handleRegion() {
      this.formDataManager.provinceId = this.formDataManager.region[0]
      this.formDataManager.cityId = this.formDataManager.region[1]
      this.formDataManager.areaId = this.formDataManager.region[2]
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    // 身份树change事件 根据选中的身份查询对应的专科信息及职称信息
    identityChange(e) {
      if (e && e === '0') {
        // 身份选'无'，专科职称也为无
        this.formDataManager.majorId = '0'
        this.formDataManager.academicId = '0'
        this.disabled = true
        return
      } else {
        this.disabled = false
        majorTreeListId(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', majorId: '0' })
          this.majorList = newArr
        })
        academicTreeListById(e).then(res => {
          const newArr = this.clearNullChildList(res, 'childList')
          newArr.push({ name: '无', academicId: '0' })
          this.academicList = newArr
        })
      }
    },
    // search
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getUnitUserList()
    },
    // service status
    handleChangeStatus() {
      this.getUnitUserList()
    },
    // unit level
    handleChangeLevel() {
      this.getUnitUserList()
    },
    handleChangeType(val) {
      this.tableQuery.condition.level = ''
      this.levelList = []
      if (val) {
        getOrganLevelList(val).then(res => {
          this.levelList = res
        })
        this.getUnitUserList()
      }
    },
    handleBlurLevel() {
      if (!this.tableQuery.condition.type) {
        this.$message.error('请先选择单位类型')
      }
    },
    // get type list
    handleGetOrganTypeList() {
      getOrganTypeList({ action: 1 }).then(res => {
        this.typeList = res
      })
    },
    // get unit user
    getUnitUserList() {
      getUnitList(this.tableQuery).then(res => {
        res.records.forEach(item => {
          const memberLevel = this.memberLevelList.find(v => v.level === item.memberLevel)
          item.memberLevelName = memberLevel ? memberLevel.name : '-'
        })
        this.userList = res.records
        this.total = res.total
      })
    },
    // add unit user
    addUnitDialogShow() {
      this.action = 'add'
      this.title = '创建单位'
      this.dialogVisible = true
    },
    addUnitUser() {
      addUnit(this.submitFormData).then(res => {
        this.$message.success('添加成功!')
        this.dialogVisible = false
        this.getUnitUserList()
        this.clearUnitSubmitForm()
      })
    },
    // set unit manager
    setManager(row) {
      this.formDataManager.orgId = row.orgId
      this.curOrgId = row.orgId
      getOrganAdminListByOrgId(row.orgId).then(res => {
        this.userManagerList = res.records
        this.dialogGetManagerShow = true
      })
    },
    // edit uint user
    editRow(row) {
      getUnitDetail(row.orgId).then(res => {
        this.formData.createTime = res.createTime
        this.formData.createUid = res.createUid
        this.formData.areaId = res.areaId
        this.formData.provinceId = res.provinceId
        this.formData.cityId = res.cityId
        this.formData.address = res.enterDto.address
        this.formData.buyVideoSize = res.enterDto.buyVideoSize
        this.formData.contacts = res.enterDto.contacts
        this.formData.industryId = res.enterDto.industryId
        this.formData.maxSizeCount = res.enterDto.maxSizeCount
        this.formData.maxStaffCount = res.enterDto.maxStaffCount
        this.formData.phone = res.enterDto.phone
        this.formData.scale = res.enterDto.scale
        this.formData.logo = res.enterDto.logo
        this.formData.logoUrl = res.enterDto.logoUrl
        this.formData.bannerId = res.enterDto.bannerId
        this.formData.bannerUrl = res.enterDto.bannerUrl
        this.formData.trainBannerId = res.enterDto.trainBannerId
        this.formData.trainBannerUrl = res.enterDto.trainBannerUrl
        this.formData.subjuctCode = res.enterDto.subjuctCode || 'green'
        this.formData.serviceProviderOrgId = res.enterDto.serviceProviderOrgId
        this.formData.socialCreditCode = res.enterDto.socialCreditCode
        this.formData.keySecret = res.enterDto.keySecret
        this.formData.level = res.level
        this.formData.orgId = res.orgId
        this.formData.orgName = res.orgName
        this.formData.parentOrgId = res.parentOrgId
        this.formData.shortCode = res.shortCode
        this.formData.status = res.status
        this.formData.type = res.type
        this.formData.faceFlag = res.faceFlag
        this.formData.examFlag = res.examFlag
        this.formData.conExtenderLiveFlag = res.conExtenderLiveFlag
        this.formData.memberLevel = res.memberLevel || null
        this.formData.memberStartTime = res.memberStartTime || ''
        this.formData.memberEndTime = res.memberEndTime || ''

        // show dialog
        this.action = 'edit'
        this.title = '编辑单位'
        this.dialogVisible = true
      })
    },
    editUnitUser() {
      editUnit(this.submitFormData).then(res => {
        this.$message.success('编辑成功!')
        this.dialogVisible = false
        this.getUnitUserList()
        this.clearUnitSubmitForm()
      })
    },
    // 创建管理员
    showCreateManager() {
      this.dialogSetManagerShow = true
    },
    // set unit user status
    setStatusRow(row) {
      this.$confirm(
        '您确认' +
          (row.status ? '停用' : '启用') +
          ' "' +
          row.orgName +
          '" 这个单位吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      ).then(() => {
        unitStatusOption(row.orgId).then(res => {
          if (row.status) {
            row.status = 0
            this.$message.success('停用成功！')
          } else {
            row.status = 1
            this.$message.success('启用成功！')
          }
          this.getUnitUserList()
        })
      })
    },
    // cancel form
    handleCancelDialogForm() {
      this.clearUnitSubmitForm()
      this.dialogVisible = false
    },
    // save form
    handleSaveDialogForm(val) {
      if (this.action === 'add') {
        this.submitFormData.enterDto.address = val.address
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.maxSizeCount = val.maxSizeCount
        this.submitFormData.enterDto.maxStaffCount = val.maxStaffCount
        this.submitFormData.enterDto.phone = val.phone === '' ? null : val.phone
        this.submitFormData.enterDto.logo = val.logo || 0
        this.submitFormData.enterDto.bannerId = val.bannerId || 0
        this.submitFormData.enterDto.trainBannerId = val.trainBannerId || 0
        this.submitFormData.enterDto.subjuctCode = val.subjuctCode
        this.submitFormData.enterDto.faceFlag = val.faceFlag
        this.submitFormData.enterDto.examFlag = val.examFlag
        this.submitFormData.enterDto.serviceProviderOrgId = val.serviceProviderOrgId
        this.submitFormData.enterDto.socialCreditCode = val.socialCreditCode
        this.submitFormData.enterDto.keySecret = val.keySecret || ''
        this.submitFormData.provinceId = val.provinceId
        this.submitFormData.cityId = val.cityId
        this.submitFormData.areaId = val.areaId
        this.submitFormData.level = val.level
        this.submitFormData.orgName = val.orgName
        this.submitFormData.shortCode = val.shortCode
        this.submitFormData.type = val.type
        this.submitFormData.status = val.status
        this.submitFormData.memberLevel = val.memberLevel
        this.submitFormData.memberStartTime = val.memberStartTime
        this.submitFormData.memberEndTime = val.memberEndTime
        this.addUnitUser()
      } else if (this.action === 'edit') {
        this.submitFormData.enterDto.address = val.address
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.maxSizeCount = val.maxSizeCount
        this.submitFormData.enterDto.maxStaffCount = val.maxStaffCount
        this.submitFormData.enterDto.buyVideoSize = val.buyVideoSize
        this.submitFormData.enterDto.contacts = val.contacts
        this.submitFormData.enterDto.introdution = val.introdution
        this.submitFormData.enterDto.industryId = val.industryId
        this.submitFormData.enterDto.phone = val.phone === '' ? null : val.phone
        this.submitFormData.enterDto.scale = val.scale
        this.submitFormData.enterDto.logo = val.logo || 0
        this.submitFormData.enterDto.bannerId = val.bannerId || 0
        this.submitFormData.enterDto.trainBannerId = val.trainBannerId || 0
        this.submitFormData.enterDto.subjuctCode = val.subjuctCode
        this.submitFormData.enterDto.serviceProviderOrgId = val.serviceProviderOrgId
        this.submitFormData.enterDto.socialCreditCode = val.socialCreditCode
        this.submitFormData.enterDto.keySecret = val.keySecret || ''
        this.submitFormData.provinceId = val.provinceId
        this.submitFormData.cityId = val.cityId
        this.submitFormData.areaId = val.areaId
        this.submitFormData.level = val.level
        this.submitFormData.orgName = val.orgName
        this.submitFormData.shortCode = val.shortCode
        this.submitFormData.type = val.type
        this.submitFormData.status = val.status
        this.submitFormData.orgId = val.orgId
        this.submitFormData.faceFlag = val.faceFlag
        this.submitFormData.examFlag = val.examFlag
        this.submitFormData.memberLevel = val.memberLevel
        this.submitFormData.memberStartTime = val.memberStartTime
        this.submitFormData.memberEndTime = val.memberEndTime
        this.editUnitUser()
      }
    },
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUnitUserList()
    },
    // 设置管理员
    handleSetManagerSubmit() {
      // 无任何信息
      if (this.currentStaffInfo.code === 1) {
        // 新增
        addUnitManager({ ...this.formDataManager, orgId: this.curOrgId }).then(
          res => {
            this.$message.success('新增管理员成功！')
            getOrganAdminListByOrgId(this.curOrgId).then(res => {
              this.userManagerList = res.records
            })
            this.dialogSetManagerShow = false
            this.hasUser = false
          }
        )
      } else if (this.currentStaffInfo.code === 2) {
        // 存在成员信息
        // 设置
        this.$confirm(
          '是否设置“' +
            this.currentStaffInfo.staffDto.username +
            '”为单位管理员？',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          setUnitManager({
            ...this.formDataManager,
            orgId: this.curOrgId,
            staffId: this.currentStaffInfo.staffDto.staffId
          }).then(res => {
            this.$message.success('设置管理员成功！')
            getOrganAdminListByOrgId(this.curOrgId).then(res => {
              this.userManagerList = res.records
            })
            this.dialogSetManagerShow = false
            this.hasUser = false
          })
        })
      } else if (this.currentStaffInfo.code === 3) {
        // 有用户信息,无成员信息
        this.formDataManager.userId = this.currentStaffInfo.staffDto.userId
        this.curUserId = this.currentStaffInfo.staffDto.userId
        addUnitManagerWithoutStaffInfo({
          ...this.formDataManager,
          orgId: this.curOrgId
        }).then(res => {
          this.$message({
            type: 'success',
            message: '邀请成员成功！'
          })
          this.dialogSetManagerShow = false
          this.dialogGetManagerShow = false
          this.hasUser = false
        })
      }
    },
    setUserManagerDialogColse() {
      this.dialogSetManagerShow = false
      this.hasUser = false
      this.clearDialogGetManagerForm()
    },
    // 手机号校验
    invitOrgAdminStaffFind() {
      if (!this.formDataManager.phone) {
        return this.$message.error('请输入手机号')
      }
      this.loading = true
      const params = {
        orgId: this.curOrgId,
        phone: this.formDataManager.phone
      }
      getDeptTree(params).then(res => {
        this.deptList = res
      })
      invitOrgAdminStaffFind(params)
        .then(res => {
          this.loading = false
          this.currentStaffInfo = res
          if ([1, 2, 3].includes(res.code)) {
            this.$message.success(this.filterCheckPhoneCode(res.code))
            this.hasUser = true
            this.formDataManager.region = [
              res.orgInfo.provinceId,
              res.orgInfo.cityId,
              res.orgInfo.areaId
            ]
            this.handleRegion()
            this.formDataManager.orgName = res.orgInfo.orgName
            this.formDataManager.orgId = res.orgInfo.orgId
            if ([2, 3].includes(res.code)) {
              this.formDataManager.identityId = res.staffDto.identityId.toString()
              this.formDataManager.majorId = res.staffDto.majorId.toString()
              this.formDataManager.realName = res.staffDto.realName
              this.formDataManager.academicId = res.staffDto.academicId
              this.formDataManager.deptId = res.staffDto.deptId.toString()
              this.identityChange(this.formDataManager.identityId.toString())
            }
          } else {
            this.$message.success(this.filterCheckPhoneCode(res.code))
          }
          this.approved = true
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 清空表单
    clearDialogGetManagerForm() {
      this.formDataManager = {
        region: [],
        realName: '',
        phone: '',
        academicId: '',
        areaId: '',
        cityId: '',
        provinceId: '',
        orgName: '',
        deptId: '',
        identityId: '',
        majorId: '',
        orgId: 0,
        userId: 0,
        skill: '',
        introduction: ''
      }
      this.approved = false
    },
    // 清空表单
    clearUnitSubmitForm() {
      const formdata = this.formData
      formdata.createTime = ''
      formdata.createUid = 0
      formdata.level = null
      formdata.maxStaffCount = 0
      formdata.maxSizeCount = 0
      formdata.orgId = 0
      formdata.orgName = ''
      formdata.parentOrgId = 0
      formdata.parentOrgIds = 0
      formdata.status = null
      formdata.type = ''
      formdata.shortCode = ''
      formdata.contacts = ''
      formdata.phone = ''
      formdata.provinceId = null
      formdata.cityId = null
      formdata.areaId = null
      formdata.address = ''
      formdata.scale = ''
      formdata.logo = ''
      formdata.logoUrl = ''
      formdata.subjuctCode = ''
      formdata.faceFlag = null
      formdata.examFlag = null
      formdata.bannerId = null
      formdata.bannerUrl = ''
      formdata.trainBannerId = null
      formdata.trainBannerUrl = ''
      formdata.serviceProviderOrgId = null
      formdata.socialCreditCode = null
      formdata.keySecret = { key: '', secret: '' }
      formdata.memberLevel = null
      formdata.memberStartTime = ''
      formdata.memberEndTime = ''
    },
    // 开通免费培训课程
    openFreeCourse(row) {
      this.currentOrgData = {
        orgId: row.orgId,
        orgName: row.orgName
      }
      this.freeCourseDialogVisible = true
    },
    // 开通免费培训课程成功回调
    handleFreeCourseSuccess() {
      // 可以在这里刷新单位列表或执行其他操作
      // this.getUnitUserList()
      console.log('免费培训课程开通成功')
    }
  }
}
</script>

<style lang="scss" scoped>
.warn-tips {
  padding: 5px 0;
  font-size: 12px;
  line-height: 16px;
  color: #666;
}
</style>
