import request from '@/utils/request'

// 专家列表
export function doctorList(params) {
  return request({
    url: '/user/doctor/list',
    method: 'post',
    data: params
  })
}

// 专家 添加/编辑
export function addDoctor(params) {
  return request({
    url: '/user/doctor/add',
    method: 'post',
    data: params
  })
}

// 专家 添加/编辑
export function editDoctor(params) {
  return request({
    url: '/user/doctor/edit',
    method: 'post',
    data: params
  })
}

// 专家 删除
export function delDoctor(userId) {
  return request({
    url: `/user/doctor/delete?userId=${userId}`,
    method: 'post'
  })
}
