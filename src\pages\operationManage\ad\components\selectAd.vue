<template>
  <el-dialog title="请选择广告" :visible.sync="visible" width="60%" center>
    <el-table fit :data="tableData" height="350" style="width: 100%" highlight-current-row @current-change="rowSelect">
      <el-table-column type="index" width="50" />
      <el-table-column prop="date" label="广告类型" width="180" />
      <el-table-column prop="name" label="广告位置" width="180" />
      <el-table-column prop="address" label="广告状态" />
      <el-table-column fixed="right" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetail(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'SelectAd',
  data() {
    return {
      visible: false,
      tableData: [],
      selectedRow: {}
    }
  },
  methods: {
    rowSelect(e) {
      this.selectedRow = e
    },
    viewDetail(row) {
      console.log(row)
    },
    confirm() {
      this.$emit('selected', this.selectedRow)
      this.visible = false
    }
  }
}
</script>
