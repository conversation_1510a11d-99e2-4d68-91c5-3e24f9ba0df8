<template>
  <el-popover
    v-model="show"
    placement="bottom-start"
    width="400"
    trigger="click"
  >
    <el-input slot="reference" placeholder="选择单位" :value="unit.length ? '已全选' + unit.length + '个层级' : ''">
      <i slot="suffix" class="el-input__icon el-icon-arrow-down" :class="{'is-reverse':show }" />
    </el-input>
    <!-- tree -->
    <div v-if="orgId" class="scroll-tree">
      <el-scrollbar>
        <el-tree
          ref="tree"
          node-key="orgId"
          show-checkbox
          :data="unitList"
          :filter-node-method="filterNode"
          lazy
          :load="handleLoadList"
          :props="defaultProps"
          @check-change="handleCheckChange"
        />
      </el-scrollbar>
    </div>
  </el-popover>

</template>

<script>
import { getChildrenByOrgId } from '@/api/wjw'

export default {
  name: 'DialogFoundationedit',
  props: {
    orgId: {
      type: String,
      default: ''
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      show: false,
      disable: false,
      unit: [],
      // tree
      defaultProps: {
        children: 'childList',
        label: 'orgName',
        isLeaf: 'leaf'
      },
      // halfKeys: [], // 半选中数组(fake click方案)
      unitList: []
    }
  },
  watch: {
    orgId(v) {
      v && this.getProjectUintList()
    },
    value(v) {
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys(v)
      }
    }
  },
  mounted() {
  },
  methods: {
    // 创建时 - 单位
    getProjectUintList() {
      getChildrenByOrgId(this.orgId)
        .then(res => {
          this.unitList = res.map(e => ({ ...e, id: e.orgId }))
        })
      return this.orgId
    },
    // tree lazy load
    handleLoadList(node, resolve) {
      if (node.level === 0) return resolve([])
      getChildrenByOrgId(node.data.orgId || this.orgId)
        .then(res => {
          res.forEach(i => {
            i.leaf = !i.hasSub
          })
          resolve(res)
        })
        .catch(res => {
          resolve([])
        })
    },
    getCheckedKeys(node) {
      function uniqueArr(array) {
        var n = [] // 一个新的临时数组
        // 遍历当前数组
        for (var i = 0; i < array.length; i++) {
          // 如果当前数组的第i已经保存进了临时数组，那么跳过，
          // 否则把当前项push到临时数组里面
          if (n.indexOf(array[i]) === -1) n.push(array[i])
        }
        return n
      }
      let arr = []
      const arrNode = []
      let parentOrgIds = []
      // 寻找所有选中，寻找所有父级
      node.forEach(i => {
        if (i.checked) {
          // console.log(i)
          arr.push(i.data.orgId)
          parentOrgIds = parentOrgIds.concat(i.data.parentOrgIds.split(','))
          if (i.childNodes.length) {
            arrNode.push({
              orgId: i.data.orgId,
              childNodes: i.childNodes
            })
          }
        }
      })
      // 父级去重
      parentOrgIds = uniqueArr(parentOrgIds)
      // 排除选中的子级选中
      arrNode.forEach(i => {
        i.childNodes.forEach(j => {
          arr = arr.filter(item => item !== j.data.orgId)
        })
      })
      // 取父级数组和选中数组的差集，去除根orgId以及空值
      parentOrgIds = parentOrgIds.concat(arrNode).filter(v => parentOrgIds.includes(v) && ![...arrNode, this.orgId, ''].includes(v))
      return [arr, parentOrgIds]
    },
    // tree 复选框选中
    handleCheckChange(data, checked, indeterminate) {
      const selectedArr = this.getCheckedKeys(this.$refs.tree.store._getAllNodes())
      // console.log(selectedArr)
      this.unit = selectedArr[0]
      this.$emit('input', selectedArr[0])
      this.$emit('half', selectedArr[1])
      this.defaultCheckedKeys = selectedArr[0]
    },
    // filter tree node
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-tree {
  height: 350px;
  overflow: hidden;
  .el-scrollbar {
    height: 100%;
    ::v-deep .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
  ::v-deep {
    .el-checkbox__input.is-disabled {
      &.is-checked .el-checkbox__inner,
      &.is-indeterminate .el-checkbox__inner {
        background-color: #409eff;
        border-color: #409eff;
      }
    }
  }
}
.el-icon-arrow-down.is-reverse {
  -webkit-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
.el-icon-arrow-down {
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
  transition: transform .3s,-webkit-transform .3s;
  font-size: 14px;
}
</style>
