<template>
  <el-dialog
    title="重签协议"
    :visible.sync="dialogVisible"
    width="420px"
    :before-close="handleClose"
    center
  >
    <el-checkbox-group v-model="list">
      <el-checkbox :label="1">入驻协议</el-checkbox>
      <el-checkbox :label="2">承揽协议</el-checkbox>
    </el-checkbox-group>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { reSignAgreement } from '@/api/marketing/userPromote'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: []
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    confirm() {
      reSignAgreement({
        applyId: this.id,
        bizTypeList: this.list
      }).then(() => {
        this.$parent.getUserList()
      })
      this.$emit('update:dialogVisible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 45px 24px;
    color: #333;
  }
}
</style>
