<template>
  <div class="container">
    <el-form
      ref="formQuestion"
      :model="formQuestion"
      label-width="100px"
      :rules="rules"
      label-position="right"
    >
      <el-form-item label="病例:" prop="titles" style=" padding-right: 20px;">
        <el-input
          v-model="formQuestion.title"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          placeholder="请输入病例"
        />
      </el-form-item>
      <el-form-item>
        <div>
          <!-- <el-scrollbar style="height: 100%"> -->
          <div class="problem-container">
            <div v-for="(li, i) in formQuestion.aone" :key="i" class="problem">
              <div style="margin: 0 auto 10px; text-align:center">问题{{ i+1 }}</div>
              <el-form-item :label="'问题'+`${i+1}`+':'" prop="title" style="margin-bottom: 20px;">
                <el-input
                  v-model="li.title"
                  type="textarea"
                  :resize="none"
                  :autosize="autosize"
                  :placeholder="'请输入问题'+`${i+1}`"
                />
              </el-form-item>
              <el-form-item
                label="选项:"
                prop="optionsList"
                style="margin-bottom: 0"
              >
                <!-- <el-radio-group v-model="li.radio"> -->
                <div v-for="(item, idx) in li.optionsList" :key="idx">
                  <div class="flex">
                    <span>{{ item.index }}.</span>
                    <el-input
                      v-model="item.content"
                      type="text"
                      placeholder="请输入选项内容"
                      @input="change($event)"
                    />
                    <el-radio
                      v-model="li.radio"
                      :label="idx"
                      @change="radioChange"
                    >{{ "" }}</el-radio>
                    <el-button
                      type="text"
                      :disabled="deleteBtn"
                      class="deleteBtn"
                      @click="btnClick(i,idx)"
                    ><i
                      class="el-icon-remove-outline"
                    /></el-button>
                  </div>
                </div>
                <!-- </el-radio-group> -->

              </el-form-item>
              <el-button
                plain
                class="xl-btn"
                :disabled="addBtn"
                @click="addOption(i)"
              >
                <i class="el-icon-plus" />
                添加选项
              </el-button>
              <el-form-item label="考点关键字:" style="margin-bottom: 20px;">
                <el-input
                  v-model="li.points"
                  type="textarea"
                  :resize="none"
                  :autosize="autosize"
                  :maxlength="300"
                  placeholder="请输入考点关键字"
                />
              </el-form-item>
              <el-form-item label="解析:" style="margin-bottom: 20px;">
                <el-input
                  v-model="li.desc"
                  type="textarea"
                  :resize="none"
                  :autosize="autosize"
                  :maxlength="3000"
                  placeholder="请输入解析"
                />
              </el-form-item>
              <div>
                <i class="el-icon-delete icon" @click="del(i)" />
              </div>
            </div>
          </div>
          <!-- </el-scrollbar> -->
        </div>
      </el-form-item>
      <el-button
        plain
        class="xl-btn"
        :disabled="addBtn"
        style="margin: 5px 0 10px 100px;"
        @click="addProblem"
      >
        <i class="el-icon-plus" />
        添加问题
      </el-button>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Aone',
  props: {
    aData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      title: 'A1',
      count: 0,
      none: 'none', // 控制快速录入输入框不能被缩放
      addBtn: false, // 控制添加选项是否禁用
      deleteBtn: false, // 删除选项是否禁用
      aThreeLabel: '问题1',
      aThreePlace: '请输入问题1',
      autosize: {
        minRows: 6,
        maxRows: 8
      },
      radio: {
        content: '',
        index: '',
        isAnswer: 0
      },
      aone: [],
      formQuestion: {
        aone: [
          {
            desc: '',
            optionsList: [
              {
                content: '',
                index: '',
                isAnswer: 0
              }
            ],
            points: '',
            title: '',
            radio: ''
          }
        ],
        title: ''
      },
      rules: {
        title: [{ required: true, message: '问题不能为空', trigger: 'input' }],
        titles: [{ required: true, message: '请输入病例', trigger: 'input' }],
        optionsList: [
          { required: true, message: '请输入活动名称', trigger: 'input' }
        ]
      }
    }
  },
  watch: {
    aData: {
      handler(newval, oldvar) {
        if (JSON.stringify(newval) !== '{}') {
          this.$nextTick(() => {
            this.formQuestion.title = newval.title
            this.formQuestion.aone = JSON.parse(JSON.stringify(newval.aone))
            this.formQuestion.aone.forEach(item => {
              item.radio = ''
              delete item.cateId
              delete item.difficulty
              delete item.parentQuestionId
              delete item.questionType
            })
            this.formQuestion.aone.forEach(item => {
              item.optionsList.forEach((ite, idx) => {
                if (ite.isAnswer === 1) {
                  item.radio = idx
                }
              })
            })
          })
        }
      },
      deep: true,
      immediate: true
    },
    isType() {
      this.formQuestion.title = ''
      const problem = [
        { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' },
        { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' },
        { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' }
      ]
      this.formQuestion.aone = problem
      for (const key in this.formQuestion.aone) {
        const arr = [
          { index: 'A', content: '', isAnswer: 0 },
          { index: 'B', content: '', isAnswer: 0 },
          { index: 'C', content: '', isAnswer: 0 },
          { index: 'D', content: '', isAnswer: 0 },
          { index: 'E', content: '', isAnswer: 0 }
        ]
        this.formQuestion.aone[key].optionsList = arr
      }
    }
  },
  created() {
    const problem = [
      { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' },
      { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' },
      { desc: '', optionsList: [{ content: '', index: '', isAnswer: 0 }], points: '', title: '', radio: '' }
    ]
    this.formQuestion.title = ''
    this.formQuestion.aone = problem
    for (const key in this.formQuestion.aone) {
      const arr = [
        { index: 'A', content: '', isAnswer: 0 },
        { index: 'B', content: '', isAnswer: 0 },
        { index: 'C', content: '', isAnswer: 0 },
        { index: 'D', content: '', isAnswer: 0 },
        { index: 'E', content: '', isAnswer: 0 }
      ]
      this.formQuestion.aone[key].optionsList = arr
    }
  },
  methods: {
    load() {
      this.count += 2
    },
    change() {
      this.$forceUpdate()
    },
    radioChange() {
      this.$forceUpdate()
    },
    // 添加选项
    addOption(i) {
      this.$forceUpdate()
      if (this.formQuestion.aone[i].optionsList.length > 9) return this.$message('最多10个选项')
      const obj = {
        index: String.fromCharCode(
          this.formQuestion.aone[i].optionsList.length + 65
        ),
        content: '',
        isAnswer: 0
      }
      this.formQuestion.aone[i].optionsList.push(obj)
    },
    // 删除选项
    btnClick(i, idx) {
      this.$forceUpdate()
      if (this.formQuestion.aone[i].optionsList.length < 3) return this.$message('最少2个选项')
      this.formQuestion.aone[i].optionsList.splice(idx, 1)
      // 删除选中答案的选项时，同时删除选中的答案
      if (this.formQuestion.aone[i].radio === idx) {
        this.formQuestion.aone[i].radio = ''
      }
      for (const key in this.formQuestion.aone[i].optionsList) {
        this.formQuestion.aone[i].optionsList[key].index = this.charCode(Number(key))
      }
    },
    // 添加問題
    addProblem() {
      if (this.formQuestion.aone.length > 9) return this.$message('最多10个问题')
      const obj = { desc: '', optionsList: [], points: '', title: '', radio: '' }
      const arr = [
        { index: 'A', content: '', isAnswer: 0 },
        { index: 'B', content: '', isAnswer: 0 },
        { index: 'C', content: '', isAnswer: 0 },
        { index: 'D', content: '', isAnswer: 0 },
        { index: 'E', content: '', isAnswer: 0 }
      ]
      obj.optionsList = arr
      this.formQuestion.aone.push(obj)
    },
    // 删除问题
    del(i) {
      const that = this
      this.$confirm('是否删除此问题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        if (that.formQuestion.aone.length < 3) {
          that.$message('最少2个问题')
        } else {
          that.formQuestion.aone.splice(i, 1)
          that.$message({
            type: 'success',
            message: '删除成功!'
          })
        }
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    charCode(val) {
      return String.fromCharCode(val + 65)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 60%;
  margin: 20px auto;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    * {
      margin: 0 10px 20px 0;
    }
  }
  ol {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: upper-latin;
    li {
      list-style-position: outside;
    }
  }
  .xl-btn {
    width: 90%;
    margin: 0px 0 10px 100px;
    border: 1px dashed #dcdfe6;
  }
  .xl-btn:hover {
    border: 1px dashed #409eff;
  }
}
// .scroll-list{
//     // height: calc(100vh - 240px);
//   }
::v-deep .el-checkbox__inner {
  border-radius: 7px;
}
::v-deep .el-radio-group {
  display: block;
  font-size: 16px;
  line-height: 16px;
}
.container .li:last-child {
  margin-bottom: -20px;
}
.problem-container {
  width: 100%;
  ul,li{
    list-style-type: none;
  }
  .problem {
    border: 1px dashed #dcdfe6;
    padding: 20px 20px 0 0;
    margin-bottom: 20px;
    position: relative;
.icon{
    position: absolute;
    top: 10px;
    right: 10px;
}
.icon:hover{
    cursor: pointer;
}
  }
  .deleteBtn {
    margin-bottom: 0px;
  }
}
</style>
