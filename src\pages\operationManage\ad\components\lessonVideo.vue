<template>
  <el-dialog title="" :visible.sync="show" top="50px" width="1200px" center :destroy-on-close="true" :before-close="handleClose" @open="open">
    <div class="video-detail">
      <h3 class="detail-title">课程名称：{{ auditDetail.name }}</h3>
      <div class="detail-content">
        <!-- chapter -->
        <div
          v-for="(chapter, chapterIndex) in auditDetail.chapterRequestDtos"
          :key="chapter.chapterId"
          class="detail-item"
          :name="chapter.chapterName"
        >
          <div class="paper-head" @click.stop="handleClickCollapse(chapter, chapterIndex)">
            <div class="paper-title" :class="chapter.view? 'bg-success':''">章节{{ chapterIndex | filtersIndex }}：{{ chapter.chapterName }}
            </div>
            <i class="paper-icon" :class="activeIndex === chapterIndex?'el-icon-arrow-down':'el-icon-arrow-right'" />
          </div>
          <!-- content -->
          <div v-if="activeIndex === chapterIndex">
            <!-- player -->
            <ali-player
              ref="aliplayer"
              :play-style="aliPlayerConfig.playStyle"
              :source="aliPlayerConfig.source"
              :cover="aliPlayerConfig.cover"
              :height="aliPlayerConfig.height"
              :skin-layout="aliPlayerConfig.skinLayout"
              @ready="handleReadyVideo($event, chapter)"
              @pause="handlePauseVideo"
              @error="handleError"
            />
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import AliPlayer from '@/components/Aliplayer/index.vue'
import { getVideoDetail, playVideoInfo } from '@/api/validManage'
import { courseDetail } from '@/api/course'
import { parseSeconds, filtersIndex, filtersEnIndex } from '@/utils'

export default {
  name: 'CourseDetailDialog',
  components: {
    AliPlayer
  },
  filters: {
    parseSeconds,
    filtersIndex,
    filtersEnIndex
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeIndex: -1,
      // 已查看的内容
      viewList: [],
      // 阿里云配置
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            'name': 'bigPlayButton',
            'align': 'cc'
          },
          {
            'name': 'H5Loading',
            'align': 'cc'
          },
          {
            'name': 'errorDisplay',
            'align': 'tlabs',
            'x': 0,
            'y': 0
          },
          {
            'name': 'infoDisplay'
          },
          {
            'name': 'tooltip',
            'align': 'blabs',
            'x': 0,
            'y': 56
          },
          {
            'name': 'controlBar',
            'align': 'blabs',
            'x': 0,
            'y': 0,
            'children': [
              {
                'name': 'progress',
                'align': 'blabs',
                'x': 0,
                'y': 44
              },
              {
                'name': 'playButton',
                'align': 'tl',
                'x': 15,
                'y': 12
              },
              {
                'name': 'timeDisplay',
                'align': 'tl',
                'x': 10,
                'y': 7
              },
              {
                'name': 'fullScreenButton',
                'align': 'tr',
                'x': 10,
                'y': 12
              },
              {
                'name': 'volume',
                'align': 'tr',
                'x': 5,
                'y': 10
              }
            ]
          }
        ]
      },
      // 课程详情
      auditDetail: {}
    }
  },
  methods: {
    open() {
      this.auditDetail = {}
      this.getCourseDetail()
    },
    // get unit user
    getCourseDetail() {
      courseDetail(this.id).then(res => {
        if (res.chapterRequestDtos.length) {
          res.chapterRequestDtos.forEach(v => {
            v.view = false
          })
          this.auditDetail = res
        }
      })
    },
    // 点击查看时
    handleClickCollapse(val, index) {
      if (val) {
        // 动态index
        if (this.activeIndex === index) {
          this.activeIndex = -1
        } else {
          this.activeIndex = index
        }
        // 视频播放
        if (this.activeIndex > -1) {
          getVideoDetail({ videoId: this.auditDetail.chapterRequestDtos[this.activeIndex].videoId, videoSource: 1 }).then(response => {
            playVideoInfo({
              videoId: response.videoId,
              videoInfoId: response.videoInfoId,
              videoFileId: response.videoFileId
            }).then(res => {
              if (res.playInfoList && res.playInfoList.length) {
                const sourceUrl = {}
                res.playInfoList.map(v => {
                  sourceUrl[v.definition] = v.playURL
                })
                this.aliPlayerConfig.cover = res.coverUrl
                this.aliPlayerConfig.source = JSON.stringify(sourceUrl)
                this.playerTitle = response.name || res.videoName
                this.dialogVisible = true
                this.$nextTick(() => {
                  this.$refs.aliplayer[0].init()
                })
              } else {
                this.$message.error('该视频暂时无法播放，请稍后重试！')
              }
            })
          })
        }
      }
    },
    // 阿里云播放器事件
    handleReadyVideo(e, val) {
      this.player = e
    },
    handlePauseVideo() {
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    },
    handleClose() {
      this.$emit('close', false)
      this.$emit('update:show', false)
    }
  }
}
</script>
 <style lang="scss" scoped>
  .head-level-second{
    text-indent: 20px;
  }
  .head-level-thrid{
    text-indent: 40px;
  }
  .bg-success{
    background: rgba($color: #67C23A, $alpha: 0.4);
  }
  .ic-success{
    font-size: 14px;
    line-height: 50px;
    color: #67C23A;
  }
  .video-detail{
    border: 1px solid #e4e4e4;
    margin: 0 auto 100px;
    width: 800px;
    .detail-title{

      margin: 0;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 600;
      line-height: 50px;
      background: #f2f5f9;
    }
    .detail-item{

    }
    .paper-head{
      display: flex;
      margin-bottom: -1px;
      line-height: 48px;
      height: 48px;
      border-bottom: 1px solid #e4e4e4;
    }
    .paper-icon{
      margin-right: 8px;
      line-height: 48px;
      font-size: 12px;
    }
    .paper-title{
      flex: 12;
      padding: 0 10px;
      font-size: 14px;
      width: 100%;
    }
    .test-content{
      background: #f2f5f9;
      .test-title, .test-item{
        padding: 0 10px;
      }
      .test-item{
        background: #fff;
      }
      .test-title, .item-title{
        border-bottom: 1px solid #e4e4e4;
        font-size: 14px;
        line-height: 40px;
      }
       .item-title{
         border-bottom: 0;
       }
      .item-content{
        text-indent: 20px;
        .option{
          font-size: 14px;
          line-height: 30px;
        }
      }
    }
  }
  .fixed-btn-group{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    .group-content{
      padding: 20px 0;
      text-align: center;
      background: #fff;
    }
  }
</style>
