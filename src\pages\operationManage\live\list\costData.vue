<template>
  <el-dialog title="直播费用" :before-close="beforeClose" :visible.sync="visible" width="80vw" top="8vh">
    <div class="app-container">
      <div class="search-column">
        <div class="search-column__item fr">
          <el-button type="primary" @click="download">导出</el-button>
        </div>
      </div>
      <div ref="imgBox">
        <h2>直播概览</h2>
        <el-row style="margin-left:20px">
          <el-table :data="costData1" border stripe style="width:75vw">
            <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
              <template slot-scope="scope">
                <span>{{ scope.row[col.prop] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
        <h2>费用明细</h2>
        <el-row style="margin-left:20px">
          <el-table :data="costData2" border stripe style="width:75vw">
            <el-table-column v-for="col in tableColumnList1" :key="col.id" v-bind="col">
              <template slot-scope="scope">
                <template v-if="col.prop==='feeSku'">
                  <span v-html="scope.row[col.prop]" />
                </template>
                <template v-else>
                  <span>{{ scope.row[col.prop] }}</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export default {
  name: 'CostData',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    costData1: {
      type: Array,
      default: () => []
    },
    costData2: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 1, label: '直播主题', align: 'center', prop: 'title' },
        { id: 2, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 3, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 4, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 5, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 6, label: '推广类型', align: 'center', prop: 'promotionType' },
        { id: 7, label: '推广人数', align: 'center', prop: 'promotionNum' },
        { id: 8, label: '直播时间', align: 'center', prop: 'startTime', width: '160px' },
        { id: 9, label: '直播时长(分)', align: 'center', prop: 'totalDuration' },
        { id: 10, label: '观看人数', align: 'center', prop: 'uvCount' }
      ]),
      tableColumnList1: Object.freeze([
        { id: 1, label: '费用项目', align: 'center', prop: 'feeItemName' },
        { id: 2, label: '规格', align: 'center', prop: 'feeSku' },
        { id: 3, label: '单价(元)', align: 'center', prop: 'price' },
        { id: 4, label: '数量', align: 'center', prop: 'num' },
        { id: 5, label: '小计(元)', align: 'center', prop: 'littleAmount' }
      ])
    }
  },
  watch: {
    visible(val) {
      if (val) {
        const total = this.costData2.reduce((sum, e) => sum + Number(e.littleAmount), 0)
        this.costData2.push({ feeItemName: '合计', littleAmount: total })
      }
    }
  },
  methods: {
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    },
    download() {
      // 设置放大倍数
      const scale = 4
      // 传入节点原始宽高
      const width = this.$refs.imgBox.offsetWidth
      const height = this.$refs.imgBox.offsetHeight
      const ops = {
        scale,
        width,
        height,
        allowTaint: false
      }
      html2canvas(this.$refs.imgBox, ops).then(canvas => {
        const contentWidth = canvas.width
        const contentHeight = canvas.height
        const imgWidth = 800
        const imgHeight = 800 / contentWidth * contentHeight
        const pageData = canvas.toDataURL('image/jpeg', 1.0)
        const pdf = new jsPDF('l', 'pt', 'a4')
        pdf.addImage(pageData, 'JPEG', 20, 20, imgWidth, imgHeight)
        pdf.save(this.costData1[0].title + '.pdf')
        this.$message.success('导出成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
td{
  text-align: center;
  height: 48px;
  border-bottom: 1px solid #eee;
}
.left{
  width: 60vw;
  border-left: 1px solid #eee;
  border-right: 1px solid #eee;
}
.right{
  width: 15vw;
  border-right: 1px solid #eee;
}
</style>
