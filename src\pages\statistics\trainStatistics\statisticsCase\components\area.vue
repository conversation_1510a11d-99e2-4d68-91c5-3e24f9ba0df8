<template>
  <div class="areaContain">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item"><span class="record-text" /> 区域分布</div>
      <div class="search-column__item fr">
        <el-button v-show="isShow === 2" type="text" @click="handlerCutChart(1)"><i class="el-icon-s-fold" style="margin-right:10px" />列表</el-button>
        <el-button v-show="isShow === 1" type="text" @click="handlerCutChart(2)"><i class="el-icon-s-data" style="margin-right:10px" />图表</el-button>
        <el-button v-show="isShow!==3" type="text" @click="handleDownload"> <i class="el-icon-download" style="margin-right:10px" />下载</el-button>
      </div>
      <div class="search-column__item fr">
        <el-cascader
          v-model="academicIds"
          placeholder="请选择职称"
          :options="academicList"
          collapse-tags
          :props="{
            multiple: true,
            value:'academicId',
            label:'name',
            children:'childList',
            emitPath:false
          }"
          clearable
          :disabled="disabled"
          @change="getList"
        />
      </div>
      <div class="search-column__item fr">
        <el-cascader
          v-model="majorIds"
          placeholder="请选择专科"
          :options="majorList"
          collapse-tags
          :props="{
            multiple: multiple,
            value:'majorId',
            label:'name',
            children:'childList',
            emitPath:false,
            checkStrictly:false
          }"
          clearable
          :disabled="disabled"
          @change="getList"
        />
      </div>
    </div>
    <div>
      <ChinaMap v-if="mapShow" v-show="isShow===2" ref="chinamap" class="chart" :data="data" :map-code="mapCode" :type="type" />

      <LevelFour v-if="!mapShow" v-show="isShow===2" :data="data" class="levelFour" />

      <div v-if="isShow===1">
        <el-table v-if="data.length" :data="data" stripe class="areaTable" border>
          <el-table-column
            type="index"
            label="序号"
            width="300px"
            align="center"
          />
          <el-table-column
            prop="name"
            label="地区"
            align="center"
          />
          <el-table-column
            prop="num"
            :label="labelName"
            align="center"
          >
            <template slot-scope="{row}">
              {{ row.num }}{{ [8, 9, 10, 11, 13].includes(type) ? '%' : '' }}
            </template>
          </el-table-column>
        </el-table>
        <div v-else class="empty">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import cityMap from '../com/js/china-main-city-map'
import request from '@/api/dataStatistics/statisticsCase'
import { majorTreeList } from '@/api/category' // 选择身份后的 专科树
import { academicTreeListById } from '@/api/academic' // 选择单个身份后的 职称树
import { getAreaTree } from '@/api/area'
import ChinaMap from '../com/chinaMap.vue'
import LevelFour from '../com/levelFour.vue'
export default {
  name: 'StatisticsArea',
  components: { ChinaMap, LevelFour },
  props: {
    areaId: {
      type: Array,
      default: () => ['0']
    },
    areaType: {
      type: Number,
      default: 1
    },
    startTime: {
      type: String,
      default: ''
    },
    endTime: {
      type: String,
      default: ''
    },
    identityIds: {
      type: Array,
      default: () => {
        return [0]
      }
    },
    type: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      request,
      identityTree: [],
      majorList: [],
      academicList: [],
      academicList1: [
        { academicId: 0, name: '无' }
      ],
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      academicIds: [],
      majorIds: [],
      // 区域分布 接口返回数据
      data: [],
      areaList: [],
      isShow: 2,
      level: 1,
      areaCode: '100000',
      val: {}, // chinaMap组件 点击区域时传回的区域信息
      disabled: true,
      multiple: false,
      mapShow: true,
      mapCode: '100000'
    }
  },
  computed: {
    labelName() {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[this.type]
    }
  },
  watch: {
    areaId: {
      handler(v) {
        this.parentClick(v)
      }
    },
    identityIds: {
      handler(arr) {
        if (arr.length === 0) { // 未选身份，禁用
          this.Reset()
          this.disabled = true
        } else if (arr.length === 1) { // 一个身份
          if (arr[0] === '0') { // 身份为'无'
            // 专科和职称都是无
            this.ident1()
            this.disabled = true
          } else {
            // 调接口
            this.ident2(arr)
            this.disabled = false
          }
        } else { // 多个身份
          this.ident3(arr)
          this.disabled = false
        }
        this.getList()
      }
    },
    startTime: {
      handler() {
        this.getList()
      }
    },
    endTime: {
      handler() {
        this.getList()
      }
    },
    type: {
      handler() {
        this.getList()
      }
    }
  },
  created() {
    this.getList()
    getAreaTree().then(res => {
      this.areaList = res
    })
  },
  methods: {
    getList() {
      const params = this.addParams()
      params.areaId = this.areaId[this.areaId.length - 1]
      params.areaType = this.areaType
      this.request.area(params).then(res => {
        if (res && res.length > 0) {
          res.forEach(item => {
            item.num = parseInt(item.num)
          })
          this.data = res
        } else {
          this.data = []
        }
      })
    },
    // 对请求参数进行处理  academicClassIds
    addParams() {
      const params = {}
      params.startTime = this.startTime
      params.endTime = this.endTime
      params.identityIds = this.identityIds
      // params.academicIds = this.academicIds
      params.majorIds = this.majorIds
      params.type = this.type
      // 应后端要求，用户未选择身份、职称、专科时，单值字段给-1
      if (this.identityIds.length === 0) {
        params.identityId = -1
      }
      if (this.academicIds.length === 0) {
        params.academicId = -1
      }
      if (this.majorIds.length === 0) {
        params.majorId = -1
      }
      // 应后端要求，选择多个身份时，职称字段用academicClassIds，单个身份时用academicIds
      if (this.identityIds.length > 1) {
        params.academicClassIds = this.academicIds
      } else {
        params.academicIds = this.academicIds
      }
      return params
    },
    // 父组件选中 区域级联框的事件
    parentClick(e) {
      if (e[0] == '0') {
        this.mapShow = true
        this.mapCode = '100000'
      } else if (e.length === 3) {
        // 切换柱状图
        this.mapShow = false
      } else {
        this.mapShow = true
        const areaName = this.getAreaName(this.areaList, e[e.length - 1])
        const i = Object.keys(cityMap).findIndex(
          v => areaName.indexOf(v) !== -1
        )
        this.mapCode = String(Object.values(cityMap)[i])
      }
      this.isShow = 2
      this.getList()
    },
    // 根据区域名，获取区域id
    getAreaId(list, areaName) {
      const _this = this
      for (let i = 0; i < list.length; i++) {
        const a = list[i]
        if (a.name.indexOf(areaName) !== -1) {
          return a.areaId
        } else {
          if (a.childList && a.childList.length > 0) {
            const res = _this.getAreaId(a.childList, areaName)
            if (res) {
              return res
            }
          }
        }
      }
    },
    // 根据区域id，获取区域名
    getAreaName(list, id) {
      const _this = this
      for (let i = 0; i < list.length; i++) {
        const a = list[i]
        if (a.areaId == id) {
          return a.name
        } else {
          if (a.childList && a.childList.length > 0) {
            const res = _this.getAreaName(a.childList, id)
            if (res) {
              return res
            }
          }
        }
      }
    },
    // 当展示图表时，下载图表图片，当展示列表时，下载excel数据表
    handleDownload() {
      // 将tabel表格转换为execl数据表，并下载为execl文件
      if (this.isShow === 1) {
        if (this.data.length < 1) {
          this.$message.info('当前数据为空')
          return
        }
        const params = this.addParams()
        params.areaId = this.areaId[this.areaId.length - 1]
        params.areaType = this.areaType
        this.request.exportArea(params).then(() => {
          this.$message.success('导出成功，请在导出管理中查看')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      }
      // 调用子组件的download()
      if (this.isShow === 2) {
        this.$refs.chinamap.download()
      }
    },
    // type 1/列表 2/图表
    handlerCutChart(type) {
      this.isShow = type
    },
    ident1() {
      this.majorList = [{
        name: '无',
        majorId: 0
      }]
      this.academicList = this.academicList1
      this.academicIds = [0]
      this.majorIds = [0]
    },
    ident2(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        this.majorList = this.clearNullChildList(res, 'childList')
      })
      academicTreeListById(arr[0]).then(res => {
        this.academicList = this.clearNullChildList(res, 'childList')
      })
    },
    ident3(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        this.majorList = this.clearNullChildList(res, 'childList')
      })
      this.academicList = this.academicList2
    },
    Reset() { // 用户清除身份时，进行reset
      this.academicIds = []
      this.majorIds = []
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.areaContain{
  border-radius: 5px;
  background: #fff;
  box-shadow: 0 0 8px rgb(167, 165, 165);

  .search-column {
    border-radius: 5px;
    padding: 15px 20px;
  }

  .chart{
    width: 100%;
    height: 670px;
  }

  .levelFour{
    width: 100%;
    height: 670px;
  }

  .empty{
    width: 100%;
    height: 670px;
    line-height: 670px;
    text-align: center;
    color: #999;
    font-size: 24px;
  }

  .areaTable{
    font-size: 15px;
    width: 100%;
    height: 670px;
    overflow-y: scroll;
    overflow-x: hidden;
  }
}
.record-text {
  margin-bottom: 10px;
  padding-left: 15px;
  border-left: 2px solid #4F8EF8;
}
</style>
