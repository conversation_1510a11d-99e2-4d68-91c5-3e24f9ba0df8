<template>
  <div>
    <p>{{ title }}</p>
    <span
      v-for="(item,index) in displayValueArray"
      :key="index"
    >{{ item }}</span>
  </div>
</template>

<script>
import { requestAnimationFrame, cancelAnimationFrame } from './requestAnimationFrame.js'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    startVal: {
      type: [Number, String],
      default: 0
    },
    endVal: {
      type: [Number, String],
      default: 0
    },
    duration: {
      type: Number,
      default: 3000
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    decimals: {
      type: Number,
      default: 0,
      validator(value) {
        return value >= 0
      }
    },
    decimal: {
      type: String,
      default: '.'
    },
    separator: {
      type: String,
      default: ','
    },
    prefix: {
      type: String,
      default: ''
    },
    suffix: {
      type: String,
      default: ''
    },
    useEasing: {
      type: Boolean,
      default: true
    },
    easingFn: {
      type: Function,
      default(t, b, c, d) {
        return c * (-Math.pow(2, -10 * t / d) + 1) * 1024 / 1023 + b
      }
    }
  },
  data() {
    return {
      localStartVal: this.startVal,
      displayValueArray: this.renderDom(this.startVal),
      printVal: null,
      paused: false,
      localDuration: this.duration,
      startTime: null,
      timestamp: null,
      remaining: null,
      rAF: null
    }
  },
  computed: {
    countDown() {
      return this.startVal > this.endVal
    }
  },
  watch: {
    startVal() {
      if (this.autoplay) {
        this.start()
      }
    },
    endVal() {
      if (this.autoplay) {
        this.start()
      }
    }
  },
  mounted() {
    if (this.autoplay) {
      this.start()
    }
    this.$emit('mountedCallback')
  },
  destroyed() {
    cancelAnimationFrame(this.rAF)
  },
  methods: {
    start() {
      this.localStartVal = this.startVal
      this.startTime = null
      this.localDuration = this.duration
      this.paused = false
      this.rAF = requestAnimationFrame(this.count)
    },
    pauseResume() {
      if (this.paused) {
        this.resume()
        this.paused = false
      } else {
        this.pause()
        this.paused = true
      }
    },
    pause() {
      cancelAnimationFrame(this.rAF)
    },
    resume() {
      this.startTime = null
      this.localDuration = +this.remaining
      this.localStartVal = +this.printVal
      requestAnimationFrame(this.count)
    },
    reset() {
      this.startTime = null
      cancelAnimationFrame(this.rAF)
      this.displayValueArray = this.renderDom(this.startVal)
    },
    count(timestamp) {
      if (!this.startTime) this.startTime = timestamp
      this.timestamp = timestamp
      const progress = timestamp - this.startTime
      this.remaining = this.localDuration - progress

      if (this.useEasing) {
        if (this.countDown) {
          this.printVal = this.localStartVal - this.easingFn(progress, 0, this.localStartVal - this.endVal, this.localDuration)
        } else {
          this.printVal = this.easingFn(progress, this.localStartVal, this.endVal - this.localStartVal, this.localDuration)
        }
      } else {
        if (this.countDown) {
          this.printVal = this.localStartVal - ((this.localStartVal - this.endVal) * (progress / this.localDuration))
        } else {
          this.printVal = this.localStartVal + (this.endVal - this.localStartVal) * (progress / this.localDuration)
        }
      }
      if (this.countDown) {
        this.printVal = this.printVal < this.endVal ? this.endVal : this.printVal
      } else {
        this.printVal = this.printVal > this.endVal ? this.endVal : this.printVal
      }

      this.displayValueArray = this.renderDom(this.printVal)
      if (progress < this.localDuration) {
        this.rAF = requestAnimationFrame(this.count)
      } else {
        this.$emit('callback')
      }
    },
    isNumber(val) {
      return !isNaN(parseFloat(val))
    },
    formatNumber(num) {
      if (this.isNumber(num)) {
        num = Number(num)
      }
      num = num.toFixed(this.decimals)
      num += ''
      const x = num.split('.')
      const x1 = x[0]
      const x2 = x.length > 1 ? this.decimal + x[1] : ''
      const rgx = /(\d+)(\d{3})/
      return this.prefix + x1 + x2 + this.suffix
    },
    renderDom(num) {
      const displayValue = this.formatNumber(num)
      let returnDom = ''
      if (displayValue.length < this.endVal.length) {
        returnDom = ('00000' + displayValue).slice(-this.endVal.length)
      } else {
        returnDom = displayValue
      }
      return returnDom.split('')
    }
  }
}
</script>

<style lang="scss" scoped>
div {
  margin-right: 53px;
  p {
    margin: 24px 0;
    font-size: 24px;
  }
  span {
    display: inline-block;
    margin-right: 5px;
    width: 25px;
    height: 34px;
    font-size: 23px;
    font-weight: bold;
    text-align: center;
    line-height: 34px;
    border-width: 1px;
    border-color: #4a90e2;
    border-style: solid;
  }
}

</style>
