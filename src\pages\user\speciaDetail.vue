<template>
  <div class="app-container">
    <div class="top-input">
      <el-input v-if="!$route.query.id" type="text" placeholder="请输入手机号/UID查询" prefix-icon="el-icon-search" @focus="handleFocus" />
    </div>
    <el-form ref="speciaForm" :model="speciaForm" :rules="rules" label-width="100px" class="demo-ruleForm">
      <el-form-item label="专家姓名" prop="realName">
        <el-input v-model="speciaForm.realName" :disabled="isAudit" type="text" placeholder="请输入专家姓名" />
      </el-form-item>
      <el-form-item label="身份" prop="identityId">
        <el-cascader
          v-model="speciaForm.identityId"
          :options="identityList"
          :show-all-levels="false"
          :props="{
            label: 'name',
            value: 'identityId',
            children:'childList',
            expandTrigger: 'hover',
            emitPath: false
          }"
          :disabled="isAudit"
          @change="identityChange"
        />
      </el-form-item>
      <el-form-item label="专科" prop="majorId">
        <el-cascader
          v-model="speciaForm.majorId"
          class="input"
          :options="majorList"
          :props="{
            label: 'name',
            value: 'majorId',
            children: 'childList',
            expandTrigger: 'hover',
            emitPath: false,
            checkStrictly: true
          }"
          filterable
          placeholder="请选择专科"
          :disabled="disabled||isAudit"
          :show-all-levels="false"
        />
      </el-form-item>
      <el-form-item label="职称" prop="academicId">
        <el-cascader
          v-model="speciaForm.academicId"
          class="input"
          :options="academicList"
          :props="{
            expandTrigger: 'hover',
            label: 'name',
            value: 'academicId',
            children: 'childList',
            emitPath: false
          }"
          filterable
          placeholder="请选择职称"
          :show-all-levels="false"
          :disabled="disabled||isAudit"
        />
      </el-form-item>
      <el-form-item label="工作单位:" prop="company">
        <el-input v-model="speciaForm.company" :disabled="isAudit" class="input" maxlength="20" type="text" placeholder="请输入专家的工作单位" />
      </el-form-item>
      <el-form-item label="部门/科室:" prop="department">
        <el-input v-model="speciaForm.department" :disabled="isAudit" class="input" maxlength="20" type="text" placeholder="请输入专家所在的部门/科室" />
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <singleImage v-model="speciaForm.avatar" width="200px" height="100px" type="article" :url="avatarUrl" />

      </el-form-item>
      <el-form-item label="个人简介" prop="introduction">
        <el-input v-model="speciaForm.introduction" type="textarea" :rows="5" maxlength="500" show-word-limit placeholder="请输入专家的个人简介" />
      </el-form-item>
      <el-form-item label="专家擅长" prop="skill">
        <el-input v-model="speciaForm.skill" type="textarea" :rows="5" maxlength="500" show-word-limit placeholder="请输入专家擅长治疗的疾病或技术" />
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm('speciaForm')">取消</el-button>
        <el-button type="primary" @click="submitForm('speciaForm')">保存</el-button>
      </el-form-item>
    </el-form>

    <div>
      <searchUser :show="dialogVisible" @confirm="handleConfirm" @close="close" />
    </div>
  </div>
</template>

<script>
import { identityTreeList, majorTreeListId } from '@/api/category'
import { academicTreeListById } from '@/api/academic'
import { treeList } from '@/api/major'
import { doctorList, addDoctor, editDoctor } from '@/api/doctor'
import { getPersonalDetail } from '@/api/userManage'
import singleImage from './components/doctorUpload.vue'
import searchUser from './components/searchUser.vue'

export default {
  name: 'SpeciaDetail',
  components: {
    singleImage,
    searchUser
  },
  data() {
    return {
      condition: {
        uid: 0
      },
      pager: {
        page: 1,
        pageSize: 10
      },
      identityList: [],
      doctorList: [],
      academicList: [],
      majorList: [],
      avatarUrl: '',
      speciaForm: {
        realName: '',
        identityId: '',
        majorId: '',
        academicId: '',
        company: '',
        department: '',
        avatar: '',
        introduction: '',
        skill: '',
        uid: '',
        updateUid: ''
      },
      rules: {
        realName: [{ required: true, message: '请输入专家姓名', trigger: 'blur' }],
        identityId: [{ required: true, message: '请选择身份', trigger: 'blur' }],
        majorId: [{ required: true, message: '请选择专科', trigger: 'blur' }],
        academicId: [{ required: true, message: '请选择职称', trigger: 'blur' }],
        company: [{ required: true, message: '请输入工作单位', trigger: 'blur' }],
        department: [{ required: true, message: '请输入部门/科室', trigger: 'blur' }],
        avatar: [{ required: true, message: '请上传头像', trigger: 'blur' }],
        introduction: [{ required: true, message: '请输入个人简介', trigger: 'blur' }],
        skill: [{ required: true, message: '请输入专家擅长', trigger: 'blur' }]
      },
      dialogVisible: false,
      disabled: true,
      isAudit: false
    }
  },
  created() {
    if (this.$route.query.id) {
      this.condition.uid = this.$route.query.id
      const params = {
        condition: this.condition,
        pager: this.pager
      }
      doctorList(params).then(res => {
        if (res.records.length === 0) {
          return
        }
        this.isAudit = res.records[0].isAudit
        if (res.records[0].identityId) {
          this.disabled = false
        } else {
          this.disabled = true
        }
        this.getAcademicList(res.records[0].identityId)
        const doctorData = res.records[0]
        Object.keys(this.speciaForm).forEach(key => {
          this.speciaForm[key] = doctorData[key] || this.speciaForm[key]
        })
        this.avatarUrl = doctorData.avatarUrl
      })
    }
    this.$route.query.id
    // 获取身份分类树
    this.getIdentityList()
    // 获取专科分类树
    this.getMajorTreeList()
  },
  methods: {
    // 获取身份分类
    getIdentityList() {
      identityTreeList()
        .then(res => {
          this.identityList = res
        })
    },
    // 获取专科分类树
    getMajorTreeList() {
      treeList().then(res => {
        this.majorList = this.clearNullChildList(res, 'childList')
      })
    },
    // 选择身份后
    identityChange(id) {
      if (id) {
        this.disabled = false
        this.speciaForm.identityId = id
        this.speciaForm.academicId = ''
        this.speciaForm.majorId = ''
        // 根据身份获取职称树
        this.getAcademicList(id)
        // 根据身份获取专科树
        this.getMajorList(id)
        this.$nextTick(() => {
          this.$refs.speciaForm.clearValidate()
        })
      } else {
        this.disabled = true
      }
    },
    // 职称 随身份联动
    getAcademicList(id) {
      academicTreeListById(id).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        newArr.push({ name: '无', academicId: '0' })
        this.academicList = newArr
      })
    },
    // 专科 随身份联动
    getMajorList(id) {
      majorTreeListId(id).then(res => {
        this.majorList = this.clearNullChildList(res, 'childList')
        if (['6', '16', '17'].includes(id)) {
          this.speciaForm.majorId = this.MajorList[0].majorId
        }
      })
    },
    submitForm(formName) {
      if (!this.speciaForm.uid) { return this.$message.error('请选择专家') }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.speciaForm))
          const pathArr = this.speciaForm.avatar.split('/')
          params.avatar = pathArr[pathArr.length - 1].split('.')[0]
          const API = this.$route.query.id ? editDoctor : addDoctor
          if (API === addDoctor) {
            this.$confirm('将为该用户同时创建专家、创作者身份，确认创建吗?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              API(params).then(res => {
                this.$message.success('保存成功')
                this.$router.go(-1)
              })
            })
          } else {
            API(params).then(res => {
              this.$message.success('保存成功')
              this.$router.go(-1)
            })
          }
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$router.go(-1)
    },
    handleFocus() {
      this.dialogVisible = true
    },
    handleConfirm(val) {
      getPersonalDetail({ userId: val.uid }).then(res => {
        if (res.userPersonalResponseDto.identityId) {
          this.disabled = false
        } else {
          this.disabled = true
        }
        this.getAcademicList(res.userPersonalResponseDto.identityId)
        this.getMajorList(res.userPersonalResponseDto.identityId)
        Object.keys(this.speciaForm).forEach(key => {
          if (key === 'avatar') {
            this.speciaForm[key] = res.userPersonalResponseDto[key] === '0' ? '' : res.userPersonalResponseDto[key]
          } else {
            this.speciaForm[key] = res.userPersonalResponseDto[key] || this.speciaForm[key]
          }
        })
        this.avatarUrl = res.userPersonalResponseDto.portraitUrl
        this.dialogVisible = false
      })
    },
    close(val) {
      this.dialogVisible = false
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>

<style lang="scss" scoped>
.top-input {
  margin: 0 0 20px 100px;
  text-align: center;
}
</style>
