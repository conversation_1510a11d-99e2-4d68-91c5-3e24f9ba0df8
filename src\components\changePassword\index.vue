<template>
  <div>
    <p v-if="pwd" class="desc">
      系统检测到您之前所设置的登录密码较弱，容易遭受攻击，请重新设置登录密码。
    </p>
    <el-form ref="form" :model="form" :rules="formRule" label-width="85px" style="width: 422px;" size="small">
      <el-form-item v-if="!pwd" label="旧密码" prop="oldPassword">
        <el-input v-model="form.oldPassword" autocomplete="off" type="password" placeholder="请输入旧密码" />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input v-model="form.newPassword" autocomplete="off" type="password" placeholder="请输入新密码" />
        <div class="pwd-desc">登录密码由8-14位字符组成，包含字母、数字、符号任意两种或以上组合。字母区分大小写</div>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPass">
        <el-input v-model="form.confirmPass" autocomplete="off" type="password" placeholder="请输入确认密码" />
      </el-form-item>
      <el-form-item label="滑块验证" prop="moveFinish">
        <slider-validation ref="slider" @moveSuccess="moveSuccess" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="loading" style="width: 100%;" @click="sub()">确认修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import SliderValidation from '@/components/SliderValidation'
import { updatePassword } from '@/api/user'
import { validPassword } from '@/utils/validate'
import { encode } from 'js-base64'
import { encryptDes } from '@/utils/des'

export default {
  name: 'Index',
  components: { SliderValidation },
  props: {
    pwd: {
      type: String,
      default: ''
    },
    closeFlag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const oldPassRules = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('旧密码不能为空'))
      } else {
        callback()
      }
    }
    const newPassRules = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请填写新密码'))
      } else if (!validPassword(value)) {
        return callback(new Error('不符合密码规则'))
      } else {
        callback()
      }
    }
    const confirmPassRules = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.newPassword) {
        return callback(new Error('确认密码与新密码不一致'))
      } else {
        callback()
      }
    }
    const confirmMoveFinish = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请拖动滑块验证'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      onLine: navigator.onLine,
      form: {
        oldPassword: '',
        newPassword: '',
        confirmPass: '',
        moveFinish: false
      },
      formRule: {
        oldPassword: [
          {
            required: true,
            trigger: 'blur',
            validator: oldPassRules
          }
        ],
        newPassword: [
          {
            required: true,
            trigger: 'blur',
            validator: newPassRules
          }
        ],
        confirmPass: [
          {
            required: true,
            trigger: 'blur',
            validator: confirmPassRules
          }
        ],
        moveFinish: [
          {
            required: true,
            trigger: 'change',
            validator: confirmMoveFinish
          }
        ]
      }
    }
  },
  // created() {
  //   if (this.$route.params.pwd) {
  //     this.form.oldPassword = this.$route.params.pwd
  //   } else {
  //     this.$router.push({
  //       path: '/login',
  //       query: this.$route.query
  //     })
  //   }
  // },
  mounted() {
    window.addEventListener('online', this.updateOnlineStatus)
    window.addEventListener('offline', this.updateOnlineStatus)
  },
  beforeDestroy() {
    window.removeEventListener('online', this.updateOnlineStatus)
    window.removeEventListener('offline', this.updateOnlineStatus)
  },
  methods: {
    updateOnlineStatus(e) {
      this.onLine = e.type === 'online'
      if (!this.onLine) {
        this.$message.error('网络断开连接，请稍后再试')
      }
    },
    // 确认修改
    sub() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.pwd) {
            this.form.oldPassword = this.pwd
          }
          this.loading = true
          const data = { ...this.form }
          data.oldPassword = encryptDes(data.oldPassword, 'mycs2020')
          data.oldPassword = encode(data.oldPassword)
          data.newPassword = encryptDes(data.newPassword, 'mycs2020')
          data.newPassword = encode(data.newPassword)
          data.confirmPass = encryptDes(data.confirmPass, 'mycs2020')
          data.confirmPass = encode(data.confirmPass)
          updatePassword(data)
            .then(res => {
              this.$message.success('修改密码成功')
              this.loading = false
              this.$emit('update')
              this.closeFlag && this.$emit('close')
            })
            .catch(() => {
              this.loading = false
            })
        }
      })
    },
    // 拖拽滑块
    moveSuccess() {
      this.form.moveFinish = true
      this.$refs.form.validateField('moveFinish')
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin-bottom: 20px;
}
</style>
