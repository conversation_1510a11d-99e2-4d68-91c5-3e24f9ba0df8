{"name": "mycs-front-cms", "version": "1.0.0", "description": "mycs cms", "author": "Ming <**********#qq.com>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:uat": "vue-cli-service build --mode uat", "build:test": "vue-cli-service build --mode test", "build:test2": "vue-cli-service build --mode test2", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "keywords": ["mycs", "mycs-cms", "management-system"], "dependencies": {"@wangeditor/editor": "^5.1.14", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "0.18.1", "bankcardinfo": "^2.0.6", "element-ui": "^2.15.13", "html2canvas": "^1.4.1", "js-base64": "^3.5.2", "js-cookie": "2.2.0", "jspdf": "^2.5.1", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "plupload": "^2.3.7", "qrcodejs2": "0.0.2", "vue": "2.6.10", "vue-router": "3.0.6", "vue-seamless-scroll": "^1.1.23", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "crypto-js": "^3.3.0", "echarts": "^5.0.1", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "^4.3.2", "sass": "^1.26.10", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "volta": {"node": "14.21.3"}}