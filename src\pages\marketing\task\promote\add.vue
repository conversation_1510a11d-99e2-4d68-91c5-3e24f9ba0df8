<template>
  <section>
    <Article v-if="$route.query.type === 'ARTICLE'" />
    <Video v-if="$route.query.type === 'VIDEO'" />
    <DoulaArticle v-if="$route.query.type === 'DOULA_ARTICLE'" />
    <DoulaVideo v-if="$route.query.type === 'DOULA_VIDEO'" />
  </section>
</template>

<script>
import Article from './components/article.vue'
import Video from './components/video'
import DoulaArticle from './components/doulaArticle'
import DoulaVideo from './components/doulaVideo'
export default {
  components: {
    Article,
    Video,
    DoulaArticle,
    DoulaVideo
  },
  data() {
    return {}
  }
}
</script>
