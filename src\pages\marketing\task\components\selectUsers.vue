<template>
  <el-dialog
    :title="type === 'CREATOR' ? '选择创作者' : '选择推广员'"
    :visible.sync="dialogVisible"
    width="600px"
    center
    :before-close="handleClose"
  >
    <el-input
      v-model="listQuery.condition.keyword"
      placeholder="搜索姓名/手机"
      class="input-with-select"
      @change="getList()"
    >
      <el-button slot="append" icon="el-icon-search" @click="getList()" />
    </el-input>
    <el-table
      :data="tableData"
      style="width: 100%"
      height="300"
    >
      <el-table-column
        prop="userId"
        label="UID"
        align="center"
      />
      <el-table-column
        prop="realName"
        label="姓名"
        align="center"
      />
      <el-table-column
        prop="phone"
        label="手机"
        align="center"
      />
      <el-table-column
        prop="hasFinishSign"
        label="状态"
        align="center"
      />
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="checkedList.find(v=> v.userId === scope.row.userId) === undefined"
            size="mini"
            type="primary"
            @click="checked(scope.row)"
          >选择</el-button>
          <el-button
            v-if="checkedList.find(v=> v.userId === scope.row.userId) !== undefined"
            size="mini"
            type="danger"
            @click="checked(scope.row)"
          >取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="handleClose('confirm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { selectUserPromotes, annualQuotaWarning } from '@/api/marketing/taskPromote'
import Pagination from '@/components/Pagination'
export default {
  components: { Pagination },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: null
    },
    type: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    productId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      listQuery: {
        condition: {
          keyword: '',
          productId: null,
          promoteType: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableData: [],
      checkedList: []
    }
  },
  watch: {
    dialogVisible() {
      this.getList()
      this.checkedList = JSON.parse(JSON.stringify(this.list))
    }
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      if (e) {
        this.$emit('update:list', JSON.parse(JSON.stringify(this.checkedList)))
      }
    },
    getList() {
      this.listQuery.condition.productId = this.id
      this.listQuery.condition.promoteType = this.type
      selectUserPromotes(this.listQuery).then(res => {
        res.records.forEach(item => {
          item.hasFinishSign = item.hasFinishSign ? '已签约' : '未签约'
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    checked(row) {
      const index = this.checkedList.findIndex(v => v.userId === row.userId)
      if (index === -1) {
        annualQuotaWarning({ userId: row.userId }).then(res => {
          if (res.type === 1) {
            row.userType = row.promoteType
            row.userName = row.realName
            this.checkedList.push(row)
          } else {
            this.$confirm(res.message, '预警提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              showCancelButton: res.type !== 3,
              type: 'warning',
              center: true
            }).then(() => {
              if (res.type !== 3) {
                row.userType = row.promoteType
                row.userName = row.realName
                this.checkedList.push(row)
              }
            })
          }
        })
      } else {
        this.checkedList.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .input-with-select {
    width: 300px;
  }
  .el-dialog__body {
    height: 450px;
    padding-bottom: 0;
  }
  .pagination-container {
    margin: 0;
    padding: 25px ;
  }
}
</style>
