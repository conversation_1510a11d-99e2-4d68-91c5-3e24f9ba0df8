<template>
  <div ref="vodUploadInt" class="vod-upload">
    <div plain class="select-btn" :style="{width: width, height: height}">
      <i class="el-icon-plus"> 本地上传</i>
      <div class="el-upload__text">支持MP4格式视频, 文件大小不超过5G</div>
      <label for="fileUpload">
        <input id="fileUpload" type="file" multiple @change="fileChange($event)">
      </label>
    </div>
    <transition>
      <div
        v-show="uploadPanelShow"
        class="upload-panel"
      >
        <div class="panel-title">上传视频中</div>
        <el-scrollbar wrap-class="panel-list">
          <div v-for="(file, index) in fileProgressList" :key="index" class="panel-item">
            <div class="file-title">{{ file.name }}</div>
            <div class="file-tool">
              <div class="file-progress-bg">
                <div
                  class="file-progress"
                  :style="{ width: Math.floor(file.progress) + '%' }"
                  :class="{
                    'is-success': file.progress === 100
                  }"
                />
              </div>
              <i class="el-icon-close" @click.prevent="remove(index)" />
            </div>
          </div>
        </el-scrollbar>
      </div>
    </transition>
  </div>
</template>
<script>
import { preUploadVideoApi, refreshUploadAuth, getUploadAuth, preUpload, cancelPreupload } from '@/api/biz'
export default {
  name: 'VodUpload',
  components: {

  },
  filters: {

  },
  mixins: [],
  props: {
    width: {
      type: String,
      default: '520px'
    },
    height: {
      type: String,
      default: '294px'
    }
  },
  data() {
    return {
      // 上传结果
      uploadResult: {
        fail: 0,
        cancel: 0,
        success: 0
      },
      // 上传面板
      uploadPanelShow: false,
      // 用于进度条展示
      fileProgressList: [],
      // 文件列表
      fileList: [],
      // 上传实例
      uploader: null,
      // 上传组件配置
      vodUploadOption: {
        timeout: null,
        partSize: null,
        parallel: null,
        retryCount: null,
        retryDuration: null,
        region: null,
        userId: null
      },
      // 视频预上传参数 - vod
      preUploadVideoQuery: [
        // {
        //   filename: null,
        //   size: 0
        // }
      ],
      // 视频预上传列表 - 本地
      preUploadList: [],
      // 视频上传参数
      uploadVideoQuery: [
        // {
        //   converId: 0,
        //   data: null
        // }
      ],
      // 视频上传返回的文件凭证列表
      uploadVideoInfo: [],
      videoInfo: {},
      videoFileId: ''
    }
  },
  watch: {

  },
  created() {

  },
  methods: {
    // 选中文件
    fileChange(e) {
      // 清空已选择的文件内容
      this.fileList = []
      this.fileProgressList = []
      this.preUploadVideoQuery = []
      this.preUploadList = []
      // 检测是否有文件
      for (let i = 0; i < e.target.files.length; i++) {
        // 校验视频格式
        if (e.target.files[i].type !== 'video/mp4') {
          this.$message.error('上传文件不是MP4格式，请重新选择上传！')
          this.fileList = []
          this.fileProgressList = []
          this.preUploadVideoQuery = []
          this.preUploadList = []
          return
        }
        // 校验视频大小
        const is5g = e.target.files[i].size / 1024 / 1024 <= 5120
        if (!is5g) {
          this.$message.error('上传文件大小超过5G,请重新选择上传!')
          this.fileList = []
          this.fileProgressList = []
          this.preUploadVideoQuery = []
          this.preUploadList = []
          return
        }
        this.fileList.push(e.target.files[i])
        this.fileProgressList.push({ name: e.target.files[i].name, cancel: false, progress: 0 })
        this.preUploadVideoQuery.push({ filename: e.target.files[i].name, size: e.target.files[i].size })
      }
      // 检测是否有文件列表
      if (!this.fileList.length) {
        alert('请先选择需要上传的文件!')
        return
      }
      if (this.fileList.length > 1) {
        alert('请勿上传多个文件!')
        this.fileList = []
        this.fileProgressList = []
        this.preUploadVideoQuery = []
        this.preUploadList = []
        return
      }
      // 预上传请求
      this.preUploadVideo()
    },
    // 取消文件上传
    remove(index) {
      if (this.uploader) {
        const params = this.preUploadList[index]
        cancelPreupload(params)
        this.uploader.cancelFile(index)
      }
    },
    // 文件上传失败
    fail(name) {
      const index = this.fileList.filter(v => { return v.name === name })
      const params = this.preUploadList[index]
      cancelPreupload(params)
    },
    // 清除上传结果
    resetUploadResult() {
      this.uploadResult = {
        success: 0,
        fail: 0,
        cancel: 0
      }
    },
    // 预上传请求
    preUploadVideo() {
      preUploadVideoApi(this.preUploadVideoQuery).then(res => {
        if (res && res.length) {
          res.forEach(v => {
            this.uploadVideoQuery.push({ convertId: 0, data: v.data })
          })
          this.preUpload(res)
          this.getVideoUploadAuth()
          this.videoFileId = res[0].videoFileId
        }
      })
    },
    // 预上传请求 -- 本地
    preUpload(res) {
      const name = res[0].filename
      preUpload(res).then(res => {
        if (res.length) {
          this.preUploadList = res
          res[0].videoFileId = this.videoFileId
          res[0].name = name
          this.videoInfo = res[0]
        }
      })
    },
    // 获取上传凭证
    getVideoUploadAuth() {
      getUploadAuth(this.uploadVideoQuery).then(res => {
        if (res.userId) {
          this.vodUploadOption.region = res.region
          this.vodUploadOption.userId = res.userId
          this.vodUploadOption.parallel = res.parallel
          this.vodUploadOption.retryCount = res.retryCount
          this.vodUploadOption.retryDuration = res.retryDuration
          this.vodUploadOption.timeout = res.timeout
          this.uploadVideoInfo = res.uploadInfo
          this.initUpload()
        } else {
          this.$message.error('视频上传发生了未知错误！请稍后尝试！')
        }
      })
    },
    // 实例化上传组件
    createUploader() {
      const self = this
      const uploader = new window.AliyunUpload.Vod({
        timeout: self.vodUploadOption.timeout || 60000,
        partSize: self.vodUploadOption.partSize || 1048576,
        parallel: self.vodUploadOption.parallel || 5,
        retryCount: self.vodUploadOption.retryCount || 3,
        retryDuration: self.vodUploadOption.retryDuration || 2,
        region: self.vodUploadOption.region,
        userId: self.vodUploadOption.userId,
        // 添加文件成功
        addFileSuccess: function(uploadInfo) {
          // console.log('addFileSuccess: ' + JSON.stringify(uploadInfo))
        },
        // 开始上传
        onUploadstarted: function(uploadInfo) {
          const fileIndex = self.fileList.findIndex(v => { return v.name === uploadInfo.file.name })
          const { uploadAuth, uploadAddress, vodVideoId } = self.uploadVideoInfo[fileIndex]
          if (!uploadInfo.videoId) {
            uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, vodVideoId)
          } else {
            uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, vodVideoId)
          }
        },
        // 文件上传成功
        onUploadSucceed: function(uploadInfo) {
          self.$emit('update:videoFileId', self.videoFileId)
          self.$emit('upInfo', self.videoInfo)
          self.uploadPanelShow = false
          self.uploadResult.success++
          // console.log('onUploadSucceed: ' + uploadInfo.file.name + ', endpoint:' + uploadInfo.endpoint + ', bucket:' + uploadInfo.bucket + ', object:' + uploadInfo.object)
        },
        // 文件上传失败
        onUploadFailed: function(uploadInfo, code, message) {
          self.$emit('update:videoFileId', '')
          self.uploadPanelShow = false
          this.fail(uploadInfo.file.name)
          self.uploadResult.fail++
          // console.log('onUploadFailed: file:' + uploadInfo.file.name + ',code:' + code + ', message:' + message)
        },
        // 取消文件上传
        onUploadCanceled: function(uploadInfo, code, message) {
          self.$emit('update:videoFileId', '')
          self.uploadPanelShow = false
          self.$message.success('"' + uploadInfo.file.name + '"文件已取消上传')
          self.uploadResult.cancel++
          // console.log('Canceled file: ' + uploadInfo.file.name + ', code: ' + code + ', message:' + message)
        },
        // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress: function(uploadInfo, totalSize, progress) {
          // console.log('onUploadProgress:file:' + uploadInfo.file.name + ', fileSize:' + totalSize + ', percent:' + Math.ceil(progress * 100) + '%')
          const index = self.fileProgressList.findIndex(item => { return item.name === uploadInfo.file.name })
          const progressPercent = Math.ceil(progress * 100)
          self.fileProgressList[index].progress = progressPercent
        },
        // 上传凭证超时
        onUploadTokenExpired: function(uploadInfo) {
          refreshUploadAuth({ vid: 0 }).then(res => {
            const uploadAuth = res.UploadAuth
            uploader.resumeUploadWithAuth(uploadAuth)
            // console.log('upload expired and resume upload with uploadauth ' + uploadAuth)
          })
        },
        // 全部文件上传结束
        onUploadEnd: function() {
          // 清空已选择的文件内容
          self.fileList = []
          self.fileProgressList = []
          self.preUploadVideoQuery = []
          self.preUploadList = []
          self.uploader.cleanList()
          // console.log('upload result: success--->' + self.uploadResult.success + ' fail--->' + self.uploadResult.fail + ' cancel--->' + self.uploadResult.cancel)
          self.resetUploadResult()
        }
      })
      return uploader
    },
    // 主动上传
    authUpload() {
      // 然后调用 startUpload 方法, 开始上传
      if (this.uploader !== null) {
        this.uploader.startUpload()
      }
    },
    // 调用上传
    initUpload() {
      this.uploadPanelShow = true
      if (this.uploader) {
        this.uploader.stopUpload()
      }
      this.uploader = this.createUploader()

      this.fileList.forEach(v => {
        const userData = '{"Vod":{"name": "' + v.name + '"}}'
        this.uploader.addFile(v, null, null, null, userData)
      })
      this.authUpload()
    }
  }
}
</script>

<style lang="scss" scoped>
%pr {
  position: relative;
}
%pa {
  position: absolute;
}
%bold {
  font-weight: bold;
}
%box {
  box-sizing: border-box;
}
.el-mask{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000000;
  background: rgba($color: #000000, $alpha: 0.4);
  z-index: 9999;
}
.vod-upload{
  overflow: visible;
  @extend %pr;
  vertical-align: middle;
}
.select-btn{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
  background: #f5f7fa;
  border-radius: 4px;
  i {
    margin-bottom: 4px;
    font-size: 22px;
    font-weight: bold;
    color: #000;
  }
  label{
    cursor:pointer;
    @extend %pa;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    input{
      @extend %pa;
      display: none;
    }
  }
}
.upload-panel {
  content: "";
  display: block;
  @extend %pa, %box;
  width: 520px;
  max-height: 294px;
  padding-top: 42px;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: -1px 2px 10px 0 rgba(16, 16, 16, 0.4);
  border-radius: 2px;
  right: calc(100% + 19px);
  bottom: 0;
  left: 0;
  text-align: left;
  font-size: 14px;
  z-index: 10000;

  // &::before {
  //   content: "";
  //   display: inline-block;
  //   border: 6px solid #f8f8f8;
  //   transform: rotate(-45deg);
  //   box-shadow: -1px 2px 10px 0 rgba(16, 16, 16, 0.4);
  //   @extend %pa;
  //   right: -6px;
  //   top: 16px;
  // }

  &::after {
    content: "";
    display: inline-block;
    @extend %pa;
    height: 42px;
    right: 0;
    top: 0;
    width: 100px;
    z-index: 10;
    background: #f8f8f8;
  }

  .panel-title {
    @extend %bold, %box, %pa;
    width: 100%;
    color: #409eff;
    background: #f8f8f8;
    line-height: 42px;
    padding-left: 18px;
    left: 0;
    top: 0;
  }

  ::v-deep .panel-list {
    max-height: calc(354px - 26px);
    padding: {
      top: 4px;
      bottom: 19px;
    }
    @extend %box;
  }
  .panel-item {
    @extend %box;
    padding: 8px 27px 8px 18px;

    .file-title {
      margin-bottom: 7px;
      width: 85%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-tool {
      display: flex;
      align-items: center;
      justify-content: space-between;

      @mixin bar($bg) {
        height: 6px;
        border-radius: 3px;
        flex: 1;
        margin-right: 21px;
        background: $bg;
      }

      .file-progress-bg {
        @extend %pr;
        @include bar(#e9e9e9);

        .file-progress {
          transition: width 0.2s ease;
          @extend %pa;
          @include bar(#409eff);

          &.is-error {
            background: #f40f0c;
          }
        }
      }

      i {
        cursor: pointer;
        color: #a5acb7;
        @extend %bold;

        &:hover {
          color: #f40f0c;
        }
      }
    }
  }
}
</style>
