<template>
  <div class="app-container">
    <el-form ref="form" label-width="120px" :model="form" :rules="rules">
      <el-form-item label="课程名称" prop="name">
        <el-input v-model="form.name" clearable maxlength="120" show-word-limit />
      </el-form-item>
      <el-form-item label="选择分类" prop="cateIds">
        <el-cascader
          v-model="form.cateIds"
          class="input-width"
          placeholder="资源分类"
          :options="treeList"
          collapse-tags
          :show-all-levels="true"
          :props="{
            emitPath:false,
            expandTrigger:'hover',
            multiple: true,
            value:'categoryId',
            label:'name',
            children:'children'
          }"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item label="课程封面" required>
        <singleImage v-model="form.coverId" width="200px" height="100px" type="course" :url.sync="form.imgUrl" :disabled="isEdit" />
      </el-form-item>
      <el-form-item label="添加作者">
        <!-- 专家 -->
        <template v-if="curAuthor.doctorId">
          <el-button size="small" type="danger" @click="remove">移除</el-button>
          <div class="info-wrapper">
            <el-image
              border
              class="avatar"
              :src="curAuthor.imgUrl"
              fit="fill"
            >
              <div slot="error" class="image-slot">
                <img :src="defaultAvatar" alt="">
              </div>
            </el-image>
            <div class="info">
              <div class="item">
                <span class="label">专家姓名</span>
                <span class="value">
                  <el-input v-model="curAuthor.name" size="small" disabled />
                </span>
              </div>
              <div class="item">
                <span class="label">专家职称</span>
                <span class="value">
                  <el-input v-model="curAuthor.jobTitle" size="small" disabled />
                </span>
              </div>
              <div class="item">
                <span class="label">工作单位</span>
                <span class="value">
                  <el-input v-model="curAuthor.company" size="small" disabled />
                </span>
              </div>
              <div class="item">
                <span class="label">部门/科室</span>
                <span class="value">
                  <el-input v-model="curAuthor.department" size="small" disabled />
                </span>
              </div>
              <div class="item">
                <span class="label">专家擅长</span>
                <span class="value">
                  <el-input v-model="curAuthor.skill" type="textarea" autosize size="small" disabled />
                </span>
              </div>
              <div class="item">
                <span class="label">个人简介</span>
                <span class="value">
                  <el-input v-model="curAuthor.desc" type="textarea" autosize size="small" disabled />
                </span>
              </div>
            </div>
          </div>
        </template>
        <!-- 单位 -->
        <template v-else-if="curAuthor.orgId">
          <el-button size="small" type="danger" @click="remove">移除</el-button>
          <div class="info-wrapper">
            <el-image
              border
              class="avatar"
              :src="curAuthor.imageUrl"
              fit="fill"
              :lazy="true"
            >
              <div slot="error" class="image-slot">
                <img :src="defaultAvatar" alt="">
              </div>
            </el-image>
            <div class="info">
              <span class="label">单位名称</span>
              <span class="value">
                <el-input v-model="curAuthor.orgName" disabled size="small" />
              </span>
            </div>
          </div>
        </template>
        <!-- 按钮 -->
        <template v-else>
          <el-button type="primary" @click="showDoctor">选择专家名医</el-button>
          <el-button type="primary" @click="showUnit">选择单位</el-button>
        </template>
      </el-form-item>
      <el-form-item label="课程介绍" prop="appDescribe">
        <tinymce ref="tinymce" v-model="form.appDescribe" :height="200" upload-type="course" />
        <!-- refs.tinymce.setContent() -->
      </el-form-item>
      <el-form-item label="章节视频" prop="chapterRequestDtos" required>
        <el-button type="primary" @click="addVideo">添加视频</el-button>
        <template v-if="form.chapterRequestDtos.length">
          <transition-group tag="ul" name="list" class="list">
            <p v-for="(item,index) in form.chapterRequestDtos" :key="item.videoId" class="list-item">
              <span>{{ item.name||item.chapterName }}</span>
              <el-button size="small" type="danger" @click="removeVideo(index)">移除</el-button>
              <i v-if="index" class="el-icon-caret-top sort-btn" @click="sortUp(index)" />
              <i v-if="index!==form.chapterRequestDtos.length-1" class="el-icon-caret-bottom sort-btn" @click="sortDown(index)" />
            </p>
          </transition-group>
        </template>
      </el-form-item>
      <el-form-item>
        <el-button @click="resetForm('form')">返回</el-button>
        <el-button type="primary" @click="submitForm('form')">保存</el-button>
      </el-form-item>
    </el-form>

    <dialog-video ref="video" :visible.sync="dialogVideoShow" @selectVideo="selectVideo" />
    <dialog-doctor ref="doctor" :visible.sync="dialogDoctorShow" @selectDoctor="selectDoctor" />
    <dialog-unit ref="unit" :visible.sync="dialogUnitShow" @selectUnit="selectUnit" />
  </div>
</template>

<script>
import singleImage from '@/components/SingleImage'
import Tinymce from '@/components/Tinymce'
import { getCategoryTreeList } from '@/api/category'
import { addCourse, courseDetail, editCourse } from '@/api/course'
import DialogVideo from './components/dialogVideo'
import DialogDoctor from './components/dialogDoctor'
import DialogUnit from './components/dialogUnit'

export default {
  name: 'LessonEdit',
  components: {
    Tinymce,
    singleImage,
    DialogVideo,
    DialogDoctor,
    DialogUnit
  },
  data() {
    var validateChapter = (rule, value, callback) => {
      if (!this.form.chapterRequestDtos.length) {
        callback(new Error('请添加章节视频'))
      } else {
        callback()
      }
    }
    return {
      treeList: [],
      form: {
        chapterRequestDtos: [],
        name: '',
        cateIds: [],
        coverId: '',
        coverUrl: '',
        appDescribe: '',
        doctorId: ''
      },
      rules: Object.freeze({
        name: [
          { required: true, message: '请输入', trigger: 'blur' },
          { min: 2, message: '长度在 2 到 120 个字符', trigger: 'blur' }
        ],
        cateIds: [{ required: true, message: '请选择', trigger: 'change' }],
        chapterRequestDtos: [{ validator: validateChapter, trigger: 'blur' }],
        appDescribe: [{ required: true, message: '请填写', trigger: 'blur' }]
      }),
      dialogVideoShow: false,
      dialogDoctorShow: false,
      dialogUnitShow: false,
      curAuthor: {

      },

      defaultAvatar: require('@/assets/images/default-avater.png')
    }
  },
  computed: {
    isEdit() {
      return !!this.$route.query.id
    }
  },
  created() {
    if (this.isEdit) {
      courseDetail(this.$route.query.id).then(res => {
        res.cateIds = res.cateIds.map(v => v.cateId)
        this.$refs.video.selectedIds = res.chapterRequestDtos
        this.curAuthor = res.fromType === 1 ? res.doctorResponseDto : res.orgResponseDto
        this.form = res
      })
    }
    getCategoryTreeList(0).then(res => {
      this.treeList = res
    })
  },
  methods: {
    showUnit() {
      this.dialogUnitShow = true
    },
    showDoctor() {
      this.dialogDoctorShow = true
    },
    addVideo() {
      this.dialogVideoShow = true
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          // 专家1 单位2
          if (this.curAuthor.orgId) {
            this.form.fromType = 2
            this.form.doctorId = this.curAuthor.orgId
          } else {
            this.form.fromType = 1
            this.form.doctorId = this.curAuthor.doctorId
          }
          const API = this.isEdit ? editCourse : addCourse
          API(this.form).then(() => {
            this.$message.success('操作成功')
            this.$router.go(-1)
          })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$router.go(-1)
    },
    selectDoctor(row) {
      this.dialogDoctorShow = false
      this.curAuthor = row
    },
    remove() {
      this.curAuthor = {}
      this.$refs.doctor.selectedIds = []
      this.$refs.unit.selectedIds = []
    },
    selectVideo(arr) {
      this.form.chapterRequestDtos = arr
      this.dialogVideoShow = false
    },
    sortUp(index) {
      this.swapArray(index, index - 1)
    },
    sortDown(index) {
      this.swapArray(index, index + 1)
    },
    swapArray(index1, index2) {
      this.form.chapterRequestDtos[
        index1
      ] = this.form.chapterRequestDtos.splice(
        index2,
        1,
        this.form.chapterRequestDtos[index1]
      )[0]
    },
    removeVideo(index) {
      this.form.chapterRequestDtos.splice(index, 1)
    },
    selectUnit(row) {
      this.dialogUnitShow = false
      this.curAuthor = row
    },
    onTinymceInput(v) {
      console.log(v)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  max-width: 700px;
  .input-width {
    width: 580px;
  }
}
.info-wrapper {
  display: flex;
  align-items: center;

  .avatar {
    width: 100px;
    height: 100px;
    margin-right: 20px;
    vertical-align: middle;
    flex-shrink: 0;
    border: 1px solid #eee;
  }

  .info {
    display: flex;
    flex-wrap: wrap;

    .item {
      width: 50%;
      // margin-right: 14px;
    }
    .label {
      display: inline-block;
      width: 66px;
    }
    .el-input,
    .el-textarea {
      max-width: 215px;
    }
  }
}

.list-move {
  transition: all 0.4s ease;
}
.list-enter,
.list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
.list-leave-active {
  transition: all 0.5s ease;
  position: absolute;
}

.list {
  margin: 0;
  padding: 0;

  .list-item {
    span {
      display: inline-block;
      width: 75%;
      vertical-align: middle;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .sort-btn {
      font-size: 20px;
      cursor: pointer;
    }
  }
}
</style>
