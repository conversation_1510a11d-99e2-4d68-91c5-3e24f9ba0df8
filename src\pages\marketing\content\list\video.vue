<template>
  <section class="videoEdit">
    <div class="videoEdit-title">
      {{ $route.query.id ? '编辑视频': '发布视频' }}
    </div>
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-form-item label="视频" required>
        <div style="display: flex">
          <div v-if="!form.content || form.content === ''" class="select-btn" @click="selsecVideoDialogVisible = true">视频素材库选择</div>
          <video-upload v-if="!form.content || form.content === ''" :key="form.content" :width="'260px'" :height="'100px'" :video-file-id.sync="form.content" />
          <video-play v-show="form.content && form.content !== ''" :video-file-id="form.content" @del="form.content = ''" />
        </div>
      </el-form-item>
      <el-form-item label="封面设置" required prop="coverIds">
        <UploadPic :key="form.coverUrls[0]" tips="添加封面图" :pic-id.sync="form.coverIds[0]" :url="form.coverUrls[0]" />
      </el-form-item>
      <el-form-item label="视频标题" prop="title">
        <el-input
          v-model="form.title"
          placeholder="请输入文章标题 (5~100个字)"
          :minlength="5"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item label="视频描述">
        <editor
          v-model="form.description"
          height="200"
        />
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="作者" required>
        <div>
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" />
        </div>
      </el-form-item>
      <el-form-item v-if="form.source && form.source === 1" label="任务">
        {{ form.taskName }}
      </el-form-item>
      <el-form-item v-if="form.source && form.source === 1" label="执行人">
        {{ form.taskUserName + '/' + form.taskUserPhone }}
      </el-form-item>
      <el-form-item>
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>

    <SelsectVideo :dialog-visible.sync="selsecVideoDialogVisible" :video-file-id.sync="form.content" />
  </section>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { saveOrUpdate, contentDetail } from '@/api/marketing/contentManage'
import VideoUpload from '@/components/Upload/videoUpload.vue'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Editor from '@/components/wangEditor'
import Author from '@/pages/marketing/task/components/author.vue'
import SelsectVideo from '@/pages/marketing/task/components/selectVideo.vue'
export default {
  components: {
    VideoUpload,
    VideoPlay,
    UploadPic,
    Editor,
    Author,
    SelsectVideo
  },
  data() {
    return {
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      form: {
        authorId: null,
        authorName: '',
        title: '',
        contentType: 'VOD_ID',
        content: '',
        description: '',
        categoryIds: [],
        coverIds: [],
        coverUrls: []
      },
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        title: [
          { required: true, message: '请输入视频标题', trigger: 'blur' },
          { min: 5, max: 100, message: '长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        categoryIds: [
          { required: true, message: '请选择专科分类', trigger: 'change' }
        ],
        coverIds: [
          { required: true, message: '请上传封面', trigger: 'change' }
        ]
      },
      detail: {},
      authorInfo: {},
      authorDialogVisible: false,
      selsecVideoDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
      }
    }
  },
  mounted() {
    if (this.$route.query.id) {
      contentDetail(this.$route.query.id).then(res => {
        this.form = res
        this.form.contentId = res.id
        this.authorInfo.authorName = res.authorName
        this.authorInfo.phone = res.authorPhone
        this.$set(this.form, 'categoryIds', res.cateIds)
        this.$set(this.form, 'coverIds', JSON.parse(res.coverImgIds).map(String))
        this.form.coverUrls = res.coverImgUrls
        this.form.fileList = res.coverImgUrls.map(v => {
          return { url: v }
        })
      })
    }
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
  },
  methods: {
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.content === '') {
          this.$message.error('视频未上传')
          return
        }
        if (!this.form.authorId) {
          this.$message.error('未选择作者')
          return
        }
        if (valid) {
          const param = {
            articleType: 'VIDEO',
            contentId: this.form.contentId,
            authorId: this.authorInfo.authorId || this.form.authorId,
            categoryIds: this.form.categoryIds,
            content: this.form.content,
            description: this.form.description,
            contentType: this.form.contentType,
            title: this.form.title,
            coverIds: this.form.coverIds
          }
          saveOrUpdate(param).then(() => {
            if (this.form.contentId) {
              this.$message.success('视频编辑完成')
            } else {
              this.$message.success('视频发布完成')
            }
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.videoEdit {
  background-color: #fff;
  width: 1173px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  .el-form {
    margin-left: 20px;
    width: 600px;
    ::v-deep .el-textarea__inner {
      height: 250px;
    }
    .el-select {
      width: 520px;
    }
    .el-tag {
      height: 36px;
      padding: 5px 10px;
      vertical-align: middle;
    }
    .select-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 100px;
      margin-right: 20px;
      font-size: 22px;
      font-weight: 700;
      color: #000;
      cursor: pointer;
      background-color: #f5f7fa;
    }
  }
}
</style>
