<template>
  <el-table v-bind="getProps()" v-on="$listeners">
    <template v-for="(item, index) in columns">
      <el-table-column v-if="item.slot" :key="index" v-bind="item.props">
        <template v-if="item.header" slot="header">
          <slot :name="item.header" />
        </template>
        <template slot-scope="{row}">
          <slot :name="item.slot" :row="row" />
        </template>
      </el-table-column>
      <el-table-column v-else :key="index" v-bind="item.props" />
    </template>
  </el-table>
</template>

<script>
import { Table } from 'element-ui'

export default {
  name: 'ATable',
  props: {
    ...Table.props,
    columns: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    getProps() {
      const props = { ...this.$props }
      delete props.columns
      return props
    }
  }
}
</script>
