<template>
  <el-table
    :data="[{}]"
    border
  >
    <el-table-column
      prop="promotionBudget"
      label="推广预算 (元)"
      align="center"
    >
      <el-input-number
        v-model="form.promotionBudget"
        :precision="2"
        :controls="false"
      />
    </el-table-column>
    <el-table-column
      prop="serviceBudget"
      label="制作预算 (元)"
      align="center"
    >
      <el-input-number
        v-model="form.serviceBudget"
        :precision="2"
        :controls="false"
      />
    </el-table-column>
    <el-table-column
      prop="extraBonus"
      label="点击奖励"
      align="center"
    >
      <el-table-column
        prop="extraBonusLimit"
        label="奖励上限 (元)"
        align="center"
      >
        <el-input-number
          v-model="form.extraBonusLimit"
          :precision="2"
          :controls="false"
          @change="change"
        />
      </el-table-column>
      <el-table-column
        prop="extraBonusPrice"
        label="单价 (元)"
        align="center"
      >
        <el-input-number
          v-model="form.extraBonusPrice"
          :precision="2"
          :controls="false"
          @change="change"
        />
      </el-table-column>
      <el-table-column
        prop="targetClickNum"
        label="目标点击量"
        align="center"
      >
        <el-input-number
          v-model="form.targetClickNum"
          :precision="0"
          :disabled="true"
          :controls="false"
        />
      </el-table-column>
      <el-table-column
        prop="settleClickNum"
        label="结算点击量"
        align="center"
      >
        <el-input-number
          v-model="form.settleClickNum"
          :precision="0"
          :controls="false"
        />
      </el-table-column>
      <el-table-column
        prop="extraBonusEffeday"
        label="有效期 (天)"
        align="center"
      >
        <el-input-number
          v-model="form.extraBonusEffeday"
          :precision="0"
          :controls="false"
        />
      </el-table-column>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      form: {
      }
    }
  },
  watch: {
    form: {
      handler(v) {
        this.$emit('getBudgetData', v)
      },
      deep: true
    }
  },
  methods: {
    change() {
      if (this.form.extraBonusLimit && this.form.extraBonusPrice) {
        this.$nextTick(() => {
          this.$set(this.form, 'targetClickNum', this.form.extraBonusLimit / this.form.extraBonusPrice)
          this.$set(this.form, 'settleClickNum', !this.form.settleClickNum ? this.form.targetClickNum : this.form.settleClickNum)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 ::v-deep table {
    width: 600px;
    border: 1px solid #ebeef5;
    border-spacing: 0;
    th,
    td {
      text-align: center;
      border-right: 1px solid #ebeef5;
      &:nth-last-child(1) {
        border-right: none;
      }
    }
    th {
      color: #909399;
      font-size: 14px;
    }
    td {
      border-top: 1px solid #ebeef5;
    }
    .el-input {
      width: 120px;
    }
  }
</style>
