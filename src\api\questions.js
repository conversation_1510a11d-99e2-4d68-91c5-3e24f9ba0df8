import request from '@/utils/request'

// 单位试题-列表
export function getUnitList(data) {
  return request({
    url: '/paper/org/list',
    method: 'post',
    data
  })
}

// 使用记录-导出
export function exportRecord(data) {
  return request({
    url: '/examquestion/record/create/file',
    method: 'post',
    data
  })
}

// 使用记录-列表
export function getRecordList(data) {
  return request({
    url: '/examquestion/record/list',
    method: 'post',
    data
  })
}

// 地区树形数据
export function getArea() {
  return request({
    url: '/area/areaTreeList',
    method: 'get'
  })
}

// 使用记录-详情
export function getDetail(data) {
  return request({
    url: '/examquestion/record/open/paper',
    method: 'post',
    data
  })
}
