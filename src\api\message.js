import request from '@/utils/request'

// 消息任务列表
export function getMessageTaskList(params) {
  return request({
    url: `/messageCenter/getMessageTaskList`,
    method: 'post',
    data: params
  })
}

// 发送消息
export function sendMsg(params) {
  return request({
    url: `/messageCenter/sendMsgByIds`,
    method: 'post',
    data: params
  })
}

// 撤回、取消撤回消息任务
export function updateWithdraw(params) {
  return request({
    url: `/messageCenter/updateWithdrawStatus`,
    method: 'post',
    data: params
  })
}

// 获取平台内容列表
export function getPlatformContents(params) {
  return request({
    url: '/messageCenter/getPlatformContents',
    method: 'post',
    data: params
  })
}

// 创建消息任务时获取消息ID
export function getMsgTaskId() {
  return request({
    url: '/messageCenter/getMsgTaskId',
    method: 'get'
  })
}

// 删除消息任务
export function delMessageTask(id) {
  return request({
    url: `/messageCenter/delMessageTask/${id}`,
    method: 'get'
  })
}

// 创建或更新消息任务
export function upsertMessageTask(params) {
  return request({
    url: '/messageCenter/upsertMessageTask',
    method: 'post',
    data: params
  })
}

// 获取消息详情
export function getMsgInfo(params) {
  return request({
    url: `/messageCenter/getMsgInfoById?msgId=${params}`,
    method: 'post'
  })
}

// 发送/已送达/未送达人数
export function overview(params) {
  return request({
    url: `/messageStatistics/overview`,
    method: 'get',
    params
  })
}

// 消息发送对象列表
export function personList(params) {
  return request({
    url: '/messageStatistics/personList',
    method: 'post',
    data: params
  })
}
