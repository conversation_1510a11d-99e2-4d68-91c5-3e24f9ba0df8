import request from '@/utils/request'

// 增加科室
export function addMajor(params) {
  return request({
    url: '/major/add',
    method: 'post',
    data: params
  })
}

// 删除科室
export function deleteMajor(params) {
  return request({
    url: '/major/delete',
    method: 'get',
    params: params
  })
}

// 编辑科室
export function updateMajor(params) {
  return request({
    url: '/major/update',
    method: 'post',
    data: params
  })
}

// 专科树
export function treeList(params) {
  return request({
    url: `/major/treeList`,
    method: 'get',
    params: params
  })
}
