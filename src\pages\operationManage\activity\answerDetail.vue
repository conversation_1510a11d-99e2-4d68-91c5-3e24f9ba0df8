<template>
  <div class="app-container">
    <div v-for="(item,index) in list" :key="item.id" class="test-item">
      <i :class="item.passed ? 'el-icon-success' : 'el-icon-error'" />
      <div class="test-title">{{ index+1|indexFmt }}. {{ item.title }} （{{ item.submit_answer }}）？（{{ item.question_type }}）</div>
      <div v-for="(option,oIndex) in item.optionsList" :key="oIndex" class="test-option">
        <i :class="option.isAnswer ? 'el-icon-check' : 'place-icon'" />
        {{ option.index }}、{{ option.content }}
      </div>
      <div class="test-tip">【正确答案】{{ item.standard_answer }}</div>
    </div>
  </div>
</template>

<script>
import request from '@/api/activity'

export default {
  name: 'AnswerDetail',
  filters: {
    indexFmt(val) {
      return val < 10 ? '0' + val : val
    }
  },
  data() {
    return {
      activityId: this.$route.query.id,
      list: []
    }
  },
  created() {
    request.actExamePaper(this.activityId).then(res => {
      this.list = res
    })
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .test-item {
    position: relative;
    font-size: 14px;
    color: #333;
    margin-bottom: 45px;

    > i {
      position: absolute;
      right: 30px;
      top: 10px;
      font-size: 40px;
      color: #46b94b;
      &.el-icon-error {
        color: #f56c6c;
      }
    }

    .test-title {
      font-weight: bold;
      margin-bottom: 14px;
    }
    .test-option {
      line-height: 30px;
      .place-icon {
        display: inline-block;
        width: 14px;
      }
    }
    .test-tip {
      margin-top: 16px;
    }
  }
}
</style>
