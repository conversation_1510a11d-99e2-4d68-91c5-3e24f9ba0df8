<template>
  <div>
    <el-button v-if="e.link === ''" type="primary" @click="dialogVisible = true">添加</el-button>
    <span>
      <el-tag
        v-if="e.link !== ''"
        closable
        @close="e.link = '',e.linkName = ''"
      >
        <a :href="e.link" target="_blank">{{ e.linkName || e.link }}</a>
      </el-tag>
    </span>
    <el-dialog title="添加链接" width="400px" center :show-close="false" :visible.sync="dialogVisible" :before-close="addLink">
      <el-input
        v-model="e.link"
        type="url"
        style="margin-bottom: 20px"
        placeholder="http://或https://开头"
      />
      <el-input v-model="e.linkName" placeholder="链接名称(选填)" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="addLink('cancel')">取 消</el-button>
        <el-button type="primary" @click="addLink('confirm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    link: {
      type: String,
      default: ''
    },
    linkName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      e: {
        link: this.link,
        linkName: this.linkName
      }
    }
  },
  watch: {
    e: {
      handler(v) {
        this.$emit('update:link', v.link)
        this.$emit('update:linkName', v.linkName)
      },
      deep: true
    },
    link(v) {
      this.e.link = v
      this.e.linkName = this.linkName
    }
  },
  methods: {
    addLink(handle) {
      if (handle && handle === 'confirm') {
        const reg = /(http|https):\/\/([\w.]+\/?)\S*/
        if (!reg.test(this.e.link)) {
          this.$message.error('请输入正确的URL')
        } else {
          this.dialogVisible = false
        }
      } else {
        this.dialogVisible = false
        this.e.link = ''
        this.e.linkName = ''
      }
    }
  }
}
</script>
