<template>
  <el-dialog
    title="同步至代征"
    :visible.sync="dialogVisible"
    width="500px"
    center
    :before-close="handleClose"
  >
    <el-form
      ref="platform"
      label-width="80px"
    >
      <el-form-item label="服务商:">
        {{ name }}
      </el-form-item>
      <el-form-item label="请选择:">
        <el-radio-group v-model="platformId">
          <el-radio
            v-for="item in platformSelectIdList"
            :key="item.id"
            :label="item.id"
            :disabled="platformIdList.includes(item.id)"
          >{{ item.desc }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="synchronize()"
      >确定</el-button>
    </span>

    <el-dialog
      title="提示"
      :visible.sync="errorDialogVisible"
      width="400px"
      top="200px"
      center
      append-to-body
      :before-close="errorHandleClose"
    >
      <div v-show="userInfo.status === 2">{{ userInfo.tipMsg }}</div>
      <div v-show="userInfo.status === 3">
        <p>{{ userInfo.tipMsg }}</p>
        <el-form
          ref="form"
          label-width="100px"
          :model="userInfo"
          :rules="rules"
        >
          <el-form-item
            label="姓名"
            prop="name"
          >
            <el-input v-model="userInfo.realName" disabled />
          </el-form-item>
          <el-form-item
            label="身份证号"
            prop="idcard"
          >
            <el-input v-model="userInfo.idcard" disabled />
          </el-form-item>
          <el-form-item v-if="[3,4].includes(platformId)" label="身份证正面:" prop="idcardFrontImg">
            <singleImage
              v-model="userInfo.idcardFrontImg"
              width="100%"
              height="150px"
              type="idCard"
              :url.sync="userInfo.idcardFrontImgUrl"
              @input="function(id) {userInfo.idcardFrontImg = id}"
            />
          </el-form-item>
          <el-form-item v-if="[3,4].includes(platformId)" label="身份证反面:" prop="idcardBehindImg">
            <singleImage
              v-model="userInfo.idcardBehindImg"
              width="100%"
              height="150px"
              type="idCard"
              :url.sync="userInfo.idcardBehindImgUrl"
              @input="function(id) {userInfo.idcardBehindImg = id}"
            />
          </el-form-item>
          <el-form-item
            label="银行卡号"
            prop="bankcard"
          >
            <el-input v-model="userInfo.bankcard" @blur="getbankinfo" />
          </el-form-item>
          <el-form-item
            label="卡类型"
            prop="bankName"
          >
            <el-input v-model="userInfo.bankName" />
          </el-form-item>
          <el-form-item
            label="开户支行"
            prop="bankSubName"
          >
            <el-input v-model="userInfo.bankSubName" />
          </el-form-item>
          <el-form-item
            label="预留手机"
            prop="bankBindPhone"
          >
            <el-input v-model="userInfo.bankBindPhone" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="errorHandleClose">取消</el-button>
        <el-button
          type="primary"
          @click="errorHandleClose('submit')"
        >{{ userInfo.status === 2 ? '已完成创建' : '确定'
        }}</el-button>
      </div>
    </el-dialog>

  </el-dialog>
</template>

<script>
import { syncTaxCollected } from '@/api/marketing/userPromote'
import { platformList } from '@/api/marketing/taskPromote'
import singleImage from '@/components/SingleImage'
import { getBankBin } from 'bankcardinfo'
export default {
  components: {
    singleImage
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: null
    },
    platformIdList: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      errorDialogVisible: false,
      platformId: null,
      platformSelectIdList: [],
      userInfo: {
        bankBindPhone: '',
        bankcard: '',
        bankSubName: '',
        bankName: '',
        idcard: '',
        idcardFrontImg: '',
        idcardBehindImg: '',
        realName: '',
        tipMsg: '',
        status: null
      },
      rules: {
        bankcard: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' }
        ],
        bankName: [
          { required: true, message: '请输入卡类型', trigger: 'blur' }
        ],
        bankSubName: [
          { required: true, message: '请输入开户支行', trigger: 'blur' }
        ],
        bankBindPhone: [
          { required: true, message: '请输入预留手机', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    platformId(val) {
      if ([3, 4].includes(val)) {
        this.rules = {
          ...this.rules,
          idcardFrontImg: [
            { required: true, message: '请上传身份证正面', trigger: 'blur' }
          ],
          idcardBehindImg: [
            { required: true, message: '请上传身份证反面', trigger: 'blur' }
          ]
        }
      }
    }
  },
  created() {
    platformList({ excludeIds: 1 }).then(res => {
      this.platformSelectIdList = res
    })
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    getbankinfo() {
      getBankBin(this.userInfo.bankcard).then(res => {
        this.userInfo.bankName = res.bankName + res.cardTypeName
      }).catch(() => { this.$message.error('银行卡号错误,请重新输入') })
    },
    taxCollected(param) {
      syncTaxCollected(param).then((res) => {
        if (res.status === 1) {
          this.errorDialogVisible = false
          this.$message.success('已成功同步')
          this.handleClose()
          this.$parent.getUserList()
        } else {
          this.userInfo = res
          this.errorDialogVisible = true
        }
      })
    },
    synchronize() {
      const param = {
        createType: 2,
        platformId: this.platformId,
        userId: this.userId
      }
      this.taxCollected(param)
    },
    errorHandleClose(type) {
      if (type === 'submit') {
        this.$refs.form.validate((valid) => {
          if ([2, 3].includes(this.userInfo.status) || valid) {
            const param = {
              createType: this.platformId === 2 && this.userInfo.status === 2 ? 1 : 2,
              platformId: this.platformId,
              userId: this.userId,
              userInfo: this.userInfo
            }
            this.taxCollected(param)
          }
        })
      } else {
        this.errorDialogVisible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 10px 30px;
    color: #000;
    max-height: 510px;
    overflow-y: auto;

    /* 整个滚动条 */
    &::-webkit-scrollbar {
      /* 对应纵向滚动条的宽度 */
      width: 10px;
      /* 对应横向滚动条的宽度 */
      height: 10px;
    }

    /* 滚动条上的滚动滑块 */
    &::-webkit-scrollbar-thumb {
      background-color: #d0d3d9;
      border-radius: 32px;
    }

    h2 {
      display: flex;
      align-items: center;
      font-size: 22px;

      &:before {
        content: '';
        display: block;
        margin-right: 10px;
        width: 3px;
        height: 24px;
        background-color: #409eff;
      }
    }

    h3 {
      font-size: 18px;

      span {
        margin-right: 170px;
      }
    }

    ul {
      margin: 0 auto;
      padding: 0;

      li {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .el-input,
        .el-select {
          margin-right: 20px;
          width: 191px;
        }

        .del {
          font-size: 18px;
          color: #409eff;
          cursor: pointer;
        }
      }
    }

    .area {
      margin-top: 45px;

      .el-cascader {
        width: 300px;
      }
    }
  }
}
</style>
