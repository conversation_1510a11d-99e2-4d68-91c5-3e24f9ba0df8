<template>
  <div>
    <div class="search-column__item">
      <el-input v-model="condition.keyword" placeholder="请输入搜索关键字" clearable @clear="init()" @keydown.enter.native="init()">
        <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="init()" />
        <el-select slot="prepend" v-model="condition.type" style="width: 120px">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-input>
    </div>
    <div class="search-column__item">
      <el-select v-model="condition.liveMethod" clearable placeholder="请选择直播方式" @change="init()">
        <el-option v-for="item in live_method" :key="item.dictId" :label="item.value" :value="item.code" />
      </el-select>
    </div>
    <div class="search-column__item">
      <el-select v-model="condition.liveType" clearable placeholder="请选择直播类型" @change="init()">
        <el-option v-for="item in live_type" :key="item.dictId" :label="item.value" :value="item.code" />
      </el-select>
    </div>
    <div class="search-column__item">
      <el-select v-model="condition.promotionType" clearable placeholder="请选择推广类型" @change="init()">
        <el-option v-for="item in promotion_type" :key="item.dictId" :label="item.value" :value="item.code" />
      </el-select>
    </div>
    <div v-if="hasAuditStatus" class="search-column__item">
      <el-select v-model="condition.auditStatus" clearable placeholder="请选择审核状态" @change="init()">
        <el-option v-for="item in auditStatusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div v-if="hasLiveStatus" class="search-column__item">
      <el-select v-model="condition.liveStatus" clearable placeholder="请选择直播状态" @change="init()">
        <el-option v-for="item in liveStatusList" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <!-- 时间选择器 -->
    <div v-if="time" class="search-column__item">
      <el-date-picker
        v-model="timeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        @change="changePicker"
        @clear="changePicker"
      />
    </div>
    <div class="search-column__item">
      <el-button type="primary" @click="clear()">重置</el-button>
    </div>
  </div>
</template>

<script>
import { getDict } from '@/api/liveManage'
export default {
  props: {
    hasAuditStatus: {
      type: Boolean,
      default: false
    },
    hasLiveStatus: {
      type: Boolean,
      default: false
    },
    time: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const _this = this
    return {
      timeRange: [],
      duration: [],
      pickerOptions: {
        disabledDate(time) {
          if (typeof (_this.duration[0]) === 'undefined') {
            return time.getTime() > Date.now()// 禁止选择以后的时间
          } else {
            const currentTime = _this.duration[0]
            const threeMonths = 60 * 60 * 1000 * 24 * 31 * 11
            if (currentTime) {
              return ((time.getTime() > currentTime.getTime() + threeMonths || time.getTime() < currentTime.getTime() - threeMonths) || time.getTime() > Date.now())
    	      }
          }
        },
        onPick({ minDate, maxDate }) {
          // 当第一时间选中才设置禁用
          if (minDate && !maxDate) {
            _this.duration[0] = minDate
          }
          if (maxDate) {
            _this.duration[1] = maxDate
          }
        }
      },
      // 请求参数
      condition: {
        keyword: '',
        type: 1,
        liveMethod: '',
        liveType: '',
        promotionType: ''
      },
      typeList: [
        { label: '直播主题', value: 1 },
        { label: '讲师姓名', value: 2 },
        { label: '内容扩展方', value: 3 }
      ],
      live_method: [],
      live_type: [],
      promotion_type: [],
      auditStatusList: [
        { label: '待审核', value: 1 },
        { label: '审核不通过', value: 2 },
        { label: '审核通过', value: 3 }
      ],
      liveStatusList: [
        { label: '未开始', value: 2 },
        { label: '进行中', value: 1 },
        { label: '已结束', value: 3 }
      ]

    }
  },
  created() {
    this.getAllType()
  },
  methods: {
    // 日期选择器
    changePicker(data) {
      if (data && data.length > 0) {
        this.condition.startTime = data[0] + ' 00:00:00'
        this.condition.endTime = data[1] + ' 23:59:59'
        this.init()
      } else {
        this.condition.startTime = ''
        this.condition.endTime = ''
        this.init()
      }
    },
    // 获取字典
    getAllType() {
      // LIVE_METHOD-方式 LIVE_TYPE-类型 PROMOTION_TYPE-推广类型
      getDict({ type: 'LIVE_METHOD' }).then(res => {
        this.live_method = res
      })
      // 直播类型
      getDict({ type: 'LIVE_TYPE' }).then(res => {
        this.live_type = res
      })
      // 推广类型
      getDict({ type: 'PROMOTION_TYPE' }).then(res => {
        this.promotion_type = res
      })
    },
    init() {
      this.$emit('search', this.condition)
    },
    clear() {
      this.condition.keyword = ''
      this.condition.liveMethod = ''
      this.condition.liveType = ''
      this.condition.promotionType = ''
      if (this.hasAuditStatus) {
        this.condition.auditStatus = ''
      }
      if (this.hasLiveStatus) {
        this.condition.liveStatus = ''
      }
      if (this.time) {
        this.timeRange = []
        this.condition.startTime = ''
        this.condition.endTime = ''
      }
      this.$emit('search', this.condition)
    }
  }
}
</script>

<style>

</style>
