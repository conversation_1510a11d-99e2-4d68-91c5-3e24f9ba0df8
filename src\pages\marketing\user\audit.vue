<template>
  <section class="audit">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        placeholder="姓名/手机"
        clearable
        @clear="handleFilter()"
        @keydown.enter.native="handleFilter()"
      >
        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.auditStatus" placeholder="审核状态" @change="handleFilter()">
        <el-option
          v-for="item in auditStateOptions"
          :key="item.label"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.condition.promoteType" placeholder="认证类型" @change="handleFilter()">
        <el-option
          v-for="item in typeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select v-model="listQuery.condition.serviceProviderId" clearable placeholder="全部服务商" @change="handleFilter()">
        <el-option
          v-for="item in serverOptions"
          :key="item.serviceOrgId"
          :label="item.serviceName"
          :value="item.serviceOrgId"
        />
      </el-select>
    </div>
    <h2>
      用户列表
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.auditStatus === '待审核'"
              type="text"
              size="mini"
              @click="getAuditDetail(scope.row.id,'审核')"
            >审核</el-button>
            <el-button
              v-if="scope.row.auditStatus !== '待审核'"
              type="text"
              size="mini"
              @click="getAuditDetail(scope.row.id,'查看')"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <!-- 审核详情弹窗 -->
    <info-detail
      :dialog-visible.sync="dialogVisible"
      :desc-dialog-visible.sync="descDialogVisible"
      :look="look"
      :info="info"
      @audit="audit"
    />
    <!-- 审核说明弹窗 -->
    <el-dialog
      class="descDialog"
      title="审核说明"
      :visible.sync="descDialogVisible"
      width="30%"
      center
    >
      <el-form label-width="80px">
        <el-form-item label="审核说明" :required="true">
          <el-input v-model="desc" type="textarea" :rows="6" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="descDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="audit(id, 3, desc)">确 定</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import InfoDetail from './components/infoDetail.vue'
import Pagination from '@/components/Pagination'
import { auditApply, auditDeail, auditList } from '@/api/marketing/userPromote'
import { serviceProviderList } from '@/api/marketing/taskPromote'
export default {
  components: {
    Pagination,
    InfoDetail
  },
  data() {
    return {
      listQuery: {
        condition: {
          keyword: '',
          auditStatus: '',
          promoteType: '',
          serviceProviderId: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      id: '',
      auditStateOptions: [
        { label: '全部状态', value: '' },
        { label: '待审核', value: [1] },
        { label: '审核通过', value: [2] },
        { label: '审核不通过', value: [3] }
      ],
      typeOptions: [
        { label: '全部类型', value: '' },
        { label: '创作者', value: 'CREATOR' },
        { label: '推广员', value: 'PROMOTER' }
      ],
      serverOptions: [],
      total: 0,
      dialogVisible: false,
      descDialogVisible: false,
      desc: '',
      look: false,
      info: {},
      tableColumn: [
        { prop: 'realName', label: '姓名', width: '120' },
        { prop: 'phone', label: '手机', width: '120' },
        { prop: 'promoteType', label: '认证类型', width: '120' },
        { prop: 'serviceProvider', label: '服务商', width: '120' },
        { prop: 'auditStatus', label: '审核状态', width: '120' },
        { prop: 'createdTime', label: '提交时间', width: '120' },
        { prop: 'auditorUname', label: '审核人', width: '120' },
        { prop: 'auditTime', label: '审核时间', width: '120' }
      ],
      tableData: []
    }
  },
  mounted() {
    this.getAuditList()
    serviceProviderList().then(res => {
      this.serverOptions = res
    })
  },
  methods: {
    handlePagination(v) {
      this.listQuery.pager = v
      this.getAuditList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getAuditList()
    },
    getAuditList() {
      auditList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.auditStatus = this.auditStateOptions.find(i => i.value[0] === v.auditStatus).label
          v.promoteType = this.typeOptions.find(i => i.value === v.promoteType).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    getAuditDetail(id, type) {
      if (type === '查看') {
        this.look = true
      } else {
        this.look = false
      }
      this.id = id
      auditDeail({ id }).then(res => {
        this.info = res
        this.info.id = this.id
        this.dialogVisible = true
      })
    },
    audit(id, status, desc) {
      const query = {
        auditDesc: desc,
        auditStatus: status,
        id
      }
      auditApply(query).then(res => {
        this.descDialogVisible = false
        this.dialogVisible = false
        this.getAuditList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.audit {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    background-color: #eaeaee;
    padding-bottom: 15px;
    .el-input,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
