import { login, getUserMenu, logOut, getUserOperations } from '@/api/user'
import {
  getToken,
  setToken,
  removeToken,
  removeCookie,
  setBatchCookies
} from '@/utils/auth'
import router, { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    userId: '',
    avatar: ''
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: state => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_USER_ID: (state, id) => {
    state.userId = id
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  REMOVE_ROLES: state => {
    state.roles = []
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then(response => {
          commit('SET_TOKEN', response.token)
          commit('SET_NAME', response.realname)
          commit('SET_USER_ID', response.userId)
          commit('SET_ORG_ID', response.currentOrgId)
          setBatchCookies({
            name: response.realname,
            userId: response.userId,
            orgId: response.currentOrgId
          })
          setToken(response.token)
          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // get user menu
  getUserMenus({ commit, state }) {
    return new Promise((resolve, reject) => {
      getUserMenu()
        .then(response => {
          if (!response) {
            reject('用户菜单获取失败，请刷新重试！')
          }
          resolve(response)
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // 权限
  getUserOperation({ commit }) {
    return new Promise((resolve, reject) => {
      getUserOperations().then(response => {
        commit('SET_OPERATION', response)
        localStorage.setItem('SET_OPERATION', JSON.stringify(response))
        resolve(response)
      })
    })
  },

  // user logout
  logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logOut({ token: state.token }).then(() => {
        commit('permission/SET_ROUTES', [], { root: true })
        commit('SET_TOKEN', '')
        removeToken()
        removeCookie('name')
        removeCookie('userId')
        resetRouter()
        commit('RESET_STATE')
        resolve()
        router.push(`/login`)
        location.reload()
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      removeToken()
      removeCookie('name')
      removeCookie('userId')
      commit('RESET_STATE')
      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
