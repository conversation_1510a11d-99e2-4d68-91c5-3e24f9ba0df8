<template>
  <div class="trend">
    <h3>{{ explain.title }}</h3>
    <div id="chart" class="chart" />
  </div>
</template>

<script>
var echarts = require('echarts/lib/echarts')
require('echarts/lib/chart/line')
require('echarts/lib/chart/bar')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/legend')
import { GridComponent, ToolboxComponent } from 'echarts/components'
echarts.use([GridComponent, ToolboxComponent])

export default {
  name: 'Trend',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    explain: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(v) {
        this.setOptions(v)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(document.getElementById('chart'))
      this.setOptions(this.data)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    })
  },
  methods: {
    setOptions(data) {
      this.myChart.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: '{b0}<br /> {a0}:{c0}<br />{a1}: {c1}%'
        },
        color: ['#5b8ff9', '#61d6a0'],
        grid: {
          top: '18%',
          left: '3%',
          right: '5%',
          bottom: '6%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          boundaryGap: true,
          alignWithLabel: true,
          data: data.date
        },
        yAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            }
          },
          {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLabel: {
              formatter: '{value}%',
              align: 'left'
            }
          }
        ],
        series: [
          {
            name: this.explain.count,
            yAxisIndex: 0,
            type: 'bar',
            symbol: 'circle',
            data: data.count || []
          },
          {
            name: this.explain.ratio,
            yAxisIndex: 1,
            type: 'line',
            symbol: 'circle',
            data: data.ratio || []
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.myChart) {
        return
      }
      this.myChart.dispose()
      this.myChart = null
    }
  }
}
</script>

<style scoped lang="scss">
.trend {
  border: 1px solid #eee;
  border-radius: 10px;
  overflow: hidden;
  h3 {
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
    i{
      cursor: pointer;
      color:#3bb19c;
    }
  }
  .chart {
    height: 400px;
  }
}
</style>
