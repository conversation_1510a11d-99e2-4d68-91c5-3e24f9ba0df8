<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable>
          <el-select slot="prepend" v-model="queryType" style="width:100px">
            <el-option label="名称" value="extenderName" />
            <el-option label="手机" value="phone" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.type" clearable placeholder="请选择类型">
          <el-option label="个人" :value="1" />
          <el-option label="企业" :value="2" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="tableQuery.condition.auditStatus" clearable placeholder="请选择审核状态">
          <el-option v-for="item in auditStatusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-button type="primary" @click="search()">查询</el-button>
        <el-button type="primary" @click="clear()">重置</el-button>
      </div>
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='auditStatus'">
            <span>{{ scope.row[col.prop] | auditStatusFilter }}</span>
          </template>
          <template v-else-if="col.prop==='type'">
            <span>{{ scope.row[col.prop] === 1 ? '个人' : '企业' }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus===1" type="text" @click="checkFn(row.applyId)">审核</el-button>
          <el-button type="text" @click="checkHistoryFn(row)">审核历史</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <el-dialog title="内容扩展方审核" :visible.sync="checkVisible" width="50vw" top="20px">
      <el-row class="detail">
        <!-- 个人 -->
        <el-form v-if="type===1" ref="form" :model="detail" label-width="200px" label-position="right">
          <h2>个人信息</h2>
          <el-form-item label="真实姓名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="身份证号码:">
            <span>{{ detail.idcard }}</span>
          </el-form-item>
          <el-form-item label="身份证正/反面:">
            <div class="imgBox">
              <el-image style="width: 300px" fit="cover" :src="detail.idcardFrontImgUrl" :preview-src-list="[detail.idcardFrontImgUrl]" />
              <el-image style="width: 300px" fit="cover" :src="detail.idcardBehindImgUrl" :preview-src-list="[detail.idcardBehindImgUrl]" />
            </div>
          </el-form-item>
          <el-form-item label="手机:">
            <span>{{ detail.phone }}</span>
          </el-form-item>

          <h2>结算信息</h2>
          <el-form-item label="开户行:">
            <span>{{ detail.bankName }}</span>
          </el-form-item>
          <el-form-item label="户名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="银行卡号:">
            <span>{{ detail.bankcard }}</span>
          </el-form-item>
        </el-form>
        <!-- 企业 -->
        <el-form v-else ref="form" :model="detail" label-width="200px" label-position="right">
          <h2>企业信息</h2>
          <el-form-item label="企业全称:">
            <span>{{ detail.orgName }}</span>
          </el-form-item>
          <el-form-item label="统一社会信用代码:">
            <span>{{ detail.socialCreditCode }}</span>
          </el-form-item>
          <el-form-item label="营业执照:">
            <el-image style="width: 300px" fit="cover" :src="detail.businessLicenseImgUrl" :preview-src-list="[detail.businessLicenseImgUrl]" />
          </el-form-item>
          <el-form-item label="企业法人:">
            <span>{{ detail.legalPerson }}</span>
          </el-form-item>
          <el-form-item label="企业法人身份证号码:">
            <span>{{ detail.legalPersonIdcard }}</span>
          </el-form-item>
          <el-form-item label="企业法人身份证正/反面:">
            <div class="imgBox">
              <el-image style="width: 300px" fit="cover" :src="detail.legalPersonIdcardFrontImgUrl" :preview-src-list="[detail.legalPersonIdcardFrontImgUrl]" />
              <el-image style="width: 300px" fit="cover" :src="detail.legalPersonIdcardBehindImgUrl" :preview-src-list="[detail.legalPersonIdcardBehindImgUrl]" />
            </div>
          </el-form-item>

          <h2>联系信息</h2>
          <el-form-item label="联系人:">
            <span>{{ detail.contacts }}</span>
          </el-form-item>
          <el-form-item label="手机:">
            <span>{{ detail.phone }}</span>
          </el-form-item>
          <el-form-item label="联系地址:">
            <span>{{ detail.areaName }}</span>
          </el-form-item>
          <el-form-item label="">
            <span>{{ detail.address }}</span>
          </el-form-item>

          <h2>结算信息</h2>
          <el-form-item label="开户行:">
            <span>{{ detail.bankName }}</span>
          </el-form-item>
          <el-form-item label="户名:">
            <span>{{ detail.orgName }}</span>
          </el-form-item>
          <el-form-item label="银行卡号:">
            <span>{{ detail.bankcard }}</span>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="text-align:center;padding-bottom:20px">
        <el-button @click="checkVisible = false">取 消</el-button>
        <el-button type="danger" @click="checkRejuct()">不通过</el-button>
        <el-button type="primary" @click="checkPass()">通 过</el-button>
      </el-row>
    </el-dialog>

    <!-- 审核不通过弹窗 -->
    <el-dialog title="审核说明" :visible.sync="rejuctVisible" width="600px">
      <div style="padding:20px">
        <el-form label-width="80px">
          <el-form-item label="审核说明:">
            <span><i class="el-icon-warning-outline" /> 请填写不通过的说明，以便修正</span>
            <el-input
              v-model="opinion"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="rejuctVisible = false">取 消</el-button>
        <el-button type="primary" @click="rejuctConfirm()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 审核历史 弹窗 -->
    <el-dialog title="审核历史" :visible.sync="hisVisible" width="90vw">
      <div style="padding:20px">
        <el-table :data="hisList" border stripe>
          <el-table-column v-for="col in hisListColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.prop==='res'">
                <span>{{ scope.row[col.prop]===0?'不通过':'通过' }}</span>
              </template>
              <template v-else-if="col.prop==='type'">
                <span>{{ scope.row[col.prop] === 1 ? '个人' : '企业' }}</span>
              </template>
              <template v-else>
                <span>{{ scope.row[col.prop] }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination class="text-center" :page-size="hisQuery.pager.pageSize" :total="hisTotal" :page="hisQuery.pager.page" @pagination="hisPagination" />
    </el-dialog>

    <!-- 企业-审核通过-创建单位 -->
    <DialogUnitCreate :visible.sync="unitCreateVisible" :detail="detail" @handleCancel="clearUnitSubmitForm" @handleSave="handleSaveDialogForm" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import DialogUnitCreate from './dialogUnitCreate.vue'
import {
  extendList,
  extendDetail,
  extendApply,
  extendAddOrg,
  extendAuditRecordList
} from '@/api/liveApply'
import { getOrganTypeList } from '@/api/userManage'

export default {
  name: 'LiveList',
  filters: {
    auditStatusFilter(val) {
      const arr = ['待提交审核', '待审核', '审核通过', '审核不通过']
      return arr[val]
    }
  },
  components: { Pagination, DialogUnitCreate },
  data() {
    return {
      keyword: '',
      queryType: 'extenderName',
      auditStatus: '',
      auditStatusList: [
        { label: '待审核', value: 1 },
        { label: '审核通过', value: 2 },
        { label: '审核不通过', value: 3 }
      ],
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: '名称', align: 'center', prop: 'extenderName' },
        { id: 1, label: '手机', align: 'center', prop: 'phone' },
        { id: 2, label: '类型', align: 'center', prop: 'type' },
        { id: 3, label: '提交审核时间', align: 'center', prop: 'updateTime' },
        { id: 4, label: '审核状态', align: 'center', prop: 'auditStatus' }
      ]),
      hisListColumnList: Object.freeze([
        { id: 0, label: '名称', align: 'center', prop: 'extenderName' },
        { id: 1, label: '手机', align: 'center', prop: 'phone' },
        { id: 2, label: '类型', align: 'center', prop: 'type' },
        { id: 3, label: '提交审核时间', align: 'center', prop: 'createTime', width: '160px' },
        { id: 4, label: '审核人', align: 'center', prop: 'auditorUaerName' },
        { id: 5, label: '审核时间', align: 'center', prop: 'updateTime', width: '160px' },
        { id: 6, label: '审核结果', align: 'center', prop: 'res' },
        { id: 7, label: '审核说明', align: 'center', prop: 'opinion' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      // 审核历史
      hisQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      hisTotal: 0,
      hisList: [],
      hisVisible: false,
      applyId: '',
      detail: {},
      checkVisible: false,
      opinion: '',
      rejuctVisible: false,
      type: null,
      unitCreateVisible: false,
      formData: {},
      enterDto: {},
      orgTypeId: ''
    }
  },
  created() {
    this.getList()
    this.getOrgTypeId()
  },
  methods: {
    getOrgTypeId() {
      getOrganTypeList().then(res => {
        res.forEach(item => {
          if (item.name === '企业') {
            this.orgTypeId = item.orgTypeId
          }
        })
      })
    },
    // save form
    handleSaveDialogForm(val) {
      const { orgName, areaId, address, contacts, phone } = this.detail

      this.formData.areaId = areaId
      this.formData.auditDto = {
        applyId: this.applyId,
        auditStatus: 2,
        type: 2
      }
      this.formData.conExtenderLiveFlag = 1
      this.formData.level = 0
      this.formData.orgName = orgName
      this.formData.shortCode = val.shortCode
      this.formData.status = val.status
      this.formData.type = this.orgTypeId

      const obj = {}
      obj.address = address
      obj.buyVideoSize = val.buyVideoSize
      obj.contacts = contacts
      obj.examFlag = val.examFlag
      obj.faceFlag = val.faceFlag
      obj.industryId = val.industryId
      obj.introdution = val.introdution
      obj.logo = val.logo || 0
      obj.maxSizeCount = val.maxSizeCount
      obj.maxStaffCount = val.maxStaffCount
      obj.phone = phone
      obj.scale = val.scale
      obj.subjuctCode = val.subjuctCode

      this.formData.enterDto = obj
      extendAddOrg(this.formData).then(res => {
        this.$message.success('创建成功!')
        this.unitCreateVisible = false
        this.checkVisible = false
        this.getList()
        this.clearUnitSubmitForm()
      })
    },
    // 清空表单
    clearUnitSubmitForm() {
      const formdata = this.formData
      formdata.areaId = '0'
      formdata.auditDto = {}
      formdata.conExtenderLiveFlag = ''
      formdata.level = '0'
      formdata.orgName = ''
      formdata.shortCode = ''
      formdata.status = ''
      formdata.type = ''
      formdata.enterDto = {}
    },
    getList() {
      extendList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    search() {
      this.tableQuery.condition.extenderName = ''
      this.tableQuery.condition.phone = ''
      this.tableQuery.condition[this.queryType] = this.keyword
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear() {
      this.keyword = ''
      this.tableQuery.condition.extenderName = ''
      this.tableQuery.condition.phone = ''
      this.tableQuery.condition.type = ''
      this.tableQuery.condition.auditStatus = ''
      this.tableQuery.pager.page = 1
      this.getList()
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    // 审核-打开弹窗
    checkFn(applyId) {
      this.applyId = applyId
      extendDetail(applyId).then(res => {
        this.type = res.type
        if (res.type === 1) {
          // 个人
          this.detail = res.userDetailedDto
        }
        if (res.type === 2) {
          // 企业
          this.detail = res.orgDetailedDto
        }
        this.checkVisible = true
      })
    },
    // 审核历史-打开弹窗
    checkHistoryFn(data) {
      this.hisQuery.condition.applyId = data.applyId
      this.hisQuery.condition.type = data.type
      this.hisQuery.pager.page = 1
      extendAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
        this.hisVisible = true
      })
    },
    // 审核历史-分页
    hisPagination(val) {
      this.hisQuery.pager = val
      extendAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
      })
    },
    // 审核-通过
    checkPass() {
      const query = {
        applyId: this.applyId,
        auditStatus: 2,
        opinion: this.opinion,
        type: this.type
      }
      // 个人-通过
      if (this.type === 1) {
        extendApply(query).then(res => {
          this.$message.success('审核成功')
          this.checkVisible = false
          this.getList()
        })
      }
      // 企业-通过-创建单位
      if (this.type === 2) {
        this.unitCreateVisible = true
      }
    },
    // 审核-不通过
    checkRejuct() {
      this.opinion = ''
      this.rejuctVisible = true
    },
    // 不通过-提交
    rejuctConfirm() {
      const query = {
        applyId: this.applyId,
        auditStatus: 3,
        opinion: this.opinion,
        type: this.type
      }
      extendApply(query).then(res => {
        this.$message.success('审核成功')
        this.rejuctVisible = false
        this.checkVisible = false
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  h2 {
    color: #47c1a8;
    margin-left: 100px;
  }
}
.imgBox {
  width: 100%;
  display: flex;
  justify-content: space-around;
}
</style>
