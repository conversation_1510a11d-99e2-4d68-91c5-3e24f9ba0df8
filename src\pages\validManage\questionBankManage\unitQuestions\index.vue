<template>
  <div class="contain">
    <div class="search-column">
      <div class="search-column__item">
        <el-select
          v-model="condition.type"
          clearable
          placeholder="请选择搜索类型"
        >
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search" @clear="search">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="search" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select
          v-model="condition.questionType"
          clearable
          placeholder="请选择题型"
          @clear="search"
          @change="search"
        >
          <el-option v-for="item in questionTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select
          v-model="condition.difficulty"
          clearable
          placeholder="请选择难易程度"
          @clear="search"
          @change="search"
        >
          <el-option v-for="item in difficultyList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>

    <div v-if="show" class="qDiv">
      <QuestionDiv v-for="(item,index) in records" :key="index" :data="item" />
    </div>
    <div v-else class="tips">暂无数据</div>

    <Pagination
      v-show="show"
      :total="total"
      :page="pager.page"
      @pagination="pagination"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import QuestionDiv from '@/components/questionDiv'
import { getUnitList } from '@/api/questions'
export default {
  components: { Pagination, QuestionDiv },
  data() {
    return {
      show: false,
      condition: {
        difficulty: null,
        keyword: null,
        questionType: null,
        type: null
      },
      typeList: [
        { label: '题干/病例', value: 1 },
        { label: '考点关键字', value: 2 },
        { label: '所属单位', value: 3 },
        { label: '创建人', value: 4 }
      ],
      questionTypeList: [
        { label: 'A1题型', value: 1 },
        { label: 'A2题型', value: 2 },
        { label: 'A3题型', value: 3 },
        { label: 'A4题型', value: 4 },
        { label: 'B1题型', value: 5 },
        { label: 'X题型', value: 6 },
        { label: '判断题型', value: 7 },
        { label: '选择填空题型', value: 8 }
      ],
      difficultyList: [
        { label: '简单', value: 1 },
        { label: '普通', value: 2 },
        { label: '困难', value: 3 }
      ],
      pager: { page: 1, pageSize: 10 },
      total: 0,
      records: []
    }
  },
  created() {
    this.getUnitList()
  },
  methods: {
    getUnitList() {
      getUnitList({ pager: this.pager, condition: this.condition }).then(res => {
        if (res.total) {
          if (res.total !== 0) {
            this.total = res.total
            this.records = res.records
            this.show = true
          } else {
            this.show = false
          }
        } else {
          this.show = false
        }
      })
    },
    search() {
      this.pager.page = 1
      this.getUnitList()
    },
    pagination(pager) {
      this.pager = pager
      this.getUnitList()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-column{
  margin: 10px 0 0 30px;
}
.qDiv{
  height: calc(100vh - 260px);
  overflow-y:scroll;
  border-bottom: 1px solid #DFDFDF;
  margin-left: 10px;
}
.tips{
  font-size: 26px;
  color: #999;
  text-align: center;
  margin-top: 50px;
}
</style>
