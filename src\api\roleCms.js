import request from '@/utils/request'

export default {
  // 角色添加
  roleCreate(data) {
    return request({
      url: `/role/add`,
      method: 'post',
      data
    })
  },
  // 角色删除
  roleDel(params) {
    return request({
      url: `/role/del/${params}`,
      method: 'post'
    })
  },
  // 角色编辑
  roleEdit(data) {
    return request({
      url: `/role/edit`,
      method: 'post',
      data
    })
  },
  // 角色列表
  list(data) {
    return request({
      url: `/role/list`,
      method: 'post',
      data
    })
  },
  // 获取权限列表(角色赋权用)
  roleList(data) {
    return request({
      url: `/permission/role/permission/list`,
      method: 'post'
    })
  },
  // 角色信息
  roleInfo(params) {
    return request({
      url: `/role/detail/${params}`,
      method: 'get'
    })
  },
  // 查看用户
  roleUser(data) {
    return request({
      url: `/role/check/user`,
      method: 'post',
      data
    })
  },
  // 移除拥有当前角色的用户
  roleRemove(data) {
    return request({
      url: `/role/remove/${data}`,
      method: 'post'
    })
  },
  // 设置角色
  roleSetUser(data) {
    return request({
      url: `/role/set/user/${data.userId}/${data.roleId}`,
      method: 'post'
    })
  },
  // 获取当前用户角色
  roleGetUser(params) {
    return request({
      url: `/role/get/role/${params}`,
      method: 'get'
    })
  },
  // 停用当前用户角色
  roleShut(data) {
    return request({
      url: `/role/shut/role/${data}`,
      method: 'post'
    })
  },
  // 角色选择的权限菜单
  rolePickList(data) {
    return request({
      url: `/role/pick/list/${data}`,
      method: 'post'
    })
  },
  // 用户选择角色列表
  roleUserList(data) {
    return request({
      url: `/role/user/list`,
      method: 'post'
    })
  }
}
