<template>
  <div class="trend">
    <h3>活动趋势
      <el-tooltip class="item" effect="dark" content="活动趋势呈现此所选时间段内，曝光次数、点击次数变化趋势" placement="top-start">
        <i class="el-icon-warning" />
      </el-tooltip>
    </h3>
    <div id="chart" class="chart" />
  </div>
</template>

<script>
var echarts = require('echarts/lib/echarts')
require('echarts/lib/chart/line')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/legend')
import { GridComponent, ToolboxComponent } from 'echarts/components'
echarts.use([GridComponent, ToolboxComponent])

export default {
  name: 'Trend',
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(v) {
        this.setOptions(v)
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(document.getElementById('chart'))
      this.setOptions(this.data)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    })
  },
  methods: {
    setOptions(data) {
      this.myChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        color: ['#5b8ff9', '#61d6a0', '#5d7197', '#eeba21'],
        legend: {
          data: ['曝光次数', '点击次数', '视频答题次数', '视频播放次数'],
          top: '4%'
        },
        grid: {
          top: '18%',
          left: '3%',
          right: '5%',
          bottom: '6%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          boundaryGap: true,
          alignWithLabel: true,
          data: data.xAxisData
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        series: [
          {
            name: '曝光次数',
            type: 'line',
            symbol: 'circle',
            data: data.exposure || []
          },
          {
            name: '点击次数',
            type: 'line',
            symbol: 'circle',
            data: data.hit || []
          },
          {
            name: '视频答题次数',
            type: 'line',
            symbol: 'circle',
            data: data.videoAnswer || []
          },
          {
            name: '视频播放次数',
            symbol: 'circle',
            type: 'line',
            data: data.videoPlay || []
          }
        ]
      })
    },
    beforeDestroy() {
      if (!this.myChart) {
        return
      }
      this.myChart.dispose()
      this.myChart = null
    }
  }
}
</script>

<style scoped lang="scss">
.trend {
  h3 {
    margin: 0;
    background: #eee;
    line-height: 50px;
    padding-left: 20px;
    i{
      cursor: pointer;
      color:#3bb19c;
    }
  }
  .chart {
    height: 400px;
  }
}
</style>
