import request from '@/utils/request'

/**
 * 人员详情
 */

export default {
  // 查询人员详情信息
  list(param) {
    return request({
      url: '/data/statistics/crew/detail',
      method: 'post',
      data: param
    })
  },

  // 人员详情 导出
  export(param) {
    return request({
      url: '/export/cmsdata/crew/detail',
      method: 'post',
      data: param
    })
  },
  // 获取扩充状态
  getMultipleStatus() {
    return request({
      url: '/multiple/enlarge/status',
      method: 'post'
    })
  },
  // 获取单个省的倍数
  getMultiple(id) {
    return request({
      url: `/multiple/area/${id}`,
      method: 'get'
    })
  }
}
