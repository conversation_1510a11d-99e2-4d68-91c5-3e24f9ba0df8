import request from '@/utils/request'

// 总体分析-概况
export function totalAnalysisSurvey(params) {
  return request({
    url: '/promoteArticle/totalAnalysisSurvey',
    method: 'post',
    data: params
  })
}

// 文章分析列表
export function articleAnalysisList(params) {
  return request({
    url: '/promoteArticle/articleAnalysisList',
    method: 'post',
    data: params
  })
}

// 导出文章分析列表
export function articleAnalysisListExport(params) {
  return request({
    url: '/promoteArticle/articleAnalysisListExport',
    method: 'post',
    data: params
  })
}

// 创作者/推广员分析分页列表
export function userAnalysis(params) {
  return request({
    url: '/promoteArticle/userAnalysis/' + params.condition.userType,
    method: 'post',
    data: params
  })
}

// 创作者/推广员分析分页列表导出
export function userAnalysisExport(params) {
  return request({
    url: '/promoteArticle/userAnalysisExport/' + params.userType,
    method: 'post',
    data: params
  })
}

// 拜访列表
export function visitList(params) {
  return request({
    url: '/visitRecord/pageList',
    method: 'post',
    data: params
  })
}

// 拜访列表导出
export function visitListExport(params) {
  return request({
    url: '/visitRecord/visitRecordExport',
    method: 'post',
    data: params
  })
}

// 拜访详情
export function visitDetail(id) {
  return request({
    url: '/visitRecord/detail/' + id,
    method: 'post'
  })
}

// 拜访列表
export function serviceList(params) {
  return request({
    url: '/promoteArticle/serviceProviderList',
    method: 'post',
    data: params
  })
}

// 拜访列表导出
export function serviceListExport(params) {
  return request({
    url: '/promoteArticle/serviceProviderExport',
    method: 'post',
    data: params
  })
}

// 根据名称/手机号查询用户名称和手机号
export function listUserForSelect(params) {
  return request({
    url: '/user/listUserForSelect',
    method: 'post',
    data: params
  })
}

// 文章/视频详情-概况+文章预览url
export function articleDetailSurvey(params) {
  return request({
    url: '/promoteArticle/articleDetailSurvey/' + params.articleId,
    method: 'post',
    data: params
  })
}

// 文章/视频详情-点击详情分页列表
export function articleClickDetailList(params) {
  return request({
    url: '/promoteArticle/articleClickDetailList',
    method: 'post',
    data: params
  })
}

// 文章/视频详情-点击详情分页列表导出
export function articleClickDetailListExport(params) {
  return request({
    url: '/promoteArticle/articleClickDetailListExport/' + params.articleId,
    method: 'post',
    data: params
  })
}

// 创作者/推广员详情-创作者/推广员概况
export function userAnalysisDetailSurvey(params) {
  return request({
    url: '/promoteArticle/userAnalysisDetailSurvey/' + params.userType + '/' + params.userId,
    method: 'post',
    data: params
  })
}

// 创作者/推广员详情里面的文章/视频列表分页查询
export function userAnalysisDetailList(params) {
  return request({
    url: '/promoteArticle/userAnalysisDetailList',
    method: 'post',
    data: params
  })
}

// 创作者/推广员详情里面的文章/视频列表导出
export function userAnalysisDetailListExport(params) {
  return request({
    url: '/promoteArticle/userAnalysisDetailListExport',
    method: 'post',
    data: params
  })
}

// 服务商概括下的文章/视频列表分页查询
export function serviceArticleList(params) {
  return request({
    url: '/promoteArticle/serviceProviderArticleList',
    method: 'post',
    data: params
  })
}

// 服务商概括下的文章/视频列表导出
export function serviceArticleListExport(params) {
  return request({
    url: '/promoteArticle/serviceProviderArticleListExport',
    method: 'post',
    data: params
  })
}

// 服务商概括下的拜访列表
export function serviceVisitList(params) {
  return request({
    url: '/visitRecord/serviceProviderVisitList',
    method: 'post',
    data: params
  })
}

// 服务商概括下的拜访列表导出
export function serviceVisitListExport(params) {
  return request({
    url: '/visitRecord/serviceProviderVisitListExport',
    method: 'post',
    data: params
  })
}

// 推广分析列表
export function promotionAnalysisList(params) {
  return request({
    url: '/promoteArticle/promotionAnalysisList',
    method: 'post',
    data: params
  })
}

// 推广分析导出
export function promotionAnalysisExport(params) {
  return request({
    url: '/promoteArticle/promotionAnalysisExport',
    method: 'post',
    data: params
  })
}

// 推广分析详情
export function promotionAnalysisDetailSurvey(articleId) {
  return request({
    url: `/promoteArticle/promotionAnalysisDetailSurvey/${articleId}`,
    method: 'post'
  })
}

