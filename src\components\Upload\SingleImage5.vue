<template>
  <el-upload
    ref="uploadFile"
    :style="cssVars"
    list-type="picture-card"
    :multiple="true"
    :action="uploadFileApi"
    :data="uploadData"
    :file-list="fileList"
    :before-upload="beforeUpload"
    :on-success="uploadSuccess"
    :on-remove="removePic"
    :disabled="disabled"
  >
    <span slot="default" class="uploadStyle">
      <i class="el-icon-plus" />
      <span>{{ tips }}</span>
    </span>
  </el-upload>
</template>

<script>
// readme
// 图片上传组件
// 封装特点：上传一张图后自动隐藏上传框
// tips为图标下方文字
// 监听函数picIdChange可获取上传图片的id
import { preUploadApi, uploadFileApi } from '@/api/biz'
import { getToken } from '@/utils/auth'

export default {
  props: {
    tips: {
      type: String,
      default: '点击上传'
    },
    picIds: {
      type: Array,
      default: () => []
    },
    urls: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    width: {
      type: String,
      default: '148px'
    },
    height: {
      type: String,
      default: '148px'
    }
  },
  data() {
    return {
      uploadFileApi,
      uploadData: {
        data: ''
      },
      uploadHeaders: {
        token: getToken()
      },
      ids: this.picIds,
      fileList: []
    }
  },
  computed: {
    cssVars() {
      return {
        '--width': this.width,
        '--height': this.height
      }
    }
  },
  watch: {
    urls: {
      handler(value) {
        if (value.length !== 0) {
          this.fileList = value.map((v, i) => {
            return { url: v, uid: this.picIds[i] }
          })
        }
      },
      immediate: true
    },
    picIds: {
      handler(value) {
        this.ids = value
      },
      immediate: true
    }
  },
  methods: {
    uploadSuccess(val, file, fileList) {
      this.fileList = fileList
      this.ids.push(val.data.id)
      this.$emit('update:picIds', this.ids)
    },
    removePic(val) {
      const index = this.fileList.findIndex(item => item.uid === val.uid)
      this.ids.splice(index, 1)
      this.fileList.splice(index, 1)
      this.$emit('update:picIds', this.ids)
    },
    async beforeUpload(val) {
      const JPEG = val.type === 'image/jpeg'
      const JPG = val.type === 'image/jpg'
      const PNG = val.type === 'image/png'
      if (!JPG && !PNG && !JPEG) {
        const uid = val.uid // 关键作用代码，去除文件列表失败文件
        const index = this.$refs.uploadFile.uploadFiles.findIndex(item => item.uid === uid) // 关键作用代码，去除文件列表失败文件（uploadFiles为el-upload中的ref值）
        this.$refs.uploadFile.uploadFiles.splice(index, 1) // 关键作用代码，去除文件列表失败文件
        this.$message.error('上传图片只能是 JPG、JPEG或者PNG 格式!')
        return false
      } else {
        const param = {
          filename: val.name,
          size: val.size,
          type: val.type
        }
        const res = await preUploadApi(param)
        this.uploadData.data = res
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload, ::v-deep .el-upload-list__item{
    width: var(--width);
    height: var(--height)
  }
.uploadStyle{
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  span{
    color: #8c939d;
    line-height: 2;
  }
}
</style>
