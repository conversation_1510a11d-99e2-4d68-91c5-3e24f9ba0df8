<template>
  <div class="container">
    <el-form
      ref="formQuestion"
      :model="formQuestion"
      label-width="100px"
      :rules="rules"
      label-position="right"
    >
      <el-form-item :label="titleLabel" prop="title">
        <el-input
          v-model="formQuestion.title"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          :placeholder="titlePlaceholder"
        />
        <!-- <ExamInput ref="examInput" :input-content="formQuestion.title" class="imgUpdate" :value.sync="formQuestion.title" /> -->
      </el-form-item>

      <!-- 多选按钮组 -->
      <el-form-item v-if="questionType==6" label="选项:" prop="optionsList" style="margin-bottom: 0">
        <el-row v-for="(item, idx) in formQuestion.optionsList" :key="idx" type="flex" justify="space-between" align="middle" style="margin-bottom: 10px">
          <el-col :span="1">
            <span>{{ item.index }}.</span>
          </el-col>
          <el-col :span="21">
            <!-- <ExamInput ref="examInput" :data="item.content" :value.sync="item.content" /> -->
            <el-input v-model="item.content" type="text" placeholder="请输入选项内容" @input="change($event)" />
            <!-- <el-input type="flie"></el-input> -->
          </el-col>
          <el-col :span="1" style="padding-left: 10px">
            <el-checkbox v-model="item.isAnswer" @change="checkChang()">{{ '' }}</el-checkbox>
          </el-col>
          <el-col :span="1">
            <el-button type="text" :disabled="deleteBtn" class="deleteBtn" @click="btnClick(idx)"><i class="el-icon-remove-outline" /></el-button>
          </el-col>
        </el-row>
      </el-form-item>

      <!-- 单选按钮组 -->
      <el-form-item v-else label="选项:" prop="optionsList" style="margin-bottom: 0">
        <!-- <el-radio-group v-model="radio"> -->
        <el-row v-for="(item, idx) in formQuestion.optionsList" :id="'a'+idx" :key="idx" type="flex" justify="space-between" align="middle" style="margin-bottom: 10px">
          <el-col :span="1">
            <span>{{ item.index }}.</span>
          </el-col>
          <el-col :span="21" style="position: relative">
            <el-input v-model="item.content" type="text" placeholder="请输入选项内容" @input="change($event)" />
            <!-- <ExamInput ref="examInput" :idx="idx" :input-content="formQuestion.optionsList[idx].content" class="imgUpdate" @abc="abc" /> -->
          </el-col>
          <el-col :span="1" style="padding-left: 10px">
            <el-radio v-model="radio" :label="idx" @change="radioChange()">{{ '' }}</el-radio>
          </el-col>
          <el-col :span="1">
            <el-button type="text" :disabled="deleteBtn" class="deleteBtn" @click="btnClick(idx)"><i class="el-icon-remove-outline" /></el-button>
          </el-col>
        </el-row>
        <!-- </el-radio-group> -->
      </el-form-item>
      <!-- 添加选项按钮 -->
      <el-button plain class="add-btn" :disabled="addBtn" @click="addOption">
        <i class="el-icon-plus" /> 添加选项
      </el-button>

      <el-form-item label="考点关键字:">
        <el-input
          v-model="formQuestion.points"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          :maxlength="300"
          placeholder="请输入考点关键字"
        />
        <!-- <ExamInput ref="examInput" :input-content="formQuestion.points" class="imgUpdate" :value.sync="formQuestion.points" /> -->
      </el-form-item>

      <el-form-item label="解析:">
        <el-input
          v-model="formQuestion.desc"
          type="textarea"
          :resize="none"
          :autosize="autosize"
          :maxlength="3000"
          placeholder="请输入解析"
        />
        <!-- <ExamInput ref="examInput" :input-content="formQuestion.desc" class="imgUpdate" :value.sync="formQuestion.desc" /> -->
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// import ExamInput from './examInput'
export default {
  name: 'Aone',
  components: {
    // ExamInput
  },
  props: {
    aData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isType: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      // examInputKey: 0,
      titleLabel: '题干：',
      titlePlaceholder: '请输入题干',
      title: 'A1', // 控制 题干与病例 输入框的显示隐藏   A1 题干  A2 病例
      none: 'none', // 控制快速录入输入框不能被缩放
      addBtn: false, // 控制添加选项是否禁用
      deleteBtn: false, // 删除选项是否禁用
      questionType: 1,
      checkes: false,
      autosize: {
        minRows: 6,
        maxRows: 8
      },
      radio: '', // 单选框组 存储选中的值
      formQuestion: {
        title: '',
        optionList: [
          {
            index: '',
            content: '',
            isAnswer: null
          }
        ],
        points: '',
        desc: ''
      },
      rules: {
        title: [{ required: true, message: '请输入题干', trigger: 'input' }],
        optionsList: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    aData: {
      handler(newval, oldvar) {
        if (JSON.stringify(newval) !== '{}') {
          if ([1, 6].includes(this.isType)) {
            this.titleLabel = '题干：'
            this.titlePlaceholder = '请输入题干'
          } else {
            this.titleLabel = '病例：'
            this.titlePlaceholder = '请输入病例'
          }
          this.questionType = this.isType
          this.formQuestion.title = newval.title
          this.formQuestion.optionsList = newval.optionsList
          this.formQuestion.points = newval.points
          this.formQuestion.desc = newval.desc
          if ([1, 2].includes(this.questionType)) {
            this.formQuestion.optionsList.forEach((item, index) => {
              if (item.isAnswer === 1) {
                this.radio = index
              }
            })
          } else {
            this.formQuestion.optionsList.forEach((item, index) => {
              if (item.isAnswer === 1) {
                item.isAnswer = true
              }
            })
          }
        }
      }
    },
    isType: {
      handler(newval, oldvar) {
        if (newval && this.questionType !== newval) {
          this.initia()
        }
        // 刷新组件
        if (this.isType === 2) {
          this.titleLabel = '病例：'
          this.titlePlaceholder = '请输入病例'
          this.questionType = newval
        } else if (this.isType === 6) {
          this.titleLabel = '题干：'
          this.titlePlaceholder = '请输入题干'
          this.questionType = newval
        } else {
          this.titleLabel = '题干：'
          this.titlePlaceholder = '请输入题干'
          this.questionType = newval
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    const arr = [
      { index: 'A', content: '', isAnswer: null },
      { index: 'B', content: '', isAnswer: null },
      { index: 'C', content: '', isAnswer: null },
      { index: 'D', content: '', isAnswer: null },
      { index: 'E', content: '', isAnswer: null }
    ]
    this.formQuestion.optionsList = arr
  },
  methods: {
    abc(v) {
      const [index, value] = v
      this.formQuestion.optionsList[index].content = value
      this.$forceUpdate()
    },
    change() {
      this.$forceUpdate()
    },
    radioChange() {
      this.$forceUpdate()
    },
    checkChang() {
      this.$forceUpdate()
    },
    // 添加选项
    addOption() {
      if (this.formQuestion.optionsList.length > 9) {
        this.$message('最多10个选项')
      } else {
        const obj = {
          index: String.fromCharCode(this.formQuestion.optionsList.length + 65),
          content: '',
          isAnswer: null
        }
        this.formQuestion.optionsList.push(obj)
      }
      this.$mount('div.container')
    },
    // 删除选项
    btnClick(idx) {
      if (this.formQuestion.optionsList.length < 3) return this.$message('最少2个选项')
      this.formQuestion.optionsList.splice(idx, 1)
      if (this.radio === idx) {
        this.radio = ''
      }
      for (const key in this.formQuestion.optionsList) {
        this.formQuestion.optionsList[key].index = this.charCode(Number(key))
      }
      this.$mount('div.container')
    },
    charCode(val) {
      return String.fromCharCode(val + 65)
    },
    // 数据初始化
    initia() {
      this.radio = ''
      this.formQuestion = {
        title: '',
        optionList: [],
        points: '',
        desc: ''
      }
      const arr = [
        { index: 'A', content: '', isAnswer: null },
        { index: 'B', content: '', isAnswer: null },
        { index: 'C', content: '', isAnswer: null },
        { index: 'D', content: '', isAnswer: null },
        { index: 'E', content: '', isAnswer: null }
      ]
      this.formQuestion.optionsList = arr
    },
    onImgClick() {
      this.$refs.examInput.update()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 60%;
  margin: 20px auto;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    * {
      margin: 0 10px 20px 0;
    }
    .deleteBtn {
      margin-bottom: 0px;
    }
  }
  ol {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: upper-latin;
    li {
      list-style-position: outside;
    }
  }
  .add-btn {
    width: 88%;
    margin: 0px 10px 10px 100px;
    border: 1px dashed #dcdfe6;
  }
  .add-btn:hover {
    border: 1px dashed #409eff;
  }
}
::v-deep .el-button--text {
  padding-left: 4px;
}
::v-deep .el-radio-group {
  display: block;
  font-size: 16px;
  line-height: 16px;
}
::v-deep .el-checkbox-button__inner {
  padding: 12px 12px;
}
.container .li:last-child {
  margin-bottom: -20px;
}
.problem {
  border: 1px dashed #dcdfe6;
}
.imgUpdate{
  position: absolute !important;
  right: 5px;
  top: 5px;
}
</style>
