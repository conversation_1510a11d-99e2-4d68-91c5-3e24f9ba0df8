<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.name" maxlength="30" placeholder="请输入热门关键字搜索" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div style="padding-top: 15px">
        <div class="search-column__item">
          <el-button type="primary" @click="dels">批量删除</el-button>
        </div>
        <div class="search-column__item fr">
          <el-button type="primary" @click="add()">新增</el-button>
        </div>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="listOrder" slot-scope="{row}">
        <el-input v-model="row.listOrder" type="number" max="9999999" min="0" style="width: 100px" @change="changeOrder(row)" />
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.state|statusFlt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button size="mini" type="text" @click="edit(row)">编辑</el-button>
        <el-button v-if="row.state == 0" size="mini" type="text" @click="offShelf(row, '设为无效')">设为无效</el-button>
        <el-button v-else size="mini" type="text" @click="onShelf(row, '设为有效')">设为有效</el-button>
        <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <dialogEdit :show.sync="editData.show" :data="editData.data" @handleSave="handleSave" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/appManage/hotwords'
import dialogEdit from './components/dialogEdit.vue'

const columns = [
  { props: { type: 'selection', align: 'center', width: '55' }},
  { props: { label: '热门词', align: 'center', prop: 'name' }},
  {
    props: { label: '排序', align: 'center', width: '160' },
    slot: 'listOrder'
  },
  {
    props: { label: '状态', align: 'center', width: '90' },
    slot: 'status'
  },
  {
    props: { align: 'center', label: '操作', width: '230' },
    slot: 'actions'
  }
]

export default {
  name: 'AppHotwords',
  filters: {
    statusFlt(v) {
      const arr = ['有效', '无效']
      return arr[v]
    }
  },
  components: {
    dialogEdit
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      condition: {
        name: ''
      },
      mainKey: 'hotWordsId',
      mainName: 'name',
      typeList: [
        { value: null, name: '全部' },
        { value: 1, name: '课程' },
        { value: 2, name: '视频' }
      ],
      editData: {
        show: false,
        type: 'create',
        data: {
          name: '',
          listOrder: '',
          state: 0
        }
      }
    }
  },
  methods: {
    add() {
      this.editData.type = 'create'
      this.editData.data = {
        name: '',
        listOrder: '',
        state: 0
      }
      this.editData.show = true
    },
    edit(row) {
      this.editData.type = 'edit'
      this.editData.data = row
      this.editData.show = true
    },
    handleSave(data) {
      this.request[this.editData.type](data).then(res => {
        this.$message.success(`${this.editData.type === 'create' ? '新增' : '编辑'}成功`)
        this.getList()
        this.editData.show = false
      })
    },
    // put on the shelf
    onShelf(row, title = '上架') {
      const data = { status: 1 }
      data[this.mainKey] = row[this.mainKey]
      this.request.putaway(data).then((res) => {
        this.getList()
        this.$message.success(title + '成功')
      })
    },
    // off shelf
    offShelf(row, title = '下架') {
      const data = { status: 0 }
      data[this.mainKey] = row[this.mainKey]
      this.confirm(title, row, 'putaway', data)
    },
    // delete row
    deleteRow(row) {
      const data = {
        hotWordsIds: []
      }
      data.hotWordsIds.push(row[this.mainKey])
      this.confirm('删除', row, 'del', data)
    },
    // 批量删除
    dels() {
      if (this.selection.length < 1) {
        return
      }
      const data = {
        hotWordsIds: this.selection.map(e => e.hotWordsId)
      }
      this.$confirm(`是否批量删除` + this.selection.length + '条数据？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request.del(data).then(() => {
          this.getList()
          this.$message.success(`删除成功`)
        })
      }).catch(console.log)
    },
    // 修改排序
    changeOrder(row) {
      if (row.listOrder > 9999999) {
        row.listOrder = 9999999
      }
      if (row.listOrder < 0) {
        row.listOrder = 0
      }
      this.request.setListOrder({ hotWordsId: row.hotWordsId, listOrder: row.listOrder }).then((res) => {
        this.getList()
        this.$message.success('修改排序成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button.title{
  white-space: normal;
}
</style>
