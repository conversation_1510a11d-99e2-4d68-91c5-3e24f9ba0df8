<template>
  <el-dialog
    title="内容列表"
    :visible.sync="dialogVisible"
    width="50%"
    center
    :before-close="handleClose"
  >
    <el-table
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column label="标题/文字">
        <template slot-scope="scope">
          <span>{{ scope.row.articleType.includes('DOULA') ? scope.row.description : scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in tableColumn"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
        align="center"
      />
      <el-table-column prop="handle" label="操作" width="60" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.isShow"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            @change="handleShow($event, scope.row.id)"
          />
        </template>
      </el-table-column>
    </el-table>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
  </el-dialog>

</template>

<script>
import Pagination from '@/components/Pagination'
import { getMyPageList, editShow } from '@/api/marketing/userPromote'
export default {
  components: {
    Pagination
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      listQuery: {
        condition: {
          userId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      tableData: [],
      tableColumn: [
        { prop: 'orgzName', label: '企业', width: '80' },
        { prop: 'productName', label: '产品', width: '180' },
        { prop: 'author', label: '作者', width: '100' },
        { prop: 'creatorName', label: '发布人', width: '100' },
        { prop: 'releaseTime', label: '发布时间', width: '140' }
      ],
      total: 0
    }
  },
  watch: {
    dialogVisible(v) {
      if (v) {
        this.listQuery.condition.userId = this.id
        this.getList()
      }
    }
  },
  methods: {
    getList() {
      getMyPageList(this.listQuery).then(res => {
        res.records.forEach(v => {
          v.author = `${v.authorName} ${v.academic} ${v.company} ${v.department}`
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
    },
    handleShow(isShow, id) {
      editShow({ isShow, id }).then(() => {
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.input-with-select {
  margin-bottom: 20px;
}
</style>
