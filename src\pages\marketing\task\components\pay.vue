<template>
  <el-dialog
    title="支付"
    :visible.sync="dialogVisible"
    width="420px"
    :before-close="handleClose"
  >
    <div class="item">
      <span>代征平台:</span>
      <el-select v-model="platformId" placeholder="请选择代征平台" size="mini">
        <el-option
          v-for="item in platformList"
          :key="item.desc"
          :label="item.desc"
          :value="item.id"
        />
      </el-select>
    </div>
    <div v-if="platformId === 1" class="item">
      <span>支付凭证:</span>
      <el-upload
        :class="{ hideUpdate: fileList.length >= 1 }"
        :action="uploadFileApi"
        :data="uploadData"
        :limit="1"
        :file-list="fileList"
        :before-upload="handleBeforeUpload"
        :on-success="handleSuccess"
        :on-remove="handleRemove"
      >
        <el-button class="el-icon-plus" size="mini" type="primary">上传</el-button>
      </el-upload>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>

    <el-dialog
      title="提示"
      :visible.sync="errorDialogVisible"
      width="400px"
      top="200px"
      center
      append-to-body
      :before-close="errorHandleClose"
    >
      <div>
        <p>支付请确认银行卡信息</p>
        <el-form
          ref="form"
          label-width="80px"
          :model="bankInfo"
          :rules="rules"
        >
          <el-form-item
            label="银行卡号"
            prop="bankCardNo"
          >
            <el-input v-model="bankInfo.bankCard" @blur="getbankinfo" />
          </el-form-item>
          <el-form-item
            label="卡类型"
            prop="bankName"
          >
            <el-input v-model="bankInfo.bankName" />
          </el-form-item>
          <el-form-item
            label="开户支行"
            prop="bankSubName"
          >
            <el-input v-model="bankInfo.bankSubName" />
          </el-form-item>
          <el-form-item
            label="预留手机"
            prop="bankBindPhone"
          >
            <el-input v-model="bankInfo.bankBindPhone" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="errorHandleClose">取消</el-button>
        <el-button
          type="primary"
          @click="errorHandleClose('submit')"
        >确定</el-button>
      </div>
    </el-dialog>

  </el-dialog>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { uploadCertificate, uploadCertificateVisit, uploadCertificateDoula } from '@/api/marketing/taskExecute'
import { platformList } from '@/api/marketing/taskPromote'
import { getBankBin } from 'bankcardinfo'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    platformIds: {
      type: Array,
      default: () => []
    },
    oldBankInfo: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: 'creator'
    }
  },
  data() {
    return {
      uploadFileApi,
      certId: null,
      fileList: [],
      uploadData: { data: '' },
      platformId: null,
      platformList: [],
      errorDialogVisible: false,
      bankInfo: {
        bankCard: '',
        bankName: '',
        bankSubName: '',
        bankBindPhone: ''
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) this.bankInfo = this.oldBankInfo
      platformList().then(res => {
      // 筛选可用代征平台, 天玑(id=1)始终可用
        this.platformIds.push(1)
        res = res.filter(item => {
          return this.platformIds.includes(item.id)
        })
        this.platformId = null
        this.platformList = res
      })
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    async handleBeforeUpload(val) {
      const param = {
        filename: val.name,
        size: val.size,
        type: 'adv'
      }
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        this.certId = res.data.id
        this.fileList = [
          {
            id: res.data.id,
            url: res.data.url,
            name: file.name
          }
        ]
      }
    },
    handleRemove() {
      this.certId = ''
      this.fileList = []
    },
    getbankinfo() {
      getBankBin(this.bankInfo.bankCard).then(res => {
        this.bankInfo.bankName = res.bankName + res.cardTypeName
      }).catch(() => { this.$message.error('银行卡号错误,请重新输入') })
    },
    confirm(type) {
      let params = { certId: this.certId, platformId: this.platformId }
      params[this.type === 'visit' ? 'visitUserId' : 'taskUserId'] = this.id
      if (this.bankInfo.bankCard !== '') {
        params = {
          ...params,
          ...this.bankInfo
        }
        if (type !== 'err') {
          this.errorDialogVisible = true
          return
        }
      }
      const certificate = this.type === 'creator' ? uploadCertificate : (this.type === 'visit' ? uploadCertificateVisit : uploadCertificateDoula)
      certificate(params).then(() => {
        this.handleClose()
        this.$parent.getExecuteList()
      }).catch(() => {
        this.errorDialogVisible = true
      })
    },
    errorHandleClose(type) {
      if (type === 'submit') {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.errorDialogVisible = false
            this.confirm('err')
          }
        })
      } else {
        this.errorDialogVisible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 45px 24px;
    color: #333;
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    span {
      margin-right: 10px;
    }
  }
}
.hideUpdate {
  ::v-deep .el-upload--text {
    display: none;
  }
}
</style>
