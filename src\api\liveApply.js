import request from '@/utils/request'

// 讲师-审核-列表
export function leturList(query) {
  return request({
    url: '/lecturer/findApplyPage',
    method: 'post',
    data: query
  })
}

// 讲师-审核-详情
export function leturDetail(userId) {
  return request({
    url: '/lecturer/lecturerApplyDetailed?userId=' + userId,
    method: 'get'
  })
}

// 讲师-审核历史-列表
export function leturAuditRecordList(query) {
  return request({
    url: '/lecturer/findAuditRecordPage',
    method: 'post',
    data: query
  })
}

// 讲师-审核-通过/不通过
export function leturApply(query) {
  return request({
    url: '/lecturer/auditorLecturerApply',
    method: 'post',
    data: query
  })
}

// 讲师列表
export function getLeturList(query) {
  return request({
    url: '/lecturer/findLecturerPage',
    method: 'post',
    data: query
  })
}

// 讲师详情
export function getLeturDetail(userId) {
  return request({
    url: '/lecturer/lecturerDetailed?userId=' + userId,
    method: 'get'
  })
}

// 扩展方-审核-列表
export function extendList(query) {
  return request({
    url: '/extender/findApplyPage',
    method: 'post',
    data: query
  })
}

// 扩展方-审核-详情
export function extendDetail(applyId) {
  return request({
    url: '/extender/extenderApplyDetailed?applyId=' + applyId,
    method: 'get'
  })
}

// 扩展方-审核-通过/不通过
export function extendApply(query) {
  return request({
    url: '/extender/auditorExtenderApply',
    method: 'post',
    data: query
  })
}

// 扩展方-审核通过-添加单位
export function extendAddOrg(query) {
  return request({
    url: '/extender/auditorApplyAndAddOrg',
    method: 'post',
    data: query
  })
}

// 扩展方-列表
export function getExtenderList(query) {
  return request({
    url: '/extender/findExtenderPage',
    method: 'post',
    data: query
  })
}

// 扩展方-审核历史-列表
export function extendAuditRecordList(query) {
  return request({
    url: '/extender/findAuditRecordPage',
    method: 'post',
    data: query
  })
}

// 查看扩展方明细
export function getExtenderDetailed(id) {
  return request({
    url: '/extender/getExtenderDetailed?id=' + id,
    method: 'get'
  })
}
