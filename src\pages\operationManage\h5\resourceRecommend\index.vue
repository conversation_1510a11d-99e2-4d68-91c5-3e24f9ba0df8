<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.keyword" maxlength="30" placeholder="请输入视频名称或课程名称进行搜索" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.type" filterable placeholder="请选择类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div style="padding-top: 15px">
        <div class="search-column__item">
          <el-button type="primary" @click="dels">批量删除</el-button>
        </div>
        <div class="search-column__item fr">
          <el-button type="primary" @click="add">新增</el-button>
        </div>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="type" slot-scope="{row}">
        <span v-if="row.type == 1">课程</span>
        <span v-if="row.type == 2">视频</span>
      </template>
      <template slot="listOrder" slot-scope="{row}">
        <el-input v-model="row.listOrder" min="0" type="number" max="9999999" style="width: 80px" @change="changeOrder(row)" />
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.status|statusFlt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button size="mini" type="text" @click="edit(row)">编辑</el-button>
        <el-button v-if="row.status == 1" size="mini" type="text" @click="offShelf(row, '设为无效')">设为无效</el-button>
        <el-button v-else size="mini" type="text" @click="onShelf(row, '设为有效')">设为有效</el-button>
        <el-button size="mini" type="text" @click="deleteRow(row)">删除</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <dialogEdit :show.sync="editShow" :rid="rid" :data="edata" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/config/resourceRecommend'
import dialogEdit from './components/dialogEdit.vue'

const columns = [
  { props: { type: 'selection', width: '55' }},
  { props: { label: '推荐内容', align: 'center', prop: 'resourceName' }},
  {
    props: { label: '推荐类型', align: 'center', width: '150' },
    slot: 'type'
  },
  { props: { label: '章节数', align: 'center', prop: 'chapterNum', width: '80' }},
  {
    props: { label: '排序', align: 'center', width: '120' },
    slot: 'listOrder'
  },
  {
    props: { label: '状态', align: 'center', width: '90' },
    slot: 'status'
  },
  {
    props: { align: 'center', label: '操作', width: '230' },
    slot: 'actions'
  }
]

export default {
  name: 'Config',
  filters: {
    statusFlt(v) {
      const arr = ['无效', '有效', '无效']
      return arr[v]
    }
  },
  components: {
    dialogEdit
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      condition: {
        keyword: ''
      },
      conditionWatch: {
        type: null
      },
      mainKey: 'id',
      mainName: 'resourceName',
      typeList: [
        { value: null, name: '全部' },
        { value: 1, name: '课程' },
        { value: 2, name: '视频' }
      ],
      editShow: false,
      rid: '',
      edata: {
        id: '',
        type: 1
      }
    }
  },
  watch: {
    editShow(v) {
      if (!v) {
        this.getList()
      }
    }
  },
  methods: {
    // 新增
    add() {
      this.editShow = true
      this.rid = ''
      this.edata = {
        id: '',
        type: 1
      }
    },
    // 新增
    edit(row) {
      this.editShow = true
      this.rid = row.id
      this.edata = {
        id: row.resourceId,
        type: row.type
      }
    },
    // delete row
    deleteRow(row) {
      this.confirm('删除', row, 'del', row.id)
    },
    // 批量删除
    dels() {
      if (this.selection.length < 1) {
        return
      }
      this.$confirm(`是否批量删除` + this.selection.length + '条数据？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.request.del(this.selection.map(e => e.id).toString(',')).then(() => {
          this.getList()
          this.$message.success(`删除成功`)
        })
      }).catch(console.log)
    },
    // put on the shelf
    onShelf(row, title = '上架') {
      const data = { status: 1 }
      data[this.mainKey] = row[this.mainKey]
      this.request.putaway(data).then((res) => {
        this.getList()
        this.$message.success(title + '成功')
      })
    },
    // off shelf
    offShelf(row, title = '下架') {
      const data = { status: 0 }
      data[this.mainKey] = row[this.mainKey]
      this.confirm(title, row, 'putaway', data)
    },
    changeOrder(row) {
      if (row.listOrder > 9999999) {
        row.listOrder = 9999999
      }
      if (row.listOrder < 0) {
        row.listOrder = 0
      }
      this.request.edit({ id: row.id, listOrder: row.listOrder, resourceId: row.resourceId, type: row.type }).then((res) => {
        this.getList()
        this.$message.success('修改排序成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-button.title{
  white-space: normal;
}
</style>
