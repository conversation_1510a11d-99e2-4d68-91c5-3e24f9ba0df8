import request from '@/utils/request'

export default {
  // 角色添加
  roleCreate(data) {
    return request({
      url: `/saas/role/create`,
      method: 'post',
      data
    })
  },
  // 角色删除
  roleDel(params) {
    return request({
      url: `/saas/role/delete/${params}`,
      method: 'get'
    })
  },
  // 角色编辑
  roleEdit(data) {
    return request({
      url: `/saas/role/edit`,
      method: 'post',
      data
    })
  },
  // 角色列表
  list(data) {
    return request({
      url: `/saas/role/list`,
      method: 'post',
      data
    })
  },
  // 角色添加/编辑菜单列表
  roleMenuList(useScope) {
    return request({
      url: `/saas/role/menu/list?useScope=${useScope}`,
      method: 'get'
    })
  },
  // 角色添加/编辑
  editRoleMenu(data) {
    return request({
      url: `/saas/role/permission/edit`,
      method: 'post',
      data: data
    })
  },
  // 角色选择的权限菜单
  rolePickList(data) {
    return request({
      url: `/saas/role/pick/list/${data}`,
      method: 'post'
    })
  },
  // 角色推送
  rolePush(params) {
    return request({
      url: `/saas/role/push/${params}`,
      method: 'get'
    })
  }
}
