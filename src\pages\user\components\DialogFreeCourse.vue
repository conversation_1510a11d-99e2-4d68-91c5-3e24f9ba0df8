<template>
  <el-dialog
    title="开通免费培训课程"
    :visible="visible"
    fullscreen
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 资源分类区域 - 移到顶部 -->
    <div class="category-section">
      <div class="tree-title">
        <span>资源分类</span>
        <el-button
          v-if="selectedCategoryName"
          type="text"
          size="mini"
          icon="el-icon-close"
          @click="clearCategorySelection"
        >
          取消选择
        </el-button>
        <span v-if="selectedCategoryName" class="selected-category-inline">
          当前分类：{{ selectedCategoryName }}
        </span>
      </div>
      <div class="tree-container">
        <Tree
          ref="freeCourseTree"
          :type="categoryType"
          :platform-type="1"
          :highlight-current="true"
          @nodeClick="handleTreeNodeClick"
        />
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="教程" name="course" />
        <el-tab-pane label="SOP" name="sop" />
      </el-tabs>

      <!-- 统一的双列表布局 -->
      <div class="dual-list-container">
        <!-- 未开通资源列表 -->
        <div class="list-section">
          <div class="list-header">
            <span class="list-title">未开通{{ currentResourceName }}</span>
            <span class="list-count">({{ currentAvailableTotal }})</span>
          </div>

          <!-- 未开通列表的搜索栏 -->
          <div class="search-column">
            <div class="search-column__item">
              <el-input
                v-model="availableQuery.condition.keyword"
                :placeholder="`请输入${currentResourceName}名称`"
                clearable
                @change="searchAvailable"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  style="cursor: pointer;"
                  @click="searchAvailable"
                />
              </el-input>
            </div>
            <div class="search-column__item">
              <el-select
                v-model="availableQuery.condition.memberLevel"
                placeholder="会员等级"
                clearable
                @change="searchAvailable"
              >
                <el-option
                  v-for="level in memberLevelList"
                  :key="'available-' + level.level"
                  :label="level.name"
                  :value="level.level"
                />
              </el-select>
            </div>
          </div>
          <el-table
            :data="currentAvailableList"
            border
            stripe
            max-height="400"
            size="mini"
            @selection-change="onAvailableSelectChange"
          >
            <el-table-column type="selection" width="40" />
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="100"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="addSingleResource(scope.row)"
                >
                  添加
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 批量操作按钮 -->
          <div v-if="currentSelectedAvailable.length > 0" class="batch-actions">
            <el-button size="mini" type="primary" @click="batchAddResources">
              批量添加选中的 {{ currentSelectedAvailable.length }} 个{{
                currentResourceName
              }}
            </el-button>
          </div>
          <!-- 分页 -->
          <Pagination
            v-show="currentAvailableTotal > 0"
            :total="currentAvailableTotal"
            :page.sync="currentAvailableQuery.pager.page"
            :limit.sync="currentAvailableQuery.pager.pageSize"
            :layout="'prev, pager, next'"
            style="margin-top: 10px;"
            @pagination="handleAvailablePagination"
          />
        </div>

        <!-- 已开通资源列表 -->
        <div class="list-section">
          <div class="list-header">
            <span class="list-title">已开通{{ currentResourceName }}</span>
            <span class="list-count">({{ currentEnabledTotal }})</span>
          </div>

          <!-- 已开通列表的搜索栏 -->
          <div class="search-column">
            <div class="search-column__item">
              <el-input
                v-model="enabledQuery.condition.keyword"
                :placeholder="`请输入${currentResourceName}名称`"
                clearable
                @change="searchEnabled"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  style="cursor: pointer;"
                  @click="searchEnabled"
                />
              </el-input>
            </div>
            <div class="search-column__item">
              <el-select
                v-model="enabledQuery.condition.memberLevel"
                placeholder="会员等级"
                clearable
                @change="searchEnabled"
              >
                <el-option
                  v-for="level in memberLevelList"
                  :key="'enabled-' + level.level"
                  :label="level.name"
                  :value="level.level"
                />
              </el-select>
            </div>
          </div>
          <el-table
            :data="currentEnabledList"
            border
            stripe
            max-height="400"
            size="mini"
            @selection-change="onEnabledSelectChange"
          >
            <el-table-column type="selection" width="40" />
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :width="column.width"
              :min-width="column.minWidth"
              :show-overflow-tooltip="column.showOverflowTooltip"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="100"
              align="center"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  style="color: #f56c6c;"
                  @click="removeSingleResource(scope.row)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 批量操作按钮 -->
          <div v-if="currentSelectedEnabled.length > 0" class="batch-actions">
            <el-button size="mini" type="danger" @click="batchRemoveResources">
              批量移除选中的 {{ currentSelectedEnabled.length }} 个{{
                currentResourceName
              }}
            </el-button>
          </div>
          <!-- 分页 -->
          <Pagination
            v-show="currentEnabledTotal > 0"
            :total="currentEnabledTotal"
            :page.sync="enabledQuery.pager.page"
            :limit.sync="enabledQuery.pager.pageSize"
            :layout="'prev, pager, next'"
            style="margin-top: 10px;"
            @pagination="handleEnabledPagination"
          />
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Tree from '@/pages/validManage/components/platformTree'
import Pagination from '@/components/Pagination'
import {
  getCourseList,
  getSopList,
  addFreeResource,
  deleteFreeResource
} from '@/api/freeCourse'
import { getVipLevelList } from '@/api/vip'

export default {
  name: 'DialogFreeCourse',
  components: {
    Tree,
    Pagination
  },
  filters: {
    filterText(value) {
      if (!value) return '-'
      return value.length > 30 ? value.slice(0, 30) + '...' : value
    }
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    orgData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: this.show,
      activeTab: 'course',
      categoryType: 2, // 2-课程，3-sop
      memberLevelList: [], // 会员等级列表
      selectedCategoryName: '', // 当前选中的分类名称

      // 教程相关数据
      availableCourseList: [],
      availableCourseTotal: 0,
      selectedAvailableCourses: [],
      enabledCourseList: [],
      enabledCourseTotal: 0,
      selectedEnabledCourses: [],

      // SOP相关数据
      availableSopList: [],
      availableSopTotal: 0,
      selectedAvailableSops: [],
      enabledSopList: [],
      enabledSopTotal: 0,
      selectedEnabledSops: [],

      // 统一的查询对象 - 只保留两个
      availableQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          platform: 1,
          status: 2,
          type: 1, // 动态设置：1-教程 2-SOP
          cateId: null,
          orgId: null,
          isExcludeExistResource: 1 // 过滤已开通的资源
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      enabledQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          platform: 1,
          status: 2,
          type: 1, // 动态设置：1-教程 2-SOP
          cateId: null,
          orgId: null,
          isGetExistResource: 1 // 获取已开通的资源
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 当前资源类型的统一数据 - 通过 watch activeTab 动态设置
      currentAvailableList: [],
      currentAvailableTotal: 0,
      currentEnabledList: [],
      currentEnabledTotal: 0,
      currentSelectedAvailable: [],
      currentSelectedEnabled: [],
      currentResourceName: '教程',
      currentQuery: null,
      currentAvailableQuery: null,
      currentEnabledQuery: null
    }
  },
  computed: {
    // 表格列配置（不包含选择列和操作列）
    tableColumns() {
      const isCourse = this.activeTab === 'course'
      const baseColumns = [
        { prop: 'memberLevelName', label: '会员等级', width: 90 },
        {
          prop: 'name',
          label: isCourse ? '教程名称' : 'SOP名称',
          width: 140,
          showOverflowTooltip: true
        },
        {
          prop: 'cateName',
          label: '分类',
          width: 90,
          showOverflowTooltip: true
        }
      ]

      // 教程有时长和简介字段，SOP没有
      if (isCourse) {
        baseColumns.push(
          { prop: 'duration', label: '时长', width: 65 },
          {
            prop: 'introduction',
            label: '简介',
            minWidth: 120,
            showOverflowTooltip: true,
            isIntroduction: true
          }
        )
      }

      return baseColumns
    }
  },
  watch: {
    show(val) {
      this.visible = val
      if (val && this.orgData.orgId) {
        this.initData()
      }
    },
    orgData: {
      handler(val) {
        if (val.orgId && this.visible) {
          this.initData()
        }
      },
      deep: true
    },

    // 监听 activeTab 变化，统一更新当前数据
    activeTab: {
      handler() {
        this.updateCurrentData()
      },
      immediate: true
    }
  },
  mounted() {
    this.loadMemberLevels()
  },
  beforeDestroy() {
    // 组件销毁前清理
    this.selectedAvailableCourses = []
    this.selectedEnabledCourses = []
    this.selectedAvailableSops = []
    this.selectedEnabledSops = []
    this.availableCourseList = []
    this.enabledCourseList = []
    this.availableSopList = []
    this.enabledSopList = []
  },
  methods: {
    // 更新当前数据 - 根据 activeTab 统一设置
    updateCurrentData() {
      const isCourse = this.activeTab === 'course'
      const resourceType = isCourse ? 1 : 2 // 1-教程 2-SOP

      // 设置当前数据引用
      this.currentAvailableList = isCourse
        ? this.availableCourseList
        : this.availableSopList
      this.currentAvailableTotal = isCourse
        ? this.availableCourseTotal
        : this.availableSopTotal
      this.currentEnabledList = isCourse
        ? this.enabledCourseList
        : this.enabledSopList
      this.currentEnabledTotal = isCourse
        ? this.enabledCourseTotal
        : this.enabledSopTotal
      this.currentSelectedAvailable = isCourse
        ? this.selectedAvailableCourses
        : this.selectedAvailableSops
      this.currentSelectedEnabled = isCourse
        ? this.selectedEnabledCourses
        : this.selectedEnabledSops
      this.currentResourceName = isCourse ? '教程' : 'SOP'

      // 设置查询对象的资源类型
      this.availableQuery.condition.type = resourceType
      this.enabledQuery.condition.type = resourceType

      // 设置当前查询对象引用
      this.currentAvailableQuery = this.availableQuery
      this.currentEnabledQuery = this.enabledQuery
    },

    // 统一的选择变更处理
    onAvailableSelectChange(selection) {
      if (this.activeTab === 'course') {
        this.onAvailableCourseSelectChange(selection)
      } else {
        this.onAvailableSopSelectChange(selection)
      }
    },

    onEnabledSelectChange(selection) {
      if (this.activeTab === 'course') {
        this.onEnabledCourseSelectChange(selection)
      } else {
        this.onEnabledSopSelectChange(selection)
      }
    },

    // 统一的单个资源操作
    addSingleResource(row) {
      if (this.activeTab === 'course') {
        this.addSingleCourse(row)
      } else {
        this.addSingleSop(row)
      }
    },

    removeSingleResource(row) {
      if (this.activeTab === 'course') {
        this.removeSingleCourse(row)
      } else {
        this.removeSingleSop(row)
      }
    },

    // 统一的批量操作
    batchAddResources() {
      if (this.activeTab === 'course') {
        this.batchAddCourses()
      } else {
        this.batchAddSops()
      }
    },

    batchRemoveResources() {
      if (this.activeTab === 'course') {
        this.batchRemoveCourses()
      } else {
        this.batchRemoveSops()
      }
    },

    // 统一的分页处理
    handleAvailablePagination(pagination) {
      this.availableQuery.pager.page = pagination.page
      this.availableQuery.pager.pageSize = pagination.pageSize

      if (this.activeTab === 'course') {
        this.loadAvailableCourseList()
      } else {
        this.loadAvailableSopList()
      }
    },

    handleEnabledPagination(pagination) {
      this.enabledQuery.pager.page = pagination.page
      this.enabledQuery.pager.pageSize = pagination.pageSize

      if (this.activeTab === 'course') {
        this.loadEnabledCourseList()
      } else {
        this.loadEnabledSopList()
      }
    },

    // 加载会员等级列表
    async loadMemberLevels() {
      try {
        const response = await getVipLevelList()
        this.memberLevelList = response || []
      } catch (error) {
        console.error('获取会员等级列表失败:', error)
      }
    },
    initData() {
      // 设置单位ID到查询条件中
      const orgId = this.orgData.orgId
      this.availableQuery.condition.orgId = orgId
      this.enabledQuery.condition.orgId = orgId

      // 更新当前数据引用
      this.updateCurrentData()

      // 根据当前tab加载数据
      if (this.activeTab === 'course') {
        this.loadAllCourseData()
      } else {
        this.loadAllSopData()
      }
    },
    // 树节点点击
    handleTreeNodeClick(data) {
      const cateId = data.categoryId || null
      this.selectedCategoryName = data.name || ''

      // 设置查询条件的分类ID
      this.availableQuery.condition.cateId = cateId
      this.enabledQuery.condition.cateId = cateId

      // 重置分页并加载数据
      this.availableQuery.pager.page = 1
      this.enabledQuery.pager.page = 1

      if (this.activeTab === 'course') {
        this.loadAllCourseData()
      } else {
        this.loadAllSopData()
      }
    },

    // 清除分类选择
    clearCategorySelection() {
      this.selectedCategoryName = ''

      // 清除查询条件的分类ID
      this.availableQuery.condition.cateId = null
      this.enabledQuery.condition.cateId = null

      // 使用nextTick确保DOM更新后再操作
      this.$nextTick(() => {
        try {
          // 清除树的选中状态
          if (
            this.$refs.freeCourseTree &&
            this.$refs.freeCourseTree.$refs.tree
          ) {
            this.$refs.freeCourseTree.$refs.tree.setCurrentKey(null)
          }
        } catch (error) {
          console.warn('清除树选中状态失败:', error)
        }
      })

      // 重置分页
      this.availableQuery.pager.page = 1
      this.enabledQuery.pager.page = 1

      if (this.activeTab === 'course') {
        this.loadAllCourseData()
      } else {
        this.loadAllSopData()
      }
    },
    // tab切换
    handleTabClick(tab) {
      this.categoryType = tab.name === 'course' ? 2 : 3
      if (tab.name === 'course') {
        this.loadAllCourseData()
      } else {
        this.loadAllSopData()
      }
    },
    // ==================== 数据加载方法 ====================

    // 加载所有教程数据（未开通 + 已开通）
    async loadAllCourseData() {
      await Promise.all([
        this.loadAvailableCourseList(),
        this.loadEnabledCourseList()
      ])
    },

    // 加载所有SOP数据（未开通 + 已开通）
    async loadAllSopData() {
      await Promise.all([
        this.loadAvailableSopList(),
        this.loadEnabledSopList()
      ])
    },

    // 加载未开通教程列表
    async loadAvailableCourseList() {
      try {
        const response = await getCourseList(this.availableQuery)
        response.records.forEach(element => {
          const memberLevel = this.memberLevelList.find(
            i => i.level === element.memberLevel
          )
          element.memberLevelName = memberLevel ? memberLevel.name : '免费会员'
        })
        this.availableCourseList = response.records || []
        this.availableCourseTotal = response.total || 0
        this.updateCurrentData() // 更新当前数据引用
      } catch (error) {
        console.error('获取未开通教程列表失败:', error)
        this.$message.error('获取未开通教程列表失败')
      }
    },

    // 加载已开通教程列表
    async loadEnabledCourseList() {
      try {
        const response = await getCourseList(this.enabledQuery)
        response.records.forEach(element => {
          const memberLevel = this.memberLevelList.find(
            i => i.level === element.memberLevel
          )
          element.memberLevelName = memberLevel ? memberLevel.name : '免费会员'
        })
        this.enabledCourseList = response.records || []
        this.enabledCourseTotal = response.total || 0
        this.updateCurrentData() // 更新当前数据引用
      } catch (error) {
        console.error('获取已开通教程列表失败:', error)
        this.$message.error('获取已开通教程列表失败')
      }
    },

    // 加载未开通SOP列表
    async loadAvailableSopList() {
      try {
        const response = await getSopList(this.availableQuery)
        response.records.forEach(element => {
          const memberLevel = this.memberLevelList.find(
            i => i.level === element.memberLevel
          )
          element.memberLevelName = memberLevel ? memberLevel.name : '免费会员'
        })
        this.availableSopList = response.records || []
        this.availableSopTotal = response.total || 0
        this.updateCurrentData() // 更新当前数据引用
      } catch (error) {
        console.error('获取未开通SOP列表失败:', error)
        this.$message.error('获取未开通SOP列表失败')
      }
    },

    // 加载已开通SOP列表
    async loadEnabledSopList() {
      try {
        const response = await getSopList(this.enabledQuery)
        response.records.forEach(element => {
          const memberLevel = this.memberLevelList.find(
            i => i.level === element.memberLevel
          )
          element.memberLevelName = memberLevel ? memberLevel.name : '免费会员'
        })
        this.enabledSopList = response.records || []
        this.enabledSopTotal = response.total || 0
        this.updateCurrentData() // 更新当前数据引用
      } catch (error) {
        console.error('获取已开通SOP列表失败:', error)
        this.$message.error('获取已开通SOP列表失败')
      }
    },
    // ==================== 搜索方法 ====================

    // 搜索未开通资源
    searchAvailable() {
      // 重置分页
      this.availableQuery.pager.page = 1

      // 加载对应数据
      if (this.activeTab === 'course') {
        this.loadAvailableCourseList()
      } else {
        this.loadAvailableSopList()
      }
    },

    // 搜索已开通资源
    searchEnabled() {
      // 重置分页
      this.enabledQuery.pager.page = 1

      // 加载对应数据
      if (this.activeTab === 'course') {
        this.loadEnabledCourseList()
      } else {
        this.loadEnabledSopList()
      }
    },

    // ==================== 选择变化处理方法 ====================

    // 未开通教程选择变化
    onAvailableCourseSelectChange(selection) {
      this.selectedAvailableCourses = selection
    },

    // 已开通教程选择变化
    onEnabledCourseSelectChange(selection) {
      this.selectedEnabledCourses = selection
    },

    // 未开通SOP选择变化
    onAvailableSopSelectChange(selection) {
      this.selectedAvailableSops = selection
    },

    // 已开通SOP选择变化
    onEnabledSopSelectChange(selection) {
      this.selectedEnabledSops = selection
    },

    // ==================== 单个操作方法 ====================

    // 添加单个教程
    async addSingleCourse(course) {
      try {
        await this.performAddFreeResource([course], false, 'course')
        this.loadAllCourseData() // 刷新数据
      } catch (error) {
        console.error('添加教程失败:', error)
      }
    },

    // 移除单个教程
    async removeSingleCourse(course) {
      try {
        await this.performRemoveFreeResource([course], 'course')
        this.loadAllCourseData() // 刷新数据
      } catch (error) {
        console.error('移除教程失败:', error)
      }
    },

    // 添加单个SOP
    async addSingleSop(sop) {
      try {
        await this.performAddFreeResource([sop], false, 'sop')
        this.loadAllSopData() // 刷新数据
      } catch (error) {
        console.error('添加SOP失败:', error)
      }
    },

    // 移除单个SOP
    async removeSingleSop(sop) {
      try {
        await this.performRemoveFreeResource([sop], 'sop')
        this.loadAllSopData() // 刷新数据
      } catch (error) {
        console.error('移除SOP失败:', error)
      }
    },
    // ==================== 批量操作方法 ====================

    // 批量添加教程
    async batchAddCourses() {
      if (this.selectedAvailableCourses.length === 0) {
        this.$message.warning('请先选择要添加的教程')
        return
      }

      try {
        await this.performAddFreeResource(
          this.selectedAvailableCourses,
          false,
          'course'
        )
        this.loadAllCourseData() // 刷新数据
      } catch (error) {
        console.error('批量添加教程失败:', error)
      }
    },

    // 批量移除教程
    async batchRemoveCourses() {
      if (this.selectedEnabledCourses.length === 0) {
        this.$message.warning('请先选择要移除的教程')
        return
      }

      try {
        await this.performRemoveFreeResource(
          this.selectedEnabledCourses,
          'course'
        )
        this.loadAllCourseData() // 刷新数据
      } catch (error) {
        console.error('批量移除教程失败:', error)
      }
    },

    // 批量添加SOP
    async batchAddSops() {
      if (this.selectedAvailableSops.length === 0) {
        this.$message.warning('请先选择要添加的SOP')
        return
      }

      try {
        await this.performAddFreeResource(
          this.selectedAvailableSops,
          false,
          'sop'
        )
        this.loadAllSopData() // 刷新数据
      } catch (error) {
        console.error('批量添加SOP失败:', error)
      }
    },

    // 批量移除SOP
    async batchRemoveSops() {
      if (this.selectedEnabledSops.length === 0) {
        this.$message.warning('请先选择要移除的SOP')
        return
      }

      try {
        await this.performRemoveFreeResource(this.selectedEnabledSops, 'sop')
        this.loadAllSopData() // 刷新数据
      } catch (error) {
        console.error('批量移除SOP失败:', error)
      }
    },

    // ==================== 弹窗操作方法 ====================

    // 取消
    handleCancel() {
      this.handleClose()
    },

    // 确认（这个方法现在主要用于关闭弹窗，实际的添加操作通过各个按钮完成）
    handleConfirm() {
      this.$message.success('操作完成')
      this.handleClose()
    },
    // ==================== 执行操作方法 ====================

    // 执行开通免费培训课程
    async performAddFreeResource(
      selectedItems,
      isCrossPage = false,
      resourceTypeStr = null
    ) {
      try {
        const resourceType =
          resourceTypeStr === 'course'
            ? 1
            : resourceTypeStr === 'sop'
            ? 2
            : this.activeTab === 'course'
            ? 1
            : 2 // 1-教程 2-sop
        const resourceIdList = selectedItems.map(item =>
          resourceType === 1 ? item.courseId : item.sopId
        )

        const params = {
          orgId: this.orgData.orgId,
          resourceIdList,
          resourceType,
          dataType: isCrossPage ? 1 : 0
        }

        // 如果是跨页开通，添加筛选条件
        if (isCrossPage) {
          if (this.availableQuery.condition.keyword) {
            params.keyword = this.availableQuery.condition.keyword
          }
          if (this.availableQuery.condition.cateId) {
            params.cateId = this.availableQuery.condition.cateId
          }
          if (this.availableQuery.condition.memberLevel) {
            params.memberLevel = this.availableQuery.condition.memberLevel
          }
        }

        await addFreeResource(params)

        const resourceName = resourceType === 1 ? '教程' : 'SOP'
        this.$message.success(
          isCrossPage
            ? `成功开通所有符合条件的${resourceName}资源！`
            : `成功开通${selectedItems.length}项${resourceName}资源！`
        )

        // 通知父组件刷新数据
        this.$emit('success')
      } catch (error) {
        console.error('开通免费培训课程失败:', error)
        this.$message.error('开通失败，请重试')
      }
    },

    // 执行移除免费培训课程
    async performRemoveFreeResource(selectedItems, resourceTypeStr) {
      try {
        const resourceType = resourceTypeStr === 'course' ? 1 : 2 // 1-教程 2-sop
        const resourceIdList = selectedItems.map(item =>
          resourceType === 1 ? item.courseId : item.sopId
        )

        const params = {
          orgId: this.orgData.orgId,
          resourceIdList,
          resourceType
        }

        // 使用专门的删除API
        await deleteFreeResource(params)

        const resourceName = resourceType === 1 ? '教程' : 'SOP'
        this.$message.success(
          `成功移除${selectedItems.length}项${resourceName}资源！`
        )

        // 通知父组件刷新数据
        this.$emit('success')
      } catch (error) {
        console.error('移除免费培训课程失败:', error)
        this.$message.error('移除失败，请重试')
      }
    },
    // 关闭弹窗
    handleClose() {
      // 重置所有数据
      this.selectedAvailableCourses = []
      this.selectedEnabledCourses = []
      this.selectedAvailableSops = []
      this.selectedEnabledSops = []
      this.selectedCategoryName = ''

      // 重置搜索条件
      // 重置查询条件 - 只需要重置两个查询对象
      this.availableQuery.condition.keyword = ''
      this.availableQuery.condition.cateId = null
      this.availableQuery.condition.memberLevel = null

      this.enabledQuery.condition.keyword = ''
      this.enabledQuery.condition.cateId = null
      this.enabledQuery.condition.memberLevel = null

      // 通知父组件关闭弹窗
      this.$emit('update:show', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-left {
  border: 1px solid #dcdfe6;
  border-radius: 4px;

  .tree-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #dcdfe6;
    font-weight: bold;
  }

  .introduction-text {
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.4 !important;
    max-height: 2.8em !important; /* 2行的高度 */
    word-break: break-word !important;
    white-space: normal !important;
    width: 100% !important;
    font-size: 14px !important;
  }

  /* 确保表格单元格也应用样式 */
  ::v-deep .el-table .el-table__body .introduction-text {
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    line-height: 1.4 !important;
    max-height: 2.8em !important;
    word-break: break-word !important;
    white-space: normal !important;
  }

  .content-container {
    padding: 10px;
  }
}

.search-column {
  display: flex;
  margin-bottom: 15px;

  &__item {
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

/* 双列表布局样式 */
.dual-list-container {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.list-section {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.list-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.list-title {
  font-weight: bold;
  font-size: 14px;
  color: #303133;
}

.list-count {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.batch-actions {
  margin-top: 10px;
  text-align: center;
}

/* 顶部分类区域样式 */
.category-section {
  margin-bottom: 20px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.category-section .tree-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
  font-size: 14px;
  color: #303133;
}

.selected-category-inline {
  margin-left: 20px;
  font-size: 12px;
  color: #409eff;
  font-weight: normal;
}

.tree-container {
  max-height: 200px;
  overflow-y: auto;
}

/* 主要内容区域样式 */
.main-content {
  flex: 1;
}
</style>
