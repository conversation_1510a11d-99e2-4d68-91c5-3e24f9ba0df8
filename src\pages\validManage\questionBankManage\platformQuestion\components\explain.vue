<template>
  <el-dialog :title="titled" :visible.sync="dialogVisible" center width="50%" @close="close()">
    <div class="container">
      <!-- 题型说明 -->
      <div v-show="titled==='题型说明'" class="explain">
        <ol>
          <li v-for="(item,index) in explain" :key="index">{{ item }}</li>
        </ol>
      </div>
      <!-- 范例 -->
      <div v-show="titled==='范例'" class="example">
        <div>
          <div class="title">{{ example[0].title }}</div>
          <div v-for="(list, idx) in example[0].list" :key="idx" class="list">
            <span>{{ list.title }}</span>
            <div v-for="(v,i) in list.optionList" :key="i">
              <div>{{ v.index }}.{{ v.content }}</div>
            </div>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>

        <div>
          <div class="title">{{ example[1].title }}</div>
          <span>{{ example[1].content }}</span>
          <div v-for="(list, idx) in example[1].list" :key="idx" class="list">
            <span>{{ list.title }}</span>
            <div v-for="(v,i) in list.optionList" :key="i">
              <div>{{ v.index }}.{{ v.content }}</div>
            </div>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>

        <div>
          <div class="title">{{ example[2].title }}</div>
          <div v-for="(item,i) in example[2].optionList" :key="i" class="list">
            <div>{{ item.index }}.{{ item.content }}</div>
          </div>
          <div v-for="(list, idx) in example[2].list" :key="idx+11" class="list">
            <span>{{ list.title }}</span>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>

        <div>
          <div class="title">{{ example[3].title }}</div>
          <div v-for="(list, idx) in example[3].list" :key="idx" class="list">
            <span>{{ list.title }}</span>
            <div v-for="(v,i) in list.optionList" :key="i">
              <div>{{ v.index }}.{{ v.content }}</div>
            </div>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>

        <div>
          <div class="title">{{ example[4].title }}</div>
          <div v-for="(list, idx) in example[4].list" :key="idx" class="list">
            <span>{{ list.title }}</span>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>

        <div>
          <div class="title">{{ example[5].title }}</div>
          <div v-for="(list, idx) in example[5].list" :key="idx" class="list">
            <span>{{ list.title }}</span>
            <div v-for="(v,i) in list.optionList" :key="i">
              <div>{{ v.index }}.{{ v.content }}</div>
            </div>
            <span>{{ list.answer }}</span>
            <span>{{ list.points }}</span>
            <span>{{ list.desc }}</span>
          </div>
        </div>
      </div>
      <!-- 规则 -->
      <div v-show="titled==='规则'" class="rule">
        <div v-for="(item, index) in rule" :key="index">
          <span>{{ item.title }}</span>
          <ol>
            <li v-for="(list, idx) in item.list" :key="idx">{{ list.value }}</li>
          </ol>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      titled: this.title,
      dialogVisible: this.visible,
      explain: [
        'A1 型题(单句型最佳选择题)：题干是以叙述式单句出现，有五个备选答案，只有1个正确答案。',
        'A2 型题(病例摘要型最佳选择题)：题干是一个简要病例，有五个备选答案，只有1个正确答案。',
        'A3 型题(病例组型最佳选择题)：题干是叙述一个以患者为中心的临床情景，提出2～3个相关问题，每个问题均与开始的临床情景有关，但测试要点不同，且问题之间相互独立。每个问题有5个备选答案，只有1个正确答案。',
        'A4 型题(病例串型最佳选择题)：题干是叙述一个单一病人或家庭为中心的临床情景，当病情逐渐展开时，可以逐步增加新的信息，有时陈述了一些次要的或有前提的假设信息，这些信息与病例中叙述的具体病人并不一定有联系。提出3～6个相关问题，每个问题均与开始的临床情景有关，又与随后的改变有关。每个问题有5个备选答案，只有1正确答案。',
        'B1 型题(标准配伍题)：试题提供5个备选答案，提出至少2个问题，每个问题都从备选答案中选择1个正确答案。在一组试题中，每个备选答案可以选用一次，也可以选用数次，但也可以一次不选用。',
        'X题型(多选题)：有5个备选答案，最少有2个正确答案。',
        '判断题：题干描述一段文字，判断对错。',
        '选择填空：一段文字中，不定数量的填空，再提供对应填空数量的备选项，每个填空仅对应1个正确选项。'
      ],
      rule: [
        {
          title: 'A1题型/A2题型',
          list: [
            {
              value: '必须有“题干：”，题干内容不能为空'
            },
            {
              value: '必须有“选项：”，最少2个选项，最多10个选项，选项依次为A,B,C,D,E,F,G,H,I,J'
            },
            {
              value: '必须有 “ 答案：”，最多1个答案'
            },
            {
              value: '可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请录入题目后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将题干、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        },
        {
          title: 'A3题型/A4题型',
          list: [
            {
              value: '必须有“病例：”，病例内容不能为空'
            },
            {
              value: '必须有“问题：”，最少2个问题，最多10个问题'
            },
            {
              value: '每个问题必须有“选项：”，最少2个选项，最多10个选项，选项依次为A,B,C,D,E,F,G,H,I,J'
            },
            {
              value: '每个问题必须有 “ 答案：”，最多1个答案'
            },
            {
              value: '每个问题可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '每个问题可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请保存后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将案例、问题、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        },
        {
          title: 'X题型',
          list: [
            {
              value: '必须有“题干：”，题干内容不能为空'
            },
            {
              value: '必须有“选项：”，最少2个选项，最多10个选项，选项依次为A,B,C,D,E,F,G,H,I,J'
            },
            {
              value: '必须有 “ 答案：”，最少2个答案'
            },
            {
              value: '可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请保存后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将题干、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        },
        {
          title: 'B1题型',
          list: [
            {
              value: '必须有“选项：”，最少2个选项，最多10个选项，选项依次为A,B,C,D,E,F,G,H,I,J'
            },
            {
              value: '必须有“题干：”，最少2个题干，最多10个题干，所有题干共用所有选项'
            },
            {
              value: '每个题干必须有 “ 答案：”，最多1个答案'
            },
            {
              value: '每个题干可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '每个题干可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请保存后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将题干、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        },
        {
          title: '判断题',
          list: [
            {
              value: '必须有“题干：”，题干内容不能为空'
            },
            {
              value: '必须有 “ 答案：”，答案必须是“正确”，或者“错误”'
            },
            {
              value: '可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请保存后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将题干、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        },
        {
          title: '选择填空题',
          list: [
            {
              value: '必须有“题干：”，题干内容不能为空，题干中的填空以（）代替，最少1个填空，最多10个填空'
            },
            {
              value: '必须有“选项：”，选项数量不得少于填空数量，最多10个选项，选项依次为A,B,C,D,E,F,G,H,I,J'
            },
            {
              value: '每个填空必须有 “ 答案：”，答案最多1个'
            },
            {
              value: '可以有 “考点关键字：” 非必需，没有可不填'
            },
            {
              value: '可以有 “解析：” 非必需，没有可不填'
            },
            {
              value: '若试题内容中包含图片，请保存后进行编辑时将图片插入即可'
            },
            {
              value: '为提升识别效率，请将题干、选项、答案、考点关键字、解析换行录入，不要放在同一行'
            }
          ]
        }
      ],
      example: [
        {
          title: 'A1题型/A2题型',
          list: [
            {
              title: '题干：关于呼吸机的相关性肺炎，下列选项描述正确的是',
              optionList: [
                { index: '选项：A', content: '呼吸机辅助48小时' },
                { index: 'B', content: '呼吸机辅助72小时' },
                { index: 'C', content: '呼吸机辅助96小时' },
                { index: 'D', content: '呼吸机辅助24小时' },
                { index: 'E', content: '呼吸机辅助12小时' }
              ],
              answer: '答案：A',
              points: '考点关键字：救助,护理',
              desc: '解析：救助护理所用的呼吸机，在应用于肺炎救助时，有时间限制'
            }
          ]
        },
        {
          title: 'A3题型/A4题型',
          content: '病例：女，25岁，已婚，停经75日，阴道中等流血4日伴发热。昨日引道排出一块肉群组织，今晨突然大量阴道流血。查体：T 38.2℃，P 116次/分，BP 80/60mmHg。子宫如近妊娠2个月大，有压痛，宫口可通过一指，阴道分泌物明显臭味。血WBC 20.5*10^9/L,Hb 65g/L.',
          list: [
            {
              title: '问题1：该患者最可能的诊断为感染合并',
              optionList: [
                { index: '选项：A', content: '先兆流产' },
                { index: 'B', content: '难免流产' },
                { index: 'C', content: '不全流产' },
                { index: 'D', content: '稽留流产' },
                { index: 'E', content: '完全流产' }
              ],
              answer: '答案：C',
              points: '考点关键字：',
              desc: '解析：'
            },
            {
              title: '问题2：除抗休克外，还需进行的紧急处理是',
              optionList: [
                { index: '选项：A', content: '注射止血剂' },
                { index: 'B', content: '注射宫缩剂' },
                { index: 'C', content: '静脉注射抗生素' },
                { index: 'D', content: '钳夹出空腔内妊娠物' },
                { index: 'E', content: '立即进行彻底清宫' }
              ],
              answer: '答案：D',
              points: '考点关键字：',
              desc: '解析：'
            },
            {
              title: '问题3：本病例发生自然流产最可能的原因是',
              optionList: [
                { index: '选项：A', content: '孕妇甲状腺功能低下' },
                { index: 'B', content: '孕妇接触放射性物质' },
                { index: 'C', content: '孕妇细胞免疫功能失调' },
                { index: 'D', content: '母子血型不合' },
                { index: 'E', content: '遗传基因缺陷' }
              ],
              answer: '答案：E',
              points: '考点关键字：',
              desc: '解析：'
            }
          ]
        },
        {
          title: 'B1题型',
          optionList: [
            { index: '选项：A', content: '黄而黏稠，坚而成块' },
            { index: 'B', content: '白而清稀' },
            { index: 'C', content: '清稀而多泡沫' },
            { index: 'D', content: '白滑而量多' },
            { index: 'E', content: '少而黏' }
          ],
          list: [
            {
              title: '题干1：寒痰的特征是',
              answer: '答案：B',
              points: '考点关键字：',
              desc: '解析：'
            },
            {
              title: '题干2：湿痰的特征是',
              answer: '答案：D',
              points: '考点关键字：',
              desc: '解析：'
            }
          ]
        },
        {
          title: 'X题型',
          list: [
            {
              title: '题干：脑室穿刺引流术后常见的并发症有',
              optionList: [
                { index: '选项：A', content: '脑室内出血' },
                { index: 'B', content: '硬膜下出血' },
                { index: 'C', content: '硬膜外出血' },
                { index: 'D', content: '穿刺局部感染' },
                { index: 'E', content: '颅内感染' }
              ],
              answer: '答案：ABCDE',
              points: '考点关键字：穿刺术',
              desc: '解析：无'
            }
          ]
        },
        {
          title: '判断题',
          list: [
            {
              title: '题干：膝关节骨性关节炎最主要的一项症状是关节疼痛',
              answer: '答案：正确',
              points: '考点关键字：穿刺术',
              desc: '解析：无'
            }
          ]
        },
        {
          title: '选择填空题',
          list: [
            {
              title: '题干：急性软组织损伤（24H之内）最佳选择是（1），这是一个真实的案例（2）',
              optionList: [
                { index: '选项：A', content: '脑室内出血' },
                { index: 'B', content: '硬膜下出血' }
              ],
              answer: '答案：AB',
              points: '考点关键字：穿刺术',
              desc: '解析：无'
            }
          ]
        }
      ]
    }
  },
  watch: {
    visible() {
      this.dialogVisible = this.visible
    },
    title() {
      this.titled = this.title
    }
  },
  methods: {
    close() {
      this.$emit('close', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.container{
  .explain{
    font-size: 16px;
    ol>li{
      line-height: 24px;
      margin: 0px 0px 10px 0px;
    }
  }
  .rule{
    span{
      font-size: 18px;
      font-weight: bold;
    }
    ol>li{
      line-height: 14px;
      margin: 0px 0px 10px 0px;
    }
  }
}
::v-deep .el-dialog__body {
  padding: 0px 25px 30px;
  font-size: 16px;
}
.title{
  font-size: 18px;
  font-weight: bold;
  color: #000;
  margin-bottom: 10px;
}
.list{
  display: flex;
  flex-direction: column;
  *{
    margin-bottom: 5px;
  }
  span:last-child{
    margin-bottom: 20px;
  }
}
</style>
