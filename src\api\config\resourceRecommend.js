import request from '@/utils/request'

/**
 * 视频推荐配置相关
 */

export default {
  // 创建视频推荐配置
  create(param) {
    return request({
      url: '/resourceRecommend/add',
      method: 'post',
      data: param
    })
  },
  putaway(params) {
    return request({
      url: '/resourceRecommend/enable',
      method: 'post',
      params
    })
  },
  // 编辑视频推荐配置
  edit(param) {
    return request({
      url: '/resourceRecommend/edit',
      method: 'post',
      data: param
    })
  },
  // 删除视频推荐配置
  del(ids) {
    return request({
      url: `/resourceRecommend/delete`,
      method: 'post',
      params: { ids }
    })
  },
  // 视频推荐配置列表
  list(param) {
    return request({
      url: '/resourceRecommend/list',
      method: 'post',
      data: param
    })
  }
}
