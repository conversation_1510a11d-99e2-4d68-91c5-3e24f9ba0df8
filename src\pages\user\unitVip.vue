<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <div class="search-column">
      <div class="fl">
        <div class="search-column__item">
          <div class="search-column__label">单位名称：</div>
          <div class="search-column__inner">
            <el-input
              v-model="tableQuery.condition.keyword"
              clearable
              placeholder="请输入搜索关键字"
              @clear="searchKeyword()"
              @keydown.enter.native="searchKeyword()"
            >
              <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKeyword()" />
            </el-input>
          </div>
        </div>
        <div class="search-column__item">
          会员等级：
          <el-select v-model="tableQuery.condition.memberLevel" clearable placeholder="请选择会员等级" @change="getMemberOrgList">
            <el-option v-for="item in memberLevelList" :key="'m'+item.level" :label="item.name" :value="item.level" />
          </el-select>
        </div>
        <div class="search-column__item">
          状态：
          <el-select v-model="tableQuery.condition.memberStatus" clearable placeholder="请选择状态" @change="getMemberOrgList">
            <el-option v-for="item in statusList" :key="'s'+item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
      <div class="fr">
        <div class="search-column__item">
          <el-button type="primary" @click="openMemberDialog()">开通会员</el-button>
        </div>
        <div class="search-column__item">
          <el-button
            type="warning"
            :disabled="selectedMembers.length === 0"
            @click="batchRenewalMember"
          >
            批量续费/升级 ({{ selectedMembers.length }})
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="memberList" border stripe @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="orgId" label="ID" width="100" />
      <el-table-column prop="orgName" label="单位名称" />
      <el-table-column prop="orgTypeName" label="单位类型" width="120" />
      <el-table-column prop="memberLevel" label="会员等级" width="120">
        <template slot-scope="scope">
          {{ getMemberLevelName(scope.row.memberLevel) }}
        </template>
      </el-table-column>
      <el-table-column prop="memberStatus" label="状态" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.memberStatus === 1 ? 'success' : 'danger'">
            {{ scope.row.memberStatus === 1 ? '正常' : '过期' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="memberStartTime" label="会员期限" width="200">
        <template slot-scope="scope">
          {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
        </template>
      </el-table-column>
      <el-table-column prop="creator" label="开通人" width="100" />
      <el-table-column prop="createTime" label="开通时间" width="160">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="renewalMember(row)">续期/升级</el-button>
          <el-button size="mini" type="text" @click="viewHistory(row)">开通历史</el-button>
          <el-button v-if="row.memberStatus === 1" size="mini" type="text" style="color: #f56c6c;" @click="deleteMember(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <Pagination
      class="text-center"
      :layout="layout"
      :total="total"
      :page="tableQuery.pager.page"
      @pagination="handlePagination"
    />

    <!-- 开通会员弹窗 -->
    <el-dialog
      :title="memberDialogTitle"
      :visible.sync="memberDialogVisible"
      fullscreen
      :close-on-click-modal="false"
      @close="closeMemberDialog"
    >
      <el-form ref="memberForm" :model="memberForm" :rules="memberRules" label-width="120px">
        <el-form-item v-if="!isRenewal" label="选择单位" prop="orgIdList">
          <!-- 搜索栏 -->
          <div style="margin-bottom: 10px;">
            <el-input
              v-model="tmpOrgQuery.condition.memberKeyword"
              placeholder="单位名称/ID（用于搜索本单位及所有子孙单位）"
              style="width: 300px; margin-right: 10px;"
              clearable
              @keyup.enter.native="searchTmpOrg"
            />
            <el-button type="primary" @click="searchTmpOrg">搜索</el-button>
            <el-button @click="resetTmpOrgQuery">重置</el-button>
            <!-- 跨页批量操作按钮 - 只有在搜索后且有结果时才显示 -->
            <el-button
              v-if="showCrossPageButtons"
              type="success"
              @click="batchAddAllOrgs"
            >
              跨页全部添加
            </el-button>
            <el-button
              v-if="showCrossPageButtons"
              type="danger"
              @click="batchRemoveAllOrgs"
            >
              跨页全部删除
            </el-button>
          </div>

          <!-- 双列表布局 -->
          <div style="display: flex; gap: 20px;">
            <!-- 待选单位列表 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 10px; font-weight: bold;">待选单位</div>
              <el-table
                v-loading="pendingOrgLoading"
                :data="pendingOrgList"
                border
                stripe
                max-height="300"
                size="mini"
                @selection-change="handlePendingOrgSelectionChange"
              >
                <el-table-column type="selection" width="40" />
                <el-table-column prop="orgId" label="ID" width="80" />
                <el-table-column prop="orgName" label="单位名称" show-overflow-tooltip />
                <el-table-column prop="typeName" label="单位类型" width="100" />
                <el-table-column prop="memberLevel" label="会员等级" width="100">
                  <template slot-scope="scope">
                    {{ getMemberLevelName(scope.row.memberLevel) }}
                  </template>
                </el-table-column>
                <el-table-column prop="memberStatus" label="会员状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.memberStatus === 1 ? 'success' : 'danger'" size="mini">
                      {{ scope.row.memberStatus === 1 ? '正常' : '过期' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="memberStartTime" label="会员期限" width="160">
                  <template slot-scope="scope">
                    <span v-if="scope.row.memberStartTime && scope.row.memberEndTime">
                      {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="60" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="addTmpOrg(scope.row)">添加</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 批量操作按钮 -->
              <div v-if="selectedPendingOrgs.length > 0" style="margin-top: 10px;">
                <el-button size="mini" type="primary" @click="batchAddSelectedOrgs">
                  批量添加选中的 {{ selectedPendingOrgs.length }} 个单位
                </el-button>
              </div>
              <!-- 待选列表分页 -->
              <pagination
                v-show="pendingOrgTotal > 0"
                :total="pendingOrgTotal"
                :page.sync="tmpOrgQuery.pager.page"
                :limit.sync="tmpOrgQuery.pager.pageSize"
                :layout="'prev, pager, next'"
                style="margin-top: 10px;"
                @pagination="handlePendingOrgPagination"
              />
            </div>

            <!-- 已选单位列表 -->
            <div style="flex: 1;">
              <div style="margin-bottom: 10px; font-weight: bold;">已选单位</div>
              <el-table
                v-loading="selectedOrgLoading"
                :data="selectedOrgList"
                border
                stripe
                max-height="300"
                size="mini"
                @selection-change="handleSelectedOrgSelectionChange"
              >
                <el-table-column type="selection" width="40" />
                <el-table-column prop="orgId" label="ID" width="80" />
                <el-table-column prop="orgName" label="单位名称" show-overflow-tooltip />
                <el-table-column prop="typeName" label="单位类型" width="100" />
                <el-table-column prop="memberLevel" label="会员等级" width="100">
                  <template slot-scope="scope">
                    {{ getMemberLevelName(scope.row.memberLevel) }}
                  </template>
                </el-table-column>
                <el-table-column prop="memberStatus" label="会员状态" width="80">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.memberStatus === 1 ? 'success' : 'danger'" size="mini">
                      {{ scope.row.memberStatus === 1 ? '正常' : '过期' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="memberStartTime" label="会员期限" width="160">
                  <template slot-scope="scope">
                    <span v-if="scope.row.memberStartTime && scope.row.memberEndTime">
                      {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
                    </span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="60" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" style="color: #f56c6c;" @click="removeTmpOrg(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 批量操作按钮 -->
              <div v-if="selectedSelectedOrgs.length > 0" style="margin-top: 10px;">
                <el-button size="mini" type="danger" @click="batchRemoveSelectedOrgs">
                  批量删除选中的 {{ selectedSelectedOrgs.length }} 个单位
                </el-button>
              </div>
              <!-- 已选列表分页 -->
              <pagination
                v-show="selectedOrgTotal > 0"
                :total="selectedOrgTotal"
                :page.sync="selectedOrgQuery.pager.page"
                :limit.sync="selectedOrgQuery.pager.pageSize"
                :layout="'prev, pager, next'"
                style="margin-top: 10px;"
                @pagination="handleSelectedOrgPagination"
              />
            </div>
          </div>
        </el-form-item>

        <!-- 批量续费时显示选中的单位 -->
        <el-form-item v-if="isBatchRenewal" label="选中单位">
          <div style="max-height: 120px; overflow-y: auto; border: 1px solid #dcdfe6; padding: 8px; border-radius: 4px;">
            <el-tag
              v-for="member in selectedMembers"
              :key="member.orgId"
              style="margin-right: 8px; margin-bottom: 8px;"
              type="info"
            >
              {{ member.orgName }} ({{ getMemberLevelName(member.memberLevel) }})
            </el-tag>
          </div>
        </el-form-item>
        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="memberForm.memberLevel" placeholder="请选择会员等级" @change="handleMemberLevelChange">
            <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="memberForm.memberLevel > 0" label="会员期限" prop="memberStartTime">
          <el-col :span="11">
            <el-date-picker
              v-model="memberForm.memberStartTime"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
          <el-col class="line" :span="2" style="text-align: center;">至</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="memberForm.memberEndTime"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeMemberDialog">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitMemberForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 开通历史弹窗 -->
    <el-dialog
      title="开通历史"
      :visible.sync="historyDialogVisible"
      width="700px"
    >
      <el-table v-loading="historyLoading" :data="historyList" border stripe>
        <el-table-column prop="memberLevel" label="会员等级" width="120">
          <template slot-scope="scope">
            {{ getMemberLevelName(scope.row.memberLevel) }}
          </template>
        </el-table-column>
        <el-table-column prop="memberStartTime" label="会员期限" width="200">
          <template slot-scope="scope">
            {{ scope.row.memberStartTime }} ~ {{ scope.row.memberEndTime }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="开通人" />
        <el-table-column prop="createTime" label="开通时间" width="160">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getVipLevelList } from '@/api/vip'
import { findOrganListPage, getTmpOrgBatchId, tmpSelectOrgList, addOrDelTmpSelectOrg } from '@/api/organization'
import { memberOrgPage, addMemberOrg, renewalMemberOrg, deleteMemberOrg, getActiveLog } from '@/api/memberOrg'

export default {
  name: 'UnitVip',
  components: {
    Pagination
  },
  data() {
    return {
      loading: false,
      layout: 'total, sizes, prev, pager, next, jumper',

      // 表格查询参数
      tableQuery: {
        condition: {
          keyword: '',
          memberLevel: null,
          memberStatus: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 表格数据
      memberList: [],
      total: 0,
      selectedMembers: [], // 选中的会员记录

      // 会员等级列表
      memberLevelList: [],

      // 状态列表
      statusList: [
        { label: '正常', value: 1 },
        { label: '过期', value: 0 }
      ],

      // 开通会员弹窗
      memberDialogVisible: false,
      memberDialogTitle: '开通会员',
      isRenewal: false, // 是否为续期/升级
      isBatchRenewal: false, // 是否为批量续期/升级
      submitLoading: false,
      memberForm: {
        orgId: null,
        orgIdList: [],
        memberLevel: null,
        memberStartTime: '',
        memberEndTime: ''
      },
      memberRules: {
        orgIdList: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.isRenewal && this.selectedOrgList.length === 0) {
                callback(new Error('请选择单位'))
              } else {
                callback()
              }
            }
          }
        ],
        memberLevel: [
          { required: true, message: '请选择会员等级', trigger: 'change' }
        ],
        memberStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        memberEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },

      // 临时单位选择相关
      tmpBatchId: null, // 临时单位查询批次ID
      selectedOrgs: [], // 保留原有字段以兼容现有逻辑
      hasSearched: false, // 标记是否已经执行过搜索

      // 待选单位列表
      pendingOrgLoading: false,
      pendingOrgList: [],
      pendingOrgTotal: 0,
      selectedPendingOrgs: [], // 待选列表中选中的单位
      tmpOrgQuery: {
        condition: {
          batchId: null,
          memberKeyword: '', // 修改为memberKeyword
          selectType: 1 // 1-待选单位列表
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 已选单位列表
      selectedOrgLoading: false,
      selectedOrgList: [],
      selectedOrgTotal: 0,
      selectedSelectedOrgs: [], // 已选列表中选中的单位
      selectedOrgQuery: {
        condition: {
          batchId: null,
          memberKeyword: '', // 修改为memberKeyword
          selectType: 2 // 2-已选单位列表
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 保留原有字段以兼容现有逻辑
      orgLoading: false,
      orgList: [],
      orgTotal: 0,
      orgQuery: {
        condition: {
          keyword: '',
          status: 1 // 只查询启用的单位
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },

      // 开通历史相关
      historyDialogVisible: false,
      historyLoading: false,
      historyList: [],
      currentOrgId: null
    }
  },

  computed: {
    // 控制跨页操作按钮的显示
    showCrossPageButtons() {
      // 只有在使用memberKeyword搜索且已经执行过搜索（有列表数据或已加载完成）时才显示
      return this.tmpOrgQuery.condition.memberKeyword &&
             this.hasSearched &&
             (this.pendingOrgTotal > 0 || this.selectedOrgTotal > 0 || (!this.pendingOrgLoading && !this.selectedOrgLoading))
    }
  },

  created() {
    this.getMemberLevelList()
    this.getMemberOrgList()
  },

  methods: {
    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedMembers = selection
    },

    // 批量续费/升级
    batchRenewalMember() {
      if (this.selectedMembers.length === 0) {
        this.$message.warning('请先选择要续费/升级的单位')
        return
      }

      // 检查选中的记录是否都有会员等级
      const invalidMembers = this.selectedMembers.filter(member => !member.memberLevel || member.memberLevel <= 0)
      if (invalidMembers.length > 0) {
        this.$message.warning('选中的单位中包含免费会员，无法批量续费/升级')
        return
      }

      this.memberDialogTitle = `批量续费/升级 (${this.selectedMembers.length}个单位)`
      this.isRenewal = true
      this.isBatchRenewal = true

      // 设置默认值
      const today = new Date()
      const startTime = this.formatDate(today)
      const endDate = new Date(today)
      endDate.setDate(today.getDate() + 364)
      const endTime = this.formatDate(endDate)

      this.memberForm = {
        orgId: null,
        orgIdList: this.selectedMembers.map(member => member.orgId),
        memberLevel: null, // 让用户选择新的会员等级
        memberStartTime: startTime,
        memberEndTime: endTime
      }
      this.memberDialogVisible = true
    },

    // 获取会员等级列表
    async getMemberLevelList() {
      try {
        const response = await getVipLevelList({ filterFree: true })
        this.memberLevelList = response || []
      } catch (error) {
        this.$message.error('获取会员等级列表失败')
      }
    },

    // 获取单位会员列表
    async getMemberOrgList() {
      this.loading = true
      try {
        const response = await memberOrgPage(this.tableQuery)
        this.memberList = response.records || []
        this.total = response.total || 0
      } catch (error) {
        this.$message.error('获取单位会员列表失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getMemberOrgList()
    },

    // 分页处理
    handlePagination(pagination) {
      this.tableQuery.pager = pagination
      this.getMemberOrgList()
    },

    // 获取会员等级名称
    getMemberLevelName(level) {
      const memberLevel = this.memberLevelList.find(item => item.level === level)
      return memberLevel ? memberLevel.name : '免费会员'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return dateTime.replace('T', ' ').substring(0, 19)
    },

    // 开通会员
    async openMemberDialog() {
      this.memberDialogTitle = '开通会员'
      this.isRenewal = false
      this.isBatchRenewal = false
      this.resetMemberForm()
      this.memberDialogVisible = true

      // 初始化临时单位选择功能
      await this.initTmpOrgSelection()
    },

    // 续期/升级会员
    renewalMember(row) {
      this.memberDialogTitle = '续期/升级会员'
      this.isRenewal = true
      this.isBatchRenewal = false

      // 续期时开始日期默认为当天
      const today = new Date()
      const startTime = this.formatDate(today)

      // 如果是付费会员，设置默认结束日期（当前日期+364天）
      let endTime = ''
      if (row.memberLevel > 0) {
        const endDate = new Date(today)
        endDate.setDate(today.getDate() + 364)
        endTime = this.formatDate(endDate)
      }

      this.memberForm = {
        orgId: null,
        orgIdList: [row.orgId], // 单个续费也使用orgIdList
        memberLevel: row.memberLevel,
        memberStartTime: startTime,
        memberEndTime: endTime
      }
      this.memberDialogVisible = true
    },

    // 重置会员表单
    resetMemberForm() {
      this.memberForm = {
        orgId: null,
        orgIdList: [],
        memberLevel: null,
        memberStartTime: '',
        memberEndTime: ''
      }
      this.selectedOrgs = []

      // 重置临时单位选择相关数据
      this.tmpBatchId = null
      this.pendingOrgList = []
      this.pendingOrgTotal = 0
      this.selectedPendingOrgs = []
      this.selectedOrgList = []
      this.selectedOrgTotal = 0
      this.selectedSelectedOrgs = []
      this.tmpOrgQuery.condition.memberKeyword = ''
      this.tmpOrgQuery.pager.page = 1
      this.selectedOrgQuery.condition.memberKeyword = ''
      this.selectedOrgQuery.pager.page = 1
      this.hasSearched = false

      if (this.$refs.memberForm) {
        this.$refs.memberForm.clearValidate()
      }
    },

    // 关闭会员弹窗
    closeMemberDialog() {
      this.memberDialogVisible = false
      this.isBatchRenewal = false
      this.resetMemberForm()
    },

    // 会员等级变化处理
    handleMemberLevelChange(level) {
      if (level && level > 0) {
        // 付费会员，设置默认时限（当前日期开始，+364天结束）
        const today = new Date()
        const endDate = new Date(today)
        endDate.setDate(today.getDate() + 364)

        this.memberForm.memberStartTime = this.formatDate(today)
        this.memberForm.memberEndTime = this.formatDate(endDate)
      } else {
        // 免费会员，清空时限
        this.memberForm.memberStartTime = ''
        this.memberForm.memberEndTime = ''
      }
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 提交会员表单
    submitMemberForm() {
      this.$refs.memberForm.validate(async(valid) => {
        if (valid) {
          this.submitLoading = true
          try {
            const params = { ...this.memberForm }

            if (this.isRenewal) {
              // 续期/升级（统一使用orgIdList，不管单个还是批量）
              await renewalMemberOrg(params)
              if (this.isBatchRenewal) {
                this.$message.success(`批量续期/升级成功，共处理${this.selectedMembers.length}个单位`)
              } else {
                this.$message.success('续期/升级成功')
              }
            } else {
              // 开通会员 - 使用已选单位列表
              params.orgIdList = this.selectedOrgList.map(org => org.orgId)
              if (params.orgIdList.length === 0) {
                this.$message.warning('请先选择要开通会员的单位')
                return
              }
              await addMemberOrg(params)
              this.$message.success(`开通成功`)
            }

            this.closeMemberDialog()
            this.getMemberOrgList()
          } catch (error) {
            this.$message.error(this.isRenewal ? '续期/升级失败' : '开通失败')
          } finally {
            this.submitLoading = false
          }
        }
      })
    },

    // 获取单位列表
    async getOrgList() {
      this.orgLoading = true
      try {
        const response = await findOrganListPage(this.orgQuery)
        this.orgList = response.records || []
        this.orgTotal = response.total || 0
      } catch (error) {
        this.$message.error('获取单位列表失败')
      } finally {
        this.orgLoading = false
      }
    },

    // 单位分页处理
    handleOrgPagination(pagination) {
      this.orgQuery.pager = pagination
      this.getOrgList()
    },

    // 单位选择变化（保留原有方法以兼容现有逻辑）
    handleOrgSelectionChange(selection) {
      this.selectedOrgs = selection

      // 触发表单验证
      this.$nextTick(() => {
        if (this.$refs.memberForm) {
          this.$refs.memberForm.validateField('orgIdList')
        }
      })
    },

    // 触发表单验证（用于临时单位选择）
    triggerFormValidation() {
      this.$nextTick(() => {
        if (this.$refs.memberForm) {
          this.$refs.memberForm.validateField('orgIdList')
        }
      })
    },

    // 重置单位查询
    resetOrgQuery() {
      this.orgQuery.condition.keyword = ''
      this.orgQuery.pager.page = 1
      this.getOrgList()
    },

    // 查看开通历史
    async viewHistory(row) {
      this.currentOrgId = row.orgId
      this.historyDialogVisible = true
      this.historyLoading = true

      try {
        const response = await getActiveLog(row.orgId)
        this.historyList = response || []
      } catch (error) {
        this.$message.error('获取开通历史失败')
      } finally {
        this.historyLoading = false
      }
    },

    // 删除会员
    deleteMember(row) {
      this.$confirm('确定要删除该单位的会员信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await deleteMemberOrg(row.orgId)
          this.$message.success('删除成功')
          this.getMemberOrgList()
        } catch (error) {
          this.$message.error('删除失败')
        }
      }).catch(() => {
        // 用户取消删除
      })
    },

    // ==================== 临时单位选择相关方法 ====================

    // 初始化临时单位选择功能
    async initTmpOrgSelection() {
      try {
        // 重置搜索标志
        this.hasSearched = false

        // 获取临时单位查询批次ID
        const batchId = await getTmpOrgBatchId()
        this.tmpBatchId = batchId

        // 设置批次ID到查询条件中
        this.tmpOrgQuery.condition.batchId = batchId
        this.selectedOrgQuery.condition.batchId = batchId

        // 加载待选和已选单位列表
        await Promise.all([
          this.getPendingOrgList(),
          this.getSelectedOrgList()
        ])
      } catch (error) {
        this.$message.error('初始化单位选择功能失败')
        console.error('初始化临时单位选择失败:', error)
      }
    },

    // 获取待选单位列表
    async getPendingOrgList() {
      this.pendingOrgLoading = true
      try {
        const response = await tmpSelectOrgList(this.tmpOrgQuery)
        this.pendingOrgList = response.records || []
        this.pendingOrgTotal = response.total || 0
      } catch (error) {
        this.$message.error('获取待选单位列表失败')
        console.error('获取待选单位列表失败:', error)
      } finally {
        this.pendingOrgLoading = false
      }
    },

    // 获取已选单位列表
    async getSelectedOrgList() {
      this.selectedOrgLoading = true
      try {
        const response = await tmpSelectOrgList(this.selectedOrgQuery)
        this.selectedOrgList = response.records || []
        this.selectedOrgTotal = response.total || 0

        // 更新selectedOrgs以兼容现有逻辑
        this.selectedOrgs = this.selectedOrgList

        // 触发表单验证
        this.triggerFormValidation()
      } catch (error) {
        this.$message.error('获取已选单位列表失败')
        console.error('获取已选单位列表失败:', error)
      } finally {
        this.selectedOrgLoading = false
      }
    },

    // 添加单位到已选列表
    async addTmpOrg(org) {
      try {
        await addOrDelTmpSelectOrg({
          batchId: this.tmpBatchId,
          operateType: 1, // 1-添加
          orgIdList: [org.orgId]
        })

        this.$message.success('添加成功')

        // 刷新两个列表
        await Promise.all([
          this.getPendingOrgList(),
          this.getSelectedOrgList()
        ])
      } catch (error) {
        this.$message.error('添加失败')
        console.error('添加单位失败:', error)
      }
    },

    // 从已选列表删除单位
    async removeTmpOrg(org) {
      try {
        await addOrDelTmpSelectOrg({
          batchId: this.tmpBatchId,
          operateType: 2, // 2-删除
          orgIdList: [org.orgId]
        })

        this.$message.success('删除成功')

        // 刷新两个列表
        await Promise.all([
          this.getPendingOrgList(),
          this.getSelectedOrgList()
        ])
      } catch (error) {
        this.$message.error('删除失败')
        console.error('删除单位失败:', error)
      }
    },

    // 搜索临时单位
    searchTmpOrg() {
      // 同时搜索待选和已选列表
      this.tmpOrgQuery.pager.page = 1
      this.selectedOrgQuery.pager.page = 1
      this.selectedOrgQuery.condition.memberKeyword = this.tmpOrgQuery.condition.memberKeyword

      // 标记已执行搜索
      this.hasSearched = true

      Promise.all([
        this.getPendingOrgList(),
        this.getSelectedOrgList()
      ])
    },

    // 重置临时单位查询
    resetTmpOrgQuery() {
      this.tmpOrgQuery.condition.memberKeyword = ''
      this.tmpOrgQuery.pager.page = 1
      this.selectedOrgQuery.condition.memberKeyword = ''
      this.selectedOrgQuery.pager.page = 1

      // 重置搜索标志
      this.hasSearched = false

      Promise.all([
        this.getPendingOrgList(),
        this.getSelectedOrgList()
      ])
    },

    // 待选单位分页处理
    handlePendingOrgPagination(pagination) {
      this.tmpOrgQuery.pager = pagination
      this.getPendingOrgList()
    },

    // 已选单位分页处理
    handleSelectedOrgPagination(pagination) {
      this.selectedOrgQuery.pager = pagination
      this.getSelectedOrgList()
    },

    // ==================== 批量选择相关方法 ====================

    // 待选单位表格选择变化
    handlePendingOrgSelectionChange(selection) {
      this.selectedPendingOrgs = selection
    },

    // 已选单位表格选择变化
    handleSelectedOrgSelectionChange(selection) {
      this.selectedSelectedOrgs = selection
    },

    // 批量添加选中的待选单位
    async batchAddSelectedOrgs() {
      if (this.selectedPendingOrgs.length === 0) {
        this.$message.warning('请先选择要添加的单位')
        return
      }

      try {
        const orgIds = this.selectedPendingOrgs.map(org => org.orgId)
        await addOrDelTmpSelectOrg({
          batchId: this.tmpBatchId,
          operateType: 1, // 1-添加
          orgIdList: orgIds
        })

        this.$message.success(`批量添加成功，共处理${orgIds.length}个单位`)

        // 刷新两个列表
        await Promise.all([
          this.getPendingOrgList(),
          this.getSelectedOrgList()
        ])
      } catch (error) {
        this.$message.error('批量添加失败')
        console.error('批量添加单位失败:', error)
      }
    },

    // 批量删除选中的已选单位
    async batchRemoveSelectedOrgs() {
      if (this.selectedSelectedOrgs.length === 0) {
        this.$message.warning('请先选择要删除的单位')
        return
      }

      try {
        const orgIds = this.selectedSelectedOrgs.map(org => org.orgId)
        await addOrDelTmpSelectOrg({
          batchId: this.tmpBatchId,
          operateType: 2, // 2-删除
          orgIdList: orgIds
        })

        this.$message.success(`批量删除成功，共处理${orgIds.length}个单位`)

        // 刷新两个列表
        await Promise.all([
          this.getPendingOrgList(),
          this.getSelectedOrgList()
        ])
      } catch (error) {
        this.$message.error('批量删除失败')
        console.error('批量删除单位失败:', error)
      }
    },

    // 跨页全部添加（当使用memberKeyword搜索时）
    async batchAddAllOrgs() {
      this.$confirm('确定要添加所有搜索结果中的单位吗？此操作将跨越所有页面。', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await addOrDelTmpSelectOrg({
            batchId: this.tmpBatchId,
            operateType: 1, // 1-添加
            orgIdList: [], // 传空数组表示跨页全部操作
            memberKeyword: this.tmpOrgQuery.condition.memberKeyword
          })

          this.$message.success('跨页全部添加成功')

          // 刷新两个列表
          await Promise.all([
            this.getPendingOrgList(),
            this.getSelectedOrgList()
          ])
        } catch (error) {
          this.$message.error('跨页全部添加失败')
          console.error('跨页全部添加失败:', error)
        }
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 跨页全部删除（当使用memberKeyword搜索时）
    async batchRemoveAllOrgs() {
      this.$confirm('确定要删除所有搜索结果中的单位吗？此操作将跨越所有页面。', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await addOrDelTmpSelectOrg({
            batchId: this.tmpBatchId,
            operateType: 2, // 2-删除
            orgIdList: [], // 传空数组表示跨页全部操作
            memberKeyword: this.tmpOrgQuery.condition.memberKeyword
          })

          this.$message.success('跨页全部删除成功')

          // 刷新两个列表
          await Promise.all([
            this.getPendingOrgList(),
            this.getSelectedOrgList()
          ])
        } catch (error) {
          this.$message.error('跨页全部删除失败')
          console.error('跨页全部删除失败:', error)
        }
      }).catch(() => {
        // 用户取消操作
      })
    }
  }
}
</script>

<style scoped>
.search-column {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

.search-column .fl {
  display: flex;
  align-items: center;
}

.search-column .fr {
  display: flex;
  align-items: center;
}

.search-column__item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.search-column__item:first-child {
  margin-left: 0;
}

.search-column__label {
  margin-right: 8px;
  white-space: nowrap;
}

.search-column__inner {
  width: 200px;
}

.dialog-footer {
  text-align: right;
}

.warn-tips {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}
</style>
