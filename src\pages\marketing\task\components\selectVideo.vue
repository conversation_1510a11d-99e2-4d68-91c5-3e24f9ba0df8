<template>
  <el-dialog
    title="选择视频素材"
    :visible.sync="dialogVisible"
    top="60px"
    width="1200px"
    center
    :before-close="handleClose"
  >
    <div class="list">
      <div class="screen">
        <el-input
          v-model="listQuery.condition.videoName"
          class="group"
          placeholder="视频名称"
          @change="search"
        >
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="search" />
        </el-input>
      </div>
      <div class="table">
        <ul>
          <li v-for="item in list" :key="item.videoFileId">
            <div class="cover">
              <el-image :src="item.coverUrl" />
              <i v-if="!['UploadSucc', 'Transcoding'].includes(item.status)" class="el-icon-video-play" @click="play(item)" />
              <p v-else>转码中...</p>
            </div>
            <div class="desc">
              <p>{{ item.name }}
              </p>
              <div v-if="!['UploadSucc', 'Transcoding'].includes(item.status)">
                <span>
                  时长: {{ item.duration | formatSeconds }}
                  <br>
                  大小: {{ (item.size / 1024 / 1024).toFixed(2) }}MB
                </span>
                <el-radio v-if="!['UploadSucc', 'Transcoding'].includes(item.status)" v-model="videoFileId" :label="item.videoFileId">&nbsp;</el-radio>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
      <el-dialog
        title="视频预览"
        :visible.sync="playDialogVisible"
        width="50%"
        center
        append-to-body
      >
        <video-play :video-file-id.sync="videoFileId" :width="'900px'" :height="'500px'" :del="false" />
      </el-dialog>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose()">取 消</el-button>
      <el-button type="primary" @click="handleClose('confirm')">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { promoteVideoList } from '@/api/marketing/videoMaterial'
import Pagination from '@/components/Pagination'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import { formatSeconds } from '@/utils/index'
export default {
  components: {
    Pagination,
    VideoPlay
  },
  filters: {
    formatSeconds
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listQuery: {
        condition: {
          videoName: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      list: [],
      total: 0,
      editValue: '',
      videoInfoId: '',
      playDialogVisible: false,
      videoFileId: ''
    }
  },
  mounted() {
    this.getVideoList()
  },
  methods: {
    handleClose(e) {
      this.$emit('update:dialogVisible', false)
      if (e) {
        this.$emit('update:videoFileId', this.videoFileId)
      }
    },
    getVideoList() {
      promoteVideoList(this.listQuery).then(res => {
        this.list = res.records
        this.total = res.total
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getVideoList()
    },
    search() {
      this.listQuery.pager.page = 1
      this.getVideoList()
    },
    play(item) {
      this.playDialogVisible = true
      this.videoFileId = item.videoFileId
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    margin-bottom: 20px;
    .el-input {
      width: 350px;
    }
  }
  .pagination-container {
    margin: 0 auto;
    padding: 0;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    background-color: #f9f9f9;
  }
  .table {
    ul {
      margin: 0;
      padding: 0;
      display: flex;
      flex-wrap: wrap;
      li {
        width: 23%;
        margin-right: 30px;
        border: 1px solid #efefef;
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        &:nth-child(4n) {
          margin-right: 0;
        }
        &:hover {
          box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        .cover {
          width: 100%;
          height: 120px;
          .el-image {
            width: 100%;
            height: 100%;
          }
          position: relative;
          cursor: pointer;
          i {
            font-size: 50px;
            color: #fff;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
          p {
            position: absolute;
            margin: 0;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #eee;
            font-size: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .desc {
          padding: 10px;
          color: #666;
          font-size: 14px;
          p {
            margin: 0 0 10px;
            color: #333;
            font-size: 16px;
          }
          div {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
        }
      }
    }
  }
}
</style>
