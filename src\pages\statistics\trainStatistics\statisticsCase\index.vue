<template>
  <div class="statisContainer">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-date-picker
          v-model="timeRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          value-format="yyyyMM"
          :clearable="false"
          :picker-options="pickerOptions"
          @change="changePicker"
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="condition.identityIds"
          placeholder="全部用户"
          :options="identityTree"
          collapse-tags
          :props="{
            multiple: true,
            value:'identityId',
            label:'name',
            children:'childList',
            emitPath: false
          }"
          clearable
          @change="identityChange"
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          ref="cascader1"
          v-model="areaId"
          placeholder="请选择区域"
          :options="areaList"
          collapse-tags
          :show-all-levels="false"
          :props="{
            value:'areaId',
            label:'name',
            children:'childList',
            checkStrictly:true
          }"
          filterable
          @change="handlerChange"
        />
      </div>
    </div>
    <!-- top -->
    <div>
      <div v-if="topShow===1" class="topEmpty">数据加载中...</div>
      <Top v-else-if="topShow===2" :data="data" :has-time="false" />
      <div v-else class="topEmpty">暂无数据</div>
    </div>
    <!-- type -->
    <div class="typeRow">
      <el-radio-group v-model="type">
        <el-radio-button :label="1">用户数</el-radio-button>
        <el-radio-button :label="2">培训人数</el-radio-button>
        <el-radio-button :label="3">培训人次</el-radio-button>
        <el-radio-button :label="4">培训任务数</el-radio-button>
        <el-radio-button :label="5">培训时长</el-radio-button>
        <el-radio-button :label="6">参与人次</el-radio-button>
        <el-radio-button :label="7">通过人次</el-radio-button>
        <el-radio-button :label="8">培训覆盖率</el-radio-button>
        <el-radio-button :label="9">培训参与率</el-radio-button>
        <el-radio-button :label="10">培训通过率</el-radio-button>
        <el-radio-button :label="11">参与达标率</el-radio-button>
        <el-radio-button :label="12">活跃用户数</el-radio-button>
        <el-radio-button :label="13">活跃率</el-radio-button>
      </el-radio-group>
    </div>
    <!-- charts -->
    <div style="display:flex">
      <div class="area">
        <Area ref="area" :area-id="areaId" :area-type="condition.areaType" :start-time="condition.startTime" :end-time="condition.endTime" :identity-ids="condition.identityIds" :type="type" />
      </div>
      <div>
        <div v-if="identShow" class="ident">
          <Ident :query="identQuery" />
        </div>
        <div v-else class="ident">
          <Major :query="identQuery" />
        </div>
        <div class="academic">
          <Academic :query="identQuery" />
        </div>
      </div>
    </div>

    <!-- 趋势 -->
    <div class="trend">
      <Trend ref="trend" :query="identQuery" />
    </div>

    <!-- 趋势对比 -->
    <div class="trend">
      <TrendContrast ref="trendContrast" :query="identQuery" />
    </div>

  </div>
</template>

<script>
import { getAreaTree } from '@/api/area'
import Top from './components/top.vue'
import Area from './components/area.vue'
import Ident from './components/ident.vue'
import Major from './components/major.vue'
import Academic from './components/academic.vue'
import Trend from '../components/trend.vue'
import TrendContrast from '../components/trendContrast.vue'
import { identityTreeList } from '@/api/category' // 身份树
import request from '@/api/dataStatistics/statisticsCase'
export default {
  name: 'StatisticsCase',
  components: { Top, Area, Trend, TrendContrast, Ident, Major, Academic },
  data() {
    const _this = this
    return {
      identQuery: {
        areaId: '0',
        areaType: 1,
        startTime: '',
        endTime: '',
        identityIds: [],
        type: 1
      },
      info: [],
      request,
      identityTree: [],
      timeRange: [],
      duration: [],
      pickerOptions: {
        disabledDate(time) {
          if (typeof (_this.duration[0]) === 'undefined') {
            return time.getTime() > Date.now()// 禁止选择以后的时间
          } else {
            const currentTime = _this.duration[0]
            const threeMonths = 60 * 60 * 1000 * 24 * 31 * 11
            if (currentTime) {
              return ((time.getTime() > currentTime.getTime() + threeMonths || time.getTime() < currentTime.getTime() - threeMonths) || time.getTime() > Date.now())
    	      }
          }
        },
        onPick({ minDate, maxDate }) {
          // 当第一时间选中才设置禁用
          if (minDate && !maxDate) {
            _this.duration[0] = minDate
          }
          if (maxDate) {
            _this.duration[1] = maxDate
          }
        }
      },
      condition: {
        identityIds: [],
        areaType: 1,
        areaId: '0',
        startTime: '',
        endTime: ''
      },
      data: {},
      hasTime: false,
      areaList: [],
      areaId: ['0'],
      type: 1,
      identShow: true,
      topShow: 1
    }
  },
  watch: {
    areaId() { // 关闭级联下拉框
      if (this.$refs.cascader1) {
        this.$refs.cascader1.dropDownVisible = false
      }
    },
    type: {
      handler() {
        this.setQuery()
        this.getList()
      }
    }
  },
  created() {
    // 获取身份树
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      const obj = {
        name: '无',
        identityId: '0'
      }
      newArr.push(obj)
      this.identityTree = newArr
    })
    // 获取区域树并添加全国选项
    getAreaTree().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      const obj = {
        name: '全国',
        areaId: '0'
      }
      newArr.unshift(obj)
      this.areaList = newArr
    })
    this.getDate()
    this.setQuery()
    this.getList()
  },
  methods: {
    setQuery() {
      const params = this.copy(this.condition)
      this.identQuery.areaId = params.areaId
      this.identQuery.areaType = params.areaType
      this.identQuery.startTime = params.startTime
      this.identQuery.endTime = params.endTime
      this.identQuery.identityIds = params.identityIds
      this.identQuery.type = this.type
    },
    // 获取top组件的数据
    getList() {
      this.topShow = 1
      const params = this.copy(this.condition)
      if (params.identityIds.length === 0) {
        params.identityId = -1
      }
      this.request.list(params).then(res => {
        if (JSON.stringify(res) === '{}') {
          this.topShow = 3
          return
        } else {
          this.topShow = 2
        }
        this.data = res
        if (this.condition.endTime) {
          this.hasTime = true
        }
      })
    },
    // 区域级联change事件
    handlerChange(e) {
      if (e) {
        this.condition.areaId = e[e.length - 1]
        if (e[0] == '0') {
          this.condition.areaType = 1
        } else {
          const arr = [1, 2, 3, 4]
          this.condition.areaType = arr[e.length]
        }
      }
      this.setQuery()
      this.getList()
    },
    identityChange(e) {
      if (e.length === 0 || e.length > 1) {
        this.identShow = true // 显示身份
        this.setQuery()
      } else if (e.length === 1) {
        this.identShow = false // 显示专科
        this.setQuery()
      }
      this.getList()
    },
    // 日期选择器
    changePicker(data) {
      this.condition.startTime = data[0]
      this.condition.endTime = data[1]
      this.setQuery()
      this.getList()
    },
    // 默认显示本月及上月
    getDate() {
      const myDate = new Date()
      const year = myDate.getFullYear() // 获取当前年份 (2021)
      const month = myDate.getMonth() // 获取当前月份 (0-11)
      const monthNew = month + 1 < 11 ? '0' + month : '' + month
      this.condition.startTime = year + monthNew
      this.condition.endTime = year + monthNew
      this.timeRange.push(this.condition.startTime)
      this.timeRange.push(this.condition.endTime)
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>

<style lang="scss" scoped>
.statisContainer{
  background: #F1F5F7;
  position: relative;
  height: calc(100vh - 50px);
  overflow-y: scroll;
  padding: 0 20px;

  .search-column{
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 100;
    background: #F1F5F7;
    margin-left: -6px;
    margin-right: -6px;
  }

  .topEmpty{
    text-align: center;
    height: 50px;
    line-height: 40px;
    color: #666;
    font-size: 20px;
  }

  .typeRow{
    position: -webkit-sticky;
    position: sticky;
    top: 70px;
    z-index: 100;
    padding-bottom: 20px;
    margin-bottom: 4px;
    margin-left: -6px;
    margin-right: -6px;
    background: #F1F5F7;
  }

  .area{
    width: 986px;
    height: 740px;
    margin-right: 20px;
  }

  .ident{
    width: 650px;
    height: 300px;
    margin-bottom: 80px;
  }

  .academic{
    width: 650px;
    height: 300px;
  }

  .trend{
    width: 1677px;
  }
}
</style>
