<template>
  <div class="app-container">
    <template v-if="!isEditPut">
      <h2>活动信息</h2>
      <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="140px" class="form">
        <el-form-item label="广告主" prop="advertiserId">
          <el-button v-if="form.advertiserName === ''" type="primary" @click="selectOrgVisible = true">选择广告主</el-button>
          <select-org
            :dialog-visible.sync="selectOrgVisible"
            @checked="checkedOrg"
          />
          <el-tag v-if="form.advertiserName !== ''" closable @close="clearOrg">{{ form.advertiserName }}</el-tag>
        </el-form-item>
        <el-form-item label="活动名称" prop="title">
          <el-input v-model="form.title" clearable maxlength="30" />
        </el-form-item>
        <el-form-item label="活动时间" prop="startTime">
          <!-- <el-date-picker
            v-model="timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            :picker-options="pickerOptions"
            :clearable="false"
            value-format="yyyy-MM-dd"
            end-placeholder="结束日期"
            @change="onTimeChange"
          /> -->
          <el-date-picker v-model="form.startTime" :disabled="isProcessing" value-format="yyyy-MM-dd" type="date" placeholder="开始日期" :picker-options="pickerOptions0" /> -
          <el-date-picker v-model="form.endTime" :disabled="isProcessing" value-format="yyyy-MM-dd" type="date" placeholder="结束日期" :picker-options="pickerOptions1" />
        </el-form-item>
        <el-form-item label="活动封面" prop="coverId">
          <!-- 上传 -->
          <singleImage v-model="form.coverId" type="activity_img" width="300px" height="200px" :disabled="readOnly" :url.sync="form.coverUrl" />
        </el-form-item>
        <el-form-item label="每天答题限制次数" prop="answerNum">
          <el-input-number v-model="form.answerNum" :min="0" label="描述文字" @change="onNumChange" />
        </el-form-item>
        <el-form-item label="活动内容" required>

          <div style="width: 700px; line-height: 1.2em;padding-bottom: 10px">
            <el-alert type="info" show-icon :closable="false">
              <pre slot="title">活动内容为多张图片纵向平铺，若区域可让用户点击链接或视频答题，则需要选择链接或关联活动视频
若选择链接，可支持苹果手机访问苹果链接，安卓手机访问安卓链接
链接：
活动底图会在活动详情页面底部纵向平铺展示
--若安卓和苹果链接一样，可只录入一个链接
--若安卓和苹果链接不一样，可录入两个链接，
安卓链接：“Android-”+“http://或https://链接”，
苹果链接：“ios-”+“http://或https://链接”，两个链接以“|”分隔</pre>
            </el-alert>
          </div>
          <!-- <el-tag type="success"><i class="el-icon-warning" /> 活动内容为多张图片纵向平铺，若区域可让用户点击链接或视频答题，则需要选择链接或关联活动视频</el-tag> -->
          <el-form-item
            v-for="(item, index) in form.contentList"
            :key="item.key"
            size="mini"
            :prop="'contentList.' + index + '.picId'"
            :rules="{ required: true, message: '内容图片不能为空', trigger: 'blur' }"
            style="margin:14px 0;"
          >
            <div class="tr">
              <singleImage v-model="item.picId" type="activity_img" width="150px" height="150px" :url.sync="item.picUrl" />
              <div>
                链接:
                <el-select v-model="item.linkType" placeholder="请选择内容类型" style="width:100px;">
                  <el-option v-for="v in contentType" :key="v.value" :label="v.label" :value="v.value" />
                </el-select>
              </div>
              <el-input v-if="item.linkType===2" v-model="item.linkAddress" autosize type="textarea" style="width:180px;" clearable placeholder="请输入链接地址" />
              <el-button :disabled="index<=0" type="text" @click.prevent="removeContent(item)">删除</el-button>
            </div>
          </el-form-item>
          <el-button plain icon="el-icon-plus" style="width:100%;" @click="addContent">添加</el-button>
        </el-form-item>
        <el-form-item label="活动底图" prop="backId">
          <div style="width: 700px; line-height: 1.2em;padding-bottom: 10px">
            <el-alert type="info" show-icon :closable="false">
              <pre slot="title">活动底图会在活动详情页面底部纵向平铺展示</pre>
            </el-alert>
          </div>
          <!-- <el-tag type="success"><i class="el-icon-warning" /> 活动底图会在活动详情页面底部纵向平铺展示</el-tag> -->
          <singleImage v-model="form.backId" type="activity_img" width="300px" height="200px" :url.sync="form.backUrl" :disabled="isProcessing" />
        </el-form-item>
        <el-form-item label="活动奖项" prop="prizeList" size="mini">
          <div class="t-head">
            <span v-for="item in thead" :key="item" class="td">{{ item }}</span>
          </div>
          <div v-for="(award, index) in form.prizeList" :key="award.key" style="margin:14px 0;">
            <div class="tr">
              <span>{{ han[index] }}等奖</span>
              <el-input v-model="award.name" style="width:120px;" maxlength="30" placeholder="请输入奖品名称" />
              <el-input-number v-model="award.num" :min="1" style="width:90px;" @change="onAwardNumChange" />
              <el-button :disabled="index<=0" type="text" @click.prevent="removeAwards(award)">删除</el-button>
            </div>
          </div>
          <el-button plain icon="el-icon-plus" style="width:100%;" @click="addAwards">添加</el-button>
        </el-form-item>
        <el-form-item label="活动视频" required>
          <div v-if="!selectVideo" class="box">
            <select-video v-model="selectVideo" class="box-select" :disabled="isProcessing" @choose="handleSelect">
              <p>请选择视频</p>
            </select-video>
            <videoUpload @upInfo="handleSelect" />
          </div>
          <div v-if="selectVideo" class="video-wrapper">
            <div class="video-container">
              <!-- 播放器 -->
              <ali-player ref="aliplayer" v-bind="aliPlayerConfig" @ready="handleReadyVideo" @timeupdate="hanleTimeupdate" />
              <el-button :disabled="isProcessing" type="primary" class="change-btn" size="mini" @click="changeVideo">更改视频</el-button>
              <el-button :disabled="isProcessing" class="insert-btn" size="mini" @click="insertPaper('add')">插入试题</el-button>
              <el-button :disabled="isProcessing" type="primary" class="change-btn" size="mini" @click="changeVideo">更改视频</el-button>
            </div>
            <!-- 试题组 -->
            <template v-if="form.videoList.length">
              <transition-group appear :css="false" @before-enter="beforeEnter" @enter="enter" @after-enter="afterEnter">
                <div v-for="(paperItem,paperIndex) in form.videoList[0].paperGroupList" :key="paperIndex" class="exam-item">
                  <div class="item-hd">
                    <span class="item-tit">{{ paperIndex + 1 }}. &nbsp;{{ paperItem.name }}</span>
                    <span class="insert-time">插入时间：{{ formatSeconds(paperItem.timeSpot) }}</span>
                    <span>答题时间：{{ paperItem.finishTime }}秒</span>
                    <span class="exam-tip">
                      <i class="el-icon-info" />提示：为防止作弊, 系统会打乱试题的选项顺序呈现给学员
                    </span>
                    <div class="exam-opr fr">
                      <el-button :disabled="isProcessing" type="primary" icon="el-icon-plus" class="add-btn" size="mini" @click="addExamItem(paperIndex, paperItem)">添加考题</el-button>
                      <el-button :disabled="isProcessing" class="group-btn" type="primary" icon="el-icon-edit-outline" @click="insertPaper('edit', paperItem,paperIndex)" />
                      <el-button :disabled="isProcessing" class="group-btn" type="danger" icon="el-icon-delete" @click="deletePaper(paperItem,paperIndex)" />
                      <i class="group-collapse pointer" :class="paperIndex === paperShowIndex ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="showPaperList(paperIndex)" />
                    </div>
                  </div>
                  <!-- 考题  -->
                  <div v-show="paperIndex === paperShowIndex">
                    <div v-for="(examItem, examIndex) in paperItem.paperQuestionList" :key="examIndex" class="item-bd">
                      <div class="exam-content">
                        <h4>
                          考题{{ examIndex + 1 }}. {{ examItem.title }}
                          <span class="exam-type">({{ examItem.type | examTypeFil }})</span>
                          <div v-if="!isProcessing" class="fr exam-opr">
                            <i class="el-icon-edit-outline pointer" @click="editExamItem(examItem, examIndex, paperIndex)" />
                            <i class="el-icon-delete pointer" @click="delExamItem(paperItem, examIndex)" />
                          </div>
                        </h4>
                        <!-- 选项列表 -->
                        <p v-for="(optionItem, optionIndex) in examItem.paperItems" :key="optionIndex">
                          <i v-if="optionItem.answerYesNo" class="el-icon-check" />
                          {{ optionArr[optionIndex] }}、{{ optionItem.content }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </transition-group>
            </template>
          </div>
        </el-form-item>
        <el-form-item label="考核通过得分" prop="passScore">
          <el-input-number v-model="form.passScore" />
        </el-form-item>
        <el-form-item label="活动对象" required>
          <el-radio v-model="form.isRestrict" :disabled="isProcessing" :label="0">全部</el-radio>
          <el-radio v-model="form.isRestrict" :disabled="isProcessing" :label="1">部分</el-radio>
        </el-form-item>
      </el-form>
    </template>
    <!-- 投放对象 -->
    <template v-if="form.isRestrict === 1">
      <h2>活动对象</h2>
      <user-list v-if="form.activityId !== ''" ref="userList" :read-only="readOnly || isProcessing" :activity-id="form.activityId" />
    </template>

    <div v-if="!isEditPut" class="action">
      <el-button @click="resetForm">{{ readOnly?'返回':'取消' }}</el-button>
      <el-button v-if="!readOnly" type="primary" @click="submit('form')">保存</el-button>
    </div>

    <!-- 插入试题 -->
    <dialog-insert
      v-if="!isEditPut"
      ref="dialogInsert"
      :is-add="isAddPaperGroup"
      :player-duration-time="playerDurationTime"
      :video-dto="videoDto"
      :edit-paper-index="editPaperIndex"
      @cancel="insertCancel"
      @save="paperSave"
    />
    <!-- 题目 -->
    <dialog-test v-if="!isEditPut" ref="dialogTest" :form-data="testDto" :is-edit="isEditPaperItem" @cancelPaperItem="cancelPaperItem" @savePaperItem="savePaperItem" />
  </div>
</template>

<script>
import selectVideo from '@/components/SelectContent/Video.vue'
import singleImage from '@/components/SingleImage/index.vue'
import AliPlayer from '@/components/Aliplayer/index.vue'
import { formatSeconds } from '@/utils'
import request from '@/api/activity'
import { initPlayerConfig } from '@/components/Aliplayer/aliplayerConfig'
import dialogInsert from './components/dialogInsert.vue'
import dialogTest from './components/dialogTest.vue'
import userList from '@/components/PublishUserList/index.vue'
import { setPaperOptionList } from '@/utils'
import selectOrg from '@/pages/operationManage/ad/components/selectOrg.vue'
import videoUpload from '@/components/Upload/videoUpload.vue'
import { getVideoPreview } from '@/api/biz'

export default {
  name: 'AddActivity',
  components: {
    selectVideo,
    AliPlayer,
    dialogInsert,
    dialogTest,
    userList,
    singleImage,
    selectOrg,
    videoUpload
  },
  filters: {
    examTypeFil: function(type) {
      switch (type) {
        case 'RadioButton':
          return '单选题'
        case 'RadioJudge':
          return '判断题'
        case 'CheckBox':
          return '多选题'
      }
    }
  },
  data() {
    var checkAward = (rule, value, callback) => {
      const flag = this.form.prizeList.some(v => !v.name || !v.num)
      if (flag) {
        callback(new Error('请完善奖项信息'))
      } else {
        callback()
      }
    }
    return {
      isEdit: this.$route.query.isEdit || false, // 是否编辑活动
      isEditPut: this.$route.query.isEditPut || false, // 是否编辑投放
      isProcessing: !!this.$route.query.isProcessing, // 当前操作数据是否下架
      readOnly: !!this.$route.query.readOnly, // 是否查看
      // form
      adOrgList: [],
      selectOrgVisible: false,
      form: {
        activityId: '', // 预创建接口返回
        advertiserId: '',
        advertiserName: '',
        title: '',
        coverId: '',
        backId: '',
        answerNum: 0,
        startTime: '',
        endTime: '',
        isRestrict: 0,
        videoList: [],
        scopeConditions: [],
        prizeList: [
          { name: '', num: 1 },
          { name: '', num: 1 },
          { name: '', num: 1 }
        ],
        contentList: [
          {
            picId: '',
            linkType: 0,
            linkAddress: ''
          }
        ],
        passScore: 0
      },
      rules: {
        title: [{ required: true, message: '请填写活动名称', trigger: 'blur' }],
        prizeList: [{ validator: checkAward, trigger: 'blur' }],
        startTime: [
          { required: true, message: '请选择活动时间', trigger: 'change' }
        ],
        answerNum: [
          { required: true, message: '请填写答题次数', trigger: 'blur' }
        ],
        coverId: [
          { required: true, message: '请上传活动封面', trigger: 'change' }
        ],
        backId: [
          { required: true, message: '请上传活动底图', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请上传活动内容图片', trigger: 'change' }
        ],
        passScore: [
          { required: true, message: '请填写通过得分', trigger: 'blur' }
        ]
      },
      pickerOptions0: {
        disabledDate: time => {
          if (this.form.endTime) {
            if (
              new Date(this.form.endTime).getDate() === new Date().getDate()
            ) {
              return (
                time.getTime() >
                  new Date(this.form.endTime).getTime() -
                    1 * 24 * 60 * 60 * 1000 || time.getTime() < Date.now()
              )
            } else {
              return (
                time.getTime() >
                  new Date(this.form.endTime).getTime() -
                    1 * 24 * 60 * 60 * 1000 ||
                time.getTime() < Date.now() - 8.64e7
              )
            }
          } else {
            return time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      pickerOptions1: {
        disabledDate: time => {
          if (this.form.startTime) {
            return time.getTime() < new Date(this.form.startTime)
          } else {
            return time.getTime() < Date.now() - 8.64e7
          }
        }
      },
      // timeRange: [],
      contentType: [
        { label: '无', value: 0 },
        { label: '视频', value: 1 },
        { label: '链接', value: 2 }
      ],
      optionArr: setPaperOptionList(),
      thead: ['奖项', '奖品名称', '奖品数量', '操作'],
      han: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'],
      // upload
      noneBtnImg: false,
      uploadHeaders: { token: this.$store.getters.token },
      uploadData: { data: '' },
      dialogImageUrl: '',
      dialogVisible: false,
      // video
      // videoFlag: false,
      // videoUploadPercent: 0,
      // videoForm: {
      //   video: ''
      // },
      selectVideo: '', // 选中活动视频
      /* 插入试题相关 */
      selectableRange: {},
      paperShowIndex: 999, // 当前展开试题下标
      editPaperIndex: 999, // 当前编辑试题组下标
      editTestIndex: 999, // 当前编辑题目下标
      isEditPaperItem: false, // 是否编辑题目
      isAddPaperGroup: true, // 是否添加试题组
      testDto: {}, // 插入题目对象
      videoDto: {
        timeSpot: formatSeconds(this.playerCurrentTime),
        name: '',
        finishTime: '',
        paperQuestionList: []
      }, // 插入试题对象
      /** Aliplayer相关 */
      aliPlayerConfig: {},
      player: null, // aliplayer对象
      playerCurrentTime: 0, // aliplayer 当前时间
      playerDurationTime: 0 // alipalyer 总时间
    }
  },
  created() {
    const {
      isEdit = false,
      readOnly = false,
      isEditPut = false
    } = this.$route.query

    if (isEdit || readOnly) {
      // 编辑/查看
      this.$route.meta.title = isEdit ? '编辑活动' : '查看活动'
      const { id } = this.$route.query
      request.detail(id).then(res => {
        this.form = res
        this.timeRange = [res.startTime, res.endTime]
        this.initPlayer()
      })
    } else if (isEditPut) {
      // 编辑投放对象
      request.detail(this.$route.query.id).then(res => {
        this.form = res
      })
    } else {
      // 添加
      this.$route.meta.title = '添加活动'
      request.preAdd().then(res => {
        this.form.activityId = res
      })
    }
  },
  methods: {
    formatSeconds,
    // 广告主
    checkedOrg(v) {
      this.form.advertiserName = v.orgName
      this.form.advertiserId = v.id
    },
    clearOrg() {
      this.form.advertiserName = ''
      this.form.advertiserId = ''
    },
    // form
    onNumChange(v) {
      if (!v) this.form.answerNum = 0
    },
    onTimeChange(v) {
      this.form.startTime = v ? v[0] : ''
      this.form.endTime = v ? v[1] : ''
    },
    resetForm() {
      this.$router.go(-1)
    },
    submit(formName) {
      if (this.isEditPut) return
      // 正常添加编辑
      this.$refs[formName].validate(valid => {
        if (valid) {
          const { videoList } = this.form
          if (!videoList.length) {
            this.$message.error('请选择活动视频')
            return
          }
          if (!videoList[0].paperGroupList.length) {
            this.$message.error('请为视频插入试卷')
            return
          }
          for (const v of videoList[0].paperGroupList) {
            if (!v.paperQuestionList.length) {
              this.$message.error(
                `试卷 ${v.name} 的试题不能为空，请补充试题或删除该试卷`
              )
              return
            }
            for (const sv of v.paperQuestionList) {
              if (!sv.paperItems.length) {
                this.$message.error(
                  `试卷 ${v.name} 的试题 ${sv.title} 的选项不能为空，请补充答案选项`
                )
                return
              }
            }
          }
          // if (!this.$refs.userList.scopeConditions.length) {
          //   this.$message.error('请筛选待选用户列表的投放类型')
          //   return
          // }
          this.save()
        } else {
          return false
        }
      })
    },
    save() {
      !this.isEdit &&
        (this.form.scopeConditions = this.form.isRestrict === 1 ? this.$refs.userList.scopeConditions : [])
      this.form.prizeList.forEach((v, i) => {
        v.level = this.han[i] + '等奖'
      })
      const API = this.isEdit ? 'edit' : 'create'
      this.form.contentList.forEach(i => {
        if (i.linkType === 2) {
          i.linkAddress = i.linkAddress.replace(/[\n\r]/g, '')
        }
      })
      request[API](this.form).then(res => {
        this.$message.success(`${this.isEdit ? '编辑' : '添加'}成功`)
        this.$router.go(-1)
      })
    },
    // 奖项
    onAwardNumChange(v) {
      if (!v) v = 1
    },
    removeAwards(item) {
      var index = this.form.prizeList.indexOf(item)
      if (index !== -1) {
        this.form.prizeList.splice(index, 1)
      }
    },
    removeContent(item) {
      var index = this.form.contentList.indexOf(item)
      if (index !== -1) {
        this.form.contentList.splice(index, 1)
      }
    },
    addAwards() {
      const { length } = this.form.prizeList
      if (length >= 10) {
        this.$message.error('等级最多为10')
        return
      }
      this.form.prizeList.push({
        name: '',
        num: 1,
        key: Date.now()
      })
    },
    addContent() {
      this.form.contentList.push({
        linkType: 0,
        linkAddress: '',
        picId: '',
        key: Date.now()
      })
    },
    handleSuccess(res, file, fileList) {
      const callback = () => {
        this.form.fileList = [
          {
            id: res.data.id,
            url: res.data.url,
            name: res.data.url
          }
        ]
      }
      this.commonSuccess(res, file, fileList, callback)
    },
    handleBgSuccess(res, file, fileList) {
      const callback = () => {
        this.form.bg = [
          {
            id: res.data.id,
            url: res.data.url,
            name: res.data.url
          }
        ]
      }
      this.commonSuccess(res, file, fileList, callback)
    },
    // 公共上传成功回调
    commonSuccess(res, file, fileList, callback) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: action => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        callback()
      }
    },
    handleRemove() {
      this.form.fileList = []
    },
    removeBg() {
      this.form.content = []
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleContentSuccess(res, file, row) {
      row.cover = file.url
    },
    // 奖品图片
    // handleAvatarSuccess(res, file, row) {
    //   row.cover = file.url
    // },
    imgChange(file, fileList) {
      this.noneBtnImg = fileList.length >= 1
    },
    handleImgRemove(file, fileList) {
      this.noneBtnImg = fileList.length >= 1
    },
    /* Alipayer */
    handleSelect(v, flag = true) {
      this.selectVideo = true
      getVideoPreview({ videoFileId: v.videoFileId }).then(res => {
        const config = {
          source: res,
          height: '300px',
          width: '450px'
        }
        initPlayerConfig(config).then(res => {
          if (flag) {
            this.$set(this.form.videoList, 0, {
              name: v.name,
              videoId: v.videoId,
              paperGroupList: []
            })
          }
          this.aliPlayerConfig = res
          this.player = this.$refs.aliplayer
          this.player.init()
        })
      }).catch(() => {
        this.selectVideo = ''
      })
    },
    changeVideo() {
      this.$confirm('确认更换视频吗？确认更换后，试题数据将被清除', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.selectVideo = ''
          this.form.videoList = []
        })
        .catch(console.log)
    },
    handleReadyVideo() {
      this.playerDurationTime = this.player.getDuration()
      const duration = formatSeconds(this.playerDurationTime)
      this.selectableRange = { selectableRange: '00:00:00 - ' + duration }
    },
    hanleTimeupdate() {
      this.playerCurrentTime = Math.floor(this.player.getCurrentTime())
    },
    chapterVideoPlay() {
      this.player.play()
    },
    chapterVideoPause() {
      this.player.pause()
    },
    // 添加考题
    addExamItem(paperIndex, paperItem) {
      if (paperItem.paperQuestionList.length >= 16) {
        this.$message.error('试题添加上限为16道')
        return
      }
      this.testDto = {}
      this.isEditPaperItem = false
      this.editPaperIndex = paperIndex
      this.$refs.dialogTest.visible = true
    },
    // 显示试题的题目
    showPaperList(index) {
      if (index === this.paperShowIndex) {
        this.paperShowIndex = 999
        return
      }
      this.paperShowIndex = index
    },
    // 删除试卷(试题组)
    deletePaper(item, index) {
      this.$confirm('是否删除 "' + item.name + '" 这个试题组？', '确认删除', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          this.form.videoList[0].paperGroupList.splice(index, 1)
        })
        .catch(console.log)
    },
    // 取消插入试题组
    insertCancel() {
      this.chapterVideoPlay()
    },
    // 取消插入题目
    cancelPaperItem() {
      this.testDto = {
        content: '',
        title: '',
        type: null,
        listOrder: 1,
        paperItems: []
      }
    },
    // 题目弹窗保存
    savePaperItem(testDto) {
      if (this.isEditPaperItem) {
        this.$set(
          this.form.videoList[0].paperGroupList[this.editPaperIndex]
            .paperQuestionList,
          this.editTestIndex,
          testDto
        )
      } else {
        this.form.videoList[0].paperGroupList[
          this.editPaperIndex
        ].paperQuestionList.push(testDto)
      }
      // if (this.isEdit) {
      //   // 编辑教程
      //   this.editPaperIndex = 999
      //   this.isEditPaperItem = false
      // } else {
      //   this.isEditPaperItem = true
      // }
      this.$refs.dialogTest.visible = false
    },
    // 插入试题
    insertPaper(type, item, index) {
      if (type === 'add') {
        this.isAddPaperGroup = true
        this.videoDto = {
          // 新增试题组
          timeSpot: formatSeconds(this.playerCurrentTime),
          name: '',
          finishTime: 30,
          paperQuestionList: []
        }
      } else {
        this.editPaperIndex = index
        this.isAddPaperGroup = false
        this.videoDto = {
          ...item,
          timeSpot: formatSeconds(item.timeSpot)
        }
      }
      this.chapterVideoPause()
      this.$refs.dialogInsert.show = true
    },
    // 插入试题弹窗保存
    paperSave(v) {
      if (this.isAddPaperGroup) {
        this.form.videoList[0].paperGroupList.push(v)
      } else {
        this.$set(this.form.videoList[0].paperGroupList, this.editPaperIndex, v)
      }
      this.chapterVideoPlay()
    },
    // 编辑考题
    editExamItem(examItem, examIndex, paperIndex) {
      this.testDto = examItem
      this.isEditPaperItem = true
      this.editPaperIndex = paperIndex
      this.editTestIndex = examIndex
      this.$refs.dialogTest.visible = true
    },
    // 删除题目
    delExamItem(paperItem, examIndex) {
      this.$confirm('您确定要删除此考题吗？', '确认删除', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => {
          paperItem.paperQuestionList.splice(examIndex, 1)
        })
        .catch(console.log)
    },
    // 编辑 查看初始化视频
    initPlayer() {
      this.selectVideo = this.form.videoList[0].videoId
      this.handleSelect(this.form.videoList[0], false)
    }, // js动画钩子 start
    beforeEnter(el) {
      el.classList.add('v-enter', 'v-enter-active')
    },
    enter(el, done) {
      const { delay } = el.dataset
      setTimeout(() => {
        el.classList.remove('v-enter')
        el.classList.add('v-enter-to')
        done()
      }, delay)
    },
    afterEnter(el) {
      el.classList.remove('v-enter-active', 'v-enter-to')
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  .form {
    max-width: 1500px;
    margin: 0 auto 60px;
    padding-top: 40px;
    .line {
      text-align: center;
    }
    %flex-aic-sa {
      display: flex;
      align-items: center;
      justify-content: space-around;
    }
    .t-head {
      background: #eee;
      @extend %flex-aic-sa;
      .td {
        flex: 1;
        text-align: center;
      }
    }
    .tr {
      @extend %flex-aic-sa;
      // justify-content: space-evenly;
    }
    ::v-deep .cover-upload {
      // .el-upload,
      // li {
      //   width: 90px;
      //   height: 90px;
      //   line-height: 90px;
      //   margin-bottom: 0;
      // }
      &.disUoloadSty {
        .el-upload--picture-card {
          display: none; /* 上传按钮隐藏 */
        }
        li {
          margin-right: 0;
        }
      }
      img {
        object-fit: cover;
      }
    }
    .user-list {
      border-radius: 4px;
      overflow: hidden;
      h3 {
        padding-left: 20px;
        background: #eee;
        line-height: 40px;
      }
    }
    .box {
      display: flex;
      width: 100%;
      height: 294px;
      &-select {
        flex: 1
      }
      p {
        flex: 1;
        line-height: 294px;
        text-align: center;
      }
      border: 1px solid #ccc;
    }
    .el-tag {
      margin-bottom: 10px;
    }
    .video-wrapper {
      background: #f2f5f9;
      padding: 30px 20px 30px;
      .video-container {
        width: 450px;
        position: relative;
        min-height: 40px;

        .insert-btn {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: 0;
          z-index: 100;
        }

        .change-btn {
          position: absolute;
          right: -80%;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      // 试题
      .exam-item {
        margin-bottom: 30px;
        .item-hd {
          padding: 10px 0;
          margin-top: 10px;
          &:not(:last-child) {
            border-bottom: 1px solid #e4e4e4;
          }
        }
        .insert-time {
          margin-right: 18px;
        }
        .add-btn {
          margin-right: 18px;
        }
        .group-btn {
          margin-left: 0;
          padding: 4px !important;
          font-size: 14px !important;
        }
        .group-collapse {
          margin-left: 10px;
        }
        .exam-tip {
          margin-left: 20px;
          font-size: 14px;
          i {
            margin-top: -2px;
            font-size: 14px;
            color: #f8b242;
          }
        }
        .item-tit {
          font-weight: bold;
          font-size: 16px;
          margin-right: 14px;
        }
        .exam-content {
          h4 {
            line-height: 20px;
          }
          p {
            color: #666;
            line-height: 1;
            padding-left: 24px;
            position: relative;
            i {
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }
  }
  .action {
    text-align: center;
  }
  .v-enter-active {
    transition: all 0.7s ease;
  }
  .v-leave-active,
  .v-move {
    transition: all 0.3s ease;
  }
  // .v-leave-active {
  //   position: absolute;
  // }
  .v-enter,
  .v-leave-to {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
  }
}
</style>
