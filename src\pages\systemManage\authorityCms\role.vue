<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="角色名称/code" clearable @change="handleFilter">
          <el-select slot="prepend" v-model="condition.type" style="width:100px">
            <el-option :value="1" label="名称" />
            <el-option :value="2" label="code" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>
    <!-- body -->
    <a-table :columns="columns" fit :data="list" border stripe>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="!row.good" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button type="text" @click="toDetail(row)">查看角色</el-button>
        <el-button type="text" @click="viewUser(row)">查看用户</el-button>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <DialogUserList :show.sync="userShow" :role-id="roleId" />
  </div>
</template>

<script>
import request from '@/api/roleCms'
import table from '@/mixins/table'
import DialogUserList from './components/dialogUserList.vue'

const columns = [
  { props: { label: 'code', align: 'center', prop: 'code' }},
  { props: { label: '角色名称', align: 'center', prop: 'name' }},
  { props: { label: '描述', align: 'center', prop: 'description' }},
  { props: { label: '修改人', align: 'center', prop: 'uname' }},
  { props: { label: '修改时间', align: 'center', prop: 'utime' }},
  { props: { label: '操作', align: 'center' }, slot: 'actions' }
]

export default {
  name: 'Role',
  components: { DialogUserList },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      condition: {
        keyword: '',
        type: 1
      },
      mainKey: 'roleId',
      editPath: 'cmsRoleAdd',
      userShow: false,
      roleId: ''
    }
  },
  methods: {
    // edit row
    editRow(row) {
      this.$router.push({
        path: this.editPath,
        query: {
          id: row[this.mainKey]
        }
      })
    },
    deleteRow(row) {
      this.$confirm('确定删除?', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          request.roleDel(row[this.mainKey]).then(res => {
            this.$message.success('删除成功')
            this.getList()
          })
        })
        .catch(console.log)
    },
    viewUser(row) {
      this.roleId = row[this.mainKey]
      this.userShow = true
    }
  }
}
</script>
