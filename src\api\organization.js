import request from '@/utils/request'

export function findOrganListPage(params) {
  return request({
    url: '/organization/findOrganListPage',
    method: 'post',
    data: params
  })
}

// 获取临时单位查询批次id
export function getTmpOrgBatchId() {
  return request({
    url: '/organization/getTmpOrgBatchId',
    method: 'get'
  })
}

// 临时单位列表（待选/已选）
export function tmpSelectOrgList(params) {
  return request({
    url: '/organization/tmpSelectOrgList',
    method: 'post',
    data: params
  })
}

// 添加/删除临时单位
export function addOrDelTmpSelectOrg(params) {
  return request({
    url: '/organization/addOrDelTmpSelectOrg',
    method: 'post',
    data: params
  })
}
