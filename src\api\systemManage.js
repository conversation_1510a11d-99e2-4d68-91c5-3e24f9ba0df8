import request from '@/utils/request'

/**
 * 平台参数
 */

// 获取平台参数配置信息
export function getPlatData() {
  return request({
    url: '/cms/platform/getPlatformConfig',
    method: 'get'
  })
}

// 更新平台参数配置信息
export function updatePlatData(qurey) {
  return request({
    url: '/cms/platform/updateConfigByKey',
    method: 'post',
    data: qurey
  })
}

/**
 * app版本管理
 */

// 版本信息列表
export function svnList(params) {
  return request({
    url: '/cms/svn/list',
    method: 'post',
    data: params
  })
}

// 新增版本
export function svnAdd(params) {
  return request({
    url: '/cms/svn/add',
    method: 'post',
    data: params
  })
}

// 删除版本信息
export function svnDelete(params) {
  return request({
    url: '/cms/svn/delete',
    method: 'post',
    data: params
  })
}

// 编辑版本信息
export function svnUpdate(params) {
  return request({
    url: '/cms/svn/update',
    method: 'post',
    data: params
  })
}

/**
 * 订单管理
 */

// 总收入
export function getTotal() {
  return request({
    url: '/cms/order/getTotalAmount',
    method: 'get'
  })
}

// 订单列表
export function getOrderList(params) {
  return request({
    url: '/cms/order/getOrderListPage',
    method: 'post',
    data: params
  })
}

// 导出订单列表
export function exportOrderList(params) {
  return request({
    url: '/cms/order/exportOrderList',
    method: 'post',
    data: params
  })
}
