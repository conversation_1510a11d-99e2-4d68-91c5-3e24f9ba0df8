<template>
  <el-dialog
    title="学员信息"
    :visible.sync="dialogVisible"
    width="550px"
    :before-close="handleClose"
  >
    <ul>
      <li>
        <span>真实姓名:</span>{{ info.realName }}
      </li>
      <li>
        <span>身份证号码:</span>{{ info.idcard }}
      </li>
      <li>
        <span>手机:</span> {{ info.phone }}
      </li>
      <li>
        <span>身份证正/反面:</span>
        <img :src="info.idcardFrontImgUrl" @click="handlePreview(info.idcardFrontImgUrl)">
        <img :src="info.idcardBehindImgUrl" @click="handlePreview(info.idcardBehindImgUrl)">
      </li>
    </ul>
    <ul>
      <li>
        <span>资格证书:</span>
        <img :src="info.certImgUrl" @click="handlePreview(info.certImgUrl)">
        <img :src="info.practiceCertImgUrl" @click="handlePreview(info.practiceCertImgUrl)">
      </li>
      <li>
        <span>本人照片</span>
        <img :src="info.avatarUrl" @click="handlePreview(info.avatarUrl)">
      </li>
      <li>
        <span>身份:</span> {{ info.identityName }}
      </li>
      <li>
        <span>执业专科:</span> {{ info.majorName }}
      </li>
      <li>
        <span>职称:</span> {{ info.academicName }}
      </li>
      <li>
        <span>工作单位:</span> {{ info.company }}
      </li>
      <li>
        <span>科室/部门:</span> {{ info.department }}
      </li>
      <li>
        <span>所在地区:</span> {{ info.provinceName + '-' + info.cityName + '-' + info.areaName }}
      </li>
    </ul>
    <el-dialog :visible.sync="imgDialogVisible" :modal="false">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    look: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      imgDialogVisible: false,
      dialogImageUrl: ''
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    handlePreview(url) {
      this.dialogImageUrl = url
      this.imgDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
      height: 509px;
      padding: 20px 30px;
      color: #333;
      overflow-y: auto;
      /* 整个滚动条 */
      &::-webkit-scrollbar {
          /* 对应纵向滚动条的宽度 */
          width: 10px;
          /* 对应横向滚动条的宽度 */
          height: 10px;
      }

      /* 滚动条上的滚动滑块 */
      &::-webkit-scrollbar-thumb {
          background-color: #d0d3d9;
          border-radius: 32px;
      }
      h3 {
        display: flex;
        align-items: center;
        margin: 0 auto;
        font-size: 16px;
        line-height: 24px;
        &:before {
          content: '';
          display: inline-block;
          margin-right: 8px;
          width: 4px;
          height: 16px;
          background-color: #409eff;
        }
      }
      li {
        display: flex;
        margin: 18px 0;
        font-size: 14px;
        span {
          margin-right: 30px;
          width: 100px;
          text-align: right;
          color: #666;
        }
        img {
          width: 150px;
          height: 92px;
          margin-right: 15px;
          border-radius: 3px;
          overflow: hidden;
          &:nth-last-child(1) {
            margin-right: 0;
          }
        }
      }
    }
</style>
