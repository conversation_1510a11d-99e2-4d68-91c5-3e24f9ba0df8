<template>
  <section class="videoEdit">
    <div class="videoEdit-title">
      抖喇短视频
    </div>
    <el-form ref="form" :rules="rules" :model="form" label-width="80px">
      <el-form-item label="推广产品" prop="productId">
        <el-select v-model="form.orgzId" clearable placeholder="全部企业" @change="getProductList()">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="form.productId" clearable placeholder="全部产品" no-data-text="请先选择企业">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="视频" required>
        <div style="display: flex">
          <div v-if="form.content.vodId === ''" class="select-btn" @click="selsecVideoDialogVisible = true">视频素材库选择</div>
          <video-upload v-if="form.content.vodId === ''" :key="form.content.vodId" :width="'260px'" :height="'100px'" :video-file-id.sync="form.content.vodId" />
          <video-play v-show="form.content.vodId !== ''" :video-file-id="form.content.vodId" @del="form.content.vodId = ''" />
        </div>
      </el-form-item>
      <el-form-item label="视频封面" required prop="coverIds">
        <UploadPic :key="form.coverUrls[0]" tips="添加封面图" :pic-id.sync="form.coverIds[0]" :url="form.coverUrls[0]" />
      </el-form-item>
      <el-form-item label="文字内容" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入内容"
          :maxlength="2000"
          show-word-limit
          style="width: 520px"
        />
      </el-form-item>
      <el-form-item label="链接">
        <add-link :link.sync="form.content.url" :link-name.sync="form.content.urlTitle" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryIds">
        <el-cascader
          v-model="form.categoryIds"
          :options="categoryIdsOptions"
          :props="categoryIdsProps"
          collapse-tags
        />
      </el-form-item>
      <el-form-item label="作者" prop="authorId">
        <div>
          <el-button v-if="!form.authorId" type="primary" @click="authorDialogVisible = true">选择作者</el-button>
          <el-tag
            v-if="form.authorId"
            type="info"
            closable
            @close="form.authorId = null"
          >{{ authorInfo.authorName }} {{ authorInfo.phone }}</el-tag>
          <author :author-dialog-visible.sync="authorDialogVisible" :author-info.sync="authorInfo" type="doula" />
        </div>
      </el-form-item>
      <el-form-item label="推广需求" required>
        <Budget @getBudgetData="getBudgetData" />
      </el-form-item>
      <el-form-item>
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button type="primary" @click="publish">发布</el-button>
      </el-form-item>
    </el-form>

    <SelsectVideo :dialog-visible.sync="selsecVideoDialogVisible" :video-file-id.sync="form.content.vodId" />
  </section>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getCategoryTreeList } from '@/api/category'
import { getUnitList } from '@/api/userManage'
import { productList, saveOrUpdatePlatformArticleCms } from '@/api/marketing/taskPromote'
import VideoUpload from '@/components/Upload/videoUpload.vue'
import VideoPlay from '@/components/Upload/videoPlay.vue'
import UploadPic from '@/components/Upload/SingleImage4.vue'
import Author from '@/pages/marketing/task/components/author.vue'
import AddLink from '@/pages/marketing/task/components/addLink.vue'
import SelsectVideo from '@/pages/marketing/task/components/selectVideo.vue'
import Budget from './budget'
export default {
  components: {
    VideoUpload,
    VideoPlay,
    UploadPic,
    AddLink,
    Author,
    SelsectVideo,
    Budget
  },
  data() {
    return {
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      form: {
        authorId: null,
        authorName: '',
        contentType: 'DOULA_VIDEO',
        content: {
          url: '',
          urlTitle: '',
          vodId: ''
        },
        description: '',
        categoryIds: [],
        coverIds: [],
        coverUrls: []
      },
      orgList: [],
      productList: [],
      categoryIdsOptions: [],
      categoryIdsProps: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      rules: {
        description: [
          { required: true, message: '请输入文字内容', trigger: 'blur' },
          { min: 1, max: 2000, message: '长度在 2000 个字符内', trigger: 'blur' }
        ],
        categoryIds: [
          { required: true, message: '请选择专科分类', trigger: 'change' }
        ],
        coverIds: [
          { required: true, message: '请上传封面', trigger: 'change' }
        ],
        productId: [
          { required: true, message: '请选择推广产品', trigger: 'change' }
        ],
        authorId: [
          { required: true, message: '请选择作者', trigger: 'change' }
        ]
      },
      detail: {},
      authorInfo: {},
      authorDialogVisible: false,
      selsecVideoDialogVisible: false
    }
  },
  watch: {
    authorDialogVisible(v) {
      if (!v) {
        this.form.authorId = this.authorInfo.authorId
        this.form.authorName = this.authorInfo.authorName
        this.form.authorPhone = this.authorInfo.phone
      }
    }
  },
  mounted() {
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
    this.getOrg()
  },
  methods: {
    getOrg() {
      const query = {
        condition: {
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getProductList() {
      if (this.form.orgzId) {
        productList({ orgzId: this.form.orgzId, pageSize: 1000 }).then(res => {
          this.productList = res
        })
      }
    },
    getBudgetData(v) {
      this.form = {
        ...this.form,
        ...v
      }
    },
    publish() {
      this.$refs.form.validate(valid => {
        if (this.form.content.vodId === '') {
          this.$message.error('视频未上传')
          return
        }
        if (!this.form.authorId) {
          this.$message.error('未选择作者')
          return
        }
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.form))
          params.taskType = this.$route.query.type
          params.content = JSON.stringify(this.form.content)
          saveOrUpdatePlatformArticleCms(params).then(res => {
            this.$message.success('短视频发布成功')
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.videoEdit {
  background-color: #fff;
  width: 1200px;
  height: 100%;
  margin: 0 auto;
  padding: 25px 30px;
  color: #333;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 60px;
    font-size: 18px;
    line-height: 32px;
    &:before {
      content: '';
      margin-right: 10px;
      width: 3px;
      height: 18px;
      background-color: #409eff;
    }
  }
  .el-form {
    margin-left: 20px;
    width: 1200px;
    ::v-deep .el-textarea__inner {
      height: 250px;
    }
    .el-select {
      width: 200px;
    }
    .select-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300px;
      height: 100px;
      margin-right: 20px;
      font-size: 22px;
      font-weight: 700;
      color: #000;
      cursor: pointer;
      background-color: #f5f7fa;
    }
  }
}
</style>
