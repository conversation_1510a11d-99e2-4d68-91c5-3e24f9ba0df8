<template>
  <!-- 纵向表头 表格组件 -->
  <div class="app-container">
    <div class="table">
      <div class="table-header">
        <table width="100%" cellspacing="0" cellpadding="0">
          <tr><td class="black_title">指标</td></tr>
          <tr><td class="left_title bottom_border">{{ labelName }}</td></tr>
          <tr><td class="left_title bottom_border" style="border-bottom: none;">增长率</td></tr>
        </table>
      </div>
      <div v-for="(item, index) in dataAry" :key="index" class="table-body">
        <table width="100%" cellspacing="0" cellpadding="0">
          <tr><td class="black_title">{{ item.dm }}</td></tr>
          <tr><td class="td_content">{{ item.num }}</td></tr>
          <tr><td class="td_content" style="border-bottom: none;">{{ item.growRate }}%</td></tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => {
        return []
      }
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      styleObject: {},
      s_showByRow: true,
      dataAry: []
    }
  },
  computed: {
    labelName() {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[this.type]
    }
  },
  watch: {
    tableData() {
      this.handleTableData()
    }
  },
  created() {
    this.styleObject = this.tableStyle
  },
  methods: {
    // 处理表格数据
    handleTableData() {
      const dataOld = JSON.parse(JSON.stringify(this.tableData))
      let dataNew = []
      // 数组排序
      var compare = function(prop) {
        return function(obj1, obj2) {
          var val1 = obj1[prop]
          var val2 = obj2[prop]
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1)
            val2 = Number(val2)
          }
          if (val1 < val2) {
            return -1
          } else if (val1 > val2) {
            return 1
          } else {
            return 0
          }
        }
      }
      dataNew = dataOld.sort(compare('dm'))
      if ([8, 9, 10, 11, 13].includes(this.type)) {
        dataNew.forEach((item, index) => {
          if (dataNew[index + 1]) {
            dataNew[index + 1].growthRate = this.divisionResult(item.num, dataNew[index + 1].num)
          }
          item.num += '%'
        })
      } else {
        dataNew.forEach((item, index) => {
          if (dataNew[index + 1]) {
            dataNew[index + 1].growthRate = this.divisionResult(item.num, dataNew[index + 1].num)
          }
        })
      }
      // dataNew.splice(0, 1)
      this.dataAry = dataNew
    },
    // 取两个数相除的结果
    divisionResult(x, y) {
      if (!isNaN(Number(x)) && !isNaN(Number(y))) {
        const val1 = Number(x)
        const val2 = Number(y)
        if ((val1 === 0 && val2 === 0) || val2 === 0 || !val2) {
          return '0%'
        } else {
          return (val1 / val2).toFixed(2)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
table,tr,td {
  margin: none;
  padding: none;
  border-collapse: collapse;
  color: #5e5f63;
}
.black_title {
  height: 50px;
  border-bottom: 1px #ebeef5 solid;
}
.td_content {
  height: 50px;
  border-bottom: 1px #ebeef5 solid;
}
.left_title {
  height: 50px;
  border-bottom: 1px #ebeef5 solid;
}
.table{
  display: flex;
  justify-content: flex-start;

  .table-header {
    width: 100%;
    border: 1px #ebeef5 solid;
    border-radius: 5px 0 0 5px;
    text-align: center;
  }
  .table-body {
    width: 100%;
    border: 1px #ebeef5 solid;
    border-left: none;
    text-align: center;
    align-items: center;
  }
  .table-body:last-child{
    border-radius: 0 5px 5px 0;
  }
}
</style>
