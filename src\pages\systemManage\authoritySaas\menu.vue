<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column-item fr">
        <el-button type="primary" @click="add">添加</el-button>
      </div>
    </div>

    <a-table row-key="id" :tree-props="{children: 'childList'}" :columns="columns" fit :data="tableData" border stripe>
      <template slot="type" slot-scope="{row}">
        <span>{{ row.type|typeFmt }}</span>
      </template>
      <template slot="status" slot-scope="{row}">
        <span>{{ row.status|statusFmt }}</span>
      </template>
      <template slot="actions" slot-scope="{row}">
        <el-button type="text" @click="editRow(row)">编辑</el-button>
        <el-button v-if="!row.status&&!row.childList.length" type="text" @click="deleteRow(row)">删除</el-button>
        <el-button v-if="row.status" type="text" @click="deactivateRow(row)">停用</el-button>
        <el-button v-else type="text" @click="activateRow(row)">启用</el-button>
      </template>
    </a-table>

  </div>
</template>

<script>
import ATable from '@/components/ATable/index.vue' // 引入再封装的element-ui table组件
import request from '@/api/menuSaas'

export default {
  name: 'Menu',
  components: { ATable },
  filters: {
    typeFmt(v) {
      const arr = ['', '菜单', '操作', '页面元素']
      return arr[v]
    },
    statusFmt(v) {
      return v ? '启用' : '停用'
    }
  },
  data() {
    return {
      columns: [
        { props: { prop: 'id', label: 'ID', width: '210px', align: 'left' }},
        { props: { prop: 'name', label: '名称', align: 'center' }},
        { props: { prop: 'type', label: '类型', width: '70px', align: 'center' }, slot: 'type' },
        { props: { prop: 'useScopeName', label: '适用范围', width: '140px', align: 'center' }},
        { props: { prop: 'listOrder', label: '排序号', width: '70px', align: 'center' }},
        { props: { prop: 'uri', label: 'URL', width: '50px', align: 'center' }},
        { props: { prop: 'code', label: 'code', align: 'center' }},
        { props: { prop: 'status', label: '状态', width: '70px', align: 'center' }, slot: 'status' },
        { props: { prop: 'description', label: '说明', align: 'center' }},
        { props: { prop: 'updateName', label: '修改人', width: '90px', align: 'center' }},
        { props: { prop: 'updateTime', label: '修改时间', width: '154px', align: 'center' }},
        { props: { label: '操作', width: '140px', align: 'center' }, slot: 'actions' }
      ],
      tableData: []
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      request.list().then(res => {
        this.tableData = res
      })
    },
    add() {
      this.$router.push('saasMenuAdd')
    },
    editRow(row) {
      this.$router.push({
        path: 'saasMenuAdd',
        query: {
          id: row.id,
          type: row.type,
          name: row.name,
          parentId: row.parentId,
          code: row.code,
          listOrder: row.listOrder,
          uri: row.uri,
          description: row.description,
          useScopeArr: row.useScopeArr
        }
      })
    },
    deleteRow(row) {
      request.delete(row.id).then(res => {
        this.$message.success(res)
        this.init()
      })
    },
    activateRow(row) {
      this.activeAPI(row)
    },
    deactivateRow(row) {
      this.$confirm('菜单（菜单/操作）停用后，将在系统不可见', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.activeAPI(row)
        })
        .catch(console.log)
    },
    activeAPI(row) {
      request.activate(row.id).then(res => {
        this.$message.success(res)
        this.init()
      })
    }
  }
}
</script>
