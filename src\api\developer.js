import request from '@/utils/request'

// 开发者列表
export function developersList(data) {
  return request({
    url: '/developers/list',
    method: 'post',
    data: data
  })
}

// 开发者创建
export function createDevelopers(data) {
  return request({
    url: '/developers/create',
    method: 'post',
    data: data
  })
}

// 检测单位是否关联
export function checkRelation(orgId) {
  return request({
    url: `/developers/check/${orgId}`,
    method: 'post'
  })
}

// 查看
export function developersDetail(oauthClientId) {
  return request({
    url: `developers/detail/${oauthClientId}`,
    method: 'get'
  })
}

// 处理
export function dispose(data) {
  return request({
    url: '/developers/dispose',
    method: 'post',
    data
  })
}

// 处理前摇
export function disposeBefore(oauthClientId) {
  return request({
    url: `/developers/dispose/before/${oauthClientId}`,
    method: 'get'
  })
}

// 启用/停用
export function enabledisable(oauthClientId) {
  return request({
    url: `/developers/enabledisable/${oauthClientId}`,
    method: 'get'
  })
}
