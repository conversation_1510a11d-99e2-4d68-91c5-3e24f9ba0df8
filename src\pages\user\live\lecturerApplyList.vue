<template>
  <div class="app-container">
    <div class="search-column" style="display:flex">
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable>
          <el-select slot="prepend" v-model="queryType" style="width:120px">
            <el-option v-for="item in queryTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-input>
      </div>
      <div class="search-column__item" style="margin:0 10px">
        <el-select v-model="auditStatus" clearable placeholder="请选择审核状态">
          <el-option v-for="item in auditStatusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <Search @search="search" @clear="clear" />
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <template v-if="col.prop==='auditStatus'">
            <span>{{ scope.row[col.prop] | auditStatusFilter }}</span>
          </template>
          <template v-else>
            <span>{{ scope.row[col.prop] }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="100">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus===1" type="text" @click="checkFn(row.userId)">审核</el-button>
          <el-button type="text" @click="checkHistoryFn(row)">审核历史</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <el-dialog title="讲师审核" :visible.sync="checkVisible" width="50vw" top="20px">
      <el-row class="detail">
        <el-form ref="form" :model="detail" label-width="200px" label-position="right">
          <h2>个人信息</h2>
          <el-form-item label="真实姓名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="身份证号码:">
            <span>{{ detail.idcard }}</span>
          </el-form-item>
          <el-form-item label="身份证正/反面:">
            <div class="imgBox">
              <el-image style="width: 300px;margin-right:10px" fit="cover" :src="detail.idcardFrontImgUrl" :preview-src-list="[detail.idcardFrontImgUrl]" />
              <el-image style="width: 300px" fit="cover" :src="detail.idcardBehindImgUrl" :preview-src-list="[detail.idcardBehindImgUrl]" />
            </div>
          </el-form-item>
          <el-form-item label="身份:">
            <span>{{ detail.identityName }}</span>
          </el-form-item>
          <el-form-item label="执业专科:">
            <span>{{ detail.majorName }}</span>
          </el-form-item>
          <el-form-item label="资格证书:">
            <el-image style="width: 300px" fit="cover" :src="detail.certImgUrl" :preview-src-list="[detail.certImgUrl]" />
          </el-form-item>
          <el-form-item label="执业证书:">
            <el-image style="width: 300px" fit="cover" :src="detail.practiceCertImgUrl" :preview-src-list="[detail.practiceCertImgUrl]" />
          </el-form-item>
          <el-form-item label="职称:">
            <span>{{ detail.academicName }}</span>
          </el-form-item>
          <el-form-item label="职称证书:">
            <el-image style="width: 300px" fit="cover" :src="detail.academicImgUrl" :preview-src-list="[detail.academicImgUrl]" />
          </el-form-item>
          <el-form-item label="工作单位:">
            <span>{{ detail.company }}</span>
          </el-form-item>
          <el-form-item label="科室/部门:">
            <span>{{ detail.department }}</span>
          </el-form-item>
          <el-form-item label="所在地区:">
            <span>{{ detail.areaName }}</span>
          </el-form-item>
          <el-form-item label="擅长:">
            <span>{{ detail.skill }}</span>
          </el-form-item>
          <el-form-item label="简介:">
            <span>{{ detail.introduction }}</span>
          </el-form-item>
          <el-form-item label="头像:">
            <el-image style="width: 300px" fit="cover" :src="detail.avatarUrl" :preview-src-list="[detail.avatarUrl]" />
          </el-form-item>
          <el-form-item label="手机:">
            <span>{{ detail.phone }}</span>
          </el-form-item>
          <el-form-item label="内容扩展方:">
            <span>{{ detail.contentExtenderCode }}</span>
          </el-form-item>

          <h2>结算信息</h2>
          <el-form-item label="开户行:">
            <span>{{ detail.bankName }}</span>
          </el-form-item>
          <el-form-item label="户名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="银行卡号:">
            <span>{{ detail.bankcard }}</span>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="text-align:center">
        <el-button @click="checkVisible = false">取 消</el-button>
        <el-button type="danger" @click="checkRejuct()">不通过</el-button>
        <el-button type="primary" @click="checkPass()">通 过</el-button>
      </el-row>
    </el-dialog>

    <!-- 审核不通过弹窗 -->
    <el-dialog title="审核说明" :visible.sync="rejuctVisible" width="600px">
      <div>
        <el-form label-width="80px">
          <el-form-item label="审核说明:">
            <span><i class="el-icon-warning-outline" /> 请填写不通过的说明，以便修正</span>
            <el-input
              v-model="opinion"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="rejuctVisible = false">取 消</el-button>
        <el-button type="primary" @click="rejuctConfirm()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 审核历史 弹窗 -->
    <el-dialog title="审核历史" :visible.sync="hisVisible" width="96vw">
      <el-table :data="hisList" border stripe>
        <el-table-column v-for="col in hisListColumnList" :key="col.id" v-bind="col">
          <template slot-scope="scope">
            <template v-if="col.prop==='res'">
              <span>{{ scope.row[col.prop]===0?'不通过':'通过' }}</span>
            </template>
            <template v-else>
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <Pagination class="text-center" :page-size="hisQuery.pager.pageSize" :total="hisTotal" :page="hisQuery.pager.page" @pagination="hisPagination" />
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  leturList,
  leturDetail,
  leturApply,
  leturAuditRecordList
} from '@/api/liveApply'
import Search from '@/pages/operationManage/live/components/search_1.vue'

export default {
  name: 'LiveList',
  filters: {
    auditStatusFilter(val) {
      const arr = ['待提交审核', '待审核', '审核通过', '审核不通过']
      return arr[val]
    }
  },
  components: { Pagination, Search },
  data() {
    return {
      keyword: '',
      queryType: 'realName',
      queryTypeList: [
        { label: '姓名', value: 'realName' },
        { label: '手机', value: 'phone' },
        { label: '工作单位', value: 'company' }
      ],
      auditStatus: null,
      auditStatusList: [
        { label: '待审核', value: 1 },
        { label: '审核通过', value: 2 },
        { label: '审核不通过', value: 3 }
      ],
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 1, label: '用户ID', align: 'center', prop: 'userId', width: '100px' },
        { id: 2, label: '姓名', align: 'center', prop: 'realName' },
        { id: 3, label: '身份证号码', align: 'center', prop: 'idcard', width: '100px' },
        { id: 4, label: '手机', align: 'center', prop: 'phone' },
        { id: 5, label: '身份', align: 'center', prop: 'identityName' },
        { id: 6, label: '专科', align: 'center', prop: 'majorName' },
        { id: 7, label: '职称', align: 'center', prop: 'academicName' },
        { id: 8, label: '工作单位', align: 'center', prop: 'company', width: '160px' },
        { id: 9, label: '科室/部门', align: 'center', prop: 'department' },
        { id: 10, label: '所在地区', align: 'center', prop: 'areaName', width: '160px' },
        { id: 11, label: '提交审核时间', align: 'center', prop: 'updateTime', width: '160px' },
        { id: 12, label: '审核状态', align: 'center', prop: 'auditStatus' }
      ]),
      hisListColumnList: Object.freeze([
        { id: 1, label: '用户ID', align: 'center', prop: 'userId' },
        { id: 2, label: '姓名', align: 'center', prop: 'realName' },
        { id: 3, label: '身份证号码', align: 'center', prop: 'idcard' },
        { id: 4, label: '手机', align: 'center', prop: 'phone' },
        { id: 5, label: '身份', align: 'center', prop: 'identityName' },
        { id: 6, label: '专科', align: 'center', prop: 'majorName' },
        { id: 7, label: '职称', align: 'center', prop: 'academicName' },
        { id: 8, label: '工作单位', align: 'center', prop: 'company' },
        { id: 9, label: '科室/部门', align: 'center', prop: 'department' },
        { id: 10, label: '所在地区', align: 'center', prop: 'areaName' },
        { id: 11, label: '提交审核时间', align: 'center', prop: 'createTime', width: '160px' },
        { id: 12, label: '审核人', align: 'center', prop: 'auditorUaerName' },
        { id: 13, label: '审核时间', align: 'center', prop: 'updateTime', width: '160px' },
        { id: 14, label: '审核结果', align: 'center', prop: 'res' },
        { id: 15, label: '审核说明', align: 'center', prop: 'opinion' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      // 审核历史
      hisQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      hisTotal: 0,
      hisList: [],
      hisVisible: false,
      userId: '',
      detail: {},
      checkVisible: false,
      opinion: '',
      rejuctVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      leturList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    search(condition) {
      condition && (this.tableQuery.condition = condition)
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.phone = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition[this.queryType] = this.keyword
      this.tableQuery.condition.auditStatus = this.auditStatus
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.phone = ''
      this.tableQuery.condition.company = ''
      this.keyword = ''
      this.tableQuery.condition.auditStatus = this.auditStatus = ''
      this.tableQuery.pager.page = 1
      this.getList()
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    // 审核-打开弹窗
    checkFn(userId) {
      this.userId = userId
      leturDetail(userId).then(res => {
        this.detail = res
        this.checkVisible = true
      })
    },
    // 审核历史-打开弹窗
    checkHistoryFn(data) {
      this.hisQuery.condition.userId = data.userId
      this.hisQuery.pager.page = 1
      leturAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
        this.hisVisible = true
      })
    },
    // 审核历史-分页
    hisPagination(val) {
      this.hisQuery.pager = val
      leturAuditRecordList(this.hisQuery).then(res => {
        this.hisList = res.records
        this.hisTotal = res.total
      })
    },
    // 审核-通过
    checkPass() {
      const query = {
        auditStatus: 2,
        userId: this.userId,
        avatarUrl: this.detail.avatarUrl
      }
      leturApply(query).then(res => {
        this.$message.success('审核成功')
        this.checkVisible = false
        this.getList()
      })
    },
    // 审核-不通过
    checkRejuct() {
      this.opinion = ''
      this.rejuctVisible = true
    },
    // 不通过-提交
    rejuctConfirm() {
      const query = {
        auditStatus: 3,
        userId: this.userId,
        avatarUrl: this.detail.avatarUrl,
        opinion: this.opinion
      }
      leturApply(query).then(res => {
        this.$message.success('审核成功')
        this.rejuctVisible = false
        this.checkVisible = false
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  h2 {
    color: #47c1a8;
    margin-left: 100px;
  }
}
.imgBox {
  width: 100%;
  display: flex;
}
</style>
