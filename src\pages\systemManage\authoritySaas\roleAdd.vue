<template>
  <div class="app-container">
    <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="120px" class="form">
      <h3>角色信息</h3>
      <el-form-item label="code" prop="code">
        <el-input v-model="form.code" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" maxlength="30" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="角色类型" required>
        <el-form-item prop="roleType">
          <el-select v-model="form.roleType" placeholder="请选择" clearable @change="switchType">
            <el-option :value="1" label="个人" />
            <el-option :value="2" label="机构" />
            <el-option :value="3" label="医务工作者" />
            <el-option :value="4" label="人物" />
            <el-option :value="5" label="讲师" />
            <el-option :value="6" label="内容扩展方" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="orgShow" prop="orgType">
          <el-select v-model="form.orgType" placeholder="请选择" clearable>
            <el-option v-for="item in orgList" :key="item.orgTypeId" :label="item.name" :value="item.orgTypeId" />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item label="是否超级管理员" prop="isSuper">
        <el-select v-model="form.isSuper" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否默认角色" prop="isDefault">
        <el-select v-model="form.isDefault" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="form.description" type="textarea" maxlength="300" show-word-limit placeholder="请输入" clearable />
      </el-form-item>
    </el-form>

    <div v-if="!readOnly">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getOrganTypeList } from '@/api/userManage'
import request from '@/api/roleSaas'

export default {
  name: 'RoleAdd',
  data() {
    return {
      readOnly: !!this.$route.query.readOnly,
      form: {
        code: '',
        name: '',
        description: '',
        roleType: '',
        orgType: '',
        isSuper: '',
        isDefault: ''
      },
      rules: {
        code: [{ required: true, message: '请输入code', trigger: 'blur' }],
        roleType: [{ required: true, message: '请选择', trigger: 'change' }],
        orgType: [{ required: true, message: '请选择', trigger: 'change' }],
        isSuper: [{ required: true, message: '请选择', trigger: 'change' }],
        isDefault: [{ required: true, message: '请选择', trigger: 'change' }],
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
      },
      typeList: [],
      orgList: []
    }
  },
  computed: {
    orgShow() {
      return this.form.roleType === 2
    }
  },
  async created() {
    if (this.$route.query.id) {
      const obj = this.$route.query
      // 防止页面刷新类型转换导致回显问题
      obj.isDefault -= 0
      obj.isSuper -= 0
      obj.roleType -= 0
      obj.orgType -= 0
      !obj.orgType && (obj.orgType = '')
      this.form = { ...obj }
    }
    getOrganTypeList().then(res => {
      this.orgList = res
    })
  },
  methods: {
    cancel() {
      this.$router.go(-1)
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            request.roleEdit(this.form).then(res => {
              this.$message.success('编辑成功')
              this.cancel()
            })
          } else {
            request.roleCreate(this.form).then(res => {
              this.$message.success('添加成功')
              this.cancel()
            })
          }
        } else {
          return false
        }
      })
    },
    switchType(v) {
      v == 1 && (this.form.orgType = '')
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  h3 {
    width: 800px;
    margin-bottom: 30px;
  }
}
</style>
