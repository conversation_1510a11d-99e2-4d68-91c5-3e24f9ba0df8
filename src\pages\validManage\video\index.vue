<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="5">
        <div class="content-left">
          <div class="tree-title">
            <span>资源分类</span>
            <div v-if="showSelect">
              <el-button type="text" @click="cancelSelect">取消选择</el-button>
            </div>
          </div>
          <el-scrollbar wrap-class="default-scrollbar__wrap" view-class="p20-scrollbar__view" class="content-container">
            <Tree
              ref="tree"
              :type="categoryType"
              :platform-type="tableQuery.condition.platform"
              :highlight-current="highlightCurrent"
              @nodeAddPlatForm="handleNodeAddPlatForm"
              @nodeClick="handleTreeNodeClick"
            />
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <div class="search-column">
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.type">
              <el-option v-for="item in videoTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-input v-model="tableQuery.condition.keyword" placeholder="根据左侧查询方式对应关键字" clearable style="width: 240px" @change="init">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="init" />
            </el-input>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.platform" @change="init">
              <el-option v-for="item in platformList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.status" @change="init">
              <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-select v-model="tableQuery.condition.memberLevel" placeholder="会员等级" clearable @change="init">
              <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
            </el-select>
          </div>
          <div class="search-column__item fr">
            <el-button v-if="selectedList.length" type="primary" @click="batchSetMemberLevel">批量设置会员等级</el-button>
            <el-button v-if="tableQuery.condition.platform ===1" :disabled="!selectedList.length" type="primary" @click="setFake">设置虚拟量</el-button>
            <el-button v-if="tableQuery.condition.platform ===1 && havChild" type="primary" @click="addVideo">增加视频</el-button>
            <el-button v-if="[1, 2].includes(tableQuery.condition.platform)" :disabled="!selectedList.length" type="primary" @click="editCategory({})">修改分类</el-button>
          </div>
        </div>

        <el-table :data="videoList" border stripe @selection-change="onSelectChange">
          <el-table-column type="selection" fixed="left" />
          <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.filter === 'name'">
                <span class="video-links" @click="handlePlayVideo(scope.row)">{{ scope.row[col.prop] }}</span>
              </template>
              <template v-else-if="col.filter === 'cateName'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop] | filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else-if="col.filter === 'permission'">
                <span>{{ scope.row.extPermission | filterExtPermission }}</span>
              </template>
              <template v-else-if="col.type==='image'">
                <el-image style="width: 100px" :src="scope.row[col.prop]" @click="preview(scope.row[col.prop])" />
              </template>
              <template v-else-if="col.filter==='platform'">
                <span>{{ scope.row.platform|platformFmt }}</span>
              </template>
              <template v-else-if="col.filter==='status'">
                <span>{{ scope.row.status|statusFmt }}</span>
              </template>
              <template v-else>
                <span>
                  {{ scope.row[col.prop] }}
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="180">
            <template slot-scope="{row}">
              <el-button v-if="row.status === 1 || row.status === 3" size="mini" type="text" @click="handleUpdateStatus(row)">上架</el-button>
              <el-button v-if="row.status === 2 && tableQuery.condition.platform ===1" size="mini" type="text" @click="handleUpdateStatus(row)">下架</el-button>
              <el-button size="mini" type="text" @click="setMemberLevel(row)">设置会员等级</el-button>
              <el-button v-if="tableQuery.condition.platform ===1" size="mini" type="text" @click="setFake(row)">设置虚拟量</el-button>
              <el-button v-if="[1, 2].includes(tableQuery.condition.platform)" size="mini" type="text" @click="editCategory(row)">修改分类</el-button>
              <el-button v-if="tableQuery.condition.platform ===1 && havChild" size="mini" type="text" @click="setSort(row)">排序</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-dialog class="playVideoDialog" :title="playerTitle" :visible.sync="dialogVisible" width="1024px" @close="handlePauseVideo">
      <div class="pos-re">
        <ali-player
          ref="aliplayer"
          :play-style="aliPlayerConfig.playStyle"
          :source="aliPlayerConfig.source"
          :cover="aliPlayerConfig.cover"
          :height="aliPlayerConfig.height"
          :skin-layout="aliPlayerConfig.skinLayout"
          @ready="handleReadyVideo"
          @pause="handlePauseVideo"
          @error="handleError"
        />
      </div>
    </el-dialog>

    <el-dialog title="设置虚拟量" width="500px" :visible.sync="dialogFormShow" :destroy-on-close="true">
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="虚拟播放量" prop="baseHits">
          <el-input-number v-model="form.baseHits" :min="0" @change="onNumChange($event,'baseHits')" />
        </el-form-item>
        <el-form-item label="虚拟点赞量" prop="basePraises">
          <el-input-number v-model="form.basePraises" :min="0" @change="onNumChange($event,'basePraises')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogImgShow">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>

    <el-dialog title="增加视频" width="800px" :visible.sync="addVideoVisible" :close-on-click-modal="false" :destroy-on-close="true" @close="addVideoCancel">
      <div class="search-column">
        <div class="search-column__item">
          <div class="search-column__label">视频名称：</div>
          <div class="search-column__inner">
            <el-input v-model="addVideoTableQuery.condition.keyword" placeholder="请输入视频名称" @change="search" @keydown="search" />
          </div>
        </div>
      </div>
      <div v-if="noQuery" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
        <span>请输入视频名称查询</span>
      </div>
      <div v-else>
        <el-table ref="singleTable" :data="addVideoTableData" border>
          <el-table-column v-for="col in addVideoTableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.filter === 'cateName'">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content" style="width: 300px;">
                    {{ scope.row[col.prop] }}
                  </div>
                  <span>{{ scope.row[col.prop] | filterFont }}</span>
                </el-tooltip>
              </template>
              <template v-else-if="col.filter==='status'">
                <span>{{ scope.row.status|statusFmt }}</span>
              </template>
              <template v-else>
                <span>
                  {{ scope.row[col.prop] }}
                </span>
              </template>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="100">
            <template slot-scope="{row}">
              <el-button v-if="videoIdList.includes(row.videoId)" size="mini" type="text" @click="handleCloseSelect(row)">已选择</el-button>
              <el-button v-else size="mini" type="text" @click="handleSelect(row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- pagination -->
        <Pagination class="text-center" :limit="tableQuery.limit" :total="addVideoTotal" :page="addVideoTableQuery.pager.page" @pagination="handleAddVideoPagination" />

        <span slot="footer" class="dialog-footer">
          <el-button @click="addVideoCancel">取 消</el-button>
          <el-button type="primary" @click="addVideoConfirm">确 定</el-button>
        </span>
      </div>
    </el-dialog>

    <el-dialog title="修改分类" width="600px" :visible.sync="editCategoryVisible" :destroy-on-close="true">
      <el-form ref="categoryForm" :model="categoryForm" label-width="120px">
        <el-form-item v-if="showCheckCategory" label="已选分类：" style="line-height: 25px;">
          <div v-for="item in categoryNameArr" :key="item+1" style="line-height: 20px !important;">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 400px;">
                {{ item }}{{ item?'；': '' }}
              </div>
              <span>{{ item | filterFont }}{{ item?'；': '' }}</span>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="资源分类：" prop="sort">
          <el-cascader
            ref="elcascader"
            v-model="categoryForm.cateIdList"
            placeholder="选择分类"
            :options="platformTreeList"
            :props="platformProps"
            :show-all-levels="true"
            clearable
            filterable
            collapse-tags
            @change="changeHandler"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editCategoryCancel">取 消</el-button>
        <el-button type="primary" @click="editCategoryConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改排序值" width="500px" :visible.sync="editSortVisible" :destroy-on-close="true">
      <el-form ref="sortForm" :model="sortForm" label-width="120px">
        <el-form-item label="排序值" prop="sort">
          <el-input-number v-model="sortForm.sort" :min="0" :max="99999" @change="onNumChange($event,'sort')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sortCancel">取 消</el-button>
        <el-button type="primary" @click="sortConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="isBatchSetMemberLevel ? '批量设置会员等级' : '设置会员等级'" width="500px" :visible.sync="memberLevelDialogVisible" :destroy-on-close="true">
      <el-form ref="memberLevelForm" :model="memberLevelForm" label-width="120px">
        <el-form-item label="会员等级" prop="memberLevel" :rules="[{ required: true, message: '请选择会员等级', trigger: 'change' }]">
          <el-select v-model="memberLevelForm.memberLevel" placeholder="请选择会员等级" style="width: 100%">
            <el-option v-for="item in memberLevelList" :key="item.level" :label="item.name" :value="item.level" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isBatchSetMemberLevel && selectedList.length >= tableQuery.pager.pageSize" label="">
          <el-checkbox v-model="memberLevelForm.crossPage" label="跨页批量" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="memberLevelCancel">取 消</el-button>
        <el-button type="primary" @click="memberLevelConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <Pagination class="text-center" :limit="tableQuery.limit" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
  </div>
</template>

<script>
import {
  videoList,
  changeVideoStatus,
  playVideoInfo,
  setVideoVirtual,
  addVideo,
  setVideoSort,
  displayHit,
  setVideoMemberLevel
} from '@/api/validManage'
import Pagination from '@/components/Pagination'
import AliPlayer from '@/components/Aliplayer'
import { getCategoryTreeList } from '@/api/category'
import { getVipLevelList } from '@/api/vip'
import Tree from '../components/platformTree.vue'

export default {
  name: 'VideoManage',
  components: { Pagination, AliPlayer, Tree },
  filters: {
    filterFont(val) {
      return val.length > 25 ? val.slice(0, 22) + '...' : val
    },
    filterExtPermission(val) {
      // 0:平台不公开 1:平台公开
      switch (val) {
        case 0:
          return '平台不公开'
        case 1:
          return '平台公开'
        default:
          return '未知权限'
      }
    },
    statusFmt(v) {
      const arr = ['', '草稿', '上架', '下架']
      return arr[v]
    },
    platformFmt(v) {
      const arr = ['', '平台资源', '单位资源']
      return arr[v]
    }
  },
  data() {
    return {
      // 视频搜索类型
      videoTypeList: Object.freeze([
        { label: '视频名称', value: 1 },
        { label: '创建者名称', value: 2 },
        { label: '单位名称', value: 3 }
      ]),
      // 资源类型
      platformList: Object.freeze([
        { label: '平台资源', value: 1 },
        { label: '单位资源', value: 2 }
      ]),
      // 状态搜索类型
      statusList: Object.freeze([
        { label: '上架', value: 2 },
        { label: '下架', value: 3 }
      ]),
      // 表格表头
      tableColumnList: [
        { id: 0, prop: 'videoId', label: 'ID', align: 'center' },
        {
          id: 1,
          prop: 'name',
          label: '视频名称',
          width: '200',
          align: 'center',
          filter: 'name'
        },
        {
          id: 2,
          prop: 'imgUrl',
          label: '封面',
          align: 'center',
          width: '110',
          type: 'image'
        },
        {
          id: 7,
          prop: 'cateName',
          label: '资源分类',
          width: '140',
          align: 'center',
          filter: 'cateName'
        },
        { id: 13, label: '资源类型', align: 'center', filter: 'platform' },
        { id: 4, label: '视频权限', align: 'center', filter: 'permission' },
        { id: 8, label: '播放量', align: 'center', prop: 'hits' },
        { id: 9, label: '虚拟播放量', align: 'center', prop: 'baseHits' },
        { id: 10, label: '点赞量', align: 'center', prop: 'praises' },
        { id: 11, label: '虚拟点赞量', align: 'center', prop: 'basePraises' },
        { id: 5, prop: 'author', label: '创建者名称', align: 'center' },
        { id: 6, prop: 'orgName', label: '单位名称', align: 'center' },
        { id: 15, prop: 'memberLevelName', label: '会员等级', align: 'center' },
        { id: 12, label: '状态', align: 'center', filter: 'status' },
        { id: 3, prop: 'createTime', label: '创建时间', align: 'center' }
      ],
      // 请求参数
      tableQuery: {
        condition: {
          keyword: '',
          type: 1,
          status: 2,
          cateId: '',
          platform: 1,
          memberLevel: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 视频列表
      videoList: [],
      total: 0,
      // 阿里播放器设置
      dialogVisible: false,
      playerTitle: '',
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            name: 'bigPlayButton',
            align: 'cc'
          },
          {
            name: 'H5Loading',
            align: 'cc'
          },
          {
            name: 'errorDisplay',
            align: 'tlabs',
            x: 0,
            y: 0
          },
          {
            name: 'infoDisplay'
          },
          {
            name: 'tooltip',
            align: 'blabs',
            x: 0,
            y: 56
          },
          {
            name: 'controlBar',
            align: 'blabs',
            x: 0,
            y: 0,
            children: [
              {
                name: 'progress',
                align: 'blabs',
                x: 0,
                y: 44
              },
              {
                name: 'playButton',
                align: 'tl',
                x: 15,
                y: 12
              },
              {
                name: 'timeDisplay',
                align: 'tl',
                x: 10,
                y: 7
              },
              {
                name: 'fullScreenButton',
                align: 'tr',
                x: 10,
                y: 12
              },
              {
                name: 'volume',
                align: 'tr',
                x: 5,
                y: 10
              }
            ]
          }
        ]
      },
      dialogFormShow: false,
      selectedList: [],
      form: {
        basePraises: 0,
        baseHits: 0
      },
      dialogImageUrl: '',
      dialogImgShow: false,
      curTarget: {},
      categoryType: 1,
      highlightCurrent: false, // 是否开启高亮
      showSelect: false, // 是否选中分类
      sortForm: {
        sort: 0,
        videoId: '',
        cateId: ''
      },
      editSortVisible: false, // 排序弹窗
      showCheckCategory: false,
      categoryNameArr: [],
      categoryForm: {
        cateIdList: []
      },
      platformTreeList: [],
      platformProps: {
        label: 'name',
        value: 'categoryId',
        children: 'children',
        expandTrigger: 'hover',
        multiple: true,
        emitPath: false
      },
      havChild: false,
      editCategoryVisible: false, // 修改分类弹窗
      noQuery: true,
      addVideoTableData: [],
      // 表格表头
      addVideoTableColumnList: Object.freeze([
        { id: 1, label: '视频名称', align: 'center', prop: 'name' },
        {
          id: 2,
          prop: 'cateName',
          label: '视频分类',
          width: '140',
          align: 'center',
          filter: 'cateName'
        },
        { id: 3, label: '状态', align: 'center', filter: 'status' }
      ]),
      // 请求参数
      addVideoTableQuery: {
        condition: {
          type: 1,
          keyword: '',
          platform: 1
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      addVideoTotal: 0,
      addVideoVisible: false,
      videoIdList: [], // 视频id 集合
      cateIdList: [], // 分类id 集合
      // 会员等级相关
      memberLevelList: [], // 会员等级列表
      memberLevelDialogVisible: false, // 设置会员等级弹窗
      memberLevelForm: {
        memberLevel: '',
        videoIds: [],
        crossPage: false
      },
      isBatchSetMemberLevel: false // 是否批量设置会员等级
    }
  },
  created() {
    // 获取资源分类树数据
    getCategoryTreeList(0).then(res => {
      this.platformTreeList = res
    })
    // 获取会员等级列表
    getVipLevelList().then(res => {
      this.memberLevelList = res || []
    })
    this.init()
  },
  methods: {
    // 上下架操作
    handleUpdateStatus(row) {
      const params = {
        videoId: row.videoId,
        videoInfoId: row.videoInfoId,
        status: row.status === 2 ? 3 : 2
      }
      this.$confirm(
        params.status === 3
          ? '下架以后该视频将不能正常播放，确认下架该视频？'
          : '确认上架该视频？',
        '温馨提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          closeOnPressEscape: false
        }
      ).then(() => {
        changeVideoStatus(params).then(res => {
          params.status === 3
            ? this.$message.success('下架成功')
            : this.$message.success('上架成功')
          this.init()
        })
      })
    },
    // 播放视频
    handlePlayVideo(row) {
      displayHit(row.videoId).then(res => {}) // 播放次数+1
      playVideoInfo({
        videoId: 0,
        videoInfoId: 0,
        videoFileId: row.videoFileId
      }).then(res => {
        if (res.playInfoList && res.playInfoList.length) {
          const sourceUrl = {}
          res.playInfoList.map(v => {
            sourceUrl[v.definition] = v.playURL
          })
          this.aliPlayerConfig.cover = res.coverUrl
          this.aliPlayerConfig.source = JSON.stringify(sourceUrl)
          this.playerTitle = res.videoName
          this.dialogVisible = true
          this.$nextTick(() => {
            this.$refs.aliplayer.init()
          })
        } else {
          this.$message.error('该视频暂时无法播放，请稍后重试！')
        }
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.init(false)
    },
    // 视频列表
    init(reset = true) {
      reset && (this.tableQuery.pager.page = 1)
      videoList(this.tableQuery).then(res => {
        if (this.havChild) {
          const flag = this.tableColumnList.some(item => {
            return item.id === 14
          })
          if (!flag) {
            this.tableColumnList.unshift({ id: 14, label: '排序', prop: 'listOrder', align: 'center', filter: 'sort' })
          }
        } else {
          this.tableColumnList = this.tableColumnList.filter(item => {
            return item.id !== 14
          })
        }
        res.records.forEach(v => {
          const memberLevelItem = this.memberLevelList.find(i => i.level === v.memberLevel)
          v.memberLevelName = memberLevelItem ? memberLevelItem.name : '-'
        })
        this.videoList = res.records
        this.total = res.total
      })
    },
    // 阿里云播放器事件
    handleReadyVideo(val) {
      this.player = val
    },
    handlePauseVideo() {
      // this.init()
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    },
    onSelectChange(arr) {
      this.selectedList = arr
    },
    setFake(row) {
      if (row.videoId) {
        this.form = {
          basePraises: row.basePraises,
          baseHits: row.baseHits
        }
        this.curTarget = row
      }
      this.dialogFormShow = true
    },
    cancel() {
      this.curTarget = {}
      this.$refs.form.resetFields()
      this.dialogFormShow = false
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const videoIds = this.curTarget.videoId ? [this.curTarget.videoId] : this.selectedList.map(v => v.videoId)
          const params = {
            ...this.form,
            videoIds
          }
          setVideoVirtual(params).then(() => {
            this.$message.success('操作成功')
            this.cancel()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    search(e) {
      if (!e) {
        this.noQuery = true
      } else {
        videoList(this.addVideoTableQuery).then(res => {
          this.noQuery = false
          this.addVideoTableData = res.records
          this.addVideoTotal = res.total
        })
      }
    },
    addVideo() {
      this.addVideoTableQuery.condition.notInCateId = this.tableQuery.condition.cateId
      this.addVideoVisible = true
    },
    addVideoCancel() {
      this.addVideoTableQuery.condition.keyword = ''
      this.addVideoTableQuery.pager.page = 1
      this.addVideoTableQuery.pager.pageSize = 10
      this.addVideoTotal = 0
      this.noQuery = true
      this.videoIdList = []
      this.addVideoVisible = false
    },
    addVideoConfirm() {
      if (this.videoIdList.length < 1) { return this.$message.error('请选择视频') }
      if (this.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 1,
        videoIdList: this.videoIdList,
        cateIdList: this.cateIdList
      }
      addVideo(data).then(res => {
        this.$message.success('添加成功')
        this.addVideoCancel()
        this.init()
        // this.$refs.tree.automaticTree()
      })
    },
    handleAddVideoPagination(val) {
      this.addVideoTableQuery.pager = val
      videoList(this.addVideoTableQuery).then(res => {
        this.addVideoTableData = res.records
        this.addVideoTotal = res.total
      })
    },
    // 取消选择事件
    handleCloseSelect(row) {
      this.videoIdList = this.videoIdList.filter(item => {
        return item !== row.videoId
      })
    },
    // 选择事件
    handleSelect(row) {
      this.videoIdList.push(row.videoId)
    },
    editCategory(row) {
      this.videoIdList = []
      if (row && JSON.stringify(row) !== '{}') {
        this.showCheckCategory = true
        this.videoIdList.push(row.videoId)
        this.categoryNameArr = row.cateName.split(';')
        this.categoryForm.cateIdList = row.cateIds
      } else {
        this.showCheckCategory = false
        this.categoryNameArr = []
        this.categoryForm.cateIdList = []
        this.selectedList.forEach(item => {
          this.videoIdList.push(item.videoId)
        })
      }
      this.editCategoryVisible = true
    },
    changeHandler() {

    },
    editCategoryCancel() {
      this.videoIdList = []
      this.cateIdList = []
      this.categoryForm.cateIdList = []
      this.editCategoryVisible = false
    },
    editCategoryConfirm() {
      if (this.videoIdList.length < 1) { return this.$message.error('请选择视频') }
      if (this.categoryForm.cateIdList.length < 1) { return this.$message.error('请选择分类') }
      const data = {
        type: 2,
        videoIdList: this.videoIdList,
        cateIdList: JSON.parse(JSON.stringify(this.categoryForm.cateIdList))
      }
      addVideo(data).then(res => {
        this.$message.success('添加成功')
        this.editCategoryCancel()
        this.init()
      })
    },
    setSort(row) {
      this.sortForm.videoId = row.videoId
      this.sortForm.sort = row.listOrder
      this.editSortVisible = true
    },
    sortCancel() {
      this.sortForm.sort = 0
      this.sortForm.videoId = ''
      this.sortForm.cateId = ''
      this.$refs.sortForm.resetFields()
      this.editSortVisible = false
    },
    sortConfirm() {
      if (this.tableQuery.condition.cateId) {
        this.sortForm.cateId = this.tableQuery.condition.cateId
      } else {
        return this.$message.error('请选择分类')
      }
      this.$refs.sortForm.validate(valid => {
        if (valid) {
          setVideoSort(this.sortForm).then(() => {
            this.$message.success('操作成功')
            this.sortCancel()
            this.init()
          })
        } else {
          return false
        }
      })
    },
    onNumChange(v, key) {
      if (key === 'sort') {
        if (!v) {
          this.$nextTick(() => {
            this.sortForm[key] = 0
          })
        }
      } else {
        if (!v) {
          this.$nextTick(() => {
            this.form[key] = 0
          })
        }
      }
    },
    preview(url) {
      this.dialogImageUrl = url
      this.dialogImgShow = true
    },
    cancelSelect() {
      const flag = this.tableColumnList.some(item => {
        return item.id === 14
      })
      if (flag) {
        this.tableColumnList.shift()
      }
      this.tableQuery.condition.cateId = ''
      this.highlightCurrent = false
      this.showSelect = false
      this.havChild = false
      this.init()
      // 刷新分类数据
      this.$refs.tree.onRegionHeaderRefresh()
    },
    // 资源分类添加事件
    handleNodeAddPlatForm(data) {
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.addVideoTableQuery.condition.notInCateId = data.categoryId
      this.addVideoVisible = true
    },
    // 获取当前试题内容
    handleTreeNodeClick(data, node) {
      if (!data.havChild) {
        this.havChild = true
      } else {
        this.havChild = false
      }
      // 添加分类id集合
      this.cateIdList = []
      this.cateIdList.push(data.categoryId)
      this.showSelect = true // 显示取消选择按钮
      this.highlightCurrent = true // 高亮所选中的分类
      this.tableQuery.condition.cateId = data.categoryId
      this.tableQuery.pager.page = 1
      this.init()
    },
    // 设置会员等级
    setMemberLevel(row) {
      this.isBatchSetMemberLevel = false
      this.memberLevelForm.videoIds = [row.videoId]
      this.memberLevelForm.memberLevel = row.memberLevel || ''
      this.memberLevelDialogVisible = true
    },
    // 批量设置会员等级
    batchSetMemberLevel() {
      if (this.selectedList.length === 0) {
        this.$message.warning('请选择要设置的视频')
        return
      }
      this.isBatchSetMemberLevel = true
      this.memberLevelForm.videoIds = this.selectedList.map(item => item.videoId)
      this.memberLevelForm.memberLevel = ''
      this.memberLevelForm.crossPage = false
      this.memberLevelDialogVisible = true
    },
    // 会员等级弹窗取消
    memberLevelCancel() {
      this.memberLevelForm = {
        memberLevel: '',
        videoIds: [],
        crossPage: false
      }
      this.memberLevelDialogVisible = false
    },
    // 会员等级弹窗确认
    memberLevelConfirm() {
      this.$refs.memberLevelForm.validate(valid => {
        if (valid) {
          const params = {
            ...this.tableQuery.condition,
            updateMemberLevel: this.memberLevelForm.memberLevel,
            videoIdList: this.memberLevelForm.videoIds,
            setType: this.memberLevelForm.crossPage ? 1 : 2
          }
          setVideoMemberLevel(params).then(() => {
            this.$message.success('设置成功')
            this.memberLevelCancel()
            this.init()
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.video-links {
  color: #409eff;
  cursor: pointer;
}

.content-left {
  padding-top: 15px;
  height: calc(100vh + 220px);

  .tree-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    span {
      font-size: 22px;
      font-weight: bold;
      margin-right: 10px;
    }
  }
  .content-container {
    width: 100%;
    height: 100%;
    padding: 0;
    background-color: #fff;
    &::v-deep .el-scrollbar__wrap.default-scrollbar__wrap {
      overflow-x: hidden;
    }
    &::v-deep .el-scrollbar__view.p20-scrollbar__view {
      background-color: #fff;
      padding-right: 15px;
      box-sizing: border-box;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      -o-box-sizing: border-box;
      -ms-box-sizing: border-box;
    }
    &::v-deep .el-scrollbar__bar.is-vertical {
      width: 8px;
    }
  }
}
.content-right {
  flex: 8;
  padding-right: 20px;

}
::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
  display:none;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
