<template>
  <div class="app-container">
    <el-tree
      ref="tree"
      class="mycs-tree"
      :data="treeData"
      node-key="categoryId"
      :filter-node-method="filterNode"
      :highlight-current="onHighlightCurrent"
      :props="{ label: 'name', children: 'childList', isLeaf: false }"
      lazy
      :load="loadNode"
      expand-on-click-node
      :accordion="true"
      @node-click="handleClick"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <span v-if="!data.havChild && (platformType===1)" @click.stop="() => handleAddPlatForm(data)">
          <i class="el-icon-plus" />
        </span>
        <span class="over-text" :title="node.label">{{ node.label }}({{ data.amount }})</span>
        <span class="fr tree-option">
          <i :class="!data.havChild ? '' : node.expanded ? 'el-icon-arrow-down':'el-icon-arrow-right'" />
        </span>
      </span>
    </el-tree>
  </div>
</template>

<script>
import { getCategoryList } from '@/api/category'

export default {
  name: 'Tree',
  props: {
    highlightCurrent: {
      type: Boolean,
      default: false
    },
    platformType: {
      type: Number,
      default: 1
    },
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      onHighlightCurrent: this.highlightCurrent,
      treeData: [],
      categoryLevel: [],
      node: null,
      resolve: null
    }
  },
  watch: {
    highlightCurrent() {
      this.onHighlightCurrent = this.highlightCurrent
    }
  },
  methods: {
    ellipsisFont(font) {
      return font.length >= 8 ? font.substring(0, 7) + '...' : font
    },

    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    },
    handleAddPlatForm(data) {
      this.$emit('nodeAddPlatForm', data)
    },
    onRegionHeaderRefresh() {
      getCategoryList(0, this.type).then(res => {
        this.treeData = res
      })
    },
    automaticTree() {
      this.treeData = []
      getCategoryList(0, 1).then(res => {
        // this.resolve(res)
        this.treeData = res
        const node = this.$refs.tree.getNode('478')
        console.log(node, 'node')
        node.expand()
      })
      // this.categoryLevel.forEach(item => {
      //   if (item.level === 0) {
      //     getCategoryList(0, 1).then(res => {
      //       return item.resolve(res)
      //     })
      // this.defaultExpandedNode.push(item.node.data.categoryId)
      // const node = this.$refs.tree.getNode(data.categoryId)
      // node.expand()
      // item.node.expand()
      // }
      // 如果展开其他级节点，动态从后台加载下一级节点列表
      // if (item.node.level >= 1) {
      //   getCategoryList(item.node.data.categoryId, 1).then(res => {
      //     return item.resolve(res)
      //   })
      // }
      // })
    },
    // 懒加载
    loadNode(node, resolve) {
      this.node = node
      this.resolve = resolve
      // 如果展开第一级节点，从后台加载一级节点列表
      if (node.level === 0) {
        getCategoryList(0, this.type).then(res => {
          return resolve(res)
        })
      }
      // 如果展开其他级节点，动态从后台加载下一级节点列表
      if (node.level >= 1) {
        getCategoryList(node.data.categoryId, this.type).then(res => {
          return resolve(res)
        })
      }
    },
    // 节点点击
    handleClick(data, node) {
      this.onHighlightCurrent = true
      data.level = node.level
      this.categoryLevel.splice(node.level, 1, data)
      this.$emit('nodeClick', data, node)
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
}
.mycs-tree ::v-deep .el-tree-node > .el-tree-node__children,
.mycs-tree ::v-deep .el-tree-node.is-expanded > .el-tree-node__children {
  overflow: visible;
}
.mycs-tree ::v-deep .el-tree-node.is-drop-inner>.el-tree-node__content .over-text{
  background: #4AB9A3;
}
.el-input {
  margin-bottom: 14px;
}
.btn-group {
  /*margin-bottom: 20px;*/
  .el-button {
    flex: 1;
    justify-content: center;
  }
}
.svg-icon{
  margin-top: 5px;
  margin-right: 5px;
}
.custom-tree-node {
  display: flex;
  position: relative;
  width: 100%;
  height: 24px;
  line-height: 24px;
  font-size: 14px;
  overflow: hidden;
}
.tree-option {
  position: absolute;
  top: 0;
  right: 20px;
  height: 24px;
  width: 20px;
  margin-right: -25px;
  .option-mask {
    padding: 10px 20px;
    position: fixed;
    margin-left: 30px;
    margin-top: -45px;
    background: #ffffff;
    width: 200px;
    z-index: 99;
    border-radius: 3px;
    box-shadow: 1px 1px 15px 0 rgba($color: #000000, $alpha: 0.2);
    .list-item {
      line-height: 2;
      color: #666;
    }
    &.bottom{
      &:after{
        top: 105px;
      }
    }
    &:after{
      content: '';
      position: absolute;
      top: 28px;
      left: -4px;
      display: block;
      border: 4px solid #ffffff;
      width: 0;
      height: 0;
      transform: rotate(45deg);
      box-shadow: -1px 1px 20px 0 rgba($color: #000000, $alpha: 0.2);
    }
  }
}
.over-text{
  font-size: 14px;
  margin: 0 20px 0 10px;
  flex: 1;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
</style>
