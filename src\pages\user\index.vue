<template>
  <div class="app-container">
    <!-- header -->
    <div class="clearfix mg-b">
      <header class="page-title fl">用户列表</header>
    </div>
    <div class="search-column">
      <div class="fl">
        <div class="search-column__item">
          <el-button type="primary" @click="addUserDialogShow()">新增用户</el-button>
        </div>
      </div>
      <div class="fr">
        <div class="search-column__item">
          <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable @clear="searchKeyword()" @keydown.enter.native="searchKeyword()">
            <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer;" @click="searchKeyword()" />
            <el-select slot="prepend" v-model="searchType" style="width: 130px" placeholder="请选择搜索的字段" @change="searchSelectChange()">
              <el-option v-for="item in searchTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-input>
        </div>
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.haveRole" clearable placeholder="是否拥有CMS权限" @change="getUserList">
            <el-option v-for="item in roleList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.isHire" clearable placeholder="是否单位用户" @change="getUserList">
            <el-option v-for="item in isHireList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.status" clearable placeholder="状态" @change="getUserList">
            <el-option label="启用" :value="1" />
            <el-option label="停用" :value="0" />
          </el-select>
        </div>
        <div class="search-column__item">
          <el-select v-model="tableQuery.condition.memberLevel" clearable placeholder="会员等级" @change="getUserList">
            <el-option v-for="item in memberLevelList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <!-- body -->
    <el-table :data="userList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" :prop="col.prop" :label="col.label" :align="col.align" :width="col.width">
        <template slot-scope="scope">
          <span v-if="col.filter">
            {{ scope.row[col.prop] === ''?'--':scope.row[col.prop] }}
          </span>
          <span v-else>
            {{ scope.row[col.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否用户单位">
        <template slot-scope="{row}">{{ row.isHire ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column align="center" label="会员等级">
        <template slot-scope="{row}">{{ row.memberLevelName }}</template>
      </el-table-column>
      <el-table-column align="center" label="状态">
        <template slot-scope="{row}">{{ row.status===1 ? '启用' : '停用' }}</template>
      </el-table-column>
      <el-table-column align="center" label="注册时间" prop="regTime" width="170px" />
      <el-table-column align="center" label="操作" width="240px">
        <template slot-scope="{row}">
          <el-button size="mini" type="text" @click="quickLogin(row.safeCode)">一键登录</el-button>
          <el-button size="mini" type="text" @click="editRow(row)">编辑</el-button>
          <el-button size="mini" type="text" @click="changeStatus(row)">{{ row.status === 1?'停用':'启用' }}</el-button>
          <el-button size="mini" type="text" @click="viewPersonal(row)">个人信息</el-button>
          <el-button size="mini" type="text" @click="resetPassword(row)">重置密码</el-button>
          <el-button v-permission="['User:settingRole']" size="mini" type="text" @click="setRole(row)">设置CMS角色</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />
    <DialogUserEdit :title="title" :show.sync="dialogVisible" :is-audit="isAudit" :data="formData" @handleCancel="handleCancelDialogForm" @handleSave="handleSaveDialogForm" />
    <DialogUserInfo :title="dialogInfoTitle" :show.sync="dialogInfoVisible" :data="userInfoData" />
    <DialogSetRole :show.sync="dialogRoleVisible" :uid="curUid" />
  </div>
</template>

<script>
import {
  userList,
  editUser,
  addUser,
  changeUserStatus,
  getPersonalDetail,
  resetPassword
} from '@/api/userManage'
import { easyLogin } from '@/api/user'
import Pagination from '@/components/Pagination'
import DialogUserEdit from './compontents/dialogUserEdit'
import DialogUserInfo from './compontents/dialogUserInfo'
import DialogSetRole from './compontents/dialogSetRole'
import permission from '@/directive/permission/index'
import { getVipLevelList } from '@/api/vip'
// import { encode } from 'js-base64'
// import { encryptDes } from '@/utils/des'
export default {
  name: 'UserList',
  components: {
    Pagination,
    DialogUserEdit,
    DialogUserInfo,
    DialogSetRole
  },
  directives: { permission },
  data() {
    return {
      layout: 'total, prev, pager, next, jumper',
      // user edit type
      action: 'add',
      // search params
      keyword: '',
      tableQuery: {
        condition: {
          haveRole: '',
          phone: null,
          realName: '',
          staffUserName: '',
          username: '',
          email: '',
          isHire: '',
          isMember: '',
          memberLevel: '',
          status: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      searchType: 'realName',
      searchTypeList: [
        { label: '用户姓名', value: 'realName' },
        { label: '手机号', value: 'phone' },
        { label: '用户账号', value: 'username' },
        { label: '员工账号', value: 'staffUserName' },
        { label: '邮箱', value: 'email' }
      ],
      memberLevelList: [],
      roleList: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ],
      isMemberList: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      isHireList: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      dialogInfoTitle: '查看账号信息',
      dialogInfoVisible: false,
      userInfoData: {},
      title: '新建用户',
      dialogVisible: false,
      dialogRoleVisible: false,
      formTemp: {
        uid: 0,
        email: '',
        password: '',
        phone: '',
        realName: ''
      },
      formData: {
        uid: 0,
        email: '',
        password: '',
        phone: '',
        realName: ''
      },
      // table field
      tableColumnList: [
        { label: 'UID', prop: 'uid', align: 'center', width: '180px' },
        { label: '姓名', prop: 'realName', align: 'center' },
        { label: '用户名', prop: 'username', align: 'center' },
        { label: '邮箱', prop: 'email', align: 'center', filter: true },
        { label: '电话', prop: 'phone', align: 'center', filter: true }
      ],
      // table list
      userList: [],
      // pagination
      total: 0,
      // desc
      descList: [
        {
          value: 0,
          label: '正常排序'
        },
        {
          value: 1,
          label: '按注册时间排序'
        }
      ],
      curUid: '',
      isAudit: false
    }
  },
  created() {
    this.getUserList()
    getVipLevelList().then(res => {
      this.memberLevelList = res.map(item => ({
        label: item.name,
        value: item.level
      }))
    })
  },
  methods: {
    // quick login
    async quickLogin(safeCode) {
      const res = await easyLogin({
        safeCode,
        actionCode: 'UserIndex' + 'easyLogin'
      })
      const newWindow = window.open()
      if (res) {
        const params = encodeURIComponent(JSON.stringify(res))
        newWindow.location.href =
          process.env.VUE_APP_SAAS_URL + '#/home?p=' + params
      }
    },
    // get user list
    getUserList() {
      this.searchSelectChange()
      userList(this.tableQuery).then(res => {
        res.records.forEach(item => {
          item.memberLevelName = this.memberLevelList.find(level => level.value === item.memberLevel).label
        })
        this.userList = res.records
        this.total = res.total
      })
    },
    searchKeyword() {
      this.tableQuery.pager.page = 1
      this.getUserList()
    },
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUserList()
    },
    // add user
    addUserDialogShow() {
      this.action = 'add'
      this.title = '新增用户'
      this.isAudit = false
      this.dialogVisible = true
    },
    // edit row
    editRow(row) {
      this.action = 'edit'
      this.title = '编辑用户'
      this.isAudit = row.isAudit
      this.dialogVisible = true
      this.formData.uid = row.uid
      this.formData.realName = row.realName
      this.formData.password = row.password
      this.formData.email = row.email
      this.formData.phone = row.phone
    },
    // disable row
    changeStatus(row) {
      const status = row.status === 1 ? '停用' : '启用'
      this.$confirm(
        '您确定' + status + ' "' + row.realName + '" 这个账号吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        changeUserStatus({
          userId: row.uid,
          status: row.status === 1 ? 0 : 1
        }).then(res => {
          this.$message.success(row.status === 1 ? '停用成功' : '启用成功')
          this.getUserList()
        })
      })
    },
    // view personal detail
    viewPersonal(row) {
      getPersonalDetail({ userId: row.uid }).then(res => {
        this.userInfoData = res
        this.dialogInfoVisible = true
      })
    },
    // reset user password
    resetPassword(row) {
      this.$confirm(
        '是否重置‘' + row.username + '’这个用户的密码为‘mycs*89.’？',
        '温馨提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        resetPassword({ userId: row.uid }).then(res => {
          this.$message({
            type: 'success',
            message: '重置成功！'
          })
        })
      })
    },
    // add user
    userOpttion() {
      if (this.action === 'add') {
        addUser(this.formData).then(res => {
          this.$message({
            type: 'success',
            message: '增加成功！'
          })
          this.dialogVisible = false
          this.getUserList()
        })
      } else if (this.action === 'edit') {
        editUser(this.formData).then(res => {
          this.$message({
            type: 'success',
            message: '编辑成功！'
          })
          this.dialogVisible = false
          this.getUserList()
        })
      }
    },
    searchSelectChange(val) {
      this.tableQuery.condition.username = ''
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.staffUserName = ''
      this.tableQuery.condition.phone = null
      this.tableQuery.condition.email = ''
      this.tableQuery.condition[this.searchType] = this.keyword.replace(/\s*/g, '')
    },
    // reset form data
    resetForm() {
      this.formData = JSON.parse(JSON.stringify(this.formTemp))
    },
    // canel form submit
    handleCancelDialogForm() {
      this.resetForm()
      this.dialogVisible = false
    },
    // save form submit
    handleSaveDialogForm(data) {
      this.formData.realName = data.realName
      this.formData.password = data.password
      // this.formData.password = encryptDes(this.formData.password, 'mycs2020')
      // this.formData.password = encode(this.formData.password)
      this.formData.email = data.email
      this.formData.phone = data.phone
      this.userOpttion()
    },
    // set role
    setRole(row) {
      this.curUid = row.uid
      this.dialogRoleVisible = true
    }
  }
}
</script>
