<template>
  <div class="app-container">
    <!-- search -->
    <div class="search-column">
      <div class="search-column__item">
        <el-select v-model="keywordName" filterable placeholder="请选择类型" style="width: 110px" @change="handleFilter">
          <el-option v-for="item in keywordType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-input v-model="keyword" placeholder="根据左侧类型进行对应关键字查询" clearable @change="handleFilter">
          <i slot="prefix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <!-- FIXME: -->
      <div class="search-column__item">
        <OrgTree v-model="conditionWatch.containChildOrgIdSet" :show="true" :org-id="orgId" @half="setOrgIdSet" />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.identityIdSet"
          placeholder="请选择身份"
          :options="identityTree"
          collapse-tags
          :props="{
            multiple: true,
            value:'identityId',
            label:'name',
            children:'childList',
            emitPath:false
          }"
          clearable
          @change="identityChange"
        />
      </div>
      <div class="search-column__item">
        <el-cascader
          v-model="conditionWatch.majorIdSet"
          placeholder="请选择专科"
          :options="majorList"
          collapse-tags
          :props="{
            multiple: true,
            value:'majorId',
            label:'name',
            children:'childList'
          }"
          :disabled="disabled"
          clearable
        />
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.displayStatus" clearable placeholder="请选择状态">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.additional" clearable placeholder="请选择激活状态">
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button type="primary" @click="reset">重置</el-button>
        <el-button type="primary" @click="dialogVisible = true">导出</el-button>
      </div>
    </div>
    <!-- table -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange" />
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />
    <el-dialog title="请问导出多少页的数据？" :visible.sync="dialogVisible" width="500px">
      <div style="margin-bottom: 10px;">
        <el-radio v-model="dialogType" label="1">导出
          <div class="el-input el-input--medium" style="width: 90px">
            <input v-model.lazy="dialogPager.start" class="el-input__inner" min="1" :max="Math.ceil(total / pager.pageSize)" type="number" placeholder="请输入">
          </div>-
          <div class="el-input el-input--medium" style="width: 90px">
            <input v-model.lazy="dialogPager.end" class="el-input__inner" :min="dialogPager.start" :max="Math.ceil(total / pager.pageSize)" type="number" placeholder="请输入">
          </div>
          页的数据
        </el-radio>
      </div>
      <div>
        <el-radio v-model="dialogType" label="2">导出全部</el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="!total" @click="exportStatus">确 定 导 出</el-button>
      </span>
    </el-dialog>
    <ExportPopup :eid="eid" />
  </div>
</template>

<script>
import table from '@/mixins/table'
import request from '@/api/userExport'
import { treeList } from '@/api/major'
import ExportPopup from '@/components/ExportPopup'
import { getChildrenByOrgId } from '@/api/wjw'
import OrgTree from './compontents/orgTree'
import { identityTreeList, majorTreeList } from '@/api/category'

const columns = [
  {
    props: { label: 'UID', align: 'center', prop: 'userId', width: '70' }
  },
  {
    props: { label: '姓名', align: 'center', prop: 'realName' }
  },
  {
    props: { label: '员工账号', align: 'center', prop: 'username' }
  },
  {
    props: { label: '手机', align: 'center', prop: 'phone' }
  },
  {
    props: { label: '部门', align: 'center', prop: 'deptName' }
  },
  {
    props: { label: '身份', align: 'center', prop: 'identityName' }
  },
  {
    props: { label: '职称', align: 'center', prop: 'academicName' }
  },
  {
    props: { label: '专科', align: 'center', prop: 'majorName' }
  },
  {
    props: { label: '所在单位', align: 'center', prop: 'orgName' }
  },
  {
    props: { label: '单位类型', align: 'center', prop: 'orgTypeName' }
  },
  {
    props: {
      label: '单位层级',
      align: 'center',
      prop: 'orgLevelName',
      width: '90'
    }
  },
  {
    props: { label: '所属区域', align: 'center', prop: 'areaName' }
  },
  {
    props: {
      label: '状态',
      align: 'center',
      prop: 'displayStatusName',
      width: '90'
    }
  },
  {
    props: {
      label: '激活状态',
      align: 'center',
      prop: 'activationName',
      width: '90'
    }
  },
  {
    props: { label: '角色', align: 'center', prop: 'roleName' }
  },
  {
    props: { label: '创建时间', align: 'center', prop: 'createTime' }
  }
]

export default {
  name: 'Staff',
  filters: {
    statusFlt(v) {
      const arr = ['未开始', '进行中', '已结束']
      return arr[v]
    }
  },
  components: { ExportPopup, OrgTree },
  mixins: [table],
  data() {
    return {
      identityTree: [],
      orgId: '', // 用于查询单位
      selectedArr: [], // 单位选中数组
      orgOptions: [], // 单位级联选项
      columns,
      request,
      timeRange: [],
      conditionWatch: {
        identityIdSet: [],
        majorIdSet: [],
        containChildOrgIdSet: [],
        orgIdSet: [],
        additional: '',
        displayStatus: ''
      },
      otherDefaultKey: ['keyword', 'keywordName'],
      majorList: [],
      keyword: '',
      keywordName: 'staffRealName',
      keywordType: [
        { value: 'staffRealName', name: '员工姓名' },
        { value: 'staffUsername', name: '员工帐号' },
        { value: 'deptName', name: '员工部门' }
      ],
      typeList: [
        { value: 1, label: '启用-待邀请' },
        { value: 2, label: '启用-邀请拒绝' },
        { value: 3, label: '启用-邀请超时' },
        { value: 4, label: '启用-邀请中' },
        { value: 5, label: '启用' },
        { value: 6, label: '停用-待邀请' },
        { value: 7, label: '停用' }
      ],
      statusList: [
        { value: 0, label: '未激活' },
        { value: 1, label: '激活' }
      ],
      dialogVisible: false,
      dialogType: '1',
      dialogPager: {
        start: 1,
        end: 1
      },
      eid: '',
      exportList: false,
      initList: false,
      disabled: false
    }
  },
  watch: {
    'dialogPager.start'(v) {
      if (!v || v < 1) {
        this.dialogPager.start = 1
      }
      if (v > Math.ceil(this.total / this.pager.pageSize)) {
        this.dialogPager.start = Math.ceil(
          this.total / this.pager.pageSize
        )
      }
      if (parseFloat(v) > parseFloat(this.dialogPager.end)) {
        this.dialogPager.end = parseFloat(v)
      }
    },
    'dialogPager.end'(v) {
      console.log(v)
      if (!v || parseFloat(v) < parseFloat(this.dialogPager.start)) {
        this.dialogPager.end = parseFloat(this.dialogPager.start)
      }
      if (v > Math.ceil(this.total / this.pager.pageSize)) {
        this.dialogPager.end = Math.ceil(
          this.total / this.pager.pageSize
        )
      }
    }
  },
  created() {
    identityTreeList().then(res => {
      const newArr = this.clearNullChildList(res, 'childList')
      newArr.push({ name: '无', identityId: '0' })
      this.identityTree = newArr
    })
    Promise.all([treeList(), request.getOrgID()]).then(res => {
      [this.majorList, this.orgId] = res
      this.getList()
      // getChildrenByOrgId(this.orgId).then(res => {
      //   this.getList()
      //   this.orgOptions = res
      // })
    })
  },
  methods: {
    identityChange(e) {
      if (e[0] === '0') {
        // 身份选'无'，专科也为无
        this.conditionWatch.majorIdSet = []
        this.disabled = true
        return
      } else {
        this.disabled = false
      }
      if (e.length > 0) {
        majorTreeList({ identityIds: e }).then(res => {
          this.majorList = res
        })
      }
    },
    setOrgIdSet(arr) {
      this.conditionWatch.orgIdSet = arr
    },
    exportStatus() {
      const query = JSON.parse(JSON.stringify(this.getListData()))
      if (this.dialogType === '1') {
        query.condition.index =
          (this.dialogPager.start - 1) * query.pager.pageSize
        const lastPage = Math.ceil(this.total / query.pager.pageSize)
        if (lastPage === parseFloat(this.dialogPager.end)) {
          query.condition.size =
          this.total - query.condition.index
        } else {
          query.condition.size =
          this.dialogPager.end * query.pager.pageSize - query.condition.index
        }
      } else {
        query.condition.index = 0
        query.condition.size = this.total
      }
      this.request.export(query.condition).then(res => {
        this.eid = res
        this.dialogVisible = false
      })
    },
    getList() {
      if (this.listLoading || this.orgId === '') return
      const data = this.getListData()
      this.listLoading = true
      return this.request.list(data).then(res => {
        this.list = res.records || []
        this.total = res.total || 0
        this.listLoading = false
      }, () => {
        this.listLoading = false
      })
    },
    getListData() {
      const data = {
        condition: this.copy(Object.assign(this.conditionWatch, this.conditionCache)),
        pager: this.pager
      }
      data.condition[this.keywordName] = this.keyword
      // 无勾选单位时 默认传递当前orgId
      const { containChildOrgIdSet, orgIdSet, majorIdSet } = data.condition
      if (!containChildOrgIdSet.length && !orgIdSet.length) {
        data.condition.containChildOrgIdSet = [this.orgId]
      }
      // 扁平化
      if (majorIdSet && majorIdSet.length) {
        data.condition.majorIdSet = this.flatten(data.condition.majorIdSet)
      }
      return data
    },
    orgLoad(node, resolve) {
      if (node.level === 0) return resolve([])
      getChildrenByOrgId(node.data.orgId)
        .then(res => {
          res.forEach(i => {
            i.leaf = !i.hasSub
          })
          // node.data.childList = res
          resolve(res)
        })
        .catch(() => {
          resolve([])
        })
    },
    onOrgChange(v) {
      console.log(this.$refs.org)
      this.condition.containChildOrgIdSet = [v]
      this.handleFilter()
    },
    // 获取全部顶级全选节点
    getCheckedKeys(node) {
      let arr = []
      const arrNode = []
      node.forEach(i => {
        if (i.checked) {
          arr.push(i.data.orgId)
          console.log(i.childNodes.length)
          if (i.childNodes.length) {
            arrNode.push({
              orgId: i.data.orgId,
              childNodes: i.childNodes
            })
          }
        }
      })
      console.log(arrNode)
      arrNode.forEach(i => {
        i.childNodes.forEach(j => {
          arr = arr.filter(item => item !== j.data.orgId)
        })
      })
      return arr
    },
    // 扁平化
    flatten(arr) {
      return arr.reduce(
        (a, b) => a.concat(Array.isArray(b) ? this.flatten(b) : b),
        []
      )
    },
    // tree 复选框选中
    handleCheckChange(data, checked, indeterminate) {
      const selectedArr = this.getCheckedKeys(
        this.$refs.org.store._getAllNodes()
      )
      console.log(selectedArr)
      // this.form.unit = selectedArr.join(',')
      // this.defaultCheckedKeys = selectedArr
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    }
  }
}
</script>
