<template>
  <el-dialog title="直播数据" :visible.sync="visible" :before-close="beforeClose" width="90vw" top="8vh">
    <div class="app-container">

      <h2>直播概览</h2>
      <el-row style="margin-left:20px">
        <el-table :data="list" border stripe>
          <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <span>{{ scope.row[col.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <h2>观众列表</h2>
      <AudienceList v-if="visible" :live-id="liveId" />

      <div class="search-column">
        <div class="search-column__item">
          <h2>评论列表</h2>
        </div>
        <div class="search-column__item fr">
          <el-button type="primary" @click="exportFile()">导出</el-button>
        </div>
      </div>
      <el-row style="margin-left:20px">
        <el-table :data="commentList" border stripe>
          <el-table-column v-for="col in tableColumn_comment" :key="col.id" v-bind="col">
            <template slot-scope="scope">
              <template v-if="col.prop==='content'">
                <span v-if="scope.row[col.prop]" v-html="scope.row[col.prop]" />
                <el-image v-else style="width: 100px; height: 100px" :src="scope.row['img']" fit="cover" />
              </template>
              <template v-else>
                <span>{{ scope.row[col.prop] }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <Pagination :total="commentTotal" :page="commentPager.page" @pagination="commentPagination" />

    </div>
  </el-dialog>
</template>

<script>
import { getLiveData, getLiveComments, exportLiveComment } from '@/api/liveManage'
import Pagination from '@/components/Pagination/index.vue'
import AudienceList from './audienceList.vue'

export default {
  name: 'CostData',
  components: { Pagination, AudienceList },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    liveId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 1, label: '直播主题', align: 'center', prop: 'title' },
        { id: 2, label: '直播方式', align: 'center', prop: 'liveMethod' },
        { id: 3, label: '直播类型', align: 'center', prop: 'liveType' },
        { id: 4, label: '内容扩展方', align: 'center', prop: 'contentExtender' },
        { id: 5, label: '讲师', align: 'center', prop: 'lecturer' },
        { id: 6, label: '直播时间', align: 'center', prop: 'startTime' },
        { id: 7, label: '直播时长(分)', align: 'center', prop: 'totalDuration' },
        { id: 8, label: '观看人数', align: 'center', prop: 'uvCount' },
        { id: 9, label: '观看人次', align: 'center', prop: 'pvCount' },
        { id: 10, label: '人均观看时长(分)', align: 'center', prop: 'agvDuration' },
        { id: 11, label: '评论数', align: 'center', prop: 'comments' },
        { id: 12, label: '点赞数', align: 'center', prop: 'likeCount' }
      ]),
      tableColumn_comment: Object.freeze([
        { id: 1, label: '观众姓名', align: 'center', prop: 'fromUsername' },
        { id: 2, label: '评论内容', align: 'center', prop: 'content' },
        { id: 3, label: '评论时间', align: 'center', prop: 'createTime' }
      ]),
      list: [],
      commentList: [],
      commentTotal: 0,
      commentPager: {
        page: 1,
        pageSize: 10
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.liveId) {
          getLiveData(this.liveId).then(res => {
            this.list.length = 0
            this.list.push(res)
          })
          this.getComments()
        }
      }
    }
  },
  methods: {
    // 评论列表
    getComments() {
      const query = {
        condition: { liveId: this.liveId },
        pager: this.commentPager
      }
      getLiveComments(query).then(res => {
        this.commentList = res.records
        this.commentTotal = res.total
      })
    },
    // 评论分页
    commentPagination(val) {
      this.commentPager = val
      this.getComments()
    },
    beforeClose(done) {
      this.$emit('update:visible', false)
      this.$nextTick(() => {
        done()
      })
    },
    exportFile() {
      exportLiveComment(this.liveId).then(res => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style>

</style>
