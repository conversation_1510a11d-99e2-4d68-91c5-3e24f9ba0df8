import request from '@/utils/request'

/**
 * 用户管理 : 员工列表查询导出
 */

export default {
  // 查询员工信息列表
  list(param) {
    return request({
      url: `/staff/export/list`,
      method: 'post',
      data: param
    })
  },
  // 创建文件
  export(param) {
    return request({
      url: `/staff/export/create/file`,
      method: 'post',
      data: param
    })
  },
  // 获取单位Id
  getOrgID(params) {
    return request({
      url: `/wjw/system/country/orgId`,
      method: 'get',
      params
    })
  }
}
