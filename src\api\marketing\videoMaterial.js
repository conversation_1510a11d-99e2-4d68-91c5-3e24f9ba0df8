import request from '@/utils/request'

// 视频素材列表
export function promoteVideoList(query) {
  return request({
    url: '/promoteVideo/list',
    method: 'post',
    data: query
  })
}

// 视频素材修改
export function promoteVideoEdit(query) {
  return request({
    url: '/promoteVideo/edit',
    method: 'post',
    data: query
  })
}
// 视频素材删除
export function promoteVideoRemove(videoInfoId) {
  return request({
    url: `/promoteVideo/remove/${videoInfoId}`,
    method: 'post'
  })
}
