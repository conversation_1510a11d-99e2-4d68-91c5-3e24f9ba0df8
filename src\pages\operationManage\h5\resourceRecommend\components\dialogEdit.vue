<template>
  <el-dialog :title="rid ? '编辑': '新增'" :visible.sync="visible" width="800px" center>
    <div class="text-center">
      <el-radio-group v-model="type" size="small">
        <el-radio-button label="1">课程</el-radio-button>
        <el-radio-button label="2">视频</el-radio-button>
      </el-radio-group>
    </div>
    <dialogList ref="list" :type="type" :data="data" @change="changeId" />
  </el-dialog>
</template>
<script>
import dialogList from './dialogList.vue'
import request from '@/api/config/resourceRecommend'

export default {
  name: 'ResourceRecommendDialogEdit',
  components: {
    dialogList
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    rid: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: this.show,
      type: '1'
    }
  },
  watch: {
    visible(v) {
      if (!v) {
        this.$emit('update:show', v)
      }
    },
    show(v) {
      this.visible = v
      if (v) {
        this.type = '1'
        this.$refs.list.recharge()
        this.$refs.list.handleFilter()
      }
    }
  },
  methods: {
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    changeId(id) {
      if (!this.rid) {
        request.create({
          type: this.type,
          resourceId: id
        }).then(res => {
          this.$emit('update:show', false)
          this.$message.success(`新增成功`)
        })
      } else {
        request.edit({
          type: this.type,
          resourceId: id,
          id: this.rid
        }).then(res => {
          this.$emit('update:show', false)
          this.$message.success(`修改成功`)
        })
      }
    }
  }
}
</script>
