<template>
  <section class="studentList">
    <h2>
      设置每个省份的学分单价，系统根据学员报名信息中的所在地区进行对应，收取对应的费用
    </h2>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="setPrice(scope.row)">设置单价</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog center title="设置单价" :visible.sync="setVisible" width="400px">
      <el-form ref="form" :model="form">
        <el-form-item label="单价:" required :label-width="120">
          <el-input-number v-model="form.price" :controls="false" :min="0.01" :precision="2" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="setVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit('form')">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import { creditAreaPriceList, setCreditPrice } from '@/api/credit'
export default {
  data() {
    return {
      tableData: [],
      setVisible: false,
      tableColumn: [
        { prop: 'areaName', label: '省份' },
        { prop: 'price', label: '单价（人民币）' }
      ],
      form: {
        price: 1,
        areaId: ''
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      creditAreaPriceList().then(res => {
        this.tableData = res
      })
    },
    setPrice(row) {
      this.form.areaId = row.areaId
      this.form.price = row.price
      this.setVisible = true
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          setCreditPrice(this.form).then(() => {
            this.$message.success('设置成功')
            this.setVisible = false
            this.getList()
          }).catch(error => {
            this.$message.error(error)
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.studentList {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input,
    .el-cascader,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding: 25px 20px 0;
    background-color: #fff;
  }
}
</style>
