<template>
  <section>
    <el-dialog
      title="拜访列表"
      :visible.sync="dialogVisible"
      width="420px"
      center
      :before-close="handleClose"
    >
      <ul>
        <li v-for="item in list" :key="item.id">
          <div>
            <p>
              <span>拜访客户</span> {{ item.customerName }}
            </p>
            <p>
              <span>客户单位</span> {{ item.companyName }}
            </p>
            <p>
              <span>拜访时间</span> {{ item.visitTime }}
            </p>
          </div>
          <div>
            <el-button
              :type="item.status === 0 ? 'warning' :(item.status === 1 ? 'primary' : 'danger')"
              @click="viewDetail(item.id)"
            >{{ item.status === 0 ? '待审核' :(item.status === 1 ? '有效' : '无效') }}</el-button>
          </div>
        </li>
      </ul>
    </el-dialog>

    <el-dialog
      :title="detail.status === 0 ? '拜访审核' : '拜访详情'"
      :visible.sync="dialogVisibleDetail"
      width="520px"
      center
    >
      <h4>拜访位置
        <p>{{ detail.targetSiteName }}</p>
        <div>{{ detail.rangeOffset + '︱' + detail.targetSiteAddress }}</div>
        <img :src="detail.targetSiteImgUrl" @click="viewImg(detail.targetSiteImgUrl)">
      </h4>
      <h4>拜访实景照片
        <img :src="detail.signImgUrl" style="width: 200px;height: 100px;display: block;" @click="viewImg(detail.signImgUrl)">
      </h4>
      <h4>产品 <span>{{ detail.productDesc }}</span></h4>
      <h4>拜访时间 <span>{{ detail.visitTime }}</span></h4>
      <h4>拜访客户 <span>{{ detail.customer }}</span></h4>
      <h4>客户单位 <span>{{ detail.companyName }}</span></h4>
      <h4>拜访方式 <span>{{ detail.visitType }}</span></h4>
      <h4>会面地点 <span>{{ detail.visitSite }}</span></h4>
      <h4>拜访目的 <span>{{ detail.visitPurpose }}</span></h4>
      <h4>跟进事项 <span>{{ detail.followMatters }}</span></h4>
      <h4>拜访时长 <span>{{ detail.visitDuration }}</span></h4>
      <h4>拜访内容 <span>{{ detail.visitContent }}</span></h4>
      <h4>反馈意见 <span>{{ detail.feedback }}</span></h4>
      <h4 v-if="detail.status === 2">退回说明
        <p>{{ detail.returnDesc }}</p>
      </h4>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleDetail = false">返回</el-button>
        <el-button v-if="detail.status === 0" type="primary" @click="audit(1)">通过</el-button>
        <el-button v-if="detail.status === 0" type="danger" @click="audit(2)">退回</el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="退回说明"
      :visible.sync="dialogVisibleReturn"
      width="520px"
      center
    >
      <el-input v-model="returnDesc" placeholder="退回必填" type="textarea" :rows="6" />
      <div slot="footer">
        <el-button @click="dialogVisibleReturn = false">取 消</el-button>
        <el-button type="primary" @click="audit()">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="dialogVisibleImg">
      <img width="100%" :src="dialogImageUrl">
    </el-dialog>
  </section>
</template>

<script>
import { userVisitRecordListCms, getVisitRecordCms, auditVisitRecordCms } from '@/api/marketing/taskExecute'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      dialogVisibleDetail: false,
      dialogVisibleReturn: false,
      dialogVisibleImg: false,
      dialogImageUrl: '',
      returnDesc: '',
      detail: {},
      detailTitle: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
      this.$parent.getExecuteList()
    },
    getList() {
      userVisitRecordListCms({ visitUserId: this.id }).then(res => {
        this.list = res
      })
    },
    viewDetail(recordId) {
      getVisitRecordCms({ recordId }).then(res => {
        this.detail = res
      })
      this.dialogVisibleDetail = true
    },
    audit(status) {
      if (status === 1) {
        auditVisitRecordCms({ id: this.detail.id, status }).then(() => { this.getList() })
        this.dialogVisibleDetail = false
      } else if (status === 2) {
        this.dialogVisibleReturn = true
      } else {
        if (this.returnDesc === '') {
          this.$message.error('请填写退回说明')
          return
        }
        auditVisitRecordCms({ id: this.detail.id, returnDesc: this.returnDesc, status: 2 }).then(() => {
          this.returnDesc = ''
          this.dialogVisibleReturn = false
          this.dialogVisibleDetail = false
          this.getList()
        })
      }
    },
    viewImg(url) {
      this.dialogImageUrl = url
      this.dialogVisibleImg = true
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 30px;
    color: #333;
    ul {
      padding: 0;
      width: 100%;
      li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        p {
          span {
            color: #999;
            margin-right: 10px;
          }
        }
      }
    }
    h4 {
      color: #999;
      font-size: 16px;
      font-weight: 400;
      p {
        margin: 16px 0 8px;
        font-size: 14px;
        color: #333;
      }
      div {
        font-size: 12px;
      }
      img {
        margin-top: 10px;
        width: 350px;
        height: 175px;
      }
      span {
        margin-left: 16px;
        color: #333;
      }
    }
  }
}
</style>
