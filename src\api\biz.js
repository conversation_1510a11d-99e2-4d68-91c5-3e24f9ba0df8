import request from '@/utils/request'

// 文件上传
export const uploadFileApi = process.env.VUE_APP_BIZ_URL + '/upload/pic/upload'

// ----------app版本管理----------
// 上传apk文件预处理
export function preUploadApk(fileName) {
  return request({
    url: '/upload/app/preUpload?fileName=' + fileName,
    method: 'get',
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}
// app版本管理中 获取上传apk成功后的url
export function getApkAdress(fileId) {
  return request({
    url: `/downloadApp/getFileUrl/${fileId}`,
    method: 'get'
  })
}
// ----------app版本管理----------

// 图片上传预处理
export function preUploadApi(query) {
  return request({
    url: '/upload/pic/preUpload',
    method: 'post',
    data: query,
    removeToken: true,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

export function uploadFile() {
  return request({
    url: '/uploadFile/pic/upload',
    method: 'post',
    removeToken: true,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// 用于 editor富文本自定义上传图片
export function editorUpload(data) {
  return request({
    url: '/upload/pic/upload',
    method: 'post',
    data,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// 视频上传预处理
export function preUploadVideoApi(query) {
  return request({
    url: '/upload/video/preUpload',
    method: 'post',
    data: query,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// 视频上传本地预处理
export function preUpload(query) {
  return request({
    url: '/video/pre/upload',
    method: 'post',
    data: query
  })
}

// 视频取消本地预上传
export function cancelPreupload(query) {
  return request({
    url: `/video/cancel/upload/${query.videoId}/${query.videoInfoId}`,
    method: 'post'
  })
}

// 刷新视频凭证
export function refreshUploadAuth(query) {
  return request({
    url: '/upload/video/refreshUploadAuth',
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// 获取上传视频凭证
export function getUploadAuth(query) {
  return request({
    url: '/upload/video/getUploadAuth',
    method: 'post',
    data: query,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// video转码状态监测
export function checkVideoStatus(query) {
  return request({
    url: '/video/check/video',
    method: 'post',
    data: query,
    baseURL: process.env.VUE_APP_SAAS_API_URL
  })
}

// 获取视频源文件Url
export function getVideoPreview(query) {
  return request({
    url: '/upload/video/getOriginalVideoPlayUrl',
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

// 刷新视频凭证阿里
export function refreshUploadAuthAli(query) {
  return request({
    url: '/upload/video/refreshUploadAuthByAli',
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_BIZ_URL
  })
}

