<template>
  <div class="app-container">
    <!-- header -->
    <h3>人员明细
      <el-tooltip class="item" effect="dark" content="统计每个点击查看广告用户的详细数据" placement="top-start">
        <i class="el-icon-warning" />
      </el-tooltip>
    </h3>
    <div class="search-column">
      <div class="search-column__item">
        <el-input v-model="tableQuery.condition.keyword" clearable placeholder="请输入姓名/手机">
          <i slot="suffix" class="el-input__icon el-icon-search" />
        </el-input>
      </div>
      <div class="fr">
        <el-button type="primary" @click="exportExcel">导出</el-button>
      </div>
    </div>

    <!-- body -->
    <el-table border stripe :data="tableData" style="width: 100%">
      <el-table-column v-for="col in tableCols" :key="col.prop" :prop="col.prop" :label="col.label" align="center" />
      <el-table-column label="操作" align="center">
        <template slot-scope="{row}">
          <el-button type="text" @click="viewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination class="text-center" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <!-- dialog -->
    <detail-dialog ref="dialog" :row="detailRow" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'
import DetailDialog from './components/detailDialog.vue'

export default {
  name: 'StatisticsDetail',
  components: { Pagination, DetailDialog },
  data() {
    return {
      // search params
      tableQuery: {
        condition: {
          keyword: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // table
      tableCols: [
        { prop: 'name', label: '姓名' },
        { prop: 'phone', label: '手机' },
        { prop: 'area', label: '所在地区' },
        { prop: 'company', label: '所属单位' },
        { prop: 'dept', label: '专科' },
        { prop: 'exposure', label: '曝光次数' },
        { prop: 'click', label: '点击次数' },
        { prop: 'clickRate', label: '点击率' }
      ],
      tableData: [
        {
          name: '张三',
          phone: '110',
          area: '天河区',
          company: '医群',
          dept: '技术',
          exposure: '1',
          click: '2',
          clickRate: '1%'
        }
      ],
      // page
      layout: 'total, prev, pager, next, jumper',
      total: 0,
      person: {},
      detailRow: {}
    }
  },
  created() {
    this.person = this.$route.row
  },
  methods: {
    // pagination change
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUserList()
    },
    viewDetail(row) {
      this.detailRow = row
      this.$refs.dialog.visible = true
    },
    // export excel
    exportExcel() {

    }
  }
}
</script>
