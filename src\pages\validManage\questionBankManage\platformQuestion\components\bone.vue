<template>
  <div class="container">
    <el-form ref="formQuestion" :model="formQuestion" label-width="100px" :rules="rules" label-position="right">
      <!-- 选项组 -->
      <el-form-item label="选项:" prop="optionsList" style="margin-bottom: 0;">
        <div v-for="(item, idx) in formQuestion.optionsList" :key="idx" class="flex">
          <span>{{ item.index }}.</span>
          <el-input v-model="item.content" type="text" placeholder="请输入选项内容" />
          <el-button type="text" :disabled="deleteBtn" class="deleteBtn" @click="btnClick(idx)"><i class="el-icon-remove-outline" /></el-button>
        </div>
      </el-form-item>
      <!-- 添加选项按钮 -->
      <el-button plain class="xl-btn" :disabled="addBtn" @click="addOption">
        <i class="el-icon-plus" />
        添加选项
      </el-button>
      <!-- 题干组 -->
      <div class="problem-container">
        <div>
          <!-- <el-scrollbar style="height: 100%"> -->
          <!-- <ul v-infinite-scroll="load" class="infinite-list" style="overflow:auto" >
            <li class="infinite-list-item" > -->
          <div v-for="(li, i) in formQuestion.aOneList" :key="i" class="problem">
            <el-form-item :label="'题干' + `${i + 1}`+':'" prop="title">
              <el-input
                v-model="li.title"
                type="textarea"
                :resize="none"
                :autosize="autosize"
                :placeholder="'请输入题干' + `${i + 1}`"
              />
            </el-form-item>
            <el-form-item
              :label="'答案' + `${i + 1}`+':'"
              prop="optionsList"
              style="margin-bottom: 10px;padding-top: 12px"
            >
              <el-radio-group v-model="li.radio" style="padding-top: 12px">
                <el-radio v-for="(item, j) in li.optionsList" :key="j" :label="j" @change="radioChange(i)">{{ item.index }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="考点关键字:">
              <el-input
                v-model="li.points"
                type="textarea"
                :resize="none"
                :autosize="autosize"
                :maxlength="300"
                placeholder="请输入考点关键字"
              />
            </el-form-item>
            <el-form-item label="解析:">
              <el-input
                v-model="li.desc"
                type="textarea"
                :resize="none"
                :autosize="autosize"
                :maxlength="3000"
                placeholder="请输入解析"
              />
            </el-form-item>
            <div>
              <i class="el-icon-delete icon" @click="del(i)" />
            </div>
          </div>
          <!-- </el-scrollbar> -->
        </div>
        <!-- </li>
        </ul> -->
      </div>
      <!-- 添加题干组按钮 -->
      <el-button
        plain
        class="xl-btn"
        :disabled="addBtn"
        style="margin: 5px 0 10px 100px;"
        @click="addProblem"
      >
        <i class="el-icon-plus" />
        添加题干
      </el-button>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Aone',
  props: {
    aData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      count: 0,
      none: 'none', // 控制快速录入输入框不能被缩放
      addBtn: false, // 控制添加选项是否禁用
      deleteBtn: false, // 删除选项是否禁用
      autosize: {
        minRows: 6,
        maxRows: 8
      },
      formQuestion: {
        aOneList: [
          {
            desc: '',
            optionsList: [
              {
                content: '',
                index: '',
                isAnswer: 0
              }
            ],
            points: '',
            title: '',
            radio: ''
          }
        ],
        optionsList: [
          {
            content: '',
            index: ''
          }
        ]
      },
      rules: {
        title: [{ required: true, message: '题干不能为空', trigger: 'input' }],
        optionsList: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    aData: {
      handler(newval, oldvar) {
        if (JSON.stringify(newval) !== '{}') {
          this.$nextTick(() => {
            this.formQuestion.aOneList = newval.aOneList
            this.formQuestion.optionsList = newval.optionsList
            newval.aOneList.forEach(item => {
              item.optionsList.forEach((itm, idx) => {
                if (itm.isAnswer === 1) {
                  item.radio = idx
                }
              })
            })
          })
        }
      },
      immediate: true
    },
    type() {
      if (this.type === 1 || this.type === 6) {
        this.title = 'A1'
      } else {
        this.title = 'A2'
      }
    },
    'formQuestion.optionsList': {
      handler(newval, oldval) {
        this.formQuestion.aOneList.forEach(item => {
          if (item.optionsList.length < newval.length) {
            const obj = {
              index: String.fromCharCode(item.optionsList.length + 65),
              content: '',
              isAnswer: 0
            }
            item.optionsList.push(obj)
          }
        })
      },
      deep: true
    }
  },
  created() {
    const arr = [
      { index: 'A', content: '', isAnswer: 0 },
      { index: 'B', content: '', isAnswer: 0 },
      { index: 'C', content: '', isAnswer: 0 },
      { index: 'D', content: '', isAnswer: 0 },
      { index: 'E', content: '', isAnswer: 0 }
    ]
    this.formQuestion.optionsList = arr
    const aOneArr = [
      { desc: '', optionsList: [], points: '', title: '', radio: '' },
      { desc: '', optionsList: [], points: '', title: '', radio: '' }
    ]
    this.formQuestion.aOneList = aOneArr
    for (const key in this.formQuestion.aOneList) {
      this.formQuestion.aOneList[key].optionsList = arr
    }
  },
  methods: {
    load() {
      this.count += 2
    },
    radioChange(idx) {
      this.$forceUpdate()
    },
    // 添加选项
    addOption() {
      if (this.formQuestion.optionsList.length > 9) return this.$message('最多10个选项')
      const obj = {
        index: String.fromCharCode(this.formQuestion.optionsList.length + 65),
        content: '',
        isAnswer: 0
      }
      this.formQuestion.optionsList.push(obj)
    },
    // 删除选项
    btnClick(idx) {
      if (this.formQuestion.optionsList.length < 3) return this.$message('最少2个选项')
      this.formQuestion.optionsList.splice(idx, 1)
      for (const key in this.formQuestion.optionsList) {
        this.formQuestion.optionsList[key].index = this.charCode(Number(key))
      }
      // 处理问题中的答案
      this.formQuestion.aOneList.forEach(item => {
        // if (item.optionsList.length > this.formQuestion.optionsList.length) {
        item.optionsList.splice(this.formQuestion.optionsList.length, 1)
        if (item.radio === idx) {
          item.radio = ''
        }
        // }
      })
    },
    // 添加题干
    addProblem() {
      if (this.formQuestion.aOneList.length > 9) return this.$message('最多10个选项')
      const obj = { desc: '', optionsList: [], points: '', title: '' }
      this.formQuestion.aOneList.push(obj)
      for (const key in this.formQuestion.aOneList) {
        const arr = [
          { index: 'A', content: '', isAnswer: 0 },
          { index: 'B', content: '', isAnswer: 0 },
          { index: 'C', content: '', isAnswer: 0 },
          { index: 'D', content: '', isAnswer: 0 },
          { index: 'E', content: '', isAnswer: 0 }
        ]
        this.formQuestion.aOneList[key].optionsList = arr
      }
    },
    // 删除题干
    del(i) {
      const that = this
      this.$confirm('是否删除此题干?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if (that.formQuestion.aOneList.length < 3) {
            that.$message('最少2个问题')
          } else {
            that.formQuestion.aOneList.splice(i, 1)
            that.$message({
              type: 'success',
              message: '删除成功!'
            })
          }
        })
        .catch(() => {})
    },
    charCode(val) {
      return String.fromCharCode(val + 65)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  width: 60%;
  margin: 20px auto;
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    * {
      margin: 0 10px 20px 0;
    }
    .deleteBtn {
      margin-bottom: 0px;
    }
  }
  ol {
    margin-top: 0;
    margin-bottom: 0;
    list-style-type: upper-latin;
    li {
      list-style-position: outside;
    }
  }
  .xl-btn {
    width: 90%;
    margin: 0px 0 20px 100px;
    border: 1px dashed #dcdfe6;
  }
  .xl-btn:hover {
    border: 1px dashed #409eff;
  }
}
::v-deep .el-checkbox__inner {
  border-radius: 7px;
}
::v-deep .el-radio-group {
  display: block;
  font-size: 16px;
  line-height: 16px;
}
::v-deep .el-form-item__content {
  padding-top: 0px;
}
.container .li:last-child {
  margin-bottom: -20px;
}
.problem-container {
  width: 100%;
  // .scroll-list{
  //   // height: calc(100vh - 240px);
  // }
  .problem {
    border: 1px dashed #dcdfe6;
    padding: 20px 20px 0 0;
    margin-bottom: 10px;
    position: relative;
    .icon {
      position: absolute;
      top: 5px;
      right: 5px;
    }
    .icon:hover {
      cursor: pointer;
    }
  }
  .deleteBtn {
    margin-bottom: 0px;
  }
}
</style>
