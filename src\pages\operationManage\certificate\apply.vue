<template>
  <section class="list">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        class="group"
        placeholder="姓名/手机/快递单号"
        @change="search"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" @click="search" />
      </el-input>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        :header-cell-style="{background:'#f9f9f9',color:'#333'}"
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column prop="handle" label="操作">
          <template slot-scope="{row}">
            <el-button
              type="text"
              size="mini"
              @click="toPreview(row.certPdfUrl)"
            >打印证书</el-button>
            <el-button
              type="text"
              size="mini"
              @click="send(row.id)"
            >寄件</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />

    <el-dialog title="寄件" :visible.sync="dialogVisible" width="500px" center>
      <el-form :model="sendForm">
        <el-form-item label="快递单号:" required>
          <el-input v-model="sendForm.expressNo" style="width: 350px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sendExpress">确 定</el-button>
      </div>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { applyPageList, sending } from '@/api/certificate'
export default {
  name: 'CertificateApply',
  components: {
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          keyword: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableColumn: [
        { prop: 'createTime', label: '申领时间', width: '50' },
        { prop: 'userName', label: '学员姓名', width: '40' },
        { prop: 'userPhone', label: '手机', width: '40' },
        { prop: 'certName', label: '证书', width: '70' },
        { prop: 'expressUserName', label: '收件人', width: '30' },
        { prop: 'expressUserPhone', label: '收件人手机', width: '40' },
        { prop: 'expressAddress', label: '收件地址', width: '100' },
        { prop: 'expressNo', label: '快递单号', width: '70' }
      ],
      tableData: [],
      dialogVisible: false,
      sendForm: {
        expressNo: '',
        id: ''
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    toPreview(certPdfUrl) {
      window.open(certPdfUrl, '_blank')
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    getList() {
      applyPageList(this.listQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    search() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    send(id) {
      this.sendForm.id = id
      this.dialogVisible = true
    },
    sendExpress() {
      if (this.sendForm.expressNo !== '') {
        sending(this.sendForm).then(() => {
          this.$message.success('更新寄件信息成功')
          this.dialogVisible = false
          this.sendForm = {
            expressNo: '',
            id: ''
          }
          this.getList()
        })
      } else {
        this.$message.error('请填写快递单号')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.list {
  padding: 15px 25px;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  .screen {
    padding-bottom: 15px;
    .el-input {
      width: 350px;
    }
    .el-select {
      width: 150px;
    }
  }
  .pagination-container {
    margin: 0 auto;
  }
  h2 {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px;
    height: 55px;
    font-size: 20px;
    line-height: 55px;
    background-color: #f9f9f9;
  }
  .table {
    padding-top: 25px;
    background-color: #fff;
  }
  ::v-deep .el-dialog__wrapper {
    &.descDialog {
      .el-dialog__body {
        height: 200px;
      }
    }
  }
}
</style>
