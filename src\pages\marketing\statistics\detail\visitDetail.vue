<template>
  <section class="detail">
    <div class="content">
      <h2 class="title">拜访详情
        <el-button type="primary" plain @click="$router.push({name: 'MarketingStatisticsVisit'})">返回</el-button>
      </h2>
      <h4>
        <div class="label">拜访位置</div>
        <div>
          <div>{{ detail.targetSiteName }}</div>
          <div style="font-size: 14px">{{ detail.rangeOffset + '︱' + detail.targetSiteAddress }}</div>
          <img :src="detail.targetSiteImgUrl">
        </div>
      </h4>
      <h4>
        <div class="label">拜访实景照片</div>
        <img
          :src="detail.signImgUrl"
          style="width: 300px;height: 200px;display: block;"
        >
      </h4>
      <h4>
        <div class="label">产品</div>
        {{ detail.productDesc }}
      </h4>
      <h4>
        <div class="label">拜访时间</div>
        {{ detail.visitTime }}
      </h4>
      <h4>
        <div class="label">拜访客户</div>
        {{ detail.customer }}
      </h4>
      <h4>
        <div class="label">客户单位</div>
        {{ detail.companyName }}
      </h4>
      <h4>
        <div class="label">拜访方式</div>
        {{ detail.visitType }}
      </h4>
      <h4>
        <div class="label">会面地点 </div>
        {{ detail.visitSite }}
      </h4>
      <h4>
        <div class="label">拜访目的</div>
        {{ detail.visitPurpose }}
      </h4>
      <h4>
        <div class="label">跟进事项</div>
        {{ detail.followMatters }}
      </h4>
      <h4>
        <div class="label">拜访时长</div>
        {{ detail.visitDuration }}
      </h4>
      <h4>
        <div class="label">拜访内容</div>
        {{ detail.visitContent }}
      </h4>
      <h4>
        <div class="label">反馈意见</div>
        {{ detail.feedback }}
      </h4>
    </div>
  </section>
</template>

<script>
import { visitDetail } from '@/api/marketing/promoteArticle'
export default {
  data() {
    return {
      detail: {}
    }
  },
  created() {
    visitDetail(this.$route.query.id).then(res => {
      this.detail = res
    })
  }
}
</script>

<style lang="scss" scoped>
.detail {
  padding: 20px 0;
  background: #eaeaee;
  .content {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    .title {
      position: relative;
      margin: 0 auto;
      padding-left: 20px;
      height: 55px;
      font-size: 20px;
      line-height: 55px;
      background-color: #f9f9f9;
      .el-button--primary.is-plain {
        position: absolute;
        top: 8px;
        right: 24px;
        // background-color: #fff;
      }
    }
    h4 {
      display: flex;
      padding-left: 20px;
      font-weight: 400;
      .label{
        margin-right: 24px;
        width: 100px;
        color: #999;
        text-align: right;
      }
      div {
        margin-bottom: 10px;
        img {
          width: 700px;
          height: 350px
        }
      }
    }
  }
}
</style>
