<template>
  <section>
    <div v-if="$route.name === 'MarketingTaskPromote'" class="task">
      <div class="task-header">
        <el-input
          v-model="listQuery.condition.keyword"
          placeholder="请输入任务名称或执行人"
          @change="handleFilter()"
        >
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="handleFilter()" />
        </el-input>
        <el-select v-model="listQuery.condition.orgzId" clearable placeholder="全部企业" @change="handleOrg()">
          <el-option
            v-for="item in orgList"
            :key="item.orgId"
            :label="item.orgName"
            :value="item.orgId"
          />
        </el-select>
        <el-select v-model="listQuery.condition.productId" clearable placeholder="全部产品" @change="handleFilter()">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-select v-model="listQuery.condition.articleType" clearable placeholder="内容类型" @change="handleFilter()">
          <el-option
            v-for="item in typeOption"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-cascader
          v-model="listQuery.condition.cateId"
          placeholder="内容分类"
          :options="categoryIdsOptions"
          :props="{
            checkStrictly: true,
            emitPath: false,
            label: 'name',
            value: 'categoryId',
            children: 'children'
          }"
          clearable
          @change="handleFilter()"
        />
      </div>
      <div class="task-main">
        <h2>
          推广列表
          <div>
            <el-button type="primary" @click="exportList()">导出</el-button>
            <el-button type="primary" @click="dialogVisible = true">新增</el-button>
          </div>
        </h2>
        <div class="table">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
          >
            <el-table-column label="标题/文字">
              <template slot-scope="scope">
                <span>{{ scope.row.articleType.includes('DOULA') ? scope.row.description : scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-for="item in tableColumn"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :min-width="item.width"
            />
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="deleteTask(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
      </div>
    </div>
    <router-view :key="$route.path" />

    <el-dialog
      title="选择内容类型"
      :visible.sync="dialogVisible"
      width="400px"
      center
    >
      内容类型: <el-select v-model="addType" clearable placeholder="内容类型">
        <el-option
          v-for="item in typeOption"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="toDetail()">确 定</el-button>
      </span>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getUnitList } from '@/api/userManage'
import { exportPlatformArticles, userTaskListCms, productList, deletePlatformArticle } from '@/api/marketing/taskPromote'
import { getCategoryTreeList } from '@/api/category'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      listQuery: {
        condition: {
          keyword: '',
          orgzId: null,
          productId: null,
          articleType: '',
          cateId: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      orgList: [],
      productList: [],
      categoryIdsOptions: [],
      typeOption: [
        { name: '文章', value: 'ARTICLE' },
        { name: '视频', value: 'VIDEO' },
        { name: '抖喇图文征集', value: 'DOULA_ARTICLE' },
        { name: '抖喇短视频征集', value: 'DOULA_VIDEO' }
      ],
      tableColumn: [
        { prop: 'productName', label: '产品', width: '100' },
        { prop: 'articleTypeStr', label: '内容类型', width: '100' },
        { prop: 'promotionBudget', label: '推广预算', width: '60' },
        { prop: 'budgetFee', label: '制作预算', width: '60' },
        { prop: 'extraFee', label: '点击奖励', width: '60' },
        { prop: 'checkingFee', label: '核算费用', width: '60' },
        { prop: 'totalFee', label: '结算费用', width: '60' },
        { prop: 'authorInfoResp.authorName', label: '作者', width: '60' },
        { prop: 'updateName', label: '发布人', width: '60' },
        { prop: 'releaseTime', label: '发布时间', width: '90' }
      ],
      tableData: [],
      dialogVisible: false,
      addType: ''
    }
  },
  created() {
    if (this.$route.name === 'MarketingTaskPromote') {
      this.getTaskList()
      this.getOrg()
      getCategoryTreeList(479).then(res => {
        this.categoryIdsOptions = res
      })
    }
  },
  methods: {
    handleOrg() {
      this.getProductList()
      this.getTaskList()
    },
    handleProduct() {
      if (this.productList.length === 0) {
        this.$message.error('请先选择企业')
      }
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getTaskList()
    },
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getTaskList()
    },
    getTaskList(v) {
      userTaskListCms(this.listQuery).then(res => {
        res.records.forEach(item => {
          item.articleTypeStr = this.typeOption.find(i => i.value === item.articleType).name
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    getOrg() {
      const query = {
        condition: {
          type: 2002
        },
        pager: {
          page: 1,
          pageSize: 1000
        }
      }
      getUnitList(query).then(res => {
        this.orgList = res.records
      })
    },
    getProductList() {
      if (this.listQuery.condition.orgzId) {
        productList({ orgzId: this.listQuery.condition.orgzId, pageSize: 1000 }).then(res => {
          this.productList = res
        })
      }
    },
    toDetail() {
      if (this.addType !== '') {
        this.$router.push(
          {
            name: 'MarketingTaskPromoteAdd',
            query: {
              type: this.addType
            }
          }
        )
      } else {
        this.$message.error('请先选择内容类型')
      }
    },
    deleteTask(id) {
      this.$confirm('确认删除该推广内容?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePlatformArticle({ id }).then(() => {
          this.$message.success('已成功删除')
          this.getTaskList()
        })
      })
    },
    exportList() {
      exportPlatformArticles(this.listQuery.condition).then(() => {
        this.$message.success('导出成功，请在导出管理中查看')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.task {
  min-height: calc(100vh - 50px);
  height: calc(100% - 50px);
  padding: 20px;
  background-color: #eaeaee;
  &-header {
    .el-input {
      width: 200px;
    }
  }
  &-main {
    width: 100%;
    margin-top: 15px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    h2 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;
      padding: 0 20px;
      height: 55px;
      background: #f9f9f9;
    }
    .table {
      padding: 16px 16px 0px;
    }
    ::v-deep .pagination-container {
      margin: 0 auto;
    }
  }
}
</style>
