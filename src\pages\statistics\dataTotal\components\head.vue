<template>
  <div class="head">
    <h1>运营大数据 <span><i class="el-icon el-icon-date">{{ time }}</i></span></h1>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      time: null
    }
  },
  created() {
    this.timeId = setInterval(() => {
      this.time = moment().format('YYYY-MM-DD HH:mm:ss')
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
.head {
  padding: 16px 0;
  height: 100px;
  h1 {
    position: relative;
    margin: 0 auto;
    font-size: 54px;
    text-align: center;
    letter-spacing: 6px;
    span {
      i::before {
        margin-right: 8px;
      }
      position: absolute;
      top: 40px;
      right: 0;
      color: #01EAFF;
      font-size: 20px;
      font-weight: 400;
      letter-spacing: 0;
    }
  }
}
</style>
