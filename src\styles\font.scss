@font-face {
  font-family: 'iconfont';
  src:  url('../icons/font/iconfont-20240117.woff2') format('woff2'),
        url('../icons/font/iconfont-20240117.woff') format('woff');
}

.mycs-icon {
  display: inline-block;
  vertical-align: middle;
  font-style: normal;
  font-family: 'iconfont' !important;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  &:before {
    display: inline-block;
  }
}

//统计分析
.mycs-icon-statistics:before {
  content: "\e626"
}

//任务管理
.mycs-icon-task:before {
  content: "\e623"
}

//产品管理
.mycs-icon-product:before {
  content: "\e62a"
}

//用户管理
.mycs-icon-user:before {
  content: "\e62b"
}

//删除
.mycs-icon-del:before {
  content: "\e608"
}

//暂停
.mycs-icon-pause:before {
  content: "\e69d"
}
