<template>
  <div class="app-container">
    <!-- 题型选项组 -->
    <el-form
      ref="formInline"
      :inline="true"
      :model="formInline"
      class="demo-form-inline"
      :rules="rules"
    >
      <div class="button-top">
        <el-form-item label="试题分类:" prop="isRequired">
          <div class="block">
            <el-cascader
              :key="cascaderKey"
              ref="cascaderHandle"
              v-model="formInline.classification"
              placeholder="请选择试题分类"
              :options="classification"
              filterable
              clearable
              :props="props"
              style="width: 200px;white-space:nowrap;"
              :show-all-levels="false"
              :change-on-select="false"
            />
          </div>
        </el-form-item>
        <el-form-item label="难易程度:" prop="isRequired">
          <el-select
            v-model="formInline.difficulty"
            clearable
            placeholder="难易程度"
          >
            <el-option
              v-for="(item, index) in difficulty"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题型:" prop="isRequired">
          <el-select v-model="formInline.type" clearable placeholder="题型" :disabled="typeDisabled">
            <el-option
              v-for="(item, index) in question"
              :key="index"
              :label="item.label"
              :value="item.value"
              @change="optionChange"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="text" @click="onExplain(1)">题型说明</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!-- 快速录入 -->
    <div class="questionEdit">
      <div>
        <span>快速录入:</span>
        <el-button
          type="text"
          class="btn-example"
          @click="onExplain(2)"
        >范例</el-button>
        <el-button type="text" @click="onExplain(3)">规则</el-button>
      </div>
      <el-button type="text" @click="isDownUp">
        <i v-show="isShow == 'up'" class="el-icon-arrow-up">收起</i>
        <i v-show="isShow == 'down'" class="el-icon-arrow-down">展开</i>
      </el-button>
    </div>

    <!-- 多行文本域 -->
    <div v-show="isShow == 'up' ? true : false" class="quick">
      <el-input
        v-model="quickTextarea"
        type="textarea"
        placeholder="请输入内容"
        :resize="none"
        :autosize="autosize"
        class="quick-input"
      />
      <el-button
        type="primary"
        class="quick-btn"
        :disabled="disabled"
        @click="distinguish"
      >识别试题</el-button>
    </div>

    <!-- 题型控件 -->
    <div v-if="isShowed">
      <Athree v-if="[3,4].includes(formInline.type)" ref="athree" :key="questionKey" :a-data="aThreeData" :is-type="formInline.type" />
      <Aone v-else-if="[1,2,6,null].includes(formInline.type)" :key="questionKey" ref="aone" :a-data="aOneData" :is-type="formInline.type" />
      <Bone v-else-if="formInline.type == 5" :key="questionKey" ref="bone" :a-data="bOneData" :is-type="formInline.type" />
      <Judge v-else-if="formInline.type==7" :key="questionKey" ref="judge" :a-data="judgeData" :is-type="formInline.type" />
      <Gap v-else ref="gap" :key="questionKey" :a-data="gapData" :is-type="formInline.type" />
    </div>

    <!-- 保存、保存及录入、返回按钮组 -->
    <div style="margin: 0; text-align: center;">
      <el-button plain @click="revert">返回</el-button>
      <el-button type="primary" @click="save()">保存</el-button>
      <el-button type="primary" @click="saveEntry">保存并继续录入</el-button>
    </div>

    <div>
      <!-- 题型说明、规则、范例 弹窗 -->
      <explain :visible="visibles" :title="titles" @close="close" />
    </div>
  </div>
</template>

<script>
import {
  recognize,
  paperCreate,
  paperDetail,
  paperupdate,
  getAllTree
} from '@/api/questionBankManage'
import Aone from './components/aone'
import Athree from './components/athree'
import Explain from './components/explain'
import Bone from './components/bone.vue'
import Gap from './components/gap.vue'
import Judge from './components/judge.vue'

export default {
  name: 'CreateQuestion',
  components: {
    Aone,
    Athree,
    Explain,
    Judge,
    Gap,
    Bone
  },
  data() {
    return {
      isShowed: true, // 刷新题型组件
      questionKey: 0, // 题型组件key
      questionId: '', // 试题ID
      isEdit: false, // 是否编辑
      isShow: 'up', // 多行文本显示隐藏
      textarea: '',
      none: 'none', // 控制快速录入输入框不能被缩放
      visibles: false,
      titles: '',
      disabled: false,
      typeDisabled: false, // 题型选框禁用状态
      autosize: {
        minRows: 10,
        maxRows: 12
      },
      questionData: {},
      quickTextarea: '', // 快速录入文本框内容
      formInline: {
        classification: [],
        difficulty: 1,
        type: 1
      },
      // 试题分类选择框
      props: {
        label: 'name',
        value: 'questionCategoryId',
        children: 'childList',
        emitPath: false,
        checkStrictly: true,
        multiple: false
      },
      aOneData: {},
      aThreeData: {},
      bOneData: {},
      gapData: {},
      judgeData: {},
      cascaderKey: 0,
      classification: [],
      difficulty: [
        { value: 1, label: '简单' },
        { value: 2, label: '普通' },
        { value: 3, label: '困难' }
      ],
      question: [
        { value: 1,
          label: 'A1题型'
        },
        {
          value: 2,
          label: 'A2题型'
        },
        {
          value: 3,
          label: 'A3题型'
        },
        {
          value: 4,
          label: 'A4题型'
        },
        {
          value: 5,
          label: 'B1题型'
        },
        {
          value: 6,
          label: 'X题型'
        },
        {
          value: 7,
          label: '判断题'
        },
        {
          value: 8,
          label: '选择填空题'
        }
      ],
      rules: {
        isRequired: [
          { required: true, message: '请输入活动名称', trigger: 'blur' }
        ],
        isLength: [
          { min: 3, max: 5, message: '长度在3到为5个字符', trigger: 'blur' }
        ]
      },
      aoneSave: {},
      athreeSave: {},
      boneSave: {},
      gapSave: {},
      judgeSave: {}
    }
  },
  watch: {
    'quickTextarea': {
      handler(newval, oldval) {
        if (newval) {
          this.disabled = false
        } else {
          this.disabled = true
        }
      }
    },
    'formInline.type': {
      handler(newval, oldval) {
        if (newval === '' || newval === null) {
          this.formInline.type = null
        }
      }
    }
  },
  created() {
    if (this.quickTextarea === '') {
      this.disabled = true
    }
    // 获取试题分类数据
    this.getTreeList()
    // 编辑时 传入试题信息
    if (this.$route.query.value) {
      this.$nextTick(() => {
        this.isEdit = true
        this.typeDisabled = true
        const obj = JSON.parse(this.$route.query.value)
        this.questionId = obj.id
        this.questionDetail(obj.id)
      })
    } else {
      this.$nextTick(() => {
        this.typeDisabled = false
      })
    }
  },
  methods: {
    // 获取试题分类数据
    async getTreeList() {
      await getAllTree().then(res => {
        this.$nextTick(() => {
          this.classification = this.formatData(res)
        })
      })
    },
    // 查询试题详情
    async questionDetail(val) {
      await paperDetail(val).then(res => {
        if (res.message === 'success') {
          this.$nextTick(() => {
            this.formInline.classification = res.data.cateId.toString()
            this.formInline.difficulty = res.data.difficulty
            this.formInline.type = res.data.questionType
          })
          // A1/A2/X 题型
          if ([1, 2, 6].includes(res.data.questionType)) {
            this.$nextTick(() => {
              this.aOneData = res.data
            })
          } else if ([3, 4].includes(res.data.questionType)) { // A3/A4 题型
            this.$nextTick(() => {
              this.aThreeData = res.data
            })
          }
          // B1题型
          if (res.data.questionType === 5) {
            this.$nextTick(() => {
              this.bOneData = res.data
            })
          }
          // 选择填空题型
          if (res.data.questionType === 8) {
            const obj = {
              title: res.data.title,
              gapGroup: res.data.gapGroup,
              optionList: res.data.optionsList,
              points: res.data.points,
              desc: res.data.desc
            }
            this.$nextTick(() => {
              this.gapData = obj
            })
          }
          // 判断题型
          if (res.data.questionType === 7) {
            this.$nextTick(() => {
              this.judgeData = res.data
            })
          }
        }
      })
    },
    // 返回
    revert() {
      this.$router.push('/questionBankManage/platformQuestion')
    },
    // 保存
    save() {
      if (this.saveData()) return
      const data = {
        aone: this.aoneSave,
        athree: this.athreeSave,
        bone: this.boneSave,
        cateId: this.formInline.classification,
        difficulty: this.formInline.difficulty,
        gap: this.gapSave,
        judge: this.judgeSave,
        type: this.formInline.type
      }
      if (this.isEdit) {
        data.id = this.questionId
        paperupdate(data).then(res => {
          this.$router.push('/questionBankManage/platformQuestion')
        })
      } else {
        paperCreate(data).then(res => {
          this.$router.push('/questionBankManage/platformQuestion')
        })
      }
    },
    saveData() {
      // 初始化保存数据
      this.aoneSave = {}
      this.athreeSave = {}
      this.boneSave = {}
      this.gapSave = {}
      this.judgeSave = {}
      let flag = false
      let sign = false // 是否符合保存所需条件
      // 试题分类是否为空
      if (!this.formInline.classification || this.formInline.classification.length < 1) {
        sign = true
        return this.$message('请选择试题分类')
      }
      // 难易程度是否为空
      if (!this.formInline.difficulty) {
        sign = true
        return this.$message('请选择难易程度')
      }
      // 题型是否为空
      if (!this.formInline.type) {
        sign = true
        return this.$message('请选择题型')
      }
      // 获取 A1/A2题型 内容
      if ([1, 2].includes(this.formInline.type)) {
        const radioData = this.$refs.aone.radio // 选中的答案
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.aone.formQuestion))
        if (!formQuestion.title) {
          return this.formInline.type === 1 ? this.$message('题干不能为空') : this.$message('病例不能为空')
        }
        formQuestion.optionsList.forEach((item, idx) => {
          if (item.content === '') {
            flag = true
            return flag
          }
          idx === radioData ? item.isAnswer = 1 : item.isAnswer = 0
        })
        if (flag) return this.$message('选项内容不能为空')
        if (radioData === '' || radioData === null) return this.$message('请选择答案')
        const obj = {
          desc: formQuestion.desc,
          optionList: formQuestion.optionsList,
          points: formQuestion.points,
          title: formQuestion.title
        }
        this.aoneSave = obj
      }
      // 获取 A3/A4题型 内容
      if ([3, 4].includes(this.formInline.type)) {
        let title = false // 判断病例是否为空
        let content = false // 判断内容是否为空
        let radio = false // 判断答案是否选中
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.athree.formQuestion))
        if (!formQuestion.title) return this.$message('病例不能为空')
        formQuestion.aone.forEach(item => {
          if (!item.title) {
            title = true
            return
          }
        })
        if (title) return this.$message('问题不能为空')
        formQuestion.aone.forEach(item => {
          item.optionsList.forEach(iem => {
            if (!iem.content) {
              content = true
              return
            }
            iem.isAnswer = 0
          })
        })
        if (content) return this.$message('选项内容不能为空')
        formQuestion.aone.forEach(item => {
          if (item.radio !== undefined && item.radio === '') {
            radio = true
            return radio
          }
        })
        if (radio) return this.$message('请选择正确答案')
        // 答案转化
        formQuestion.aone.forEach(item => {
          item.optionsList[item.radio].isAnswer = 1
        })
        formQuestion.aone.forEach(item => {
          delete item.radio
        })
        const obj = {
          aone: [],
          title: formQuestion.title
        }
        formQuestion.aone.forEach((item, index) => {
          const objs = {
            desc: item.desc,
            optionList: item.optionsList,
            points: item.points,
            title: item.title
          }
          obj.aone.push(objs)
        })
        this.athreeSave = obj
      }
      // 获取 B1题型 内容
      if (this.formInline.type === 5) {
        let title = false
        let content = false
        let radio = false
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.bone.formQuestion))
        formQuestion.optionsList.forEach(item => {
          if (item.content === '') {
            content = true
            return
          }
        })
        if (content) return this.$message('选项内容不能为空')
        formQuestion.aOneList.forEach(item => {
          if (item.radio === '') {
            radio = true
            return
          }
          item.optionsList.forEach(v => {
            v.isAnswer = 0
          })
        })
        if (radio) return this.$message('请选择正确答案')
        formQuestion.aOneList.forEach(item => {
          if (item.title === '') {
            title = true
            return
          }
        })
        if (title) return this.$message('题干不能为空')
        formQuestion.aOneList.forEach(item => {
          item.optionsList[item.radio].isAnswer = 1
        })
        formQuestion.aOneList.forEach(item => {
          delete item.radio
        })
        const obj = {
          aone: [],
          optionsList: formQuestion.optionsList
        }
        formQuestion.aOneList.forEach(item => {
          const objs = {
            desc: item.desc,
            optionList: item.optionsList,
            points: item.points,
            title: item.title
          }
          obj.aone.push(objs)
        })
        this.boneSave = obj
      }
      // 获取 X题型 内容
      if (this.formInline.type === 6) {
        // const checkedData = this.$refs.aone.checked // 选中的答案
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.aone.formQuestion))
        if (!formQuestion.title) return this.$message('题干不能为空')
        // if (checkedData.length < 2) return this.$message('最少两个正确答案')
        formQuestion.optionsList.forEach(item => {
          if (item.content === '') {
            flag = true
            return flag
          }
          if (item.isAnswer) {
            item.isAnswer = 1
          } else {
            item.isAnswer = 0
          }
        })
        if (flag) return this.$message('选项内容不能为空')
        // 验证是否选择两个或两个以上答案
        let num = 0
        formQuestion.optionsList.forEach(item => {
          if (item.isAnswer === 1) {
            num = num + 1
            return num
          }
        })
        if (num < 2) return this.$message('最少两个正确答案')
        const obj = {
          desc: formQuestion.desc,
          optionList: formQuestion.optionsList,
          points: formQuestion.points,
          title: formQuestion.title
        }
        this.aoneSave = obj
      }
      // 获取 判断题 内容
      if (this.formInline.type === 7) {
        const radioData = this.$refs.judge.radio // 选中的答案
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.judge.formQuestion))
        if (!formQuestion.title) return this.$message('题干不能为空')
        if (radioData === '') return this.$message('请选择答案')
        formQuestion.optionsList.forEach(item => {
          if (item.content === radioData) {
            item.isAnswer = 1
          } else {
            item.isAnswer = 0
          }
        })
        this.judgeSave = formQuestion
      }
      // 获取 选择填空题型 内容
      if (this.formInline.type === 8) {
        let content = false
        let opt = false
        const formQuestion = JSON.parse(JSON.stringify(this.$refs.gap.formQuestion))
        if (!formQuestion.title) return this.$message('题干不能为空')
        formQuestion.optionList.forEach(item => {
          if (!item.content) {
            content = true
            return content
          }
        })
        if (content) return this.$message('选项内容不能为空')
        formQuestion.gapGroup.answer.forEach(item => {
          if (!item.opt) {
            opt = true
            return opt
          }
        })
        if (opt) return this.$message('请选择正确答案')
        this.gapSave = formQuestion
      }
      return sign
    },
    // 保存并录入
    saveEntry() {
      if (this.saveData()) return
      const data = {
        aone: this.aoneSave,
        athree: this.athreeSave,
        bone: this.boneSave,
        cateId: this.formInline.classification,
        difficulty: this.formInline.difficulty,
        gap: this.gapSave,
        judge: this.judgeSave,
        type: this.formInline.type
      }
      if (this.isEdit) {
        data.id = this.questionId
        paperupdate(data).then(res => {
          if (res === 'sucess' || res === '创建成功!') {
            this.$message('保存成功')
          }
        })
      } else {
        paperCreate(data).then(res => {
          if (res === 'sucess' || res === '创建成功!') {
            this.$message('保存成功')
          }
        })
      }
    },
    // 识别试题
    distinguish() {
      if (this.formInline.type === '' || this.formInline.type === null) return this.$message('请选择题型')
      const textArea = this.quickTextarea.replace(/\s/g, '')
      const obj = {
        examPaper: textArea,
        type: this.formInline.type
      }
      recognize(obj).then(res => {
        this.isShow = 'down'
        this.quickTextarea = ''
        if ([1, 2, 6].includes(this.formInline.type)) {
          this.aOneData = res.aone
        } else if ([3, 4].includes(this.formInline.type)) {
          this.aThreeData = res.athree
        }
        if (this.formInline.type === 5) {
          this.bOneData = res.bone
        }
        if (this.formInline.type === 8) {
          const obj = {
            title: res.gap.title,
            gapGroup: {
              answer: [],
              group: []
            },
            optionList: res.gap.optionsList,
            points: res.gap.points,
            desc: res.gap.desc
          }
          res.gap.opList.forEach(item => {
            const objb = {
              num: item.index,
              opt: item.content
            }
            obj.gapGroup.answer.push(objb)
          })
          this.gapData = obj
        }
        if (this.formInline.type === 7) {
          this.judgeData = res.judge
        }
      })
    },
    // 打开说明弹窗
    onExplain(data) {
      this.visibles = true
      if (data === 1) {
        this.titles = '题型说明'
      } else if (data === 2) {
        this.titles = '范例'
      } else {
        this.titles = '规则'
      }
    },
    // 关闭说明弹窗
    close(value) {
      this.visibles = false
    },
    // 是否收起录入框
    isDownUp() {
      if (this.isShow === 'up') {
        this.isShow = 'down'
      } else {
        this.isShow = 'up'
      }
    },
    // 格式化试题分类数据
    formatData(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].childList.length < 1) {
          data[i].childList = undefined
        } else {
          this.formatData(data[i].childList)
        }
      }
      return data
    },
    // 题型选项chang事件
    optionChange(e) {
      this.isShowed = false
      this.aOneData = {}
      this.aThreeData = {}
      this.bOneData = {}
      this.gapData = {}
      this.judgeData = {}
      this.isShowed = true
      this.questionKey + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.button-top {
  text-align: center;
}
.questionEdit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .btn-example {
    margin-left: 10px;
  }
}
.input-frame {
  width: 100%;
  height: 200px;
}
.quick {
  position: relative;
  .el-textarea__inner {
    padding: 5px 15px 50px 15px;
  }
  .quick-btn {
    position: absolute;
    right: 25px;
    bottom: 10px;
  }
}
.questionForm {
  padding-top: 20px;
  // align-items: cen;
}
</style>
