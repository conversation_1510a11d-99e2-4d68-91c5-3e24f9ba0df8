<template>
  <div class="contain">

    <div class="title flex">
      <div class="tx-title">
        <span>{{ data.type|questionTypeList }}题型</span>
        <span>{{ data.difficulty|difficultyList }}</span>
        <div>{{ data.uname }}<span v-if="data.uname&&data.orgName">/</span>{{ data.orgName }}</div>
        <span>ID:{{ data.id }}</span>
        <span>{{ data.source === 0 ? '医考通' : '名医传世' }}</span>
      </div>
      <div>
        <el-button size="small" @click="editBtn()">编辑</el-button>
        <el-button size="small" @click="deleteBtn()">删除</el-button>
      </div>
    </div>

    <!-- A1,A2,X -->
    <div v-if="data.type===1||data.type===2||data.type===6">
      <div>
        <div class="A_title" v-html="data.aone.title" />
        <ul>
          <li v-for="(item,index) in data.aone.optionsList" :key="index" :class="[{'bingo':item.isAnswer===1}]">
            <span>{{ item.index }}.</span> <span v-html="item.content" />
          </li>
        </ul>
      </div>
      <div class="keyword"><span>考点关键字：<span v-html="data.aone.points" /></span></div>
      <div class="explain"><span>解析：<span v-html="data.aone.desc" /></span></div>
    </div>

    <!-- A3,A4 -->
    <div v-else-if="data.type===3||data.type===4">
      <div class="big_title" v-html="data.athree.title" />

      <div v-for="(item,index) in data.athree.aone" :key="index">
        <div class="A_title">{{ index+1 }}. <span v-html="item.title" /></div>
        <ul>
          <li v-for="(v,i) in item.optionsList" :key="i" :class="[{'bingo':v.isAnswer===1}]">
            <span>{{ v.index }}.</span> <span v-html="v.content" />
          </li>
        </ul>
        <div class="keyword"><span>考点关键字：<span v-html="item.points" /></span></div>
        <div class="explain"><span>解析：<span v-html="item.desc" /></span></div>
      </div>
    </div>

    <!-- B1 -->
    <div v-else-if="data.type===5">
      <ul class="B1_ul">
        <li v-for="(v,i) in data.bone.optionsList" :key="i">
          {{ v.index }}.<span v-html="v.content " />
        </li>
      </ul>

      <div v-for="(item,index) in data.bone.aOneList" :key="index">
        <div class="B1_title">{{ index+1 }}.
          <span v-html="item.title" />
          <span class="bingo">{{ item.answer }}</span>
        </div>
        <div class="keyword"><span>考点关键字：<span v-html="item.points" /></span></div>
        <div class="explain"><span>解析：<span v-html="item.desc" /></span></div>
      </div>

    </div>

    <!-- 判断 -->
    <div v-else-if="data.type===7">
      <div class="A_title" v-html="data.judge.title" />
      <ul>
        <li v-for="(item,index) in data.judge.optionsList" :key="index" :class="[{'bingo':item.isAnswer===1}]">
          <span v-html="item.content" />
        </li>
      </ul>
      <div class="keyword"><span>考点关键字：<span v-html="data.judge.points" />{{ data.judge.points }}</span></div>
      <div class="explain"><span>解析：<span v-html="data.judge.desc" /></span></div>
    </div>

    <!-- 选择填空 -->
    <div v-else-if="data.type===8">
      <div class="A_title" v-html="data.gap.title" />
      <!-- 选项 -->
      <ul>
        <li v-for="(item,index) in data.gap.optionsList" :key="index">
          {{ item.index }}. <span v-html="item.content" />
        </li>
      </ul>
      <div class="A_title">正确答案：</div>
      <!-- 正确答案组 -->
      <ul>
        <li v-for="(item,index) in data.gap.gapGroup.answer" :key="index" class="bingo">
          {{ item.num }}. <span>{{ item.opt }}</span>
        </li>
      </ul>
      <div class="keyword"><span>考点关键字：<span v-html="data.gap.points" /></span></div>
      <div class="explain"><span>解析：<span v-html="data.gap.desc" /></span></div>
    </div>

  </div>
</template>

<script>
export default {
  filters: {
    questionTypeList(val) {
      const arr = ['A1', 'A2', 'A3', 'A4', 'B1', 'X', '判断', '填空']
      return arr[val - 1]
    },
    difficultyList(val) {
      const arr = ['简单', '普通', '困难']
      return arr[val - 1]
    }
  },
  props: {
    data: {
      required: true,
      type: Object
    }
  },
  data() {
    return {
      oldData: {}
    }
  },
  watch: {
    data: {
      handler(n) {
        this.oldData = JSON.parse(JSON.stringify(n))
        if (n && n.type === 8) {
          const group = JSON.parse(JSON.stringify(n.gap.gapGroup.group))
          if (group && group.length > 0) {
            group.forEach(item => {
              const a = item[0]
              const b = item[1]
              const aOpt = n.gap.gapGroup.answer[a - 1].opt
              const bOpt = n.gap.gapGroup.answer[b - 1].opt
              n.gap.gapGroup.answer[a - 1].opt = aOpt + '/' + bOpt
              n.gap.gapGroup.answer[b - 1].opt = bOpt + '/' + aOpt
            })
          }
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    editBtn() {
      this.$emit('edit', this.oldData)
    },
    deleteBtn() {
      this.$mount('div.contain')
      this.$emit('del', this.oldData)
    }
  }
}
</script>

<style lang="scss" scoped>
.contain{
  border: 2px solid #000;
  margin-bottom: 20px;
}
.flex{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
// 主标题
.title{
  padding: 15px;
  div{
    font-weight: 700;
    font-size: 18px;
    display: inline;
  }

  span{
    font-weight: 700;
    font-size: 18px;
    margin-right: 8px;
  }
}

.A_title{
  padding: 10px 0 0 15px;
  word-break: break-all;
}

.big_title{
  padding: 0 0 10px 15px;
}

.B1_title{
  padding: 10px 0 15px 38px;
}

.keyword{
  font-size: 15px;
  padding: 0 10px 16px 15px;
  &>span{
    display: inline-block;
    padding: 5px;
    background: #E7F7FF;
    border: 1px solid #93D6FD;
    border-radius: 4px;
    word-break: break-all;
  }
}

.explain{
  font-size: 15px;
  padding: 0 10px 16px 15px;
  &>span{
    display: inline-block;
    padding: 5px;
    background: #f4f4f5;
    border: 1px solid #ccc;
    border-radius: 4px;
    word-break: break-all;
  }
}

li{
  list-style: none;
  margin: 0;
  margin-bottom: 5px;
}

// 正确答案
.bingo{
  color: #3399ff;
}

.B1_ul{
  padding-bottom: 10px;
  border-bottom: 1px solid #000;
}
.tx-title{
  padding-left: 30px;
}
</style>
