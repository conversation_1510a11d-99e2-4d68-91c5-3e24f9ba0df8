<template>
  <section class="message-creator">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="form" :disabled="$route.query.type === 'detail'">
      <div v-if="$route.query.type !== 'add'" class="basic">
        <el-form-item label="消息类型" prop="messageType">
          <el-select v-model="form.messageType" placeholder="请选择消息类型">
            <el-option label="通知消息" value="notify" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息渠道" required>
          <el-checkbox v-model="form.appPushFlag" :true-label="1" :false-label="0">App push</el-checkbox>
          <el-checkbox v-model="form.stationMsgFlag" :true-label="1" :false-label="0">站内信</el-checkbox>
        </el-form-item>
        <el-form-item label="消息标题" prop="title">
          <el-input
            v-model="form.title"
            resize="none"
            :autosize="{ minRows: 4 }"
            placeholder="请输入消息标题"
            :maxlength="100"
            show-word-limit
            type="textarea"
          />
        </el-form-item>
        <el-form-item label="副标题/内容" prop="subTitle">
          <el-input
            v-model="form.subTitle"
            resize="none"
            :autosize="{ minRows: 8, maxRows: 8 }"
            placeholder="请输入副标题/内容"
            :maxlength="300"
            show-word-limit
            type="textarea"
          />
        </el-form-item>
        <el-form-item label="落地页" required>
          <el-radio-group v-model="form.contentType" @input="form.content = ''">
            <el-radio
              v-for="item in contentTypeList"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-radio-group>
          <div v-if="!['RICH_TEXT', 'PDF', 'LINK'].includes(form.contentType)" class="platform">
            <el-button type="primary" @click="selectContent">选择内容</el-button>
            <el-input
              v-model="form.contentTitle"
              placeholder="请选择平台内容"
              style="width: 400px"
              disabled
            />
            <select-platform
              :key="form.contentType"
              :dialog-visible.sync="dialogVisible"
              :type="form.contentType"
              :content-id.sync="form.content"
              :content-title.sync="form.contentTitle"
            />
          </div>
          <editor
            v-if="form.contentType === 'RICH_TEXT'"
            v-model="form.content"
            height="300"
          />
          <el-upload
            v-if="form.contentType === 'PDF'"
            accept="application/pdf"
            :show-file-list="false"
            :class="{ hideContentUpdate: form.contentList.length >= 1 }"
            :action="uploadFileApi"
            :data="uploadData"
            :limit="1"
            :file-list="form.contentList"
            :before-upload="handleBeforeUpload"
            :on-success="handleSuccess"
          >
            <div class="uploadPdf">
              <h2>
                <i class="el-icon-plus avatar-uploader-icon" /> 上传PDF
              </h2>
              大小不超过50M
            </div>
          </el-upload>
          <div v-if="form.contentType === 'PDF' && form.contentList.length >= 1" class="pdf-success">
            <img src="@/assets/images/fabu_icon_pdf.png">
            <p>{{ form.contentTitle }}</p>
            <img src="@/assets/images/fabu_icon_del.png" class="pdf-success-del" @click="handleContentRemove">
          </div>
          <el-input v-if="form.contentType === 'LINK'" v-model="form.content" placeholder="请输入包含https://或http://的完整URL链接" />
        </el-form-item>
        <el-form-item label="发送时间" required>
          <el-radio-group v-model="now">
            <el-radio :label="true">立即发送</el-radio>
            <el-radio :label="false">定时发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发送对象" prop="sendType">
          <el-radio-group v-model="form.sendType" :disabled="$route.query.type === 'edit'">
            <el-radio :label="1">全部发送</el-radio>
            <el-radio :label="0">部分发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!now">
          <el-date-picker
            v-model="form.sendTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择发送时间"
          />
        </el-form-item>
      </div>
      <el-form-item v-if="form.sendType === 0">
        <userList
          v-if="$route.query.type !== 'edit' && form.messageTaskId"
          :superaddition="$route.query.type === 'add'"
          :read-only="$route.query.type === 'detail'"
          :msg-id="form.messageTaskId"
          :api-type="'message'"
        />
      </el-form-item>
    </el-form>
    <div class="submit">
      <el-button @click="resetForm('form')">返回</el-button>
      <el-button v-if="!['detail', 'add'].includes(this.$route.query.type)" type="primary" @click="submitForm('form')">保存</el-button>
    </div>
  </section>
</template>

<script>
import Editor from '@/components/wangEditor/index.vue'
import SelectPlatform from '../components/selectPlatform.vue'
import UserList from '@/components/PublishUserList/index.vue'
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { getMsgTaskId, upsertMessageTask, getMsgInfo } from '@/api/message'
export default {
  components: {
    Editor,
    SelectPlatform,
    UserList
  },
  data() {
    return {
      form: {
        messageTaskId: '',
        messageType: 'notify',
        appPushFlag: 0,
        stationMsgFlag: 0,
        title: '',
        subTitle: '',
        contentType: 'RICH_TEXT',
        content: '',
        contentList: [],
        contentTitle: '',
        sendTime: null,
        sendType: 0
      },
      rules: {
        title: [
          { required: true, message: '请输入消息标题', trigger: 'blur' }
        ],
        subTitle: [
          { required: true, message: '请输入副标题/内容', trigger: 'blur' }
        ]
      },
      uploadData: { data: '' },
      uploadFileApi,
      preUploadApi,
      now: true,
      dialogVisible: false,
      contentTypeList: [
        { label: '直接编辑', value: 'RICH_TEXT' },
        { label: 'PDF', value: 'PDF' },
        { label: '链接', value: 'LINK' },
        { label: '视频资源', value: 'PLATFORM_VIDEO' },
        { label: '课程资源', value: 'PLATFORM_COURSE' },
        { label: '营销文章', value: 'PROMOTE_ARTICLE' },
        { label: '营销视频', value: 'PROMOTE_VIDEO' },
        { label: '抖喇图文', value: 'PROMOTE_DOULA_ARTICLE' },
        { label: '抖喇短视频', value: 'PROMOTE_DOULA_VIDEO' },
        { label: '证书培训', value: 'EXAMINE_CERT' }
      ]
    }
  },
  watch: {
    now(v) {
      if (v) {
        this.form.sendTime = null
      }
    }
  },
  created() {
    if (['edit', 'add', 'detail'].includes(this.$route.query.type)) {
      this.$route.meta.title = this.$route.query.type === 'edit' ? '编辑消息' : (this.$route.query.type === 'add' ? '追加消息对象' : '消息详情')
      getMsgInfo(this.$route.query.id).then(res => {
        this.form = res
        this.form.contentList = [
          {
            url: res.content,
            name: res.contentTitle
          }
        ]
        this.form.messageType = res.msgType
        this.form.messageTaskId = this.$route.query.id
      })
      this.now = false
    } else {
      getMsgTaskId().then(res => {
        this.form.messageTaskId = res.id
      })
    }
  },
  methods: {
    selectContent() {
      this.dialogVisible = true
    },
    async handleBeforeUpload(val) {
      console.log(val)
      if (val.size / 1024 / 1024 > 50) {
        this.$message.error('上传文件大小不能超过 50M')
        return
      }
      if (val.type !== 'application/pdf') {
        this.$message.error('只能上传PDF文件')
        return
      }
      const param = {
        filename: val.name,
        size: val.size,
        type: 'application/pdf'
      }
      this.pdfName = val.name
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        this.form.contentTitle = file.name
        this.form.content = res.data.id
        this.form.contentList = [
          {
            url: this.form.content,
            name: this.form.contentTitle
          }
        ]
      }
    },
    handleContentRemove() {
      this.form.contentList = []
      this.form.content = ''
      this.form.contentTitle = ''
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.stationMsgFlag === 0 && this.form.appPushFlag === 0) {
            this.$message.error('请选择消息渠道')
            return
          }
          if (this.form.content === '') {
            this.$message.error('落地页不能为空')
            return
          }
          upsertMessageTask(this.form).then(() => {
            this.$message.success('创建消息成功')
            this.$router.push({ name: 'MessageList' })
          })
        } else {
          this.$message.error('请完善消息内容')
          return false
        }
      })
    },
    resetForm() {
      this.$router.push({ name: 'MessageList' })
    }
  }
}
</script>

<style lang="scss" scope>
.message-creator {
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin-top: 30px;
  .form {
    margin: 0 auto;
    .basic {
      margin: 0 auto;
      width: 790px;
    }
    .el-form-item {
      &__label {
        color: #666;
      }
      .pdf-success,.uploadPdf {
        width: 650px;
        height: 184px;
        background: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
        color: #999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        h2 {
          margin: 0 auto;
          font-size: 22px;
          color: #000;
        }
      }
      .pdf-success {
        position: relative;
        img{
          position: absolute;
          top: 14px;
          width: 60px;
          &.pdf-success-del {
            position: absolute;
            top: 14px;
            right: 14px;
            width: 24px;
            cursor: pointer;
          }
        }
        p {
          width: 100%;
          text-align: center;
          word-wrap:break-word;
        }
      }
      .platform {
        font-size: 0;
        .el-button {
          border-radius: 0;
        }
        .el-input__inner {
          border-radius: 0;
        }
      }
      .el-radio {
        margin-bottom: 10px;
      }
    }
  }
  .submit {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }
}
.hideContentUpdate {
  display: none;
}
</style>
