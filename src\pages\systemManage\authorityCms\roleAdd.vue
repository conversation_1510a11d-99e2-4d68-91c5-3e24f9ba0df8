<template>
  <div class="app-container">
    <el-form ref="form" :disabled="readOnly" :model="form" :rules="rules" label-width="100px" class="form">
      <h3>角色信息</h3>
      <el-form-item label="code" prop="code">
        <el-input v-model="form.code" :disabled="!!form.id" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name" show-word-limit maxlength="30" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="form.description" type="textarea" maxlength="300" show-word-limit placeholder="请输入" clearable />
      </el-form-item>

      <h3>权限信息</h3>
      <el-form-item label="功能权限" prop="dtos">
        <!-- tree -->
        <div class="scroll-tree">
          <el-scrollbar>
            <el-tree
              ref="tree"
              node-key="permissionId"
              show-checkbox
              :data="unitList"
              :filter-node-method="filterNode"
              :props="{
                children: 'childList',
                label: 'name'
              }"
              :default-checked-keys="defaultCheckedKeys"
              @check-change="handleCheckChange"
            />
          </el-scrollbar>
        </div>
      </el-form-item>
    </el-form>

    <div v-if="!readOnly">
      <el-button @click="cancel">返回</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/api/roleCms'

export default {
  name: 'RoleAdd',
  data() {
    const dtosValid = (rule, value, cb) => {
      if (!this.form.dtos.length) {
        cb(new Error('请选择权限'))
      } else {
        cb()
      }
    }
    return {
      readOnly: !!this.$route.query.readOnly,
      form: {
        code: '',
        name: '',
        description: '',
        dtos: []
      },
      rules: {
        code: [{ required: true, message: '请输入code', trigger: 'blur' }],
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ],
        dtos: [{ validator: dtosValid, trigger: 'change' }]
      },
      unitList: [],
      defaultCheckedKeys: []
    }
  },
  async created() {
    const res = await request.roleList()
    this.unitList = res
    if (this.$route.query.id) {
      request.roleInfo(this.$route.query.id).then(res => {
        this.form = {
          ...res,
          roleId: res.id
        }
      })
      this.getPickList()
    }
  },
  methods: {
    // tree 复选框选中
    handleCheckChange(data, checked, indeterminate) {
      // const selectedArr = this.getCheckedKeys(
      //   this.$refs.tree.store._getAllNodes()
      // )
      const selectedArr = this.$refs.tree.getCheckedNodes().map(v => ({ code: v.code, pid: v.permissionId }))
      console.log(selectedArr)
      const half = this.$refs.tree.getHalfCheckedNodes().map(v => ({ code: v.code, pid: v.permissionId }))
      this.form.dtos = half.concat(selectedArr)
      this.defaultCheckedKeys = selectedArr
    },
    getPickList() {
      request.rolePickList(this.$route.query.id).then(res => {
        // const sv = {}
        // res.pick.forEach(si => {
        //   sv[si] = true
        // })
        // this.unitList.forEach(v => {
        //   if (v.childList.length && sv[v.permissionId]) { // 判断是否父级切已选
        //     if (v.childList.some(j => !sv[j.permissionId])) { // 判断子级是否全选
        //       delete sv[v.permissionId] // 删除已选
        //     }
        //   }
        // })
        // const gotChildNodes = this.unitList.filter(v => v.childList.length)
        const gotChildNodes = this.flat(this.unitList)
        const sv = {}
        res.pick.forEach(si => {
          sv[si] = true
        })
        gotChildNodes.forEach(v => {
          if (sv[v.permissionId] && v.childList.some(j => sv[j.permissionId])) {
            delete sv[v.permissionId]
          }
        })
        this.defaultCheckedKeys = Object.keys(sv)
        this.form.dtos = Object.keys(sv)
      })
    },
    flat(arr) {
      let arrList = []
      arr.forEach(v => {
        if (v.childList.length) {
          arrList.push(v)
          arrList = arrList.concat(this.flat(v.childList))
        }
      })
      return arrList
    },
    getCheckedKeys(node) {
      let arr = []
      const arrNode = []
      node.forEach(i => {
        if (i.checked) {
          arr.push({
            code: i.data.code,
            pid: i.data.permissionId
          })
          if (i.childNodes.length) {
            arrNode.push({
              code: i.data.code,
              pid: i.data.permissionId,
              childNodes: i.childNodes
            })
          }
        }
      })
      arrNode.forEach(i => {
        i.childNodes.forEach(j => {
          arr = arr.filter(item => item !== j.data.permissionId)
        })
      })
      return arr
    },
    // filter tree node
    filterNode(value, data) {
      if (!value) return true
      return data.orgName.indexOf(value) !== -1
    },
    cancel() {
      this.$router.go(-1)
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id) {
            request.roleEdit(this.form).then(res => {
              this.$message.success('编辑成功')
              this.cancel()
            })
          } else {
            request.roleCreate(this.form).then(res => {
              this.$message.success('添加成功')
              this.cancel()
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  h3 {
    width: 800px;
    margin-bottom: 30px;
  }
  .scroll-tree {
    height: 350px;
    overflow: hidden;
    .el-scrollbar {
      height: 100%;
      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
    ::v-deep {
      .el-checkbox__input.is-disabled {
        &.is-checked .el-checkbox__inner,
        &.is-indeterminate .el-checkbox__inner {
          background-color: #409eff;
          border-color: #409eff;
        }
      }
    }
  }
}
</style>
