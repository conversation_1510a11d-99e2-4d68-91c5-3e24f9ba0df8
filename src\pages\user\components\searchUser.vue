<template>
  <el-dialog title="" :visible.sync="show" top="50px" width="1200px" center :destroy-on-close="true" :before-close="handleClose" @open="open">
    <div class="search-column">
      <div class="search-column__item">
        <div class="search-column__inner">
          <el-select v-model="type" @change="handleType">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>

        </div>
      </div>
      <div class="search-column__item">
        <div class="search-column__label">{{ type===1?'UID':'手机号码' }}：</div>
        <div class="search-column__inner">
          <el-input v-if="type ===1" v-model="tableQuery.condition.uid" placeholder="请输入用户UID" clearable @change="search" @keydown="search" />
          <el-input v-else v-model="tableQuery.condition.phone" placeholder="请输入手机号" clearable @change="search" @keydown="search" />
        </div>
      </div>
    </div>
    <div v-if="noQuery" style="min-height: 300px; display: flex; align-items: center; justify-content: center;">
      <span>请输入UID或手机号查询</span>
    </div>
    <div v-else>
      <el-table ref="singleTable" :data="tableData" :row-style="rowClass" highlight-current-row style="width: 100%" @current-change="handleCurrentChange">
        <el-table-column property="uid" label="UID" width="400px" />
        <el-table-column property="realName" label="姓名" />
        <el-table-column property="phone" label="手机号码" width="400px" />
      </el-table>

      <!-- pagination -->
      <Pagination class="text-center padding-none" :layout="layout" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm()">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
import { validPhone } from '@/utils/validate'
import { userList } from '@/api/userManage'
import Pagination from '@/components/Pagination'

export default {
  name: 'DialogUser',
  components: {
    Pagination
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    current: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      type: 1,
      typeList: [
        { label: 'UID', value: 1 },
        { label: '手机号', value: 2 }
      ],
      tableData: [],
      // 请求参数
      tableQuery: {
        condition: {
          uid: '',
          phone: ''
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      // 分页
      total: 0,
      layout: 'total, prev, pager, next, jumper',
      // uid搜索
      searchObj: {
        fn: this.searchUid
      },
      currentRow: null,
      currentRowIndex: null,
      noQuery: true
    }
  },
  methods: {
    open() {
      this.tableQuery.condition.uid = ''
      this.tableQuery.condition.phone = ''
      this.tableData = []
      this.noQuery = true
      this.currentRow = null
      this.currentRowIndex = null
      if (!(JSON.stringify(this.current) === '{}')) {
        this.currentRow = this.current
        this.handleCurrentChange(this.current)
      }
    },
    // get unit user
    getUnitUserList() {
      userList(this.tableQuery).then(res => {
        this.tableData = res.records
        this.total = res.total
      })
    },
    rowClass({ row, rowIndex }) {
      const currentRow = {
        background: '#ecf5ff'
      }
      if (rowIndex === this.currentRowIndex) {
        return currentRow
      }
    },
    handleCurrentChange(val) {
      if (val) {
        this.currentRowIndex = this.tableData.findIndex(item => {
          return item.orgId === val.orgId
        })
        this.currentRow = val
      }
    },
    search() {
      if (this.tableQuery.condition.phone && !validPhone(this.tableQuery.condition.phone)) {
        this.$message.error('手机号格式不正确')
        return
      }
      if (this.tableQuery.condition.uid && !(/^[0-9]*$/.test(this.tableQuery.condition.uid))) {
        this.$message.error('UID只能是数字')
        return
      }
      if (!this.tableQuery.condition.uid && !this.tableQuery.condition.phone) {
        this.noQuery = true
        return
      }
      this.noQuery = false
      this.tableQuery.pager.page = 1
      this.getUnitUserList()
    },
    handleType(val) {
      if (val === 1) {
        this.tableQuery.condition.phone = ''
      } else if (val === 2) {
        this.tableQuery.condition.uid = ''
      }
    },
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getUnitUserList()
    },
    handleConfirm() {
      if (!this.currentRow) return this.$message.warning('请选择关联专家')
      this.$emit('confirm', this.currentRow)
    },
    handleClose() {
      this.currentRow = null
      this.$emit('update:show', false)
      this.$emit('close', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination-container {
  padding: 0 !important;
}
.check-row {
  background: #66b1ff !important;
  color: #fff !important;
}
.dialog-footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
