<template>
  <el-dialog title="sop详情" :visible.sync="show" top="50px" width="1200px" center :destroy-on-close="true" :before-close="handleClose" @open="open">
    <div class="video-detail">
      <h3 class="detail-title">SOP名称：{{ auditDetail.name }}</h3>
      <div class="detail-content">
        <!-- course -->
        <div
          v-for="(course, courseIndex) in auditDetail.courseResponseDtos"
          :key="course.courseId"
          class="detail-item"
          :name="course.name"
        >
          <div class="paper-head" @click.stop="handleClickCollapse(course, courseIndex)">
            <div class="paper-title" :class="course.view? 'bg-success':''">
              课程{{ courseIndex | filtersIndex }}：{{ course.name }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;通过率：{{ course.accuracy }}%
            </div>
            <i class="paper-icon" :class="activeIndex === courseIndex?'el-icon-arrow-down':'el-icon-arrow-right'" />
          </div>
          <!-- content -->
          <div v-if="activeIndex === courseIndex">
            <div
              v-for="(chapter, chapterIndex) in course.chapterResponseDtos"
              :key="chapter.chapterId"
              class="detail-item"
              :name="chapter.name"
            >
              <div class="paper-head" @click.stop="handleClickCourseCollapse(chapter, course, chapterIndex)">
                <div class="paper-title head-level-second" :class="chapter.view? 'bg-success':''">章节{{ chapterIndex | filtersIndex }}：{{ chapter.name }}
                </div>
                <i class="paper-icon" :class="activeChapterIndex === chapterIndex?'el-icon-arrow-down':'el-icon-arrow-right'" />
              </div>
              <!-- player -->
              <div v-if="activeChapterIndex === chapterIndex">
                <ali-player
                  ref="aliplayer"
                  :play-style="aliPlayerConfig.playStyle"
                  :source="aliPlayerConfig.source"
                  :cover="aliPlayerConfig.cover"
                  :height="aliPlayerConfig.height"
                  :skin-layout="aliPlayerConfig.skinLayout"
                  @ready="handleReadyVideo"
                  @pause="handlePauseVideo"
                  @error="handleError"
                />
                <div
                  v-for="(test, testIndex) in chapter.webTestPaperGroupDtos"
                  :key="test.id"
                  class="test-content"
                >
                  <div class="test-title head-level-thrid">试题{{ testIndex | filtersIndex }}: {{ test.name }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;插入时间：{{ test.timeSpot | parseSeconds }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;答题时间：{{ test.answerTime }}秒</div>
                  <div v-for="item in test.webTestPaperDtos" :key="item.id" class="test-item">
                    <div class="item-title head-level-thrid">试题名称：{{ item.name }}</div>
                    <div class="item-content head-level-thrid">
                      <div v-for="option in item.webPaperItemDtos" :key="option.id" class="option">
                        {{ option.order | filtersEnIndex }}、{{ option.content }} <i v-if="option.answerStatus" class="el-icon-success" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </el-dialog>
</template>

<script>
import AliPlayer from '@/components/Aliplayer/index'
import { getSopDetail, openCourse, displayHit } from '@/api/validManage'
import { parseSeconds, filtersIndex, filtersEnIndex } from '@/utils'

export default {
  name: 'CourseDetailDialog',
  components: {
    AliPlayer
  },
  filters: {
    parseSeconds,
    filtersIndex,
    filtersEnIndex
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 课程当前
      activeIndex: -1,
      // 章节当前
      activeChapterIndex: -1,
      // 已查看的内容
      viewList: [],
      // 按钮
      btnComfirmDisabled: true,
      // 阿里云配置
      player: null,
      aliPlayerConfig: {
        width: '960px',
        height: '540px',
        cover: null,
        source: null,
        skinLayout: [
          {
            'name': 'bigPlayButton',
            'align': 'cc'
          },
          {
            'name': 'H5Loading',
            'align': 'cc'
          },
          {
            'name': 'errorDisplay',
            'align': 'tlabs',
            'x': 0,
            'y': 0
          },
          {
            'name': 'infoDisplay'
          },
          {
            'name': 'tooltip',
            'align': 'blabs',
            'x': 0,
            'y': 56
          },
          {
            'name': 'controlBar',
            'align': 'blabs',
            'x': 0,
            'y': 0,
            'children': [
              {
                'name': 'progress',
                'align': 'blabs',
                'x': 0,
                'y': 44
              },
              {
                'name': 'playButton',
                'align': 'tl',
                'x': 15,
                'y': 12
              },
              {
                'name': 'timeDisplay',
                'align': 'tl',
                'x': 10,
                'y': 7
              },
              {
                'name': 'fullScreenButton',
                'align': 'tr',
                'x': 10,
                'y': 12
              },
              {
                'name': 'volume',
                'align': 'tr',
                'x': 5,
                'y': 10
              }
            ]
          }
        ]
      },
      // sop详情
      auditDetail: {}
    }
  },
  methods: {
    open() {
      this.auditDetail = {}
      this.getSopDetail()
    },
    // get unit user
    getSopDetail() {
      getSopDetail(this.id).then(res => {
        if (res.courseResponseDtos.length) {
          res.courseResponseDtos.forEach(v => {
            v.view = false
            v.chapterResponseDtos = []
          })
          this.auditDetail = res
        }
      })
    },
    // 点击查看时
    handleClickCollapse(val, index) {
      if (val) {
        // 动态index
        if (this.activeIndex === index) {
          this.activeIndex = -1
        } else {
          this.activeIndex = index
        }
      }
      if (!val.chapterResponseDtos.length) {
        openCourse(val.courseInfoId).then(res => {
          res.chapterResponseDtos.forEach(v => {
            v.view = false
          })
          val.chapterResponseDtos = res.chapterResponseDtos
        })
      }
    },
    // 点击查看章节
    handleClickCourseCollapse(val, fval, index) {
      if (val) {
        // 动态index
        if (this.activeChapterIndex === index) {
          this.activeChapterIndex = -1
        } else {
          this.activeChapterIndex = index
        }
      }
      // 变更状态
      val.view = true
      const result = []
      let isAllView = false
      fval.chapterResponseDtos.forEach(v => {
        result.push(v.view)
      })
      // 判断父级是否可变更状态
      isAllView = result.some(r => { return r === false })
      if (!isAllView) {
        fval.view = true
        if (!this.viewList.includes(fval.courseId)) {
          this.viewList.push(fval.courseId)
          if (this.viewList.length === this.auditDetail.courseResponseDtos.length) {
            this.btnComfirmDisabled = false
          } else {
            this.btnComfirmDisabled = true
          }
        }
      }

      // 视频播放
      if (this.activeChapterIndex > -1) {
        const VideoUrl = fval.chapterResponseDtos[this.activeChapterIndex].videoUrl
        if (VideoUrl.length) {
          const sourceURL = {}
          VideoUrl.reverse().map(v => {
            sourceURL[v.definition] = v.playURL
          })
          this.aliPlayerConfig.source = JSON.stringify(sourceURL)
          this.$nextTick(() => {
            this.$refs.aliplayer[0].init()
          })
          displayHit(val.videoId).then(res => {}) // 播放次数+1
        } else {
          this.$message.error('视频源获取失败，请刷新重试！')
        }
      }
    },
    // 阿里云播放器事件
    handleReadyVideo(e, val) {
      this.player = e
    },
    handlePauseVideo() {
      this.player.pause()
    },
    handleError(val) {
      this.$message.error('视频加载错误，请重新刷新页面')
    },
    handleClose() {
      this.$emit('close', false)
      this.$emit('update:show', false)
    }
  }
}
</script>
 <style lang="scss" scoped>
  .head-level-second{
    text-indent: 20px;
  }
  .head-level-thrid{
    text-indent: 40px;
  }
  .bg-success{
    background: rgba($color: #67C23A, $alpha: 0.4);
  }
  .ic-success{
    font-size: 14px;
    line-height: 50px;
    color: #67C23A;
  }
  .video-detail{
    border: 1px solid #e4e4e4;
    margin: 0 auto 100px;
    width: 800px;
    .detail-title{

      margin: 0;
      padding: 0 10px;
      font-size: 16px;
      font-weight: 600;
      line-height: 50px;
      background: #f2f5f9;
    }
    .detail-item{

    }
    .paper-head{
      display: flex;
      margin-bottom: -1px;
      line-height: 48px;
      height: 48px;
      border-bottom: 1px solid #e4e4e4;
    }
    .paper-icon{
      margin-right: 8px;
      line-height: 48px;
      font-size: 12px;
    }
    .paper-title{
      flex: 12;
      padding: 0 10px;
      font-size: 14px;
      width: 100%;
    }
    .test-content{
      background: #f2f5f9;
      .test-title, .test-item{
        padding: 0 10px;
      }
      .test-item{
        background: #fff;
      }
      .test-title, .item-title{
        border-bottom: 1px solid #e4e4e4;
        font-size: 14px;
        line-height: 40px;
      }
       .item-title{
         border-bottom: 0;
       }
      .item-content{
        text-indent: 20px;
        .option{
          font-size: 14px;
          line-height: 30px;
        }
      }
    }
  }
  .fixed-btn-group{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    .group-content{
      padding: 20px 0;
      text-align: center;
      background: #fff;
    }
  }
</style>
