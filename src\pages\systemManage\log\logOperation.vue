<template>
  <div class="app-container">
    <div class="search-column">
      <div class="search-column__item">
        <el-date-picker v-model="timeRange" value-format="timestamp" :picker-options="pickerOptions" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onTimeChange" />
      </div>
      <div class="search-column__item">
        <el-input v-model="condition.keyword" placeholder="账号/姓名" clearable @change="handleFilter">
          <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="handleFilter" />
        </el-input>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.system" clearable placeholder="请选择系统">
          <el-option v-for="item in osList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.operatorType" clearable placeholder="请选择类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item">
        <el-select v-model="conditionWatch.authType" clearable placeholder="请选择入口类型">
          <el-option v-for="item in authTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="search-column__item fr">
        <el-button @click="reset">重置</el-button>
      </div>
    </div>

    <!-- body -->
    <a-table :columns="columns" fit :data="list" border stripe @selection-change="onSelectChange">
      <template slot="data" slot-scope="{row}">
        <el-button v-if="row.operatorFieldData||[15, 16].includes(row.operatorType)" class="btn" type="text" @click="viewDetail(row)">{{ row.operatorDesc }}</el-button>
        <span v-else>{{ row.operatorDesc }}</span>
      </template>
      <template slot="system" slot-scope="{row}">
        <span>{{ row.system|systemFmt }}</span>
      </template>
      <template slot="authType" slot-scope="{row}">
        <span>{{ row.authType|authTypeFmt }}</span>
      </template>
      <template slot="operatorType" slot-scope="{row}">
        <span>{{ row.operatorType|typeFmt }}</span>
      </template>
      <template slot="module" slot-scope="{row}">
        <span>{{ row.module+'-'+row.childModule }}</span>
      </template>
      <template slot="time" slot-scope="{row}">
        <span>{{ row.createTime|dateFmt }}</span>
      </template>
    </a-table>
    <!-- pagination -->
    <Pagination class="text-center" :layout="layout" :total="total" :page.sync="pager.page" @pagination="handlePagination" />

    <DialogDetail :show.sync="dialogShow" :detail="detail" />
  </div>
</template>

<script>
import DialogDetail from './components/dialogDetail.vue'
import request from '@/api/logOperation'
import table from '@/mixins/table'
import { parseTime, getDayLastSecond } from '@/utils'

const columns = [
  {
    props: { label: '操作类型', align: 'center' },
    slot: 'operatorType'
  },
  {
    props: { label: '账号', align: 'center', prop: 'username' }
  },
  {
    props: { label: '姓名', align: 'center', prop: 'realName' }
  },
  {
    props: { label: 'IP', align: 'center', prop: 'userIp' }
  },
  {
    props: { label: '系统', align: 'center' },
    slot: 'system'
  },
  {
    props: { label: '入口类型', align: 'center' },
    slot: 'authType'
  },
  {
    props: { label: '操作模块', align: 'center' },
    slot: 'module'
  },
  {
    props: { label: '操作数据', align: 'center' },
    slot: 'data'
  },
  {
    props: { label: '操作时间', align: 'center' },
    slot: 'time'
  }
]

export default {
  name: 'LogOperation',
  components: { DialogDetail },
  filters: {
    systemFmt(v) {
      const arr = ['', 'cms', 'saas', '安卓', 'ios', 'app']
      return arr[v]
    },
    typeFmt(v) {
      const arr = ['', '登录', '退出', '新增', '编辑', '删除', '停用', '启用', '重置密码', '一键登录;', '导入', '导出', '分配权限', '修改密码', '重置所有员工密码', '查看平台教程答案', '查看单位教程答案']
      return arr[v]
    },
    authTypeFmt(v) {
      const arr = ['', '名医传世', 'SDK', '钉钉']
      return arr[v]
    },
    dateFmt(v) {
      return parseTime(v)
    }
  },
  mixins: [table],
  data() {
    return {
      columns,
      request,
      timeRange: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      osList: [
        { value: 1, label: 'cms' },
        { value: 2, label: 'saas' },
        { value: 3, label: '安卓' },
        { value: 4, label: 'ios' },
        { value: 5, label: 'app' }
      ],
      typeList: [
        { value: 3, label: '新增' },
        { value: 4, label: '编辑' },
        { value: 5, label: '删除' },
        { value: 6, label: '停用' },
        { value: 7, label: '启用' },
        { value: 8, label: '重置密码' },
        { value: 9, label: '一键登录' },
        { value: 10, label: '导入' },
        { value: 11, label: '导出' },
        { value: 12, label: '分配权限' },
        { value: 13, label: '修改密码' },
        { value: 14, label: '重置所有员工密码' },
        { value: 15, label: '查看平台教程答案' },
        { value: 16, label: '查看单位教程答案' }
      ],
      authTypeList: [
        { value: 1, label: '名医传世' },
        { value: 2, label: 'SDK' },
        { value: 3, label: '钉钉' }
      ],
      conditionWatch: {
        operatorType: '',
        system: '',
        authType: '',
        startTime: '',
        endTime: ''
      },
      condition: {
        keyword: ''
      },
      dialogShow: false,
      detail: {}
    }
  },
  methods: {
    onTimeChange(v) {
      this.conditionWatch.startTime = v ? v[0] : ''
      this.conditionWatch.endTime = v ? getDayLastSecond(v[1]) : ''
    },
    viewDetail(row) {
      this.detail = {}
      if ([15, 16].includes(row.operatorType)) {
        request.getLogDetail(row._id).then(res => {
          if (JSON.stringify(res) == '{}') {
            this.$message.warning('暂无数据')
            return
          }

          if (res.faceDetail.contrastResult) {
            const arr = []
            arr.push(res.faceDetail)
            this.detail.faceDetail = arr
          } else {
            this.detail.faceDetail = []
          }

          this.detail.lookPaperRecords = res.lookPaperRecords
          this.dialogShow = true
        })
      } else {
        if (row.operatorFieldData) {
          this.detail.arr = JSON.parse(row.operatorFieldData)
          this.dialogShow = true
        } else {
          this.$message.warning('暂无详情')
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn{
  white-space: normal;
}
</style>
