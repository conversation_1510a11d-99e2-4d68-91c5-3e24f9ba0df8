import Layout from '@/layout'

const userRouter = {
  path: '/user',
  component: Layout,
  name: 'User',
  redirect: { name: 'UserIndex' },
  meta: { title: '用户管理', icon: 'peoples' },
  children: [
    {
      path: 'index',
      component: () => import('@/pages/user/index'),
      name: 'UserIndex',
      meta: { title: '用户列表' }
    },
    {
      path: 'staff',
      component: () => import('@/pages/user/staff'),
      name: 'StaffIndex',
      meta: { title: '员工列表' }
    },
    {
      path: 'unit',
      component: () => import('@/pages/user/unit'),
      name: 'UnitIndex',
      meta: { title: '单位管理' }
    },
    {
      path: 'unitVip',
      component: () => import('@/pages/user/unitVip'),
      name: 'UnitVip',
      meta: { title: '单位会员' }
    },
    {
      path: 'doctor',
      component: () => import('@/pages/user/index'),
      name: 'DoctorIndex',
      meta: { title: '医生列表' },
      hidden: true
    },
    {
      path: 'other',
      component: () => import('@/pages/user/index'),
      name: 'otherIndex',
      meta: { title: '其他列表' },
      hidden: true
    },
    {
      path: 'wjw',
      component: () => import('@/pages/wjw/index'),
      name: 'WjwIndex',
      meta: { title: '单位关系' }
    },
    {
      path: 'foundation',
      component: () => import('@/pages/user/foundation'),
      name: 'FoundationIndex',
      meta: { title: '统计权限' }
    },
    {
      path: 'adminRule',
      component: () => import('@/pages/user/adminRule'),
      name: 'AdminRule',
      meta: { title: '管理员守则签署' }
    },
    {
      path: 'cancelCheck',
      component: () => import('@/pages/user/cancelCheck'),
      name: 'CancelCheck',
      meta: { title: '注销审核' }
    },
    {
      path: 'specialist',
      component: () => import('@/pages/user/specialist'),
      name: 'Specialist',
      meta: { title: '专家列表' }
    },
    {
      path: 'speciaDetail',
      component: () => import('@/pages/user/speciaDetail'),
      name: 'SpeciaDetail',
      meta: { title: '编辑' },
      hidden: true
    },
    // 讲师
    {
      path: 'lecturerApplyList',
      component: () => import('@/pages/user/live/lecturerApplyList'),
      name: 'LecturerApplyList',
      meta: { title: '讲师审核' }
    },
    {
      path: 'lecturerList',
      component: () => import('@/pages/user/live/lecturerList'),
      name: 'LecturerList',
      meta: { title: '讲师列表' }
    },
    // 扩展方
    {
      path: 'extendApplyList',
      component: () => import('@/pages/user/live/extendApplyList'),
      name: 'ExtendApplyList',
      meta: { title: '扩展方审核' }
    },
    {
      path: 'extendList',
      component: () => import('@/pages/user/live/extendList'),
      name: 'ExtendList',
      meta: { title: '扩展方列表' }
    },
    {
      path: 'professionalList',
      component: () => import('@/pages/user/professional'),
      name: 'ProfessionalList',
      meta: { title: '专业人士' }
    },
    {
      path: 'ProfessionaApplylList',
      component: () => import('@/pages/user/professionalApply'),
      name: 'ProfessionaApplylList',
      meta: { title: '专业认证审核' }
    },
    {
      path: 'ProfessionaDetail',
      component: () => import('@/pages/user/professionalDetail'),
      name: 'ProfessionaDetail',
      meta: { title: '添加专业人士' }
    }
  ]
}

export default userRouter
