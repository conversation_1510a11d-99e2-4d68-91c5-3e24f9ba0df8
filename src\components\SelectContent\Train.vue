<template>
  <div>
    <el-input v-model="dvalue" placeholder="请选择活动" @focus="dialogVisible = true" />
    <el-dialog title="选择任务内容" :visible.sync="dialogVisible" width="1095px" class="selectTrainContent" center @close="handleClose">
      <div>
        <!-- <div class="flex flexCenter">
          <el-button-group>
            <el-button :type="activeName === 'course' ? 'primary' : ''" @click="swithCourseType('course')">教程</el-button>
            <el-button :type="activeName === 'sop' ? 'primary' : ''" @click="swithCourseType('sop')">SOP</el-button>
          </el-button-group>
        </div> -->
        <!-- 搜索条件 -->
        <!-- <el-row class="search-column">
          <div class="search-column__item">
            <el-select v-model="listQuery.condition.cateId" size="small" placeholder="全部专科" clearable @change="getData({page: 1, pageSize: 10})">
              <el-option v-for="(state,index) in categoryList" :key="index" :label="state.name" :value="state.categoryId" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-select v-model="listQuery.condition.gainWay" size="small" placeholder="教程类型" @change="getData({page: 1, pageSize: 10})">
              <el-option v-for="(state,index) in platformType" :key="index" :label="state.label" :value="state.value" />
            </el-select>
          </div>
          <div class="search-column__item">
            <el-input v-model="listQuery.condition.keyword" v-search="searchOption" size="small" placeholder="关键字搜索">
              <i slot="suffix" class="el-input__icon el-icon-search pointer" @click="getData({page: 1, pageSize: 10})" />
            </el-input>
          </div>
        </el-row> -->
        <el-row>
          <el-col>
            <el-table class="listTable" :data="tableData" size="mini" fit height="380" style="width: 100%">
              <el-table-column align="center" type="index" label="序号" />
              <el-table-column align="center" label="教程名称">
                <template slot-scope="{row}">
                  <span>{{ row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="cateName" label="教程分类" />
              <el-table-column v-if="activeName === 'sop'" align="center" prop="courseNum" label="包含课程数" />
              <el-table-column v-if="activeName === 'course'" align="center" label="播放时长">
                <template v-if="activeName === 'course'" slot-scope="{row}">{{ formatSeconds(row.duration) }}</template>
              </el-table-column>
              <el-table-column align="center" label="教程简介" width="200">
                <template slot-scope="{row}">
                  <el-tooltip v-if="row.desc.length>26" class="item" popper-class="desc-tool-tip" effect="dark" :content="row.desc" placement="top">
                    <p class="desc-tip">{{ row.desc }}</p>
                  </el-tooltip>
                  <p v-else class="desc-tip">{{ row.desc }}</p>
                </template>
              </el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="{row}">
                  <el-button type="text" @click="isSelected(row.id)?'':handleSelect(row)">{{ isSelected(row.id)? '已选':'选择' }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <pagination :page="listQuery.pager.page" :total="total" @pagination="getData" />
      </div>
      <span v-if="enterType === 'materialDetail'" slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getAllCategory } from '@/api/category'
import { formatSeconds } from '@/utils'

export default {
  name: 'SelectTrainContent',
  components: { Pagination },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    // 选中的条目
    selectedList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 不同页面进入时做区分 暂时先用这样做不同处理
    enterType: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchOption: {
        delay: 500,
        fn: this.getData
      },
      dvalue: '',
      formatSeconds,
      dialogVisible: this.show,
      activeName: 'course',
      listLoading: false,
      listQuery: {
        condition: {
          cateId: null,
          keyword: '',
          materialType: 1, // 类型 course--1 sop--2
          gainWay: 1, // 本单位-1 平台-2
          deptId: '', // 专科
          categoryId: '' // 教程类型
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      categoryList: [],
      tableData: [],
      total: 0,
      selectedCourseItemList: [], // 选择之后的教程表
      deptList: [],
      // 平台类型
      platformType: [
        { label: '平台教程', value: 1 },
        { label: '本单位教程', value: 2 }
      ],
      // 教程类型
      courseType: [
        { label: '平台教程', value: 1 },
        { label: '本单位教程', value: 2 }
      ],
      // sop类型
      SOPType: [
        { label: '平台SOP', value: 1 },
        { label: '本单位SOP', value: 2 }
      ]
    }
  },
  watch: {
    value() {
      this.dvalue = this.value
    },
    show() {
      this.dialogVisible = this.show
    },
    selectedList: {
      handler(val) {
        this.selectedCourseItemList = val
      },
      immediate: true
    }
  },
  mounted() {
    this.platformType = this.courseType
    // 获取课程分类
    this.getCategorize()
    this.getData()
  },
  methods: {
    isSelected(val) {
      if (this.selectedList && this.selectedList.length) {
        if (this.activeName === 'course') {
          // 教程 tab进入
          return this.selectedList.includes(val)
        } else {
          // SOP tab进入
          let flag = false
          this.selectedList.forEach(v => {
            if (v.sopId === val) {
              if (v.children.length) {
                flag = true
              } else {
                flag = false
              }
            }
          })
          return flag
        }
      }
    },
    // 获取视频标签
    getCategorize(page) {
      getAllCategory().then(res => {
        this.categoryList = res
      })
    },
    // 获取教程主方法
    getData(val) {
      // if (val) this.listQuery.pager = val
      // this.listLoading = true
      // if (this.listQuery.condition.materialType === 1) {
      //   getTaskCourseList(this.listQuery).then(res => {
      //     this.listLoading = false
      //     this.tableData = res.records
      //     this.total = res.total
      //   })
      // } else {
      //   getTaskSOPList(this.listQuery).then(res => {
      //     this.listLoading = false
      //     const tableDataTemp = []

      //     if (res.records.length) {
      //       res.records.forEach(v => {
      //         tableDataTemp.push({
      //           name: v.name,
      //           cateName: v.cateName,
      //           courseNum: v.courseNum,
      //           desc: v.introduction,
      //           id: v.id,
      //           sid: v.sid
      //         })
      //       })
      //     }
      //     this.tableData = tableDataTemp
      //     this.total = res.total
      //   })
      // }
    },
    // 切换教程类型
    swithCourseType(type) {
      if (this.activeName === type) return
      if (type === 'course') {
        this.listQuery.condition.materialType = 1
        this.platformType = this.courseType
      } else {
        this.listQuery.condition.materialType = 2
        this.platformType = this.SOPType
      }
      this.listQuery.condition.gainWay = 1
      this.activeName = type
      this.total = 0
      this.listQuery.pager.page = 1
      this.getData()
    },
    handleClose() {
      this.$emit('update:show', false)
      this.$emit('handleClose')
    },
    // 当选中时
    handleSelect(row) {
      // if (this.activeName === 'course') {
      //   // course
      //   const courseId = row.id
      //   const courseSource = this.listQuery.condition.gainWay
      //   getCourseDetail(`${courseId}/${courseSource}`).then(res => {
      //     if (this.enterType === 'materialDetail') {
      //       const rowData = {
      //         audit: res.audit,
      //         courseId: res.courseId,
      //         courseInfoId: res.courseInfoId,
      //         name: res.name,
      //         listOrder: '-',
      //         accuracy: 0,
      //         coverId: res.coverId,
      //         imgUrl: res.imgUrl,
      //         introduction: res.introduction,
      //         responseDto: res.cateDtos,
      //         chapterResponseDtos: res.webChapterDtos,
      //         materialType: this.listQuery.condition.materialType
      //       }
      //       this.selectedCourseItemList.push(rowData)
      //       this.$emit('handleSelect', rowData.courseId)
      //     } else if (this.enterType === 'addtask') {
      //       if (res.webChapterDtos.length) {
      //         let timeFlag = false
      //         let gapFlag = false
      //         for (const v of res.webChapterDtos) {
      //           const len = v.webTestPaperGroupDtos.length
      //           if (len) {
      //             if (v.webTestPaperGroupDtos[len - 1].timeSpot >= v.chapterDuration) {
      //               timeFlag = true
      //               break
      //             }
      //             if (v.webTestPaperGroupDtos[0].timeSpot <= 5 || v.webTestPaperGroupDtos[len - 1].timeSpot >= v.chapterDuration - 5) {
      //               gapFlag = true
      //               break
      //             }
      //           }
      //         }
      //         if (timeFlag) return this.$message.error('题目时长超过视频时长，无法发布')
      //         if (gapFlag) return this.$message.error('视频开始及结束前5秒不能有题目')
      //       } else {
      //         return this.$message.error('该课程无章节')
      //       }
      //       const rowData = {
      //         materialType: 1,
      //         materialId: res.courseId,
      //         passRate: 0,
      //         name: res.name,
      //         listOrder: 0,
      //         childList: [
      //           {
      //             materialType: 1,
      //             materialId: res.courseId,
      //             name: res.name,
      //             listOrder: 0,
      //             passRate: 0
      //           }
      //         ]
      //       }
      //       this.$emit('handleSelect', rowData)
      //       this.dialogVisible = false
      //     }
      //   }).catch(() => {
      //     this.dialogVisible = false
      //   })
      // } else {
      //   // sop
      //   const params = {
      //     sopId: row.id,
      //     sopSource: this.listQuery.condition.gainWay
      //   }
      //   const API = this.enterType === 'materialDetail' ? getSopCourseDetail : getSopTaskDetail
      //   API(params).then(res => {
      //     if (res.courseResponseDtos.length) {
      //       const selectedChildrenCourseIdList = [] // 选中的子级教程courseId集合
      //       let selectedflag = false
      //       let isMessage = false
      //       for (const v of res.courseResponseDtos) {
      //         // FIX:forEach con't return
      //         if (this.enterType === 'addtask') {
      //           let timeFlag = false
      //           let gapFlag = false
      //           for (const sv of v.webChapterDtos) {
      //             const len = sv.webTestPaperGroupDtos.length
      //             if (len) {
      //               if (sv.webTestPaperGroupDtos[len - 1].timeSpot >= sv.chapterDuration) {
      //                 timeFlag = true
      //                 break
      //               }
      //               if (sv.webTestPaperGroupDtos[0].timeSpot <= 5 || sv.webTestPaperGroupDtos[len - 1].timeSpot >= sv.chapterDuration - 5) {
      //                 gapFlag = true
      //                 break
      //               }
      //             }
      //           }
      //           if (timeFlag) return this.$message.error('题目时长超过视频时长，无法发布')
      //           if (gapFlag) return this.$message.error('视频开始及结束前5秒不能有题目')
      //         }
      //         if (this.selectedCourseItemList && this.selectedCourseItemList.length) {
      //           selectedflag = this.selectedCourseItemList.some(sv => sv.courseId === v.courseId)
      //           if (selectedflag) {
      //             isMessage = true
      //           } else {
      //             this.selectedCourseItemList.push({
      //               courseId: v.courseId,
      //               courseInfoId: v.courseInfoId,
      //               name: v.name,
      //               listOrder: v.listOrder,
      //               accuracy: v.accuracy
      //             })
      //           }
      //         } else {
      //           this.selectedCourseItemList.push({
      //             courseId: v.courseId,
      //             courseInfoId: v.courseInfoId,
      //             name: v.name,
      //             listOrder: v.listOrder,
      //             accuracy: v.accuracy
      //           })
      //         }
      //         // sop里面的课程
      //         if (this.enterType === 'materialDetail') {
      //           selectedflag = false
      //           selectedChildrenCourseIdList.push(v.courseId)
      //           return
      //         } else if (this.enterType === 'addtask') {
      //         // sop里面的课程 type为1 materialId是课程id
      //           selectedChildrenCourseIdList.push({ materialType: 1, name: v.name, materialId: v.courseId, passRate: v.accuracy })
      //         }
      //       }
      //       if (isMessage) {
      //         return this.$message.error('包含两部相同的教程,系统默认仅适用一部')
      //       }
      //       // sop单条数据格式
      //       let selectedItem
      //       if (this.enterType === 'materialDetail') {
      //         selectedItem = { sopId: params.sopId, children: selectedChildrenCourseIdList }
      //       } else if (this.enterType === 'addtask') {
      //         // sop单条数据格式  materialType 教程列表1 sop列表2
      //         selectedItem = { materialType: 2, materialId: row.sid, childList: selectedChildrenCourseIdList, name: res.name }
      //         this.dialogVisible = false
      //       }
      //       this.$emit('handleSelect', selectedItem, isMessage)
      //     } else {
      //       this.$message.warning('您选中的SOP暂无教程, 请重新选择')
      //     }
      //   }).catch(() => {
      //     this.dialogVisible = false
      //   })
      // }
    },
    // 点击确定
    handleSubmit() {
      this.$emit('handleSubmit', this.selectedCourseItemList)
      this.selectedCourseItemList = [] // 清空已存在的对象
      // this.selectedChildrenCourseIdList = []
      this.dialogVisible = false
    },
    // 点击取消
    handleCancel() {
      this.selectedCourseItemList = []
      // this.selectedChildrenCourseIdList = []
      this.$emit('handleCancel')
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.selectTrainContent {
  &::v-deep .el-dialog__body {
    padding: 25px 35px;

    .el-button-group {
      margin-bottom: 23px;
    }
  }

  .search-column {
    height: 51px;
    line-height: 51px;
    padding: 0 15px;
    background: #dae5f1;
    margin-bottom: 17px;
  }

  .listTable {
    border-top: 1px solid #e4e4e4;
  }
}
.desc-tip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
