/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  return str.trim().length >= 0
}

/**
 * @param {string} password
 * @returns {Boolean}
 */
export function validPassword(password) {
  return /(?!^\d+$)(?!^[A-Za-z]+$)(?!^[^A-Za-z0-9]+$)(?!^.*[\u4E00-\u9FA5].*$)^\S{8,14}$/.test(password) // 同saas
}

/**
 * @param {string} phone
 * @returns {Boolean}
 */
export function validPhone(phone) {
  return /^[1][3,4,5,7,8,9][0-9]{9}$/.test(phone)
}
