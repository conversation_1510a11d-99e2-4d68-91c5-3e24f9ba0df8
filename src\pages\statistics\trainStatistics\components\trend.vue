<template>
  <div class="app-container">
    <div class="container">
      <div class="search-column">
        <div class="search-column__item"><span class="record-text" />{{ query.type|typeList }}变化趋势</div>
        <div class="search-column__item fr">
          <el-cascader
            v-model="majorIds"
            placeholder="请选择专科"
            :options="majorList"
            collapse-tags
            :props="{
              multiple: multiple,
              value:'majorId',
              label:'name',
              children:'childList',
              emitPath: false,
              checkStrictly: false
            }"
            clearable
            :disabled="disabled"
            @change="getList"
          />
          <el-cascader
            v-model="academicIds"
            placeholder="请选择职称"
            :options="academicList"
            collapse-tags
            :props="{
              multiple: true,
              value:'academicId',
              label:'name',
              children:'childList',
              emitPath: false
            }"
            :disabled="disabled"
            clearable
            @change="getList"
          />

          <el-button v-show="isShow === 2" type="text" @click="handlerCutChart(1)"><i class="el-icon-s-fold" style="margin-right:10px" />列表</el-button>
          <el-button v-show="isShow === 1" type="text" @click="handlerCutChart(2)"><i class="el-icon-s-data" style="margin-right:10px" />图表</el-button>
          <el-button type="text" @click="handleDownload"> <i class="el-icon-download" style="margin-right:10px" />下载</el-button>
        </div>
      </div>

      <div class="table">
        <div v-if="isShow===3" class="empty">暂无数据</div>
        <div v-show="isShow===2" id="trend" style="width: 100%;height: 400px" />
        <div v-if="isShow===1">
          <Xtable :table-data="tableData" :type="query.type" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const echarts = require('echarts/lib/echarts')
require('echarts/lib/component/title')
require('echarts/lib/component/toolbox')
require('echarts/lib/component/tooltip')
require('echarts/lib/component/grid')
require('echarts/lib/component/legend')
require('echarts/lib/component/dataZoom')
require('echarts/lib/chart/bar')
require('echarts/lib/chart/line')

import { majorTreeList } from '@/api/category' // 选择身份后的 专科树
import { academicTreeListById } from '@/api/academic' // 选择单个身份后的 职称树
import request from '@/api/dataStatistics/statisticsCase'
import Xtable from './xTable.vue'

export default {
  name: 'StatisticsTrend',
  filters: {
    typeList(v) {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[v]
    }
  },
  components: {
    Xtable
  },
  props: {
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      request,
      myChart: null,
      majorList: [],
      academicList: [],
      academicList1: [
        { academicId: 0, name: '无' }
      ],
      academicList2: [
        { academicId: 1004, name: '正高级' },
        { academicId: 1003, name: '副高级' },
        { academicId: 1002, name: '中级' },
        { academicId: 1001, name: '初级' },
        { academicId: 0, name: '无' }
      ],
      isShow: 2,
      tableData: [],
      academicIds: [],
      majorIds: [],
      data: [], // 接口返回数据
      disabled: true,
      multiple: false
    }
  },
  computed: {
    labelName() {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      return typeArr[this.query.type]
    }
  },
  watch: {
    query: {
      handler() {
        this.getList()
      },
      immediate: true,
      deep: true
    },
    'query.identityIds': {
      handler(arr) {
        if (arr.length === 0) { // 未选身份，禁用
          this.Reset()
          this.disabled = true
        } else if (arr.length === 1) { // 一个身份
          if (arr[0] === '0') { // 身份为'无'
            // 专科和职称都是无
            this.ident1()
            this.disabled = true
          } else {
            // 调接口
            this.ident2(arr)
            this.disabled = false
          }
        } else { // 多个身份
          this.ident3(arr)
          this.disabled = false
        }
      },
      deep: true
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (!this.myChart) {
      return
    }
    this.myChart.dispose()
    this.myChart = null
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        this.myChart = echarts.init(document.getElementById('trend'))
        window.addEventListener('resize', () => {
          this.myChart.resize()
        })
      })
    },
    // 获取用户数变化趋势数据
    async getList() {
      this.tableData = []
      const params = this.getParams()
      await this.request.trend(params).then(res => {
        if (JSON.stringify(res) === '{}' || JSON.stringify(res) === '[]') {
          this.isShow = 3
          return
        } else {
          res.forEach(item => {
            item.num = parseInt(item.num)
          })
          this.data = JSON.parse(JSON.stringify(res))
          this.tableData = JSON.parse(JSON.stringify(this.data))
          this.isShow = 2
          this.initCharts()
        }
      })
      // 加载柱状图
      this.setOptions()
    },
    getParams() {
      const params = this.copy(this.query)
      // params.academicIds = this.academicIds
      params.majorIds = this.majorIds
      // 应后端要求，用户未选择身份、职称、专科时，单值字段给-1
      if (params.identityIds.length === 0) {
        params.identityId = -1
      }
      if (this.academicIds.length === 0) {
        params.academicId = -1
      }
      if (this.majorIds.length === 0) {
        params.majorId = -1
      }
      // 应后端要求，选择多个身份时，职称字段用academicClassIds，单个身份时用academicIds
      if (params.identityIds.length > 1) {
        params.academicClassIds = this.academicIds
      } else {
        params.academicIds = this.academicIds
      }
      return params
    },
    // type 1/列表 2/图表
    handlerCutChart(type) {
      this.isShow = type
      if (type === 1) {
        // 加载表格
        this.$nextTick(() => {
          this.tableData = JSON.parse(JSON.stringify(this.data))
        })
      }
    },
    // 当展示图表时，下载图表图片，当展示列表时，下载excel数据表
    handleDownload() {
      // 将tabel表格转换为execl数据表，并下载为execl文件
      if (this.isShow === 1) {
        if (this.tableData.length < 1) {
          this.$message.info('当前数据为空')
          return
        }
        const params = this.getParams()
        this.request.exportTrend(params).then(() => {
          this.$message.success('导出成功，请在导出管理中查看')
        }).catch(() => {
          this.$message.error('导出失败')
        })
      }
      // 将echarts图表转换为canvas,并将canvas下载为图片
      if (this.isShow === 2) {
        const aLink = document.createElement('a')
        const blob = this.base64ToBlob()
        const evt = document.createEvent('HTMLEvents')
        evt.initEvent('click', true, true)
        aLink.download = '用户数变化趋势'
        aLink.href = URL.createObjectURL(blob)
        aLink.click()
      }
    },
    exportImg() { // echarts返回一个 base64的URL
      const myChart = echarts.init(
        document.getElementById('trend')
      )
      return myChart.getDataURL({
        type: 'png',
        pixelRatio: 1,
        backgroundColor: '#fff'
      })
    },
    base64ToBlob() { // 将base64转换blob
      const img = this.exportImg()
      const parts = img.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], { type: contentType })
    },
    // 柱状图配置信息
    setOptions() {
      const typeArr = ['', '用户数', '培训人数', '培训人次', '培训任务数', '培训时长', '参与人次', '通过人次', '培训覆盖率', '培训参与率', '培训通过率', '参与达标率', '活跃用户数', '活跃率']
      this.myChart.clear()
      this.myChart.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: `{b0}<br />${typeArr[this.query.type]}：{c0}${[8, 9, 10, 11, 13].includes(this.query.type) ? '%' : ''}<br />{a1}：{c1}%`,
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#283b56'
            }
          }
        },
        legend: {
          top: 'bottom',
          bottom: 10,
          left: 'center',
          data: [this.labelName, '增长率']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: { lineStyle: { color: '#939495' }},
          axisLabel: {
            textStyle: { color: '#939495', fontSize: '14' }
          },
          data: (() => {
            let res = []
            const dataAry = []
            this.data.forEach(item => {
              dataAry.push(item.dm)
            })
            // 数组排序
            const compare = (x, y) => {
              if (x < y) {
                return -1
              } else if (x > y) {
                return 1
              } else {
                return 0
              }
            }
            res = dataAry.sort(compare)
            // res.splice(0, 1)
            return res
          })()
        },
        yAxis: [
          {
            type: 'value',
            name: '',
            splitLine: {
              show: false
            },
            axisLabel: {
              show: true,
              fontSize: 14,
              color: '#939495'
            },
            axisLine: {
              min: 0,
              max: 10,
              lineStyle: { color: '#939495' }
            } // 左线色
          },
          {
            type: 'value',
            name: '',
            show: true,
            axisLabel: {
              show: true,
              fontSize: 14,
              formatter: '{value} %',
              color: '#939495'
            },
            axisLine: { lineStyle: { color: '#939495' }}, // 右线色
            splitLine: {
              show: true,
              lineStyle: { color: '#939495' }
            }
          }
        ],
        series: [
          {
            name: this.labelName,
            type: 'bar',
            data: (() => {
              const res = []
              const dataOld = JSON.parse(JSON.stringify(this.data))
              let dataNew = []
              // 数组排序
              var compare = function(prop) {
                return function(obj1, obj2) {
                  var val1 = obj1[prop]
                  var val2 = obj2[prop]
                  if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
                    val1 = Number(val1)
                    val2 = Number(val2)
                  }
                  if (val1 < val2) {
                    return -1
                  } else if (val1 > val2) {
                    return 1
                  } else {
                    return 0
                  }
                }
              }
              dataNew = dataOld.sort(compare('dm'))
              // dataNew.splice(0, 1)
              dataNew.forEach(item => {
                res.push(item.num)
              })
              return res
            })(),
            barWidth: '50',
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: '#56B9FF'
              }
            },
            barGap: '0.2'
          },
          {
            name: '增长率',
            type: 'line',
            yAxisIndex: 1,
            data: (() => {
              const res = []
              const dataOld = JSON.parse(JSON.stringify(this.data))
              let dataNew = []
              // 数组排序
              var compare = function(prop) {
                return function(obj1, obj2) {
                  var val1 = obj1[prop]
                  var val2 = obj2[prop]
                  if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
                    val1 = Number(val1)
                    val2 = Number(val2)
                  }
                  if (val1 < val2) {
                    return -1
                  } else if (val1 > val2) {
                    return 1
                  } else {
                    return 0
                  }
                }
              }
              dataNew = dataOld.sort(compare('dm'))
              dataNew.forEach(item => {
                res.push(item.growRate)
              })
              return res
            })(),
            lineStyle: {
              color: '#4DCC71',
              width: 3
            },
            itemStyle: {
              color: '#4DCC71'
            },
            smooth: true
          }
        ]
      })
    },
    // 取两个数相除的结果
    divisionResult(x, y) {
      if (!isNaN(Number(x)) && !isNaN(Number(y))) {
        const val1 = Number(x)
        const val2 = Number(y)
        if ((val1 === 0 && val2 === 0) || val2 === 0 || !val2) {
          return '0%'
        } else {
          return (val1 / val2).toFixed(2)
        }
      }
    },
    // 选择身份无
    ident1() {
      this.majorList = [{
        name: '无',
        majorId: 0
      }]
      this.academicList = this.academicList1
      this.academicIds = [0]
      this.majorIds = [0]
    },
    // 选择一个身份
    ident2(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.majorList = newArr
      })
      academicTreeListById(arr[0]).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.academicList = newArr
      })
    },
    // 选择多个身份
    ident3(arr) {
      this.multiple = true
      majorTreeList({ identityIds: arr }).then(res => {
        const newArr = this.clearNullChildList(res, 'childList')
        this.majorList = newArr
      })
      this.academicList = this.academicList2
    },
    Reset() { // 用户清除身份时，进行reset
      this.academicIds = []
      this.majorIds = []
    },
    // 去除级联空子集
    clearNullChildList(arr, child) {
      arr.forEach((v, i) => {
        if (v[child] && v[child].length) {
          this.clearNullChildList(v[child], child)
        } else {
          delete v[child]
        }
      })
      return arr
    },
    copy(obj) {
      return JSON.parse(JSON.stringify(obj))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding-left: 0;
}
.container {
  border-radius: 5px;
  background: #fff;
  box-shadow: 0 0 8px rgb(167, 165, 165);
  padding-top: 14px;
  padding-bottom: 20px;

  .search-column {
    border-radius: 5px;
    padding: 5px 20px;
  }

  .empty{
    text-align: center;
    width: 100%;
    height: 200px;
    line-height: 40px;
    color: #999;
    font-size: 24px;
  }

  .table {
    width: 100%;
    height: 400px;
    padding: 10px 10px 0;
  }
}
.record-text {
  margin-bottom: 10px;
  padding-left: 15px;
  border-left: 2px solid #4F8EF8;
}
</style>
