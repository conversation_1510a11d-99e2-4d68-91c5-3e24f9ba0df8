import request from '@/utils/request'

// 登录
export function login(data) {
  return request({
    url: `/web/name/login`,
    method: 'post',
    data,
    headers: { system: 1, device: 2 },
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 退出
export function logout() {
  return request({
    url: '/vue-admin-template/user/logout',
    method: 'post'
  })
}

// 一键登录
export function easyLogin(params) {
  return request({
    url: `/web/easyLogin`,
    method: 'get',
    headers: { system: 2, actionCode: params.actionCode },
    params: { safeCode: params.safeCode },
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 用户权限
export function userPermissions() {
  return request({
    url: `/user/permissions`,
    method: 'post',
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 用户菜单
export function getUserMenu() {
  return request({
    url: `/cms/user/menus`,
    method: 'get',
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 用户操作/元素权限
export function getUserOperations() {
  return request({
    url: `/cms/user/operations`,
    method: 'get',
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 登出
export function logOut(query) {
  return request({
    url: `/mycs/logout`,
    method: 'get',
    headers: { system: 1, ...query },
    baseURL: process.env.VUE_APP_LOGIN_API
  })
}

// 通过旧密码修改密码
export function updatePassword(data) {
  return request({
    url: `/user/cms/chang/password`,
    method: 'post',
    data
  })
}
