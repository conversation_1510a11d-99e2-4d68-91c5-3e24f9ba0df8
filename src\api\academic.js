import request from '@/utils/request'

// 职称列表树
export function academicTreeList(params) {
  return request({
    url: `/academic/academicTreeList`,
    method: 'get',
    params: params
  })
}

// 添加职称
export function addAcademic(params) {
  return request({
    url: '/academic/addAcademic',
    method: 'post',
    data: params
  })
}

// 删除职称
export function deleteAcademic(params) {
  return request({
    url: '/academic/deleteAcademic',
    method: 'get',
    params: params
  })
}

// 更新职称
export function updateAcademic(params) {
  return request({
    url: '/academic/updateAcademic',
    method: 'post',
    data: params
  })
}

// 通过身份id查询职称列表树-qyc
export function academicTreeListById(id) {
  return request({
    url: '/academic/academicTreeListByIdentityId?identityId=' + id,
    method: 'get'
  })
}
