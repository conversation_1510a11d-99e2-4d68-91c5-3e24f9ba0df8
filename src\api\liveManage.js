import request from '@/utils/request'

// 获取费用规则
export function getRule() {
  return request({
    url: '/live/fee/rule',
    method: 'post'
  })
}

// 保存费用规则
export function saveRule(query) {
  return request({
    url: '/live/save/rule',
    method: 'post',
    data: query
  })
}

// 直播数据字典 LIVE_METHOD-方式 LIVE_TYPE-类型 PROMOTION_TYPE-推广类型
export function getDict(query) {
  return request({
    url: '/live/dict',
    method: 'get',
    params: query
  })
}

// 审核
export function liveAudit(query) {
  return request({
    url: '/live/audit',
    method: 'post',
    data: query
  })
}

// 直播审核列表
export function liveAuditList(query) {
  return request({
    url: '/live/audit/list',
    method: 'post',
    data: query
  })
}

// 直播信息审核历史记录表
export function liveAuditRecordList(query) {
  return request({
    url: `/live/audit/record/list`,
    method: 'post',
    data: query
  })
}

// 确认直播费用
export function confirmCost(liveId, query) {
  return request({
    url: `/live/confirm/cost/${liveId}`,
    method: 'post',
    data: query
  })
}

// 审核详情
export function liveDetail(liveId) {
  return request({
    url: `/live/detail/${liveId}`,
    method: 'get'
  })
}

// 直播费用参考
export function referCost(liveId) {
  return request({
    url: `/live/refer/cost/${liveId}`,
    method: 'post'
  })
}

// 直播选择用户列表 （包含未选、已选）
export function userList(query) {
  return request({
    url: '/live/positive/userList',
    method: 'post',
    data: query
  })
}

// 添加、删除直播用户（单个）
export function addOrDelUser(query) {
  return request({
    url: '/live/positive/addOrDelUser',
    method: 'post',
    data: query
  })
}

// 批量添加、删除直播用户
export function batchAddOrDelUser(query) {
  return request({
    url: '/live/positive/batchAddOrDelUser',
    method: 'post',
    data: query
  })
}

// 审核完成
export function liveAuditFinish(liveId) {
  return request({
    url: `/live/audit/finish/${liveId}`,
    method: 'post'
  })
}

// 直播列表
export function liveList(query) {
  return request({
    url: '/live/list',
    method: 'post',
    data: query
  })
}
// 人脸记录列表
export function liveFaceList(query) {
  return request({
    url: '/live/face/list',
    method: 'post',
    data: query
  })
}

// 直播回看
export function liveLookBack(liveId) {
  return request({
    url: `/live/look/back/${liveId}`,
    method: 'get'
  })
}

// 直播分享
export function getShareUrl(liveId) {
  return request({
    url: `/live/share/${liveId}`,
    method: 'get'
  })
}

// 直播管理
export function getLiveManageUrl(liveId) {
  return request({
    url: `/live/manage/${liveId}`,
    method: 'get'
  })
}

// 直播费用概览
export function getCostData(liveId) {
  return request({
    url: `/live/cost/${liveId}`,
    method: 'get'
  })
}

// 直播数据概览
export function getLiveData(liveId) {
  return request({
    url: `/live/data/${liveId}`,
    method: 'get'
  })
}

// 评论列表
export function getLiveComments(query) {
  return request({
    url: '/live/comments',
    method: 'post',
    data: query
  })
}

// 直播观众列表
export function getLiveAudience(query) {
  return request({
    url: '/live/positive/audience',
    method: 'post',
    data: query
  })
}

// 观看详情
export function getLiveWatchDetail(liveId, userId) {
  return request({
    url: `/live/watch/detail/${liveId}/${userId}`,
    method: 'get'
  })
}

// 观众列表导出
export function exportLiveUser(liveId) {
  return request({
    url: `/export/live/user/${liveId}`,
    method: 'post'
  })
}

// 评论列表导出
export function exportLiveComment(liveId) {
  return request({
    url: `/export/live/comments/${liveId}`,
    method: 'post'
  })
}
