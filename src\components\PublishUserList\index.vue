<template>
  <div>
    <!-- type 1待选 2已选 -->
    <single-user-list v-if="!readOnly" :type="1" v-bind="$props" @filter="filter" @change="change" />
    <single-user-list :disabled="readOnly || superaddition" :type="2" v-bind="$props" @change="change" />
  </div>
</template>

<script>
import singleUserList from './singleUserList'
import { getAreaTree } from '@/api/area'
import { identityList } from '@/api/category'
import { treeList } from '@/api/major'
import { deptNameList } from '@/api/dept'
import { academicTreeList } from '@/api/academic'

export default {
  name: 'UserList',
  components: { singleUserList },
  props: {
    activityId: {
      type: String,
      default: ''
    },
    advertisementId: {
      type: String,
      default: ''
    },
    msgId: {
      type: String,
      default: null
    },
    batchId: {
      type: String,
      default: null
    },
    apiType: {
      type: String,
      default: 'activity'
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    superaddition: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      areaList: [],
      identityList: [],
      majorList: [],
      deptList: [],
      academicTreeList: [],
      scopeConditions: []
    }
  },
  created() {
    Promise.all([deptNameList({}), getAreaTree(), identityList(), treeList(), academicTreeList()]).then(res => {
      [this.deptList, this.areaList, this.identityList, this.majorList, this.academicTreeList] = res
    })
  },
  methods: {
    filter(obj) {
      const Index = this.scopeConditions.findIndex(v => v.property === obj.property)
      if (Index !== -1) {
        this.scopeConditions[Index] = obj
      } else {
        this.scopeConditions.push(obj)
      }
    },
    change() {
      this.$children.forEach(v => v.getList())
    },
    delSC(i) {
      this.scopeConditions.splice(i, 1)
    }
  }
}
</script>
