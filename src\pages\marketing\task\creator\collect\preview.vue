<template>
  <section class="preview">
    <div class="content">
      <iframe
        v-if="h5url"
        ref="iframe"
        :src="h5url"
      />
      <ul>
        <li>
          <h2>封面</h2>
          <div class="imgs">
            <img
              v-for="(item, index) in detail.covers"
              :key="index"
              :src="item"
            >
          </div>
        </li>
        <li>
          <h2>分类</h2>
          <p>{{ detail.categories.join() }}</p>
        </li>
        <li v-if="detail.taskUserStatus !== 4">
          <h2>退回说明</h2>
          <el-input
            v-if="[3, 6].includes(detail.taskUserStatus)"
            v-model="returnDesc"
            type="textarea"
            :rows="5"
            placeholder="退回必填"
            resize="none"
          />
          <p v-if="detail.taskUserStatus === 5">{{ detail.returnDesc }}</p>
        </li>
      </ul>
    </div>
    <div class="buttons">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button v-if="[3, 6].includes(detail.taskUserStatus)" type="primary" @click="audit(1)">通过</el-button>
      <el-button v-if="[3, 6].includes(detail.taskUserStatus)" type="danger" @click="audit(2)">退回</el-button>
    </div>
  </section>
</template>

<script>
import { auditArticle, lookArticle } from '@/api/marketing/taskExecute'
export default {
  data() {
    return {
      h5url: '',
      returnDesc: '',
      detail: {}
    }
  },
  mounted() {
    lookArticle({ articleId: this.$route.query.articleId }).then(res => {
      this.detail = res
      this.$route.meta.title = res.articleType === 'ARTICLE' ? '文章详情' : '视频详情'
      this.h5url = `${process.env.VUE_APP_FRONT_H5_URL}/#/marketing${res.articleType === 'ARTICLE' ? 'Article' : 'Video'}?articleId=${this.$route.query.articleId}&source=2`
    })
  },
  methods: {
    audit(status) {
      if (status === 2 && this.returnDesc === '') {
        this.$message.error('请输入退回说明')
        return
      }
      const query = {
        id: this.$route.query.articleId,
        returnDesc: this.returnDesc,
        status
      }
      auditArticle(query).then(res => {
        this.$message.success('审核完成')
        this.$router.go(-1)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.preview {
  padding-top: 100px;
  .content {
    display: flex;
    justify-content: center;
    iframe {
      width: 360px;
      height: 600px;
      border: 1px solid #d0d3d9;
      overflow-x: hidden;
      overflow-y: auto;
      /* 整个滚动条 */
      &::-webkit-scrollbar {
        /* 对应纵向滚动条的宽度 */
        width: 10px;
        /* 对应横向滚动条的宽度 */
        height: 10px;
      }
      /* 滚动条上的滚动滑块 */
      &::-webkit-scrollbar-thumb {
        background-color: #d0d3d9;
        border-radius: 32px;
      }
    }
    ul {
      margin: 0;
      width: 500px;
      li{
        margin-bottom: 15px;
        h2 {
          margin: 0 auto 10px;
          font-size: 20px;
        }
        .imgs {
          display: flex;
          img {
            width: 180px;
            height: 100px;
            margin-right: 15px;
            background-color: #4cf;
          }
        }
      }
    }
  }
  .buttons {
    margin-top: 50px;
    display: flex;
    justify-content: center;
  }
}
</style>
