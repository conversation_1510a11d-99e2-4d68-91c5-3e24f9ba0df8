<template>
  <section class="content">
    <div class="screen">
      <el-input
        v-model="listQuery.condition.keyword"
        style="width: 350px;"
        placeholder="内容名称/发布人/作者"
        clearable
        @keyup.enter.native="handleFilter()"
        @clear="clearSelect()"
      >
        <i slot="prefix" class="el-input__icon el-icon-search" style="cursor:pointer" @click="handleFilter()" />
      </el-input>
      <el-select v-model="listQuery.condition.serviceProviderId" clearable placeholder="全部服务商" @change="handleFilter()">
        <el-option
          v-for="item in serverOptions"
          :key="item.serviceOrgId"
          :label="item.serviceName"
          :value="item.serviceOrgId"
        />
      </el-select>
      <el-select v-model="listQuery.condition.orgzId" clearable placeholder="全部企业" @change="changeOrg()">
        <el-option
          v-for="item in companyOptions"
          :key="item.orgId"
          :label="item.orgName"
          :value="item.orgId"
        />
      </el-select>
      <el-select v-model="listQuery.condition.productId" clearable placeholder="全部产品" @change="handleFilter()">
        <el-option
          v-for="item in productList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.condition.type"
        clearable
        placeholder="内容类型"
        @change="handleFilter()"
      >
        <el-option
          v-for="item in typeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-cascader
        v-model="listQuery.condition.categoryId"
        :options="categoryIdsOptions"
        :props="categoryIdsProps"
        placeholder="内容分类"
        clearable
        @change="handleFilter()"
      />
      <el-select
        v-model="listQuery.condition.status"
        clearable
        placeholder="审核状态"
        @change="handleFilter()"
      >
        <el-option
          v-for="item in statusList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column
          v-for="item in tableColumn"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :min-width="item.width"
        />
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="toDetail(scope.row.id, scope.row.articleType)"
            >{{ scope.row.status === 0 ? '审核' : '查看' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <Pagination :page="listQuery.pager.page" :total="total" @pagination="handlePagination" />
    <router-view :search-param.sync="listQuery" />
  </section>
</template>

<script>
import { findOrganListPage } from '@/api/organization'
import { productList, serviceProviderList, auditList } from '@/api/marketing/taskPromote'
import { getCategoryTreeList } from '@/api/category'
import Pagination from '@/components/Pagination'
export default {
  components: {
    Pagination
  },
  data() {
    return {
      tableData: [],
      tableColumn: [
        { prop: 'title', label: '内容名称', width: '100' },
        { prop: 'typeName', label: '内容类型', width: '100' },
        { prop: 'serviceProvider', label: '服务商', width: '100' },
        { prop: 'productName', label: '产品', width: '60' },
        { prop: 'categories', label: '内容分类', width: '60' },
        { prop: 'targetClick', label: '点击目标', width: '60' },
        { prop: 'creatorName', label: '发布人', width: '60' },
        { prop: 'author', label: '作者', width: '60' },
        { prop: 'statusName', label: '状态', width: '100' },
        { prop: 'auditName', label: '审核人', width: '100' },
        { prop: 'auditTime', label: '审核时间', width: '60' }
      ],
      total: 0,
      serverOptions: [],
      serverInfo: {},
      companyOptions: [],
      productList: [],
      categoryIdsOptions: [],
      categoryIdsProps: {
        checkStrictly: true,
        emitPath: false,
        label: 'name',
        value: 'categoryId',
        children: 'children'
      },
      typeList: [
        { label: '文章', value: 'ARTICLE' },
        { label: '视频', value: 'VIDEO' },
        { label: '抖喇图文', value: 'DOULA_ARTICLE' },
        { label: '抖喇短视频', value: 'DOULA_VIDEO' }
      ],
      statusList: [
        { label: '待审核', value: 0 },
        { label: '审核通过', value: 1 },
        { label: '审核不通过', value: 2 }
      ],
      orgListParam: {
        condition: {
          status: 1,
          type: 2002,
          serviceProviderOrgId: null
        },
        pager: {
          page: 1,
          pageSize: 100
        }
      },
      listQuery: {
        condition: {
          keyword: '',
          serviceProviderId: '',
          orgzId: '',
          productId: '',
          categoryId: null,
          status: null,
          type: null
        },
        pager: {
          page: 1,
          pageSize: 10
        }
      }
    }
  },
  created() {
    this.getList()
    this.getOrganListPage()
    this.changeOrg()
    this.getServiceList()
    getCategoryTreeList(479).then(res => {
      this.categoryIdsOptions = res
    })
  },
  methods: {
    handleFilter() {
      this.listQuery.pager.page = 1
      this.getList()
    },
    getList() {
      auditList(this.listQuery).then(res => {
        res.records.forEach(item => {
          item.author = `${item.authorInfoResp.authorName} ${item.authorInfoResp.academic} ${item.authorInfoResp.company} ${item.authorInfoResp.department}`
          item.statusName = this.statusList.find(i => i.value === item.status).label
          item.typeName = this.typeList.find(i => i.value === item.articleType).label
        })
        this.tableData = res.records
        this.total = res.total
      })
    },
    handlePagination(v) {
      this.listQuery.pager = v
      this.getList()
    },
    getServiceList() {
      serviceProviderList().then(res => {
        this.serverOptions = res
      })
    },
    getOrganListPage() {
      findOrganListPage(this.orgListParam).then(res => {
        this.companyOptions = res.records
      })
    },
    changeOrg() {
      this.getList()
      const param = {}
      if (this.listQuery.condition.orgzId !== '' && this.listQuery.condition.orgzId !== null) {
        param.orgzId = this.listQuery.condition.orgId
      }
      param.page = 1
      param.pageSize = 100
      this.listQuery.condition.productId = null
      productList(param).then(res => {
        this.productList = res
      })
    },
    toDetail(id, articleType) {
      this.$router.push(
        {
          name: articleType.indexOf('DOULA') === -1 ? 'MarketingTaskContentPreview' : 'MarketingTaskContentDoulaPreview',
          query: {
            articleId: id
          }
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  padding: 1px 25px;
  .screen {
    margin: 14px auto;
    .el-input,
    .el-date-editor {
      width: 270px;
    }
    .el-select {
      width: 150px;
    }
  }
  ::v-deep .pagination-container {
    margin: 0 auto;
  }
}
// 修改下拉列表的宽度，使得其可以自动适应内容。
::v-deep.el-autocomplete .el-popper{
    width: auto !important;
}
</style>
