import request from '@/utils/request'

// 获取合同协议类型
export function getLinkType(params) {
  return request({
    url: '/promoteBizContract/listContractAgreementType',
    method: 'get',
    params: params
  })
}

// 获取合同协议链接
export function getLink(params) {
  return request({
    url: '/promoteBizContract/getLink',
    method: 'get',
    params: params
  })
}

// 以类型获取合同协议链接
export function getContractLink(params) {
  return request({
    url: '/promoteBizContract/getContractLink',
    method: 'get',
    params: params
  })
}

// 实名审核详情
export function auditDeail(params) {
  return request({
    url: '/userPromote/auditDetail',
    method: 'get',
    params: params
  })
}

// 实名审核列表
export function auditList(params) {
  return request({
    url: '/userPromote/auditList',
    method: 'post',
    data: params
  })
}

// 审核
export function auditApply(params) {
  return request({
    url: '/userPromote/auditApply',
    method: 'post',
    data: params
  })
}

// 创作者/推广员管理-列表
export function userList(params) {
  return request({
    url: '/userPromote/userList',
    method: 'post',
    data: params
  })
}

// 创作者/推广员管理-设置详情
export function settingDetail(params) {
  return request({
    url: '/userPromote/settingDetail',
    method: 'get',
    params
  })
}

// 创作者/推广员管理-设置修改
export function settingEdit(params) {
  return request({
    url: '/userPromote/settingEdit',
    method: 'post',
    data: params
  })
}

// 创作者/推广员管理-设置修改
export function settingEditByError(params) {
  return request({
    url: '/userPromote/finishSettingEditByError',
    method: 'post',
    data: params
  })
}

// 创作者、推广员-搜索
export function searchUserPromote(params) {
  return request({
    url: '/userPromote/searchUserPromote',
    method: 'post',
    data: params
  })
}

// 获取创作者详细信息，用于后台创建的创作者，创建/编辑创作者时拉取详细信息
export function getCreatorDetail(uid) {
  return request({
    url: `/userPromote/getCreatorDetail/${uid}`,
    method: 'post'
  })
}

// 后台创建创作者-创建/编辑
export function saveAuthApply(data) {
  return request({
    url: '/userPromote/saveAuthApply',
    method: 'post',
    data
  })
}

// 重签协议(入驻协议/承揽协议)
export function reSignAgreement(data) {
  return request({
    url: '/promoteTaskUser/reSignAgreement',
    method: 'post',
    data
  })
}

// 同步至代征平台
export function syncTaxCollected(data) {
  return request({
    url: '/userPromote/syncTaxCollected',
    method: 'post',
    data
  })
}

// 获取个人内容列表
export function getMyPageList(data) {
  return request({
    url: '/promoteContent/getMyPageList',
    method: 'post',
    data
  })
}

// 内容列表是否展示
export function editShow(data) {
  return request({
    url: '/promoteContent/isShow',
    method: 'post',
    data
  })
}

// 字段显示导出设置修改
export function settingClickEdit(data) {
  return request({
    url: '/promoteFeildsSetting/settingEdit',
    method: 'post',
    data
  })
}

// 字段显示导出设置详情
export function settingClickDetail(params) {
  return request({
    url: '/promoteFeildsSetting/settingDetail',
    method: 'get',
    params
  })
}

