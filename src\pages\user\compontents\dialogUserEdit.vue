<template>
  <el-dialog :title="title" :visible.sync="visible" :show="show" width="800px" @close="close()">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item prop="realName" label="名称">
        <el-input v-model="form.realName" :disabled="isAudit" />
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="form.email" />
      </el-form-item>
      <el-form-item prop="password" label="密码">
        <el-input v-model="form.password" maxlength="11" :placeholder="title==='新增用户'?'留空密码则为mycs*89.':'留空时密码为原密码'" />
      </el-form-item>
      <el-form-item prop="phone" label="电话">
        <el-input v-model="form.phone" maxlength="11" />
      </el-form-item>
    </el-form>

    <span slot="footer">
      <el-button @click="handleCloseDialog">取 消</el-button>
      <el-button type="primary" @click="handleSaveForm">确 定</el-button>
    </span>
  </el-dialog>

</template>

<script>
import { treeList } from '@/api/major'
import { academicTreeList } from '@/api/academic'
import { validPassword } from '@/utils/validate'
export default {
  name: 'DialogUserEdit',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增用户'
    },
    data: {
      type: Object,
      default: () => {}
    },
    isAudit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value && !validPassword(value)) {
        callback(
          new Error(
            '登录密码由8-14位字符组成，包含至少两种以上字母、数字、符号两种以上组合，区分大小写'
          )
        )
      } else {
        callback()
      }
    }
    return {
      rules: {
        realName: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
          {
            min: 2,
            max: 30,
            message: '名称长度应该在4到50个字符之间',
            trigger: 'blur'
          }
        ],
        // email: [
        //   { required: true, message: '请输入email邮箱', trigger: 'blur' },
        //   { type: 'email', message: '请输入正确的email格式', trigger: 'blur' }
        // ],
        phone: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
        password: [
          { required: true, trigger: 'blur', validator: validatePassword }
        ]
      },
      visible: this.show,
      form: this.data,
      // 职称相关
      academicId: '',
      academicList: [],
      // 科室相关
      majorId: '',
      majorList: []
    }
  },
  watch: {
    show() {
      this.visible = this.show
    },
    data(v) {
      this.form = v
    }
  },
  created() {
    // 科室
    this.getMajorTree()
    // 职称
    this.getAcademicTreeList()
  },
  methods: {
    getMajorTree() {
      treeList().then(res => {
        this.majorList = res
      })
    },
    getAcademicTreeList() {
      academicTreeList().then(res => {
        this.academicList = res
      })
    },
    handleCloseDialog() {
      this.$emit('handleCancel')
    },
    close() {
      this.$emit('handleCancel')
      this.$emit('update:show', false)
    },
    handleSaveForm() {
      this.$emit('handleSave', this.data)
    }
  }
}
</script>
