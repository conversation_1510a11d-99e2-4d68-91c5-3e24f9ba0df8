<template>
  <div class="app-container">
    <div class="search-column" style="display:flex">
      <div class="search-column__item" style="margin-right:10px;">
        <el-input v-model="keyword" placeholder="请输入搜索关键字" clearable>
          <el-select slot="prepend" v-model="type" style="width:130px">
            <el-option label="姓名" value="realName" />
            <el-option label="工作单位" value="company" />
          </el-select>
        </el-input>
      </div>
      <Search @search="search" @clear="clear" />
    </div>

    <el-table :data="tableList" border stripe>
      <el-table-column v-for="col in tableColumnList" :key="col.id" v-bind="col">
        <template slot-scope="scope">
          <span>{{ scope.row[col.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="80px">
        <template slot-scope="{row}">
          <el-button type="text" @click="openDetail(row.uid)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination class="text-center" :page-size="tableQuery.pager.pageSize" :total="total" :page="tableQuery.pager.page" @pagination="handlePagination" />

    <el-dialog title="讲师信息" :visible.sync="visible" width="50vw" top="20px">
      <el-row class="detail">
        <el-form
          ref="form"
          :model="detail"
          label-width="200px"
          label-position="right"
        >
          <h2>个人信息</h2>
          <el-form-item label="真实姓名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="身份证号码:">
            <span>{{ detail.idcard }}</span>
          </el-form-item>
          <el-form-item label="身份证正/反面:">
            <div class="imgBox">
              <el-image
                style="width: 300px"
                fit="cover"
                :src="detail.idcardFrontImgUrl"
                :preview-src-list="[detail.idcardFrontImgUrl]"
              />
              <el-image
                style="width: 300px"
                fit="cover"
                :src="detail.idcardBehindImgUrl"
                :preview-src-list="[detail.idcardBehindImgUrl]"
              />
            </div>
          </el-form-item>
          <el-form-item label="身份:">
            <span>{{ detail.identityName }}</span>
          </el-form-item>
          <el-form-item label="执业专科:">
            <span>{{ detail.majorName }}</span>
          </el-form-item>
          <el-form-item label="资格证书:">
            <el-image
              style="width: 300px"
              fit="cover"
              :src="detail.certImgUrl"
              :preview-src-list="[detail.certImgUrl]"
            />
          </el-form-item>
          <el-form-item label="执业证书:">
            <el-image
              style="width: 300px"
              fit="cover"
              :src="detail.practiceCertImgUrl"
              :preview-src-list="[detail.practiceCertImgUrl]"
            />
          </el-form-item>
          <el-form-item label="职称:">
            <span>{{ detail.academicName }}</span>
          </el-form-item>
          <el-form-item label="职称证书:">
            <el-image
              style="width: 300px"
              fit="cover"
              :src="detail.academicImgUrl"
              :preview-src-list="[detail.academicImgUrl]"
            />
          </el-form-item>
          <el-form-item label="工作单位:">
            <span>{{ detail.company }}</span>
          </el-form-item>
          <el-form-item label="科室/部门:">
            <span>{{ detail.department }}</span>
          </el-form-item>
          <el-form-item label="所在地区:">
            <span>{{ detail.areaName }}</span>
          </el-form-item>
          <el-form-item label="擅长:">
            <span>{{ detail.skill }}</span>
          </el-form-item>
          <el-form-item label="简介:">
            <span>{{ detail.introduction }}</span>
          </el-form-item>
          <el-form-item label="头像:">
            <el-image
              style="width: 300px"
              fit="cover"
              :src="detail.avatarUrl"
              :preview-src-list="[detail.avatarUrl]"
            />
          </el-form-item>
          <el-form-item label="手机:">
            <span>{{ detail.phone }}</span>
          </el-form-item>
          <el-form-item label="内容扩展方:">
            <span>{{ detail.contentExtenderCode }}</span>
          </el-form-item>

          <h2>结算信息</h2>
          <el-form-item label="开户行:">
            <span>{{ detail.bankName }}</span>
          </el-form-item>
          <el-form-item label="户名:">
            <span>{{ detail.realName }}</span>
          </el-form-item>
          <el-form-item label="银行卡号:">
            <span>{{ detail.bankcard }}</span>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="text-align:center">
        <el-button @click="visible = false">返  回</el-button>
      </el-row>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getLeturList, getLeturDetail } from '@/api/liveApply'
import Search from '@/pages/operationManage/live/components/search_1.vue'

export default {
  name: 'LecturerList',
  components: { Pagination, Search },
  data() {
    return {
      keyword: '',
      type: 'realName',
      // 表格表头
      tableColumnList: Object.freeze([
        { id: 0, label: '用户ID', align: 'center', prop: 'uid', width: '100px' },
        { id: 1, label: '姓名', align: 'center', prop: 'realName' },
        { id: 2, label: '身份', align: 'center', prop: 'identityName' },
        { id: 3, label: '专科', align: 'center', prop: 'majorName' },
        { id: 4, label: '职称', align: 'center', prop: 'academicName' },
        { id: 5, label: '工作单位', align: 'center', prop: 'company' },
        { id: 6, label: '科室/部门', align: 'center', prop: 'department' },
        { id: 7, label: '所在地区', align: 'center', prop: 'areaName', width: '170px' },
        { id: 8, label: '入驻时间', align: 'center', prop: 'auditTime', width: '160px' },
        { id: 9, label: '直播场次', align: 'center', prop: 'liveNumber', width: '80px' }
      ]),
      // 请求参数
      tableQuery: {
        condition: {},
        pager: {
          page: 1,
          pageSize: 10
        }
      },
      total: 0,
      tableList: [],
      detail: {},
      visible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search(condition) {
      if (condition) {
        this.tableQuery.condition = condition
      }
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.company = ''
      this.tableQuery.condition[this.type] = this.keyword
      this.tableQuery.pager.page = 1
      this.getList()
    },
    clear(condition) {
      this.tableQuery.condition = condition
      this.tableQuery.condition.realName = ''
      this.tableQuery.condition.company = ''
      this.keyword = ''
      this.tableQuery.pager.page = 1
      this.getList()
    },
    getList() {
      getLeturList(this.tableQuery).then(res => {
        this.tableList = res.records
        this.total = res.total
      })
    },
    // 分页操作
    handlePagination(val) {
      this.tableQuery.pager = val
      this.getList()
    },
    openDetail(uid) {
      getLeturDetail(uid).then(res => {
        this.detail = res
        this.visible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
