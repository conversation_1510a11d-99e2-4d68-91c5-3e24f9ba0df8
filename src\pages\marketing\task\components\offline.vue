<template>
  <el-dialog
    title="线下授权"
    :visible.sync="dialogVisible"
    width="420px"
    :before-close="handleClose"
  >
    <div>
      <p class="el-icon-warning-outline"> 请确保线下已签署版权授权协议, 避免法律纠纷</p>
      <div class="content">
        <span>授权协议:</span>
        <el-upload
          :class="{ hideUpdate: fileList.length >= 1 }"
          :action="uploadFileApi"
          :data="uploadData"
          :limit="1"
          :file-list="fileList"
          :before-upload="handleBeforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
        >
          <el-button class="el-icon-plus" size="mini" type="primary">上传</el-button>
        </el-upload>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadFileApi, preUploadApi } from '@/api/biz'
import { offlineAuthorSign, offlineAuthorSignDoula } from '@/api/marketing/taskExecute'
export default {
  name: 'Offline',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'creator'
    }
  },
  data() {
    return {
      uploadFileApi,
      fileId: null,
      fileList: [],
      uploadData: { data: '' }
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false)
    },
    async handleBeforeUpload(val) {
      const param = {
        filename: val.name,
        size: val.size,
        type: 'adv'
      }
      // upload without token
      const res = await preUploadApi(param)
      this.uploadData.data = res
    },
    handleSuccess(res, file, fileList) {
      if (res.code !== 1) {
        fileList.splice(fileList.length - 1, 1)
        switch (res.code) {
          case 400:
          case 40003:
            this.$alert('登录已过期，请重新登录', '温馨提示', {
              confirmButtonText: '确定',
              showClose: false,
              lockScroll: true,
              callback: (action) => {
                this.$store.dispatch('user/resetToken').then(() => {
                  location.reload()
                })
              }
            })
            break
          default:
            this.$message.error(res.msg)
        }
      } else {
        this.fileId = res.data.id
        this.fileList = [
          {
            id: res.data.id,
            url: res.data.url,
            name: file.name
          }
        ]
      }
    },
    handleRemove() {
      this.fileId = ''
      this.fileList = []
    },
    confirm() {
      const authorSign = this.type === 'creator' ? offlineAuthorSign : offlineAuthorSignDoula
      authorSign({ fileId: this.fileId, taskUserId: this.id }).then(() => {
        this.handleClose()
        this.$parent.getExecuteList()
        this.$message.success('授权成功')
      }).catch(err => {
        this.$message.error(err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    display: flex;
    align-items: center;
    padding: 0 24px;
    color: #333;
    display: flex;
    align-items: center;
    p {
      font-size: 12px;
      color: #999;
    }
    .content {
      display: flex;
      align-items: center;
      span {
        margin-right: 10px;
      }
    }
  }
}
.hideUpdate {
  ::v-deep .el-upload--text {
    display: none;
  }
}
</style>
