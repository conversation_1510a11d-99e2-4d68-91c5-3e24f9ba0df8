import request from '@/utils/request'

// 创作任务执行列表
export function executeList(params) {
  return request({
    url: '/promoteTaskUser/userTaskListCms',
    method: 'post',
    data: params
  })
}

// 拜访任务执行列表
export function executeListVisit(params) {
  return request({
    url: '/promoteVisitUser/userTaskListCms',
    method: 'post',
    data: params
  })
}

// 抖喇任务执行列表
export function executeListDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/userTaskListCms',
    method: 'post',
    data: params
  })
}

// 创作任务核算费用
export function calculateFee(params) {
  return request({
    url: '/promoteTaskUser/calculateFeeCms',
    method: 'post',
    data: params
  })
}

// 拜访任务核算费用
export function calculateFeeVisit(params) {
  return request({
    url: '/promoteVisitUser/calculateFeeCms',
    method: 'post',
    data: params
  })
}

// 抖喇任务核算费用
export function calculateFeeDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/calculateFeeCms',
    method: 'post',
    data: params
  })
}

// 创作任务支付
export function uploadCertificate(params) {
  return request({
    url: '/promoteTaskUser/uploadCertificate',
    method: 'post',
    data: params
  })
}

// 拜访任务支付
export function uploadCertificateVisit(params) {
  return request({
    url: '/promoteVisitUser/uploadCertificate',
    method: 'post',
    data: params
  })
}

// 抖喇任务支付
export function uploadCertificateDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/uploadCertificate',
    method: 'post',
    data: params
  })
}

// 导出创作任务执行数据包
export function exportTaskUserExecZip(params) {
  return request({
    url: '/promoteTaskUser/exportTaskUserExecZip',
    method: 'get',
    params
  })
}

// 导出拜访任务执行数据包
export function exportTaskUserExecZipVisit(params) {
  return request({
    url: '/promoteVisitUser/exportTaskUserExecZip',
    method: 'get',
    params
  })
}

// 导出抖喇任务执行数据包
export function exportTaskUserExecZipDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/exportTaskUserExecZip',
    method: 'get',
    params
  })
}

// 发布文章/视频
export function saveArticle(params) {
  return request({
    url: '/promoteTaskUser/saveOrUpdateArticleCms',
    method: 'post',
    data: params
  })
}

// 审核文章/视频
export function auditArticle(params) {
  return request({
    url: '/promoteTaskUser/auditArticleCms',
    method: 'post',
    data: params
  })
}

// 查看文章/视频
export function lookArticle(params) {
  return request({
    url: '/promoteTaskUser/getArticleCms',
    method: 'get',
    params
  })
}

// 发布抖喇文章/视频
export function saveArticleDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/saveOrUpdateArticleCms',
    method: 'post',
    data: params
  })
}

// 审核抖喇文章/视频
export function auditArticleDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/auditArticleCms',
    method: 'post',
    data: params
  })
}

// 查看抖喇文章/视频
export function lookArticleDoula(params) {
  return request({
    url: '/promoteDoulaTaskUser/getArticleCms',
    method: 'get',
    params
  })
}

// 作者列表
export function authorList(query) {
  return request({
    url: '/promoteTaskUser/selectAuthor',
    method: 'post',
    data: query
  })
}

export function authorListDoula(query) {
  return request({
    url: '/promoteDoulaTaskUser/selectAuthor',
    method: 'post',
    data: query
  })
}

// 线下授权
export function offlineAuthorSign(query) {
  return request({
    url: '/promoteTaskUser/offlineAuthorSign',
    method: 'post',
    data: query
  })
}

// 线下授权
export function offlineAuthorSignDoula(query) {
  return request({
    url: '/promoteDoulaTaskUser/offlineAuthorSign',
    method: 'post',
    data: query
  })
}

// 重签作者授权协议
export function reSignAuthorizationAgreement(id) {
  return request({
    url: `/promoteTaskUser/reSignAuthorizationAgreement/${id}`,
    method: 'post'
  })
}

// 重签抖喇作者授权协议
export function reSignAuthorizationAgreementDoula(id) {
  return request({
    url: `/promoteDoulaTaskUser/reSignAuthorizationAgreement/${id}`,
    method: 'post'
  })
}

// 重签创作任务费用确认单
export function reSignCostConfirmation(id) {
  return request({
    url: `/promoteTaskUser/reSignCostConfirmation/${id}`,
    method: 'post'
  })
}

// 重签拜访任务费用确认单
export function reSignCostConfirmationVisit(id) {
  return request({
    url: `/promoteVisitUser/reSignCostConfirmation/${id}`,
    method: 'post'
  })
}

// 重签抖喇任务费用确认单
export function reSignCostConfirmationDoula(id) {
  return request({
    url: `/promoteDoulaTaskUser/reSignCostConfirmation/${id}`,
    method: 'post'
  })
}

// 重签创作任务确认单
export function reSignTaskConfirmation(id) {
  return request({
    url: `/promoteTaskUser/reSignTaskConfirmation/${id}`,
    method: 'post'
  })
}

// 重签拜访任务确认单
export function reSignTaskConfirmationVisit(id) {
  return request({
    url: `/promoteVisitUser/reSignTaskConfirmation/${id}`,
    method: 'post'
  })
}

// 重签抖喇任务确认单
export function reSignTaskConfirmationDoula(id) {
  return request({
    url: `/promoteDoulaTaskUser/reSignTaskConfirmation/${id}`,
    method: 'post'
  })
}

// 根据执行id获取拜访列表
export function userVisitRecordListCms(params) {
  return request({
    url: `/visitRecord/userVisitRecordListCms`,
    method: 'post',
    data: params
  })
}

// 查看拜访详情
export function getVisitRecordCms(params) {
  return request({
    url: '/visitRecord/getVisitRecordCms',
    method: 'get',
    params
  })
}

// 审核拜访
export function auditVisitRecordCms(params) {
  return request({
    url: `/visitRecord/auditVisitRecordCms`,
    method: 'post',
    data: params
  })
}

