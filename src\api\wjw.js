import request from '@/utils/request'

// 卫计委列表
export function wjwList(params) {
  return request({
    url: `/wjw/system/organization/list`,
    method: 'post',
    data: params
  })
}

// 机构添加下级列表
export function addWjwSubOrg(params) {
  return request({
    url: `/wjw/system/add/sub`,
    method: 'post',
    data: params
  })
}

// 卫计委获取下级
export function getChildrenByOrgId(params) {
  return request({
    url: `/wjw/system/children/by/oid/${params}`,
    method: 'get'
  })
}

// 卫计委解绑
export function unbindWjw(params) {
  return request({
    url: `/wjw/system/unbind/${params}`,
    method: 'post'
  })
}

// 卫计委子级列表
export function getWjwSubList(params) {
  return request({
    url: `/wjw/system/wjw/sub/list`,
    method: 'post',
    data: params
  })
}

// 医院子级列表
export function getHospitalSubList(params) {
  return request({
    url: `/wjw/system/hospital/list`,
    method: 'post',
    data: params
  })
}

export default {
  // 下载导入卫计委模板
  getDownloadWjwTemplate(token) {
    return process.env.VUE_APP_BASE_API + `/organization/excel/download/wjw/template?token=${token}`
  },
  // 下载导入医院模板
  getDownloadYyTemplate(token) {
    return process.env.VUE_APP_BASE_API + `/organization/excel/download/yy/template?token=${token}`
  },
  // 导入卫计委
  getImportWjw() {
    return process.env.VUE_APP_BASE_API + `/organization/excel/import/wjw`
  },
  // 导入医院
  getImportYy() {
    return process.env.VUE_APP_BASE_API + `/organization/excel/import/yy`
  },
  // 导入单位记录列表
  getImportHistory(params) {
    return request({
      url: `/organization/excel/import/record/list`,
      method: 'post',
      data: params
    })
  },
  // 导出结果消息
  getDownloadRecordMessage(params, token) {
    return process.env.VUE_APP_BASE_API + `/organization/excel/download/record/rowError?recordId=${params.recordId}&token=${token}`
    // return request({
    //   url: `/download/record/message`,
    //   method: 'get',
    //   data: params
    // })
  }
}
