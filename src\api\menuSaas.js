import request from '@/utils/request'

export default {
  // 菜单权限列表
  list(params) {
    return request({
      url: '/saas/menu/list',
      method: 'get'
    })
  },
  // 添加
  create(data) {
    return request({
      url: '/saas/menu/create',
      method: 'post',
      data
    })
  },
  // 删除
  delete(params) {
    return request({
      url: `/saas/menu/delete/${params}`,
      method: 'get'
    })
  },
  // 编辑
  edit(data) {
    return request({
      url: `/saas/menu/edit/`,
      method: 'post',
      data
    })
  },
  // 启用/禁用
  activate(params) {
    return request({
      url: `/saas/menu/enable/disable/${params}`,
      method: 'get'
    })
  }
}
